<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

    <%--<div style="width: 600px; height: 15px;">--%>
        <%--<h3>${vo.busTitle}</h3>--%>
    <%--</div>--%>



<div id="system_org_tbaList" class="easyui-layout"  fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="tsNoticeList" title="业务动态管理"  actionUrl="tsBusinessDynamicsVoWebController.do?findTsBusinessDynamicsUserList&id=${vo.id}"
                    singleSelect="false"  idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
            <t:dgCol title="姓名" width="120" field="fullName" query="true"></t:dgCol>
            <t:dgCol title="部门" width="120" field="orgName"></t:dgCol>

            <t:dgCol title="职位" width="120" field="positionName" ></t:dgCol>

            <t:dgCol title="是否分享" width="120" field="isShare" hidden="true" query="true"></t:dgCol>

            <t:dgCol title="是否已读" width="120" field="isRead" hidden="true" query="true"></t:dgCol>
        </t:datagrid>
    </div>
    <input type="text">
</div>






<script type="text/javascript">
    $(function () {
    //     $("input[name*='pushDate']").addClass("pushDate").css({
    //         'width': '100px'
    //     }).removeAttr("onfocus").on("focus", function () {
    //         WdatePicker({dateFmt: 'yyyy-MM-dd'});
    //     });

        $("input[name='isShare']").combobox({
            url:'',
            valueField:'value',
            textField:'text',
            data: [{
                text: '未分享',
                value: '0'
            },{
                text: '已分享',
                value: '1'
            }]
        });

        $("input[name='isRead']").combobox({
            url:'',
            valueField:'value',
            textField:'text',
            data: [{
                text: '未读',
                value: '0'
            },{
                text: '已读',
                value: '1'
            }]
        });



    })


</script>











