<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>知识库管理</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <style>
    .sureFiles {display:none;}
    .sureFilesDetail_a{float:left; margin-left: -130px;}
    .sureFilesDetail_img{float:left;padding:4px 15px 0 5px;cursor:pointer;}
	 .icon-download {
		 display: inline-block;
		 width: 130px;
		 padding-left: 150px;
		 margin-top: 3px;
	 }
	 #content .inputxt,#steps form select{width:250px;}
	    
        #formobj {
            padding: 0 5px;
        }

        #steps form div.form {
            float: left;
            width: 300px;
            min-height:22px;
        }

        #steps form div.form-2 {
            min-width: 700px;
        }

        #steps form div.form-2 textarea {
            width:100%;
        }
        
        #steps form div.form .Validform_label {
            width: 85px;
            margin-right: 5px;
            font-weight:normal;
        }
        
        #steps form div.form .Validform_label input[type="radio"],#steps form div.form .Validform_label input[type="checkbox"]{
        	position:absolute;
        	left:3px;
        	top:3px;
        	width:15px;
        	height:15px;
        }

        .formDiv {
            float: left;
        }

        .line-title {
            font-size: 14px;
            padding: 5px;
            /* background:#b4d2ea; */
        }
		
		.form_label{float:left;padding-right: 20px;line-height:26px;}
        .form_label input[type="radio"] {
            float: left;
            margin: 5px 0 0 0px;
            width: 17px;
            height: 17px;
        }

        .promotionForm_tem {
            margin: 10px;
            padding: 5px;
            border: 1px solid #a5aeb6;
            background: #f5f5f5;
            line-height: 30px;
        }

        .promotionForm_tem input {
            margin: 0 10px;
            width: 60px;
            border: 0;
        }
        .promotionArea{border: 1px solid #ddd;background: #fff;min-height: 80px;padding:5px;width: 310px;}
        .promotionArea a{margin-right:5px;}
        .selectArea{background:#ffc6f2 !important;}
        
  </style>
  
  <script type="text/javascript">
        //编写自定义JS代码
        String.prototype.trim = function () {
            return this .replace(/^\s\s*/, '' ).replace(/\s\s*$/, '' );
        };

        function uploadFile(data){

            if($("#filediv").children().length>0){
            	$("#file_upload").data("uploadifive").settings.formData={"file_upload":data.flagId};
                $("#file_upload").uploadifive("upload");
                return false;
            }else{
                frameElement.api.opener.reloadTable();
                frameElement.api.close();
            }
            W.tip(data.msg);
        }
	</script>
</head>
	<body>
	<t:formvalid formid="formobj" dialog="true" callback="@Override uploadFile" layout="div"
			 action="tsKnowledgeBaseController.do?saveTsKnowledge" beforeSubmit="caculeteLine()">
			<input id="id" name="id" type="hidden" value="${knowledgeBaseVo.id}">
			<div>
			<div class="form">
            	<label class="Validform_label"><span style="color:red;">*</span>文档标题:</label>
            	<input datatype="*" name="knowledgeTitle" id="knowledgeTitle" value="${knowledgeBaseVo.knowledgeTitle}">
        	</div>
			<div class="form">
            	<label class="Validform_label"><span style="color:red;">*</span>文档内容:</label>
            	<textarea style="width: 100%;height: 100px;padding:10px" cols="30" rows="5" ignore="ignore" name="knowledgeContent" >${knowledgeBaseVo.knowledgeContent}</textarea>
        	</div>
        	<div class="form">
            	<label class="Validform_label">文档类型:</label>
            	<t:dictSelect field="knowledgeType" dataType="*" typeGroupCode="knowledge_type" hasLabel="false" defaultVal="${knowledgeBaseVo.knowledgeType}"></t:dictSelect>
            	
        	</div>
        	<div class="">
            	<table style="width:96%;">
			<tr>
				<td>
					&nbsp;&nbsp;&nbsp;&nbsp; <label>组织范围:</label>	
				</td>
			</tr>
			<tr>
				<td>
					<div style="border:1px solid #c3cbce;margin:0 20px;">
						<label>
							<a name="" style="color:#fff; font-size:12px;background-color:#5c98b0;padding:5px 15px;display:inline-block;">包含</a>
							<a name="deladdarea" href="#" onclick="areaAdd('#getArea');" style="color:#31357c;margin-left:15px; font-size:12px;">组织添加</a>
							<%--<a name="delete" href="#" onclick="deleteArea(this,'#getArea');" style="color:#31357c;margin-left:15px;font-size:12px;">删除</a>--%>
						</label>
						<div id="getArea" style="overflow:hidden;overflow-y:auto;height:100px;padding:10px;">
							
							<%-->调取方法    fillSpan(data,aimObj)   data--> 后台数据，aimObj，目标对象，这里是'#getArea'--%>
						</div>
						 <input id="receivingOrgCode" type="hidden" name="receivingOrgCode" value="">
						 <input id="receivingOrgName" type="hidden" name="receivingOrgName" value="">
					</div>		
				</td>
			</tr>			
		</table>
        	</div>
        	<div class="form">
			<label class="Validform_label">附件：</label>

			<c:forEach var="piclist" items="${pics}">
				<div >
					    <span>
							${piclist.imgTypeRemark}
						 </span>
					<span class='delpart'>
						   <a href="#" onclick="deljt(this,'${piclist.id}');" style="color:red; font-size:12px;">删除</a>
					    </span>
				</div>
			</c:forEach>
	</div>
        	</div>	
        <div class="form">
			<label class="Validform_label">知识库附件: </label>
		</div>
	<div class="form">
		<label style="color: green">可上传附件类型:"*.doc、*.docx、*.xls、*.xlsx、*.ppt、*.pdf、*.jpg、*.jpeg、*.png、*.gif、*.ico"</label>
		<div class="form eispDetailss">
			<t:uploadH5 name="fiels"  id="file_upload" onUploadSuccess="uploadForSet"
					  extend="*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pdf;*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.ico;*.tif"
					  buttonText="添加文件"
					  uploader="tsKnowledgeBaseController.do?saveProductfile">
			</t:uploadH5>
		</div>
		<div class="form" id="filediv"></div>
	</div>
		</t:formvalid>
	</body>
	<script type="text/javascript">
	var certId = "${knowledgeBaseVo.id}";

	$(function(){
		var receivingOrgCode = "${knowledgeBaseVo.receivingOrgCode}".split(",");
		var receivingOrgName = "${knowledgeBaseVo.receivingOrgName}".split(",");
		var appendtext = "";
		if(receivingOrgCode !=null && receivingOrgCode !="") {
			for (var i = 0; i < receivingOrgCode.length; i++) {

				appendtext += '<tr>';
				appendtext += '<td class="myfirst">' + receivingOrgName[i] + '</td>';
				appendtext += '<td class="mysecond">' + receivingOrgCode[i] + '</td>';
				appendtext += '<td onclick="delselectedTd(this)" style="color:blue; font-size:12px;">删除</td>';
				appendtext += '</tr>';
			}
		}
		$("#getArea").append(appendtext);
	})	
	
	//区域添加
	function areaAdd(par){
		var orgId;
		var orgName;
		var appendtext = "";
		$.dialog({
			content : 'url:' + 'tsKnowledgeBaseController.do?gotsOrgMainForKnowledgeBase',
	          title : "组织管理",
	          cache : false,
	          lock : true,
	          width : 500,
	          height : 400,
	          zIndex: 9999,
	          ok : function() {

	        	  iframe = this.iframe.contentWindow;
                  var selected = iframe.getSelectRows();
                  $.each(selected, function (i, n) {

					  appendtext +='<tr>';
					  appendtext +='<td class="myfirst">'+n.text+'</td>';
					  appendtext +='<td class="mysecond">'+n.orgCode+'</td>';
					  appendtext +='<td onclick="delselectedTd(this)" style="color:blue; font-size:12px;">删除</td>';
					  appendtext +='</tr>';
                  });
                  $("#getArea").append(appendtext);
	          }
		});
	}
	function delselectedTd(obj){
		$(obj).parent().remove();
	}
	//提交前编辑范围
	function caculeteLine(){
		var innnerp ;
		var innerpname;
		var inputp = $("#getArea").children('tr').children('td.mysecond');
		var inputpname = $("#getArea").children('tr').children('td.myfirst');

		 $.each(inputp, function (i, n) {
			 if(i==0){
				 innnerp =n.innerHTML;
			 }else{
			 	innnerp =innnerp+ ','+n.innerHTML;
			 }
		 });
		$.each(inputpname, function (i, n) {
			if(i==0){
				innerpname =n.innerHTML;
			}else{
				innerpname =innerpname+ ','+n.innerHTML;
			}
		});
		$("#receivingOrgCode").val(innnerp);
		$("#receivingOrgName").val(innerpname);

	}
	
	//删除图片
    function deljt(obj,this_id){
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function () {

            $.ajax({
                async: false,
                cache: false,
                type: 'POST',
                data : {
                    id : this_id
                },
                url: 'tsKnowledgeBaseController.do?delPic',// 请求的action路径
                beforeSend: beforeSendFun,
                complete: completeFun,
                error: function () {// 请求失败处理函数
                    $.messager.show({
                        title : '提示消息',
                        msg : '删除失败',
                        timeout : 2000,
                        showType : 'slide'
                    });
                },
                success: function (data) {
                    var d = $.parseJSON(data);
                    var msg = d.msg;
                    tip(d.msg);
                    if (d.success) {
                        $(obj).parent().parent().remove();
                    }
                }
            });
        });
    }
  //complete 方法
    function completeFun() {
        $.messager.progress('close');
        _flag = false;
    }

    //beforeSend 方法
    function beforeSendFun() {
        _flag = true;
        $.messager.progress({text: '数据操作中~~~~'});
    }
    function uploadForSet(d,file,response){
		frameElement.api.opener.reloadTable();
		frameElement.api.close();
        W.tip(d.msg);
    }

</script>
</html>