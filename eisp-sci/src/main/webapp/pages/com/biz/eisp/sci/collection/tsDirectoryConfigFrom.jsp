<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div class="easyui-layout" fit="true">
	<div region="west" style="width:400px;">
		<t:formvalid formid="formobj" layout="div" dialog="true" action="tsDirectoryConfigController.do?saveTsDirectoryConfig" beforeSubmit="addInputInfo" refresh="true">
			<input type="hidden" id="inputConfigInfo" name="inputConfigInfo">
			<input type="hidden" id="id" name="id" value="${vo.id}">
			<div class="form">
				<label class="Validform_label">拜访对象: </label>
				<input type="hidden" name="visitObjectName" id="visitObjectName"  value="${vo.visitObjectName}" />
				<t:dictSelect id="visitObjectCode" field="visitObjectCode" type="select" defaultVal="${vo.visitObjectCode}"
							  typeGroupCode="visit_obj" dataType="*" extend="onchange='changeObj();'">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">主目录名称: </label>
				<input type="hidden" name="primaryDirectoryName" id="primaryDirectoryName" value="${vo.primaryDirectoryName}" />
				<t:dictSelect id="primaryDirectoryCode" field="primaryDirectoryCode" type="select" defaultVal="${vo.primaryDirectoryCode}"
							  typeGroupCode="primary_directory" dataType="*" extend="onchange='changePrimary();'">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">所属事业部: </label>
				<input type="hidden" name="businessUnitName" id="businessUnitName" value="${vo.businessUnitName}" />
				<t:dictSelect id="businessUnitCode" field="businessUnitCode" type="checkbox" defaultVal="${vo.businessUnitCode}"
							  typeGroupCode="SYBManager" dataType="*" >
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">目录名称: </label>
				<input type="text" name="directoryName" id="directoryName" datatype="*" value="${vo.directoryName}" />
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">采集类型: </label>
				<input type="hidden" name="collectionType" id="collectionType" datatype="*" value="${vo.collectionType}">
				<%--<c:if test="${not empty vo.collectionType}">--%>
					<label><input name="type" type="checkbox" onclick="change(this)" value="1" <c:if test="${fn:contains(vo.collectionType,'1')}">checked="checked"</c:if> />SCI</label>
					<label><input name="type" type="checkbox" onclick="change(this)" value="2" <c:if test="${fn:contains(vo.collectionType,'2')}">checked="checked"</c:if>/>DRP</label>
				<%--</c:if>
				<c:if test="${empty vo.collectionType}">
					<label><input name="type" type="checkbox" value="1" />SCI</label>
					<label><input name="type" type="checkbox" value="2" />DRP</label>
				</c:if>--%>
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">是否启用: </label>
				<t:dictSelect id="enableStatus" field="enableStatus" type="radio" defaultVal="${vo.enableStatus}"
							  typeGroupCode="market_yn" dataType="*">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">备注: </label>
				<textarea rows="3" cols="20"  name="remark" id="remark" >${vo.remark}</textarea>
			</div>
		</t:formvalid>
	</div>
	<div region="center">
		<t:datagrid name="tsInputConfigList" title="临时采集项控件"  actionUrl="tsDirectoryConfigController.do?findTsInputConfigListByDirectId&directId=${vo.id}"
					idField="id" fit="true"  fitColumns="false" pagination="false" queryMode="group" onLoadSuccess="starEdit" >
			<t:dgCol title="id"  field="id"  hidden="true"  queryMode="single"  ></t:dgCol>

			<t:dgCol title="控件编码"  field="lableCode"  hidden="false"  queryMode="single"  ></t:dgCol>
			<t:dgCol title="控件名称"  field="lableName"  hidden="false"  queryMode="single"  ></t:dgCol>
			<t:dgCol title="控件类型"  field="inputType"  hidden="false" dictionary="input_type"  queryMode="single"  ></t:dgCol>
			<t:dgCol title="控件值对应数据字典编码"  field="dictionaryCode"  hidden="false"  queryMode="single"  ></t:dgCol>
			<t:dgCol title="后缀"  field="postfix"  hidden="false"  queryMode="single"  ></t:dgCol>
			<t:dgCol title="备注"  field="remark"  hidden="false"  queryMode="single"  ></t:dgCol>
			<t:dgCol title="排序"  field="sort"  hidden="false"  queryMode="single" editor="{type:'numberbox',options:{min:0}}"></t:dgCol>
			<c:if test='${type!="detail"}'>
			<t:dgToolBar title="添加" icon="icon-add" url="" funname="addInputConfig" ></t:dgToolBar>
			<t:dgToolBar title="移除" icon="icon-remove" id="removeBar" url="" funname="removeInputConfig" ></t:dgToolBar>
			</c:if>
		</t:datagrid>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
	function starEdit(rowData){
        var rows = rowData.rows;
        var gridObj = $("#tsInputConfigList");
	    $.each(rows,function (index,row) {
            openEdit(gridObj,index);
        });
	}
	function openEdit(gridObj,index){
        gridObj.datagrid('endEdit', index);
        gridObj.datagrid('beginEdit', index);
	}
    function sortInput(value) {
        if(value==null){
            value="";
		}
		var str = "<input type='text' onkeyup='verifyNum(this)'  value='"+value+"' name='sort'>";
		return str;
    }

    function verifyNum(obj) {
        obj.value = obj.value.replace(/[^\d]/g,""); // 清除"数字"以外的字符
    }

	//拜访对象名称
	function changeObj() {
        var checkText=$("#visitObjectCode").find("option:selected").text();
        $("#visitObjectName").val(checkText);
    }
    //主目录名称
    function changePrimary(){
	    var name=$("#primaryDirectoryCode").find("option:selected").text();
	    $("#primaryDirectoryName").val(name);
	}
    //添加控件
    function addInputConfig() {
        var dg = $('#tsInputConfigList');
        var ids="";
        var rows = $("#tsInputConfigList").datagrid("getRows");
        var ids="";
        if(rows!=null){
            for(var i=0;i<rows.length;i++) {
                if(ids==""){
                    ids+=rows[i].id;
                }else{
                    ids+=","+rows[i].id;
                }
            }
		}

        var myOptions = {
            content : "url:tsInputConfigController.do?selectInputConfig&ids="+ids ,
            lock : true,
            width : 800,
            height : 600,
            title : "添加控件",
            opacity : 0.3,
            cache : true,
            async: false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var datas = iframe.checkQuoAct();
                if(datas!=""){
                    for(i in datas){
                        var dataObj = datas[i];
                        dataObj.sort="";
						dg.datagrid("appendRow",dataObj);
                        toOpenEdit(dg,dataObj);
                    }
                    return true;
                }else{
                    return false;
                }
            },
            cancelVal : '关闭',
            cancel : true
        };
        safeShowDialog(myOptions);
    }
    function toOpenEdit(grObj,row){
        var index = grObj.datagrid("getRowIndex",row);
        openEdit(grObj,index);
	}
    //移除
    function removeInputConfig(){
        var obj = $('#tsInputConfigList');
        var rows = obj.datagrid("getSelections");
        if (rows.length <= 0) {
            tip("请选择要移除的控件","error");
            return ;
        }
        //移除数据
        var selectRows = [];
        for (var i = 0; i < rows.length; i++) {
            var dataTemp = rows[i];
            selectRows.push(dataTemp);
        }
        for(var j =0;j<selectRows.length;j++){
            var index = $('#tsInputConfigList').datagrid('getRowIndex',selectRows[j]);
            obj.datagrid('deleteRow',index);
        }
    }
    function change(obj){
        var checks = document.getElementsByName("type");
        var types = "";
        if(checks&&checks.length>0){
            for(var i=0;i<checks.length;i++){
                if(checks[i].checked){
                    if(types.length>0){
                        types += "," + checks[i].value;
                    }else{
                        types += checks[i].value;
                    }
                }
            }
        }
        $("#collectionType").val(types);
	}
    //提交前验证
    function addInputInfo() {
        var r=document.getElementsByName("businessUnitCode");
        var name="";
        for(var i=0;i<r.length;i++){
            if(r[i].checked){
                if(name==""){
                    name+=r[i].nextSibling.nodeValue;
				}else{
                    name+=","+r[i].nextSibling.nodeValue;
				}
            }
        }
        $("#businessUnitName").val(name);

		var dg=$('#tsInputConfigList');
		var rows = dg.datagrid("getRows");
		for(var i=0;i<rows.length;i++){
            rows[i].sort = getEditorValue(dg,i,"sort");
		}
        var inputInfo=JSON.stringify(rows);
		$("#inputConfigInfo").val(inputInfo);
		return true;
    }

    // 获取输入框编辑器对象
    function getEditorObj(gridObj,index,clomnName){
        // 获取输入框编辑器对象
        return gridObj.datagrid('getEditor', {index:index,field:clomnName});
    }
    //返回数据
    function returnEditorValueByEditorObj(ed){
        return $(ed.target).val();
    }
    //获取编辑器的值
    function getEditorValue(gridObj,index,clomnName){
        // 获取输入框编辑器的值
        var ed = getEditorObj(gridObj,index,clomnName);
        return $(ed.target).val();
    }

</script>