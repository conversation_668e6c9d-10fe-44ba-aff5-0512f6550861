<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0,uc-fitscreen=yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <title>走访</title>
    <!-- miniMObile.css、js -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/css/miniMobile.css"/>
    <script type="text/javascript" src="pages/mobile/js/zepto.min.js"></script>
    <script type="text/javascript" src="pages/mobile/js/miniMobile.js"></script>
    <!-- mobileSelect -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/mobileSelect/mobileSelect.css">
    <script src="pages/mobile/plugins/mobileSelect/mobileSelect.js" type="text/javascript"></script>
    <!-- icheck -->
    <script src="pages/mobile/plugins/icheck/icheck.js" type="text/javascript" charset="utf-8"></script>
    <!-- noUiSlider -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/noUiSlider.10.0.0/nouislider.css" />
    <script src="pages/mobile/plugins/noUiSlider.10.0.0/nouislider.js" type="text/javascript" charset="utf-8"></script>
    <!-- switchery -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/switchery/switchery.css"/>
    <script src="pages/mobile/plugins/switchery/switchery.js" type="text/javascript" charset="utf-8"></script>
    <!-- iconfont -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/fonticon01/iconfont.css" />
    <!-- animate.css -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.css" />
    <!--script src="pages/mobile/js/jquery.min.js"></script-->
    <script src="pages/mobile/js/showText.js"></script>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
        font-family: "Courier New", Courier, monospace !important;
    }
    .container {
        width: 100%;
        height: 37%;
        overflow: auto;
        clear: both;
    }

    .z_photo {
        /*width: 5rem;
        height: 3rem;*/
        padding: 0.2rem;
        overflow: auto;
        clear: both;
        margin: .3rem auto;
        border: 1px solid #555;
    }

    .z_photo img {
        width: 1.4rem;
        height: 1.4rem;
        margin-bottom: 0.2rem;
    }

    .z_addImg {
        float: left;
        margin-right: 0.2rem;
    }

    .z_file {
        width: 1.4rem;
        height: 1.4rem;
        background: url(pages/mobile/img/z_add.png) no-repeat;
        background-size: 100% 100%;
        float: left;
        margin-right: 0.2rem;
        margin-bottom: 0.2rem;
    }

    .z_file input::-webkit-file-upload-button {
        width: 1rem;
        height: 1rem;
        border: none;
        position: absolute;
        outline: 0;
        opacity: 0;
    }

    .z_file input#file {
        display: block;
        width: auto;
        border: 0;
        vertical-align: middle;
    }
    /*遮罩层*/

    .z_mask {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, .5);
        position: fixed;
        top: 0;
        left: 0;
        z-index: 999;
        display: none;
    }

    .z_alert {
        width: 3rem;
        height: 2rem;
        border-radius: .2rem;
        background: #fff;
        font-size: .24rem;
        text-align: center;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -1.5rem;
        margin-top: -2rem;
    }

    .z_alert p:nth-child(1) {
        line-height: 1.5rem;
    }

    .z_alert p:nth-child(2) span {
        display: inline-block;
        width: 49%;
        height: .5rem;
        line-height: .5rem;
        float: left;
        border-top: 1px solid #ddd;
    }

    .z_cancel {
        border-right: 1px solid #ddd;
    }
    header{
        position: fixed;
        z-index: 998;
        top: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        height: 44px;
        border-bottom:solid 1px #EEE;
        padding: 2px;

    }


    .jq22-navBar {
        height: 44px;
        position: relative;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        z-index: 102;
        background: orangered;
    }

    .jq22-navBar-item {
        height: 44px;
        min-width: 25%;
        -webkit-box-flex: 0;
        -webkit-flex: 0 0 25%;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        padding: 0 0.9rem;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        font-size: 0.7rem;
        white-space: nowrap;
        overflow: hidden;
        color: #808080;
        position: relative;
    }

    .jq22-navBar-item:first-child {
        -webkit-box-ordinal-group: 2;
        -webkit-order: 1;
        -ms-flex-order: 1;
        order: 1;
        margin-right: -25%;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .jq22-navBar-item:last-child {
        -webkit-box-ordinal-group: 4;
        -webkit-order: 3;
        -ms-flex-order: 3;
        order: 3;
        -webkit-box-pack: end;
        -webkit-justify-content: flex-end;
        -ms-flex-pack: end;
        justify-content: flex-end;
    }

    .jq22-center {
        -webkit-box-ordinal-group: 3;
        -webkit-order: 2;
        -ms-flex-order: 2;
        order: 2;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        height: 44px;
        width: 50%;
        margin-left: 25%;
    }

    .jq22-center-title {
        text-align: center;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        display: block;
        text-overflow: ellipsis;
        font-size: 1rem;
        color: white;
    }


</style>
<body>
<header class="ui-header clearfix w75 h8 f46 pl3 pr3 color8 bg-color-primary t-c">
    <div class="ui-header-l fl w5" onclick="javascript :history.back(-1);">
        <b class="icon iconfont icon-yixianshi-" style="font-size: 40px;padding-right: 12px ; !important;"></b>
    </div>
    <div class="ui-header-c fl f30 w59">
        拜访详情
    </div>
    <div class="ui-header-r fr w5" onclick="javascript :history.back(-1);">
        <i class="icon iconfont icon-yixianshi-" style="font-size: 40px;padding-right: 12px ; !important;"></i>
    </div>
</header>

<div class="p3 f30 f30 w75">
    <form>
        <input type="hidden" name="terminalCode" value="${vo.terminalCode}"/>
        <div class="pt2 pb2">
            <span style="font-weight: bold;color: orangered;">&nbsp;</span>
        </div>
        <div class="pt2 pb2">
            <span style="font-weight: bold;color: orangered;">店铺名称</span>：${vo.terminalName}
        </div>
        <div class="pt2 pb2">
            <span style="font-weight: bold;color: darkred;">老板姓名</span>：${vo.linkman}
        </div>
        <div class="pt2 pb2">
            <span style="font-weight: bold;color: mediumvioletred;">老板手机</span>：${vo.linkmanPhone}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>拜访时间</span>：${bean.crdat}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>1、门店门头类别</span></br>
            <label class="mr3"><input type="radio" class="checkDIY" name="adsType" <c:if test="${bean.adsType eq 'A'}"> checked </c:if> disabled="disabled" value="A"/>天能门头</label>
            <label class="mr3"><input type="radio" class="checkDIY" name="adsType" <c:if test="${bean.adsType eq 'B'}"> checked </c:if> disabled="disabled" value="B" />超威门头</label></br>
            <label class="mr3"><input type="radio" class="checkDIY" name="adsType" <c:if test="${bean.adsType eq 'C'}"> checked </c:if> disabled="disabled" value="C"/>其他电池品牌门头</label>
            <label class="mr3"><input type="radio" class="checkDIY" name="adsType" <c:if test="${bean.adsType eq 'D'}"> checked </c:if> disabled="disabled" value="D" />整车品牌门头</label></br>
            <label class="mr3"><input type="radio" class="checkDIY" name="adsType" <c:if test="${bean.adsType eq 'E'}"> checked </c:if> disabled="disabled" value="E"/>自有门头</label>
            <label class="mr3"><input type="radio" class="checkDIY" name="adsType" <c:if test="${bean.adsType eq 'F'}"> checked </c:if> disabled="disabled" value="F" />其他</label>
        </div>
        <div class="mt4 mb4">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>2、门店经营电池品牌<span style="color: red !important;">（多选）</span></span></br>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'A')}"> checked </c:if> value="A"  disabled="disabled"/>天能</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'B')}"> checked </c:if> value="B"  disabled="disabled"/>超威</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'C')}"> checked </c:if> value="C"  disabled="disabled"/>汇源</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'D')}"> checked </c:if> value="D"  disabled="disabled"/>南都</label></br>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'E')}"> checked </c:if> value="E"  disabled="disabled"/>京球</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'F')}"> checked </c:if> value="F"  disabled="disabled"/>旭派</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'G')}"> checked </c:if> value="G"  disabled="disabled"/>金超威</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="brand" <c:if test="${fn:contains(bean.brand,'H')}"> checked </c:if> value="H"  disabled="disabled"/>其他</label>
        </div>
        <div class="mt4 mb4">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>3、门店电池进货渠道<span style="color: red !important;">（多选）</span></span></br>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="channel" <c:if test="${fn:contains(bean.channel, 'A')}"> checked </c:if>  value="A"  disabled="disabled"/>电池代理商</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="channel" <c:if test="${fn:contains(bean.channel, 'B')}"> checked </c:if>  value="B"  disabled="disabled"/>二批商</label>
            <label class="mr3"><input type="checkbox" class="checkDIY" name="channel" <c:if test="${fn:contains(bean.channel, 'C')}"> checked </c:if>  value="C"  disabled="disabled"/>整车厂家</label>
        </div>
        <div class="mt3 mb3"><span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>4、门店主销电池型号及月均销量<span style="color: red !important;">（多选）</span></span></div>
        <div >
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '4812')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="4812"/>4812:<input type="number" value="${bean.s4812}" name="s4812" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '4820')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="4820"/>4820:<input type="number" value="${bean.s4820}" name="s4820" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '4832')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="4832"/>4832:<input type="number" value="${bean.s4832}" name="s4832" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '4845')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="4845"/>4845:<input type="number" value="${bean.s4845}" name="s4845" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '4852')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="4852"/>4852:<input type="number" value="${bean.s4852}" name="s4852" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '4858')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="4858"/>4858:<input type="number" value="${bean.s4858}" name="s4858" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6020')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6020"/>6020:<input type="number" value="${bean.s6020}" name="s6020" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6032')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6032"/>6032:<input type="number" value="${bean.s6032}" name="s6032" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6045')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6045"/>6045:<input type="number" value="${bean.s6045}" name="s6045" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6052')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6052"/>6052:<input type="number" value="${bean.s6052}" name="s6052" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6058')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6058"/>6058:<input type="number" value="${bean.s6058}" name="s6058" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '7220')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="7220"/>7220:<input type="number" value="${bean.s7220}" name="s7220" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '7232')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="7232"/>7232:<input type="number" value="${bean.s7232}" name="s7232" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6414')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6414"/>6414:<input type="number" value="${bean.s6414}" name="s6414" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label></br>
            <label class="mr3"><input type="checkbox" name="sales" <c:if test="${fn:contains(bean.sales , '6420')}"> checked </c:if>  disabled="disabled" class="checkDIY" value="6420"/>6420:<input type="number" value="${bean.s6420}" name="s6420" disabled="disabled" class="w14  form-control" min="0" max="1000"  maxlength="3"/>组</label>
        </div>
        <div class="mt3 mb3"><span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>5、门店主销电池价格
            <span style="color: red !important;">（多选）</span>
            <span style="color: deeppink !important;">单位：元/组</span>
        </span></div>
        <div>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '4812')}"> checked </c:if> disabled="disabled" class="checkDIY" value="4812"/>4812:<input type="number" value="${bean.p4812}" name="p4812" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '4820')}"> checked </c:if> disabled="disabled" class="checkDIY" value="4820"/>4820:<input type="number" value="${bean.p4820}" name="p4820" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '4832')}"> checked </c:if> disabled="disabled" class="checkDIY" value="4832"/>4832:<input type="number" value="${bean.p4832}" name="p4832" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '4845')}"> checked </c:if> disabled="disabled" class="checkDIY" value="4845"/>4845:<input type="number" value="${bean.p4845}" name="p4845" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '4852')}"> checked </c:if> disabled="disabled" class="checkDIY" value="4852"/>4852:<input type="number" value="${bean.p4852}" name="p4852" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '4858')}"> checked </c:if> disabled="disabled" class="checkDIY" value="4858"/>4858:<input type="number" value="${bean.p4858}" name="p4858" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6420')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6020"/>6020:<input type="number" value="${bean.p6020}" name="p6020" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6032')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6032"/>6032:<input type="number" value="${bean.p6032}" name="p6032" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6045')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6045"/>6045:<input type="number" value="${bean.p6045}" name="p6045" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6052')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6052"/>6052:<input type="number" value="${bean.p6052}" name="p6052" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6058')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6058"/>6058:<input type="number" value="${bean.p6058}" name="p6058" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '7220')}"> checked </c:if> disabled="disabled" class="checkDIY" value="7220"/>7220:<input type="number" value="${bean.p7220}" name="p7220" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '7232')}"> checked </c:if> disabled="disabled" class="checkDIY" value="7232"/>7232:<input type="number" value="${bean.p7232}" name="p7232" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6414')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6414"/>6414:<input type="number" value="${bean.p6414}" name="p6414" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label></br>
            <label class="mr3"><input type="checkbox" name="price" <c:if test="${fn:contains(bean.price , '6420')}"> checked </c:if> disabled="disabled" class="checkDIY" value="6420"/>6420:<input type="number" value="${bean.p6420}" name="p6420" disabled="disabled" class="w14  form-control" min="0" max="1000" maxlength="3" />元</label>
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>6、门店电池月均销量<span style="color: red !important;">（组）</span></span>：
            <input type="number" class="w21  form-control" min="0" max="1000" disabled="disabled" name="salesMon" value="${bean.salesMon}"/>
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>7、天能电池月均销量<span style="color: red !important;">（组）</span></span>：
            <input type="number" class="w21  form-control" min="0" max="1000" disabled="disabled" name="salesMonTn" value="${bean.salesMonTn}"/>
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>8、天能电池本月订购<span style="color: red !important;">（组）</span></span>：
            <input type="number" class="w21  form-control" min="0" max="1000" disabled="disabled" name="purMonTn" value="${bean.purMonTn}"/>
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>9、属于未攻克竞品门店？</span>
            <label class="mr3"><input type="radio" class="checkDIY" name="take" <c:if test="${bean.take eq '1'}"> checked </c:if> disabled="disabled" value="1"/>是&nbsp;</label>
            <label class="mr3"><input type="radio" class="checkDIY" name="take" <c:if test="${bean.take eq '0'}"> checked </c:if> disabled="disabled"  value="0" />不是</label>
        </div>

        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;">*</span>10、门店照片（若为竞品门店攻克，请上传本次订购天能电池照片，可通过照片数清订购数量）
            <!--span style="color: red !important;">（1）每次选择不超过6张照片，点击照片即可删除</span>
            <span style="color: red !important;">（2）实时拍照只能传一张，建议拍完从相册选取</span>
            <span style="color: red !important;">（3）每次选择照片，将会清除上一次选择的照片</span--></span>
        </div>

        <div class="container"><!--    照片添加   accept="image/gif,image/jpeg,image/png"  -->
            <div class="z_photo">
                <div class="z_file">
                    <input type="file" name="file" id="file" value="" disabled="disabled" accept="image/*" multiple onchange="imgChange('z_photo','z_file');" />
                </div>
                <c:forEach items="${picList}" var="imgBean" >
                <div class="z_addImg">
                    <img src="${imgBean.imgPath}">
                </div>
                </c:forEach>
            </div>
        </div>

    </form>
</div>

<script type="text/javascript">
    //icheck 多选框插件
    $('.checkDIY').iCheck({
        checkboxClass: 'ui-checkbox check-primary',
        radioClass: 'ui-radio check-primary'
    });
</script>

</body>

</html>