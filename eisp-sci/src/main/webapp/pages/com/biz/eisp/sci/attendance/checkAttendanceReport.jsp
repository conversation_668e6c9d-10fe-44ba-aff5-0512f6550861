<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center" style="padding:1px;">
		<t:datagrid name="checkAttendanceList" fitColumns="false" title="考勤报表" actionUrl="tsWorkAttendanceWebController.do?collectAttendanceList" idField="id" fit="true" queryMode="group">
			<t:dgCol title="编号" field="id" hidden="true" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" query="true" formatter="yyyy-MM" width="80"></t:dgCol>
			<%--<t:dgCol title="销售部" field="salOrgName" query="true" width="80"></t:dgCol>--%>
			<t:dgCol title="组织" field="orgName" query="true" width="150"></t:dgCol>
			<%--
                <t:dgCol title="组织" field="parent_id" hidden="true"   formType="combotree" formUrl="tsExtendsMdmController.do?getOrgForTree"  query="true" width="130"></t:dgCol>
            --%>
			<t:dgCol title="人员账号" field="userName" query="true" width="100"></t:dgCol>
			<t:dgCol title="人员名称" field="fullName" query="true" width="100"></t:dgCol>
            <%--<t:dgCol title="职位编码" field="positionCode" hidden="true"  width="130"></t:dgCol>
			<t:dgCol title="职位" field="positionName" query="true"  width="130"></t:dgCol>--%>
			<t:dgCol title="本地出勤天数" field="onAttendanceBenDi" width="100"></t:dgCol>
			<t:dgCol title="外地出勤天数" field="onAttendanceWaiDi" width="100"></t:dgCol>
			<t:dgCol title="出勤总天数" field="onAttendance1" width="100"></t:dgCol>
			<t:dgCol title="未出勤天数" field="onAttendance2" width="100"></t:dgCol>
			<t:dgCol title="迟到次数" field="onAttendance3"  width="150"></t:dgCol>
			<t:dgCol title="早退次数" field="onAttendance4" width="100"></t:dgCol>
			<t:dgCol title="正常次数" field="onAttendance5" width="150"></t:dgCol>
			<t:dgCol title="请假天数" field="onAttendance6" width="150"></t:dgCol>
			<t:dgToolBar title="导出"   icon="icon-dataOut"  url="" operationCode="putout" funname="toExcel()"></t:dgToolBar>
			<t:dgToolBar title="考勤明细" icon="icon-search" operationCode="detail" url="tsWorkAttendanceWebController.do?goCheckAttendance&isNotFun=1&title=考勤明细" funname="systemAddOneTab" params="yearMonth,userName" ></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
    $(function () {
        $("#checkAttendanceListtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("#parent_id").combotree({width:250});
    })
    //导出
    function toExcel(){
        var departId = $("input[name='parent_id']").val();
        var accessEntry = $("#accessEntry").val();

// 		alert(departId);
        excelExport("tsWorkAttendanceWebController.do?exportExcelAttendanceReport&accessEntry="+accessEntry,"checkAttendanceList");
    }

    $(function () {
        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });
    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>