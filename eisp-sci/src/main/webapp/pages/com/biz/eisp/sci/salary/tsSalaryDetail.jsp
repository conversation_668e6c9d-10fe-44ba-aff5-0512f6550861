<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0,uc-fitscreen=yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <title>我的工资条</title>
    <!-- miniMObile.css、js -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/css/miniMobile.css"/>
    <script type="text/javascript" src="pages/mobile/js/zepto.min.js"></script>
    <script type="text/javascript" src="pages/mobile/js/miniMobile.js"></script>
    <!-- mobileSelect -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/mobileSelect/mobileSelect.css">
    <script src="pages/mobile/plugins/mobileSelect/mobileSelect.js" type="text/javascript"></script>
    <!-- icheck -->
    <script src="pages/mobile/plugins/icheck/icheck.js" type="text/javascript" charset="utf-8"></script>
    <!-- noUiSlider -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/noUiSlider.10.0.0/nouislider.css" />
    <script src="pages/mobile/plugins/noUiSlider.10.0.0/nouislider.js" type="text/javascript" charset="utf-8"></script>
    <!-- switchery -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/switchery/switchery.css"/>
    <script src="pages/mobile/plugins/switchery/switchery.js" type="text/javascript" charset="utf-8"></script>
    <!-- iconfont -->
    <link rel="stylesheet" type="text/css" href="pages/mobile/plugins/fonticon01/iconfont.css" />
    <!-- animate.css -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.css" />
    <!--script src="pages/mobile/js/jquery.min.js"></script-->
    <script src="pages/mobile/js/showText.js"></script>
    <script src="pages/mobile/js/jquery.imagecompress.js"></script>
</head>

<body>
<header class="ui-header clearfix w75 h8 f46 pl3 pr3 color8 bg-color-primary t-c">
    <div class="ui-header-l fl w5" onclick="javascript :history.back(-1);">
        <b class="icon iconfont icon-yixianshi-" style="font-size: 40px;padding-right: 12px ;!important;"></b>
    </div>
    <div class="ui-header-c fl f30 w59">
        工资详情
    </div>
    <div class="ui-header-r fr w5">
        <i class="icon iconfont icon-yixianshi-" style="font-size: 40px;padding-right: 12px ; !important;"></i>
    </div>
</header>

<div class="mt3 mb3" <c:if test="${bean.symbol eq '1'}"> hidden="hidden" </c:if>>
    <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>&nbsp;&nbsp;&nbsp;系统提示</span></br></br>&nbsp;&nbsp;
    <span style="color: red !important;font-size:20px;">${bean.errorInfo}</span>
</div>
<div class="mt3 mb3" <c:if test="${bean.setPwd eq '0'}"> hidden="hidden" </c:if>>
    <form method="post" action="tsSalaryController.do?goPassword" enctype="multipart/form-data" data-ajax="false"  name="formVisit" id="formVisit" >
        <input type="hidden" id="ssocode" name="ssocode" value="${bean.ssocode}">
        <input type="hidden" id="workcode" name="workcode" value="${bean.workcode}">
        <input type="hidden" id="id" name="id" value="${bean.id}">
        <input type="hidden" id="subcompanyid1" name="subcompanyid1" value="${bean.subcompanyid1}">
    <input type="submit" class="btn f38 btn-primary radius5 w30 h9" style="border-color: white !important;" value="设置查询密码" />
    </form>

</div>
<div class="p3 f30 f30 w75" <c:if test="${bean.symbol eq '0'}"> hidden="hidden" </c:if>>
    <span style="color: red !important;">
    必读：工资条现通过钉钉告知您本人，请认真核对工资结构、金额及绩效等级，如有任何异议，请在收到工资条后，3日内向人力资源中心书面反馈，逾期不反馈，视为认同。请严格遵守公司薪资保密制度，如有违反，将按规定严肃处理。
    </span>
        <form >
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>员工工号</span>：${bean.workcode}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>工资年月</span>：${bean.yymm}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>绩效等级</span>：${bean.perform}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>应发金额</span>：${bean.totalpay}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;"><span style="color: red !important;"></span>实发金额</span>：<span style="color:red;font-weight: bold;font-size: 24px;">${bean.finalSa}</span>
        </div>
        <header class="ui-header clearfix w75 h8 f46 pl3 pr3 color8 bg-color-primary t-c">
            <div class="ui-header-c fl f30 w59">
                加项
            </div>
        </header>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">工资</span>：${bean.bSa}
        </div>

        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">职务/技能津贴</span>：${bean.skill}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">保密费</span>：${bean.secret}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">加班工资</span>：
            ${bean.oSa}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">绩效奖金</span>：
            ${bean.pBonus}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">学历/职称标准津贴</span>：
            ${bean.xlzcWs}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">学历/职称津贴出勤率</span>：
            ${bean.xlzcCqWs}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">其他奖金</span>：
            ${bean.days}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">上月补差</span>：
            ${bean.addLMon}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">其他补贴/保密费</span>：
            ${bean.oAllow}
        </div>

        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">扣款</span>：
            ${bean.cutPay}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">租房补贴</span>：
            ${bean.rAllow}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">通讯补贴</span>：
            ${bean.cAllow}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">电信补贴</span>：
            ${bean.tCAllow}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">伙食补贴</span>：
            ${bean.fAllow}
        </div>

        <header class="ui-header clearfix w75 h8 f46 pl3 pr3 color8 bg-color-primary t-c">

            <div class="ui-header-c fl f30 w59">
                减项
            </div>

        </header>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">代扣社会保险</span>：
            ${bean.sIPay}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">代扣住房公积金</span>：
            ${bean.hFPay}
        </div>

        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">工会费</span>：
            ${bean.unionPay}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">代扣餐费</span>：
            ${bean.foodPay}
        </div>

            <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 ne '405'}"> hidden="hidden" </c:if>>
                <span style="font-weight: bold;color: darkblue;">负激励</span>：
                ${bean.fjlWs}
            </div>
            <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 ne '405'}"> hidden="hidden" </c:if>>
                <span style="font-weight: bold;color: darkblue;">现金已发</span>：
                ${bean.cashWs}
            </div>

        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 ne '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">绩效基金</span>：
            ${bean.pMWs}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 ne '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">责任基金</span>：
            ${bean.zrzj1Ws}
        </div>
        <div class="mt3 mb3" <c:if test="${bean.subcompanyid1 eq '405'}"> hidden="hidden" </c:if>>
            <span style="font-weight: bold;color: darkblue;">卫生费(宿舍)</span>：
            ${bean.pMPay}
        </div>

        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">爱心费</span>：
            ${bean.charityPay}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">代扣电费(宿舍)</span>：
            ${bean.powerPay}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">个人所得税</span>：
            ${bean.ptaxPay}
        </div>
        <div class="mt3 mb3">
            <span style="font-weight: bold;color: darkblue;">其他扣款</span>：
            ${bean.oPay}
        </div>


    </form>

</div>

<script type="text/javascript">
    //icheck 多选框插件
    $('.checkDIY').iCheck({
        checkboxClass: 'ui-checkbox check-primary',
        radioClass: 'ui-radio check-primary'
    });
</script>
</body>

</html>