<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div>
    <div region="center" style="width: auto;height: 60%;">
        <t:datagrid name="materialList"
                    fitColumns="false" title="活动物料申请"
                    actionUrl="materialActivityController.do?getMaterialActivityByPage&searchSelf=1"
                    idField="id" fit="true" queryMode="group" singleSelect="true" pageSize="20" checkbox="true"
                    pagination="true">
            <p style="color: red; margin: 20px 0;">温馨提示:请在创建后30天内完成报销。超过30天未完成的报销申请将自动失效，无法再进行报销操作。</p>
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动单号" field="matNo" query="true"></t:dgCol>
            <t:dgCol title="经销商名称" field="supplierName" query="true"></t:dgCol>
            <t:dgCol title="经销商编号" field="supplierCode"></t:dgCol>
            <t:dgCol title="物料项目" field="materialNameStr"></t:dgCol>
            <t:dgCol title="物料类型" field="materialType" query="true" replace="常规物料_1,拉力赛物料_2"></t:dgCol>
            <t:dgCol title="申请总额(元)" field="applicationAmount"></t:dgCol>
<%--            <t:dgCol title="实际报销总额(元)" field="actualAmount"></t:dgCol>--%>
            <t:dgCol title="审批状态" field="frontStatus" hidden="true" query="true" dictionary="material_audit_status"
                     sortable="false"></t:dgCol>
            <t:dgCol title="状态" field="frontStatusStr"></t:dgCol>
            <t:dgCol title="活动日期" field="activityDate" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="创建时间" field="createTime" formatter="yyyy-MM-dd HH:mm:ss" query="true"
                     queryMode="group"></t:dgCol>
            <t:dgCol title="更新时间" field="updateTime" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>

            <t:dgToolBar title="新增" icon="icon-add" operationCode="add" onclick="addMaterial()"></t:dgToolBar>
            <t:dgToolBar title="撤回" icon="icon-process_back" operationCode="update" onclick="reject()"></t:dgToolBar>
            <t:dgToolBar title="报销" icon="icon-submit" operationCode="rbsSubmit" onclick="rbsSubmit()"></t:dgToolBar>
            <t:dgToolBar title="编辑" icon="icon-edit" operationCode="edit" onclick="editMaterial()"></t:dgToolBar>
            <t:dgToolBar title="查看" icon="icon-log" operationCode="detail" onclick="detailSee()"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>

<script>

    $(window).resize(function () {
        $('#materialList').datagrid('resize', {
            width: $(window).width()
        });
    });

    function addMaterial() {
        var url = "materialActivityController.do?goMaterialActivitySubmit";
        $.dialog({
            id: 'materialActivityDialog',
            title: "新增活动物料申请",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function editMaterial() {
        // 实现编辑物料功能
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请至少选择一条数据");
            return false;
        }
        var status = select[0].status;
        //状态如果不在4，5，9,10中，不能编辑
        if (status != 4 && status != 5) {
            tip("只有撤回，驳回的数据可编辑，当前数据不可编辑");
            return false;
        }
        var url = "materialActivityController.do?goMaterialActivityEditPage&id=" + select[0].id;
        $.dialog({
            id: 'materialActivityEditDialog',
            title: "修改活动物料申请",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function rbsSubmit() {
        // 实现编辑物料功能
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请至少选择一条数据");
            return false;
        }
        var status = select[0].status;
        //状态如果不在3,9,10中，不能报销
        if (status != 3 && status != 9 && status != 10) {
            tip("只有待报销，驳回和撤销的数据可报销，当前数据不可报销");
            return false;
        }
        var url = "materialActivityController.do?goMaterialActivityRbsSubmitPage&id=" + select[0].id;
        $.dialog({
            id: 'materialActivityRbsSubmitDialog',
            title: "报销活动物料",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function detailSee() {
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请至少选择一条数据");
            return false;
        }
        var url = "materialActivityController.do?goMaterialActivityDetailPage&id=" + select[0].id;
        $.dialog({
            id: 'materialActivityDetailDialog',
            title: "查看活动物料申请",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function reject() {
        // 撤回
        var select = $("#materialList").datagrid('getSelections');
        if (select == null || select == "") {
            tip("请至少选择一条数据");
            return false;
        }
        var auditType = select[0].auditType;
        if (auditType == 1) {
            var status = select[0].status;
            if (status != 1) {
                tip("只有待区域经理审批的数据才可以撤回，撤回失败");
                return false;
            }
            var applicationViewFlag = select[0].applicationViewFlag;
            if (applicationViewFlag == 1) {
                tip("该数据已经被区域经理查看，不能撤回");
                return false;
            }
            var id = select[0].id;
            var url = "materialActivityController.do?cancelActivity&id=" + id;
        } else {
            var status = select[0].status;
            if (status != 6) {
                tip("只有待区域经理审批的数据才可以撤回，撤回失败");
                return false;
            }
            var reimbursementViewFlag = select[0].reimbursementViewFlag;
            if (reimbursementViewFlag == 1) {
                tip("该数据已经被区域经理查看，不能撤回");
                return false;
            }
            var id = select[0].id;
            var url = "materialActivityController.do?cancelReimbursement&id=" + id;
        }
        fetch(url).then(response => {
            if (response.ok) {
                return response.json(); // 将响应数据转换为JSON
            }
            throw new Error('Network response was not ok.');
        }).then(data => {
            console.log('data', data)
            if (data.head && data.head.code == 100) {
                tip(data.head.message);
                $('#materialList').datagrid('reload');
            } else {
                tip(data.head.message);
            }
        }).catch(error => {
            console.error('There was a problem previewing the data:', error);
        });
    }

    function refreshList() {
        $('#materialList').datagrid('reload');
    }

    function removeMaterial() {
        // 实现删除物料功能
    }

</script>
