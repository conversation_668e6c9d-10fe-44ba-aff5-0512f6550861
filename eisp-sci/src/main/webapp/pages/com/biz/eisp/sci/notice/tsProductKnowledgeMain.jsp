<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="tsNoticeList" title="知识管理"  actionUrl="tsNoticeWebController.do?findTsNoticeList&srcType=${srcType}"
                    singleSelect="false"  idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="知识标题" width="120" field="noticeTitle" query="true"></t:dgCol>
            <%-- <t:dgCol title="知识类型" width="120" field="noticeType" query="true" dictionary="sci_notice_type"></t:dgCol> --%>
            <t:dgCol title="发布部门" width="120" field="orgName"></t:dgCol>
            <t:dgCol title="发布部门" field="parent_id" hidden="true"   formType="combotree" formUrl="tmOrgController.do?getParentOrg"  query="true" width="130"></t:dgCol>
            <t:dgCol title="发布时间" width="120" formatter="yyyy-MM-dd hh:mm:ss" field="pushDate" query="true" queryMode="group"></t:dgCol>
            <t:dgCol title="有效开始时间" width="120" field="viewStartDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="有效结束时间" width="120" field="viewEndDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="创建人" width="120" field="createName"></t:dgCol>
            <t:dgCol title="最近更新时间" width="120" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
            <t:dgCol title="访问次数" width="100" field="visitCount"></t:dgCol>
            <t:dgToolBar title="新增" operationCode="add" width="800" height="500" icon="icon-add" url="tsProductKnowledgeWebController.do?goTsNoticeForm" funname="add"></t:dgToolBar>
            <t:dgToolBar title="编辑" operationCode="update"  width="800" height="500" icon="icon-edit" url="tsProductKnowledgeWebController.do?goTsNoticeForm" funname="update"></t:dgToolBar>
            <t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="tsNoticeWebController.do?deleteAll" funname="deleteALLSelect"></t:dgToolBar>
            <t:dgToolBar title="明细" operationCode="detail"   icon="icon-search" url=""   funname="showInfo"></t:dgToolBar>
        </t:datagrid>
    </div>
    <input type="text">
</div>
<script type="text/javascript">
    $(function () {
        $("input[name*='pushDate']").addClass("pushDate").css({
            'width': '100px'
        }).removeAttr("onfocus").on("focus", function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    })



    function showInfo() {
        gridname="tsNoticeList";
        var rows = $("#tsNoticeList").datagrid('getSelections');
        if(rows.length<1) {
            tip("请至少选择一条数据")
            return false;
        }
        if(rows.length>1) {
            tip("只能选择一条数据进行操作")
            return false;
        }

        var url = "tsProductKnowledgeWebController.do?goTsProductKnowledgeDetail&id="+rows[0].id;



        createwindowExt(rows[0].noticeTitle,url,1000,500, {
            button:[{
                name:'关闭',
                cancel : function() {
                    return true;
                }

            }]

            // cancelVal : '关闭',
            // cancel : function() {
            //     return true;
            // }
        });

    }
</script>
