<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div>
    <div region="center" style="width: auto;height: 60%;">
        <t:datagrid name="tsActOutDoorAdvCheckList"
                    fitColumns="false" title="户外广告检查"
                    actionUrl="tsActApplyCheckController.do?findTsActApplyCheckList&actType=1"
                    idField="id" fit="true" queryMode="group" singleSelect="true" pageSize="20"
                    onClick="getPictureInfo()">

            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="bussinessId" field="bussinessId" hidden="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" query="true"></t:dgCol>
            <%--<t:dgCol title="活动类型" field="actType" dictionary="act_type"></t:dgCol>--%>
            <t:dgCol title="活动细类" field="costTypeName" query="true"></t:dgCol>
            <t:dgCol title="图片数量" field="imgNumber"></t:dgCol>
            <%--<t:dgCol title="终端网点名" field="terminalName" query="true"></t:dgCol>--%>
            <t:dgCol title="定位地址" field="gpsAddress" query="true"></t:dgCol>
            <t:dgCol title="补充说明" field="remarks"></t:dgCol>

            <t:dgCol title="检查人登录名" field="checkUser" hidden="true"></t:dgCol>
            <t:dgCol title="检查人姓名" field="checkFullName" query="true"></t:dgCol>
            <t:dgCol title="检查人所属组织ID" field="orgId" hidden="true"></t:dgCol>
            <t:dgCol title="检查人组织" field="checkOrgName" query="true"></t:dgCol>
            <t:dgCol title="检查时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>

            <t:dgCol title="检查总结" field="inspectSummary" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="未发现广告" field="isUndiscoveredAdv" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="灯泡异常" field="isAbnormalLight" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="未按规定亮灯" field="isUnlightedLamp" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="版本陈旧" field="isOldVersion" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="拒绝" field="isRefuse" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="影响安全" field="isImpactSafety" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="破损" field="isDamaged" dictionary="flaw_yn" sortable="false"  hidden="false"  ></t:dgCol>

            <t:dgCol title="审批状态" field="bpmStatus" dictionary="bpm_status_simple" query="true"></t:dgCol>
            <t:dgCol title="审批人姓名" field="auditFullName"></t:dgCol>
            <t:dgCol title="审批人职位" field="auditPositionName"></t:dgCol>
            <t:dgCol title="审批时间" field="auditDate" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>

            <t:dgCol title="检查日期" field="queryDate" query="true" queryMode="group" formatter="yyyy-MM-dd"
                     hidden="true"></t:dgCol>

            <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut"
                         url="tsActApplyCheckController.do?exportExcel&actType=1&title=1" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="审批" icon="icon-edit" operationCode="edit" onclick="audit()"></t:dgToolBar>

        </t:datagrid>
    </div>

    <div id="trainPictureDiv" style="clear:both;width:100%;height: 40%;">
        <t:datagrid name="trainPictureList" title="图片视频信息"
                    actionUrl="tsTrainActCheckAuditController.do?findTrainPictureList"
                    idField="id" fit="true" fitColumns="false" pagination="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true"></t:dgCol>
            <t:dgCol title="类型" field="imgType" dictionary="sfa_photo_type" width="200"></t:dgCol>
            <t:dgCol title="类型描述" field="imgTypeRemark" width="200"></t:dgCol>
            <t:dgCol title="缩略图" field="imgPath" formatterjs="showInfo" width="200"></t:dgCol>
        </t:datagrid>
    </div>
</div>

<div id="outerdiv"
     style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src=""/>
    </div>
</div>


<div id="outerdiv2"
     style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv2" style="position:absolute;">
        <video src="" id="biVideo" style='width: 120px;height:80px;cursor: pointer;controls:controls;'></video>
    </div>
</div>

<script>

    $(window).resize(function () {
        $('#tsActOutDoorAdvCheckList').datagrid('resize', {
            width: $(window).width()
        });
        $('#trainPictureList').datagrid('resize', {
            width: $(window).width()
        });
    });


    function showInfo(value) {
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&path="+value;
            return "<a href="+url+">点击下载</a>"
        }
    }

    function picture(value) {
        value = '/image/' + value;
        var img = value;
        if (value != "") {
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src=" + value + "  onclick='picBig(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if (value != "") {
            value = '/image/' + value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src=" + value + "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj) {
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if (src == '' || src == null) {
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function () {
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width", 600);//以最终的宽度对图片缩放
            $("#bigimg").css("height", 500);//以最终的宽度对图片缩放

            var w = (windowW - 600) / 2;//计算图片与窗口左边距
            var h = (windowH - 500) / 2;//计算图片与窗口上边距
            $("#innerdiv").css({"top": h, "left": w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function () {//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }

    function videoBig(obj) {
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if (src == '' || src == null) {
            tip("视频不存在!")
            return false;
        }
        $.dialog({
            title: "视频查看",
            content: "url:tsPictureController.do?showVideo&path=" + src,
            lock: true,
            width: "500",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function getPictureInfo() {
        var row = $("#tsActOutDoorAdvCheckList").datagrid('getSelections');
        var bussinessId = row[0].bussinessId;
        var id = row[0].id;
        $('#trainPictureList').datagrid('load', {id: id});
    }

    function audit() {
        var row = $('#tsActOutDoorAdvCheckList').datagrid('getSelections');

        if (row == null || row.length == 0 || row.length > 1) {
            tip("请选择一条数据进行操作");
            return;
        }

        var id = row[0].id;
        $.ajax({
            url: "tsActApplyCheckController.do?saveTsActApplyCheckAudit",
            data: {
                id: id
            },
            success: function (data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    tip(d.msg, 'info');
                    $("#tsActOutDoorAdvCheckList").datagrid("reload");
                } else {
                    tip(d.msg, 'error');
                }
            },
            error: function (data) {
                tip("操作失败", "error")
            }
        });
    }

</script>