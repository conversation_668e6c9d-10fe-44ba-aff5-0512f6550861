<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>终端审核</title>
	<t:base type="jquery,easyui,tools"></t:base>
	<style type="text/css">
		#steps form textarea{
			margin-left:-40px;
			margin-top:8px;
		}
		#steps form div label.Validform_label{
			margin-left:-50px;
		} 
	</style>
</head>
<body>
	<div class="easyui-layout" fit="true">
			<div region="north" style="width:500px;height:200px;" split="true">  
			   	<t:datagrid name="terminalList" title="类似门店列表" queryMode="group"  
			   	            actionUrl='tsTerminalBpmController.do?findSameTerminalList&id=${terVo.id}&extChar5=${terVo.extChar5}&extChar6=${terVo.extChar6}&terminalName=${terVo.terminalName}&address=${terVo.address}&linkman=${terVo.linkman}&linkmanPhone=${terVo.linkmanPhone}' 
			   				idField="id" fit="true"  fitColumns="true" pagination="false">
			   		<t:dgCol title="编码" field="id" hidden="true" width="100"></t:dgCol>
			      	<t:dgCol title="终端编码" field="terminalCode" width="100"></t:dgCol>
			     	<t:dgCol title="终端名称" field="terminalName" width="100"></t:dgCol>
			      	<t:dgCol title="终端地址" field="address" width="200"></t:dgCol>
			      	<t:dgCol title="终端联系人" field="linkman" width="100"></t:dgCol>
			      	<t:dgCol title="终端联系人电话" field="linkmanPhone" width="100"></t:dgCol>
<%-- 			      	<t:dgCol title="照片1" field="extChar12" formatterjs="picture" width="100"></t:dgCol>
			      	<t:dgCol title="照片2" field="extChar13" formatterjs="picture" width="100"></t:dgCol>
			      	<t:dgCol title="照片3" field="extChar14" formatterjs="picture" width="100"></t:dgCol> --%>
			      	<t:dgCol title="操作" field="opt" width="100"></t:dgCol>
			      	<t:dgFunOpt funname="viewDetail(id)" title="查看详情"></t:dgFunOpt>
			      	<t:dgFunOpt funname="viewPicture(id)" title="查看图片"></t:dgFunOpt>
	   			</t:datagrid>
			</div>
			<div region="center" style="width:500px;height:350px;" split="true">  
			   	<t:datagrid name="terminalList2" title="待审批门店" queryMode="group"  
			   	            actionUrl="tsTerminalBpmController.do?findTerminalById&id=${terVo.id}" 
			   				idField="id" fit="true"  fitColumns="true" pagination="false">
					<t:dgCol title="编码" field="id" hidden="true" width="100"></t:dgCol>			   				
			      	<t:dgCol title="终端编码" field="terminalCode" width="100"></t:dgCol>
			     	<t:dgCol title="终端名称" field="terminalName" width="100"></t:dgCol>
			      	<t:dgCol title="终端地址" field="address" width="200"></t:dgCol>
			      	<t:dgCol title="终端联系人" field="linkman" width="100"></t:dgCol>
			      	<t:dgCol title="终端联系人电话" field="linkmanPhone" width="100"></t:dgCol>
			      	<t:dgCol title="照片1" field="extChar12" formatterjs="picture" width="100"></t:dgCol>
			      	<t:dgCol title="照片2" field="extChar13" formatterjs="picture" width="100"></t:dgCol>
			      	<t:dgCol title="照片3" field="extChar14" formatterjs="picture" width="100"></t:dgCol>
			      	<t:dgCol title="操作" field="opt" width="100"></t:dgCol>
			      	<t:dgFunOpt funname="viewDetail(id)" title="查看详情"></t:dgFunOpt>
	   			</t:datagrid>
			</div>
			<div region="south" style="width:500px;height:150px;" split="true">  
			    <t:formvalid formid="dictTreegrid" layout="div" dialog="true" action="">
			    	<input type="hidden" id="id" value="${terVo.id}">
			    	<div class="form">
						<label class="Validform_label"><b>审批意见:</b></label>
						<textarea style="resize:none;width:600px;height:100px;" id="comment"></textarea> 
					</div>
			    </t:formvalid>
			</div>
	</div>
	<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
 	<div id="innerdiv" style="position:absolute;">
	<img id="bigimg" style="border:5px solid #fff;" src="" />
</body>
</html>
<script type="text/javascript">
function viewPicture(id){
 	safeShowDialog({
		content : "url:tsTerminalBpmController.do?goTerminalPicture&id="+id,
		lock : true,
		title : "门店照片",
		width : 800,
		height : 500,
		left :'50%',
		cache : false,
		cancelVal : '关闭',
		cancel : true
	}); 
}
function viewDetail(id){
 	safeShowDialog({
		content : "url:tsTerminalBpmController.do?goTmTerminal&id="+id+"&optype=2&load=detail",
		lock : true,
		title : "门店详情",
		width : 1000,
		height : 600,
		left :'85%',
		cache : false,
		cancelVal : '关闭',
		cancel : true
	}); 
}
function picture(value) {  
    var img = value;  
    var str="";
    if(null !=img && "null" != img&&img.indexOf("null")==-1){
    	str = "<img style='width: 120px;height:80px;cursor: pointer;' src="+img + "  onclick='picBig(this)'>";      	
    }
    debugger;
    return str;  
}
function picBig(obj){  
    var src = obj.src;//获取当前点击的pimg元素中的src属性  
    $("#bigimg").attr("src", src);//设置#bigimg元素的src属性  
  
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/  
    $("<img/>").attr("src", src).load(function(){  
        var windowW = $(window).width();//获取当前窗口宽度  
        var windowH = $(window).height();//获取当前窗口高度  
        var realWidth = this.width;//获取图片真实宽度  
        var realHeight = this.height;//获取图片真实高度  
        var imgWidth, imgHeight;  
                $("#bigimg").css("width",600);//以最终的宽度对图片缩放  
                $("#bigimg").css("height",500);//以最终的宽度对图片缩放  
          
        var w = (windowW-600)/2;//计算图片与窗口左边距  
        var h = (windowH-500)/2;//计算图片与窗口上边距  
        $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性  
        $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg  
    });  
      
    $("#outerdiv").click(function(){//再次点击淡出消失弹出层  
        $(this).fadeOut("fast");  
    });  
} 
</script>
