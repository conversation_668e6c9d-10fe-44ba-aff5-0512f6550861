<%@ page language="java" import="java.util.*"
		 contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>公告信息表</title>
	<t:base type="jquery,easyui,tools,DatePicker"></t:base>
	<script type="text/javascript">
        //编写自定义JS代码
        String.prototype.trim = function () {
            return this .replace(/^\s\s*/, '' ).replace(/\s\s*$/, '' );
        };

        function uploadFile(data){
            if($("#filediv").children().length>0){
                $("#file_upload").data("uploadifive").settings.formData={"file_upload":data.flagId};
                $("#file_upload").uploadifive("upload");
                return false;
            }else{
                frameElement.api.opener.reloadTable();
                frameElement.api.close();
            }
            W.tip(data.msg);
        }
	</script>
</head>
<body>
<t:formvalid formid="formobj" dialog="true" callback="@Override uploadFile" layout="div"
			 action="tsNoticeWebController.do?saveNotice" beforeSubmit="checkOrg">
	<input id="id" name="id" type="hidden" value="${vo.id }" />
	<input id="srcType" name="srcType" type="hidden" value="${srcType }" />
	<div class="form">
		<label class="Validform_label">公告标题: </label>
		<input name="noticeTitle" id="noticeTitle" datatype="*" class="inputxt" value="${vo.noticeTitle}" />
		<span style="color: red;">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">公告内容: </label>
		<textarea style="height: 100px;width: 500px;padding:10px" name="noticeContent" id="noticeContent" cols = "100" rows = "10" datatype="*" class="inputxt" value="" >${vo.noticeContent}</textarea>
		<span style="color: red;">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">发布范围: </label>
		<input name="receivingOrgCode" id="receivingOrgCode"  readonly="readonly" class="inputxt" style="width:500px;" />
			<%--<textarea name="receivingOrgCode" id="receivingOrgCode" style="width: 150px;" rows="5" cols="20" readonly="readonly" value="${vo.receivingOrgCode}"></textarea>--%>
		<span class="Validform_checktip">发布范围必填</span>
	</div>
	<div class="form">
		<label class="Validform_label">接收角色: </label>
		<input name="receivingRoleCode" id="receivingRoleCode"  readonly="readonly" class="inputxt" style="width:250px;" />
			<%--<textarea id="receivingRoleCode" name="receivingRoleCode" style="width: 150px;" rows="5" cols="20" readonly="readonly" value="${vo.receivingRoleCode}"></textarea>--%>
			<%--<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="popRole();"></a>--%>
		<span class="Validform_checktip">为空时所有角色都可以接收</span>
	</div>
	<div class="form">
		<label class="Validform_label">有效开始时间: </label>
		<input name="viewStartDate" id="viewStartDate" datatype="*" class="Wdate" style="width: 150px;"
			   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'viewEndDate\')}',onpicked:function(){$('.Wdate').blur();}})"
			   readonly="readonly"  value="${vo.viewStartDate}" />
		<span style="color: red;">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">有效结束时间: </label>
		<input name="viewEndDate" id="viewEndDate" datatype="*" class="Wdate" style="width: 150px;"
			   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'viewEndDate\')}',onpicked:function(){$('.Wdate').blur();}})"
			   readonly="readonly"  value="${vo.viewEndDate}" />
		<span style="color: red;">*</span>
	</div>
    <div class="form">
			<label class="Validform_label">图片展示：</label>

			<c:forEach var="piclist" items="${pics}">
				<div >
					    <span>
							${piclist.imgTypeRemark}
						 </span>
					<span class='delpart'>
						   <a href="#" onclick="deljt(this,'${piclist.id}');" style="color:red; font-size:12px;">删除</a>
					    </span>
				</div>
			</c:forEach>
	</div>
	<div class="form">
		<label class="Validform_label">公告附件: </label>
	</div>
	<div class="form">
		<label style="color: green">可上传附件类型:"*.doc、*.docx、*.xls、*.xlsx、*.ppt、*.pdf、*.jpg、*.jpeg、*.png、*.gif、*.ico"</label>
		<div class="form eispDetailss">
			<t:uploadH5 name="fiels"  id="file_upload" onUploadSuccess="uploadForSet"
					  extend="*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pdf;*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.ico;*.tif"
					  buttonText="添加文件"
					  uploader="tsNoticeWebController.do?saveProductfile">
			</t:uploadH5>
		</div>
		<div class="form" id="filediv"></div>
	</div>
	</table>
</t:formvalid>
</body>
<script type="text/javascript">

	function getSrcType(){
	    return '${srcType}';
	}

    function popRole(notid){
        var receivingOrgCode= document.getElementsByName("receivingOrgCode");
        var obj="";
        for(var i=0;i<receivingOrgCode.length;i++){
            if(i==0){
                obj+=receivingOrgCode[i].value;
            }else{
                obj+=",";
                obj+=receivingOrgCode[i].value;
            }
        }
        $("#receivingRoleCode").combotree({url:"tsNoticeWebController.do?findRoleByPosMainGrid&receivingOrgCode="+obj+notid,cascadeCheck:false,multiple:true})

    }
    $(function(){
        var notid="";
        var receivingOrgCode='${vo.receivingOrgCode}';
        if(receivingOrgCode!=null&&receivingOrgCode!=""){
            notid="&notid="+'${vo.id}';
        }
        $("#receivingOrgCode").combotree({url:"tsNoticeWebController.do?findOrgMainGrid"+notid,
            cascadeCheck:false,multiple:true,onCheck: function (node, checked) {
                popRole(notid);
                notid="";
            }})
    });


    //删除图片
    function deljt(obj,this_id){
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function () {

            $.ajax({
                async: false,
                cache: false,
                type: 'POST',
                data : {
                    id : this_id
                },
                url: 'tsNoticeWebController.do?delPic',// 请求的action路径
                beforeSend: beforeSendFun,
                complete: completeFun,
                error: function () {// 请求失败处理函数
                    $.messager.show({
                        title : '提示消息',
                        msg : '删除失败',
                        timeout : 2000,
                        showType : 'slide'
                    });
                },
                success: function (data) {
                    var d = $.parseJSON(data);
                    var msg = d.msg;
                    tip(d.msg);
                    if (d.success) {
                        $(obj).parent().parent().remove();
                    }
                }
            });
        });
    }
    //complete 方法
    function completeFun() {
        $.messager.progress('close');
        _flag = false;
    }

    //beforeSend 方法
    function beforeSendFun() {
        _flag = true;
        $.messager.progress({text: '数据操作中~~~~'});
    }
    function uploadForSet(d,file,response){
		frameElement.api.opener.reloadTable();
		frameElement.api.close();
        W.tip(d.msg);
    }
    
    function checkOrg(){
    	var receivingOrgCode= document.getElementsByName("receivingOrgCode");
    	if(0 >= receivingOrgCode.length){
    		$.messager.alert('提示',"请选择发布范围");
    		return false;
    	}
    }
</script>