<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="resources/custom_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="tsTravelExpensesList" title="差旅报销"  actionUrl="tsTravelExpensesWebController.do?findTsTravelExpensesList"
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="审批状态" field="bpmStatus" dictionary="bpm_status" query="true" hidden="false" ></t:dgCol>
			<t:dgCol title="标题" field="title" query="true" hidden="false" ></t:dgCol>
			<t:dgCol title="申请日期" field="applyDate" query="true" formatter="yyyy-MM-dd" queryMode="group" hidden="false" ></t:dgCol>
			<%--<t:dgCol title="申请日期" field="applyDateStr" query="true" queryMode="group" hidden="true" frozenColumn="true" ></t:dgCol>--%>
			<t:dgCol title="申请人" field="applyName" query="true" hidden="false" ></t:dgCol>
			<t:dgCol title="申请人部门" field="orgName" query="true" hidden="false" ></t:dgCol>
			<t:dgCol title="报销金额合计" field="submitAmount" hidden="false" ></t:dgCol>
			<t:dgCol title="实际报销金额" field="payableAmount" hidden="false" ></t:dgCol>
			<t:dgCol title="预算科目" field="accountName" hidden="false" ></t:dgCol>
			<t:dgCol title="当前审批人" field="approverName" hidden="false" ></t:dgCol>
			<t:dgCol title="最近更新人" field="updateName" hidden="false" ></t:dgCol>
			<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd" hidden="false" ></t:dgCol>
			<t:dgToolBar title="创建" operationCode="createData" icon="icon-add" url="tsTravelExpensesWebController.do?goTsTravelExpensesForm&optype=0" width="1000" height="700" funname="addTravelExpenses"></t:dgToolBar>
			<t:dgToolBar title="发起" operationCode="star" icon="icon-submit" onclick="starSubmitForm()" ></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="updateData" icon="icon-edit" url="tsTravelExpensesWebController.do?goTsTravelExpensesForm&optype=1" width="1000" height="700" funname="updateTravelExpenses"></t:dgToolBar>
			<t:dgToolBar title="删除" operationCode="delData" icon="icon-remove" url="tsTravelExpensesWebController.do?deleteSelectAll" funname="deleteALLSelect"></t:dgToolBar>
			<%--<t:dgToolBar title="附件" icon="icon-edit" url="" funname="updateYear"></t:dgToolBar>--%>
			<t:dgToolBar title="导出" operationCode="exportData" icon="icon-dataOut" url="tsTravelExpensesWebController.do?exportXls" funname="excelExport"></t:dgToolBar>
			<%--<t:dgToolBar title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detail" width="1200"></t:dgToolBar>--%>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<script type="text/javascript">
    $(function(){
        /*addWdateClass("applyDate_beginDate");
        addWdateClass("applyDate_endDate");*/
        $("input[name='orgName']").attr("readonly",true).attr("id","orgName").attr("style","width:180px").click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });
    function addWdateClass(inputName){
        //日期格式查询条件
        $("input[name='" + inputName + "']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); }).attr("readonly",true);
	}

    //打开组织选择
    function openOrgSelect(){
        var orgCode = $('#orgCode').val();
        var currentOrgCode='${currentOrgCode}';
        orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
        var paraArr = {
            textName: 'orgCode,orgName',
            inputTextName: 'orgCode,orgName',
            searchType: '1',//查询类型？？
            encapsulationType: 'input',//封装类型--不传或默认
            isCouldRemove: true,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            currentOrgCode:currentOrgCode,
            pageData: {
                orgCode: orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    //政策申请
    function addTravelExpenses(title, url, grid, width, height){
        gridname = grid;
        openWindOwn(title, url);
    }
    //修改
    function updateTravelExpenses(title, url, grid, width, height){
        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var rowData = rowDatas[0];
        if (rowData.bpmStatus == 2){
            tip("处理中的数据不可编辑");
            return ;
        }else if (rowData.bpmStatus == 3){
            tip("审批通过的数据不可编辑");
            return ;
        }
        var id = rowData.id;
        if (checkThisDataCouldToUpdate(id)){
            url += "&id=" + id;
            openWindOwn(title, url);
        }
    }

    //----------------------提交流程star--------------------------//
    //提交流程
    function starSubmitForm() {
        var rowDatas = $("#tsTravelExpensesList").datagrid("getSelections");
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }

        var ids = [];
        for ( var i = 0; i < rowDatas.length; i++) {
            var rowData = rowDatas[i];
            if (rowData.bpmStatus == 2){
                tip("处理中的数据不可再次发起");
                return ;
            }else if (rowData.bpmStatus == 3){
                tip("审批通过的数据不可再次发起");
                return ;
            }
            ids.push(rowData.id);
        }

        var processKeyType='direct_travel_expenses_type';
        var params = {
            processKeyType:processKeyType
        };
        customSubmitDialog(ids.join(","),"","","com.biz.eisp.tpm.travelexpenses.controller.TsTravelExpensesWebWorkFlowController",JSON.stringify(params))
    }

    //----------------------提交流程end----------------------//

    //检查
    function checkThisDataCouldToUpdate(id){
        var falg = false;
        var thisData = {
            ids : id
        }
        var url = "tsTravelExpensesWebController.do?checkTheDataIsCouldDo";
        var data = ajaxPost(thisData,url);
        if (data.success){
            falg = true;
        }else{
            tip(data.msg)
        }
        return falg;
    }


    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }

    function openWindOwn(title, url){
        createwindowExt(title,url,"1000","700",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }

</script>
