/* droid-sans-regular - latin */
@font-face {
  font-family: 'Droid Sans';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/droid-sans-v6-latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Droid Sans'), local('DroidSans'),
       url('../fonts/droid-sans-v6-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/droid-sans-v6-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/droid-sans-v6-latin-regular.woff') format('woff'), /* Modern Browsers */
       url('../fonts/droid-sans-v6-latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
       url('../fonts/droid-sans-v6-latin-regular.svg#DroidSans') format('svg'); /* Legacy iOS */
}
/* droid-sans-700 - latin */
@font-face {
  font-family: 'Droid Sans';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/droid-sans-v6-latin-700.eot'); /* IE9 Compat Modes */
  src: local('Droid Sans Bold'), local('DroidSans-Bold'),
       url('../fonts/droid-sans-v6-latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/droid-sans-v6-latin-700.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/droid-sans-v6-latin-700.woff') format('woff'), /* Modern Browsers */
       url('../fonts/droid-sans-v6-latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
       url('../fonts/droid-sans-v6-latin-700.svg#DroidSans') format('svg'); /* Legacy iOS */
}
