SELECT 
	t1.id, 
	t1.full_name AS realName,
	t1.apply_date AS applyDate,
	to_char(t1.begin_date,'yyyy-MM-dd') ||'至'|| to_char(t1.end_date,'yyyy-MM-dd') AS travelDate,
	t1.travel_destination AS travelDestination,
	t1.approval_opinion AS approvalOpinion,
	t1.approval_status AS approvalStatus,
	t1.travel_has_stay AS travelHasStay,
	t1.travel_intent AS travelIntent,
	t1.travel_has_stay AS hasStay,
	t1.travel_intent AS intent,
	t1.TRAVEL_LOCATION_ADDRESS as locationAdress
FROM ts_travel_leave t1 
WHERE t1.type = '2'
AND t1.status = '1'
AND t1.approval_status = '0'
<#if vo.userName ?exists && vo.userName ?length gt 0>
	AND t1.user_name IN (${vo.userName})
	<#else>
	AND 1=2
</#if>

ORDER BY t1.create_date DESC
