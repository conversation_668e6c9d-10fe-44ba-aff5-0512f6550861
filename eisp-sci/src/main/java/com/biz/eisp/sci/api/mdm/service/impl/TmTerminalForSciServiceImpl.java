package com.biz.eisp.sci.api.mdm.service.impl;

import com.biz.eisp.api.util.Globals;
import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.generatednum.num.util.TbNumRuleProvider;
import com.biz.eisp.mdm.customer.entity.TmCustomerEntity;
import com.biz.eisp.mdm.customer.vo.TmCustomerVo;
import com.biz.eisp.mdm.dict.util.DictUtil;
import com.biz.eisp.mdm.position.entity.TmPositionEntity;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import com.biz.eisp.mdm.terminal.entity.TmRTermCustPosBGEntity;
import com.biz.eisp.mdm.terminal.entity.TmTerminalEntity;
import com.biz.eisp.mdm.terminal.service.TmTerminalService;
import com.biz.eisp.mdm.terminal.vo.TmTermCustPostVo;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import com.biz.eisp.mdm.user.service.TmUserService;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.sci.actApply.vo.TmTerminalVoNew;
import com.biz.eisp.sci.api.mdm.dao.MdmApiDao;
import com.biz.eisp.sci.api.mdm.entity.TmTerminalApproveEntity;
import com.biz.eisp.sci.api.mdm.entity.TmTerminalEditApproveEntity;
import com.biz.eisp.sci.api.mdm.entity.TmTerminalForSciEntity;
import com.biz.eisp.sci.api.mdm.service.MdmApiService;
import com.biz.eisp.sci.api.mdm.service.TmTerminalForSciService;
import com.biz.eisp.sci.api.mdm.transform.TmTerminalForSciVoToTmTerminalForSciEntity;
import com.biz.eisp.sci.api.mdm.vo.*;
import com.biz.eisp.sci.pi.pimpl.vo.PhoneConstantUtil;
import com.biz.eisp.sci.picture.dao.TsPictureDao;
import com.biz.eisp.sci.picture.service.TsPictureService;
import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.sci.tebo.TeboShopClientUtil;
import com.biz.eisp.sci.tebo.TeboShopReq;
import com.biz.eisp.sci.tebo.TeboShopResp;
import com.biz.eisp.sci.util.SfaGlobals;
import com.biz.eisp.sci.visitdetail.service.TsVisitDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
/**
 * sci终端service实现.
 * <AUTHOR>
 * @version v1.0
 */
@Service("tmTerminalForSciService")
@Transactional
public class TmTerminalForSciServiceImpl extends BaseServiceImpl implements TmTerminalForSciService{

	@Autowired
	private TmTerminalService tmTerminalService;
	@Autowired
	private TsPictureService tsPictureService;
	@Autowired
	private MdmApiDao mdmApiDao;
	@Autowired
	private MdmApiService mdmApiService;
	@Autowired
	private TsPictureDao tsPictureDao;
	@Autowired
	private TsVisitDetailService tsVisitDetailService;
	@Autowired
	private TmUserService tmUserService;
	final static  String carIcon = "car_icon";
	@SuppressWarnings("deprecation")
	@Override
	public void saveTmTerminalXNY(TmTerminalExtendVo tmTerminalExtendVo) {

		//编码生成
		TbNumRuleProvider tbNumRuleProvider=
				(TbNumRuleProvider) ApplicationContextUtils.getContext().getBean("tbNumRuleProvider");
		String terminalCode=tbNumRuleProvider.getMaxNum(com.biz.eisp.base.common.constant.Globals.AuthTerminal);
		//开始保存
		//this.saveOrUpdate(tmTerminalForSciEntity);
		//2.保存图片信息
		if((tmTerminalExtendVo.getPicVoList()!=null&&tmTerminalExtendVo.getPicVoList().size()>0) || StringUtil.isNotBlank(tmTerminalExtendVo.getPicVoListJson())) {
			tsPictureService.saveTsPictureList(tmTerminalExtendVo.getPicVoList(),tmTerminalExtendVo.getPicVoListJson());
		}
	}

	@SuppressWarnings("deprecation")
	@Override
	public void saveTmTerminal(TmTerminalExtendVo tmTerminalExtendVo) {
//		tmTerminalExtendVo.setTerminalType("terminal");----????干啥的??

		//判断是否是经销商账号，如果是把经销商账号的省市区覆盖到终端
		validateCustomerUser(tmTerminalExtendVo);

		String sql1 =StringUtils.EMPTY;
		String sql2 =StringUtils.EMPTY;
		//车贴可以单独在创建一个, 也就是说. 其他门店性质一个账号, 车贴门店性质一个账号
		if(carIcon.equals(tmTerminalExtendVo.getTerminalType())){
			//新增的是车贴门店, 只查询车贴门店qq
			  sql1 ="select * from tm_terminal tt where tt.linkman_Phone = ? and terminal_Type = 'car_icon' and tt.ssjxs is null ";
			  sql2 ="select * from tm_terminal tt where tt.linkman_Phone = ? and terminal_Type = 'car_icon' and tt.ssjxs is not null ";
		}else{
			//其他类型,排除门店
			  sql1 ="select * from tm_terminal tt where tt.linkman_Phone = ? and (terminal_Type != 'car_icon' or terminal_Type is null) and tt.ssjxs is null ";
			  sql2 ="select * from tm_terminal tt where tt.linkman_Phone = ? and (terminal_Type != 'car_icon' or terminal_Type is null) and tt.ssjxs is not null ";
		}

		//检查重复   2020-02-26   区分推广和广告用途
		List<TmTerminalEntity> tmTerminalEntities = new ArrayList<TmTerminalEntity>();

		if(tmTerminalExtendVo.getSsjxs() == null || "".equals(tmTerminalExtendVo.getSsjxs())){
			tmTerminalEntities = super.findBySql(TmTerminalEntity.class,sql1,tmTerminalExtendVo.getLinkmanPhone());
		} else {
			tmTerminalEntities = super.findBySql(TmTerminalEntity.class,sql2,tmTerminalExtendVo.getLinkmanPhone());
		}
		 //super.findByProperty(TmTerminalEntity.class,"linkmanPhone",tmTerminalExtendVo.getLinkmanPhone());
		if(CollectionUtil.listNotEmptyNotSizeZero(tmTerminalEntities)){
			throw new BusinessException("该店铺已登记过，直接从店铺列表选择即可。如有疑义，请联系系统操作指导。");
//			boolean check = true;
//			String id = tmTerminalExtendVo.getId();
//			if(StringUtil.isNotEmpty(id)){
//				if(tmTerminalEntities.size() > 1){
//					throw new BusinessException("价值终端数据错误，重复多条！！请联系管理员");
//				}
//				if(!id.equals(tmTerminalEntities.get(0).getId())){//2019-07-30  增加判断该店铺是否已经做过门头申请
//					check = smartPreHandle(tmTerminalEntities.get(0).getTerminalCode(),tmTerminalExtendVo.getUserName(),tmTerminalExtendVo.getLinkmanPhone());
//                    if(check){
//						throw new BusinessException("1该店铺已登记过，直接从店铺列表选择即可。如有疑义，请联系系统操作指导。");
//					}
//				}
//			}else{//2019-07-30  增加判断该店铺是否已经做过门头申请
//				check = smartPreHandle(tmTerminalEntities.get(0).getTerminalCode(),tmTerminalExtendVo.getUserName(),tmTerminalExtendVo.getLinkmanPhone());
//				if(check){
//					throw new BusinessException("2该店铺已登记过，直接从店铺列表选择即可。如有疑义，请联系系统操作指导。");
//				}
//				//throw new BusinessException("该店铺已登记过,老板手机（" + tmTerminalExtendVo.getLinkmanPhone() + "）重复，请勿重复登记！");
//			}
		}


		//1.保存
		TmTerminalForSciEntity tmTerminalForSciEntity = new TmTerminalForSciVoToTmTerminalForSciEntity(
				this).apply(tmTerminalExtendVo);
		tmTerminalForSciEntity.setExtChar7("0");//处理中
		//开始保存
		saveTerm(tmTerminalForSciEntity,tmTerminalExtendVo);
		//2.保存图片信息
		if((tmTerminalExtendVo.getPicVoList()!=null&&tmTerminalExtendVo.getPicVoList().size()>0) || StringUtil.isNotBlank(tmTerminalExtendVo.getPicVoListJson())) {
			tsPictureService.saveTsPictureList(tmTerminalExtendVo.getPicVoList(),tmTerminalExtendVo.getPicVoListJson());
		}
//		submitApply(tmTerminalExtendVo);
//		saveTerm(tmTerminalForSciEntity,tmTerminalExtendVo);
	}

	private static String DEL_TERMINAL = "DELETE FROM TM_TERMINAL WHERE TERMINAL_CODE = ? AND TERMINAL_CODE NOT IN ( SELECT TERMINAL_CODE FROM " +
			" TS_ACT_APPLY WHERE  TERMINAL_CODE = ? and DELMARK = 0 )";
	private static String DEL_TSAPPLY = "DELETE FROM TS_ACT_APPLY WHERE TERMINAL_CODE = ? AND DELMARK = 1";
	private static String sql_log_11 = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,15,?)";
	@Transactional
	private boolean smartPreHandle(String terminalCode,String oper,String linkphone){
		int a = this.executeSql(DEL_TERMINAL,terminalCode,terminalCode);
		if(a == 1){//前提是terminalCode不会重复
			this.executeSql(DEL_TSAPPLY,terminalCode);
			this.executeSql(sql_log_11,terminalCode,a+"成功"+linkphone,oper);
			return false;
		} else {
			this.executeSql(sql_log_11,terminalCode,a+"失败"+linkphone,oper);
			return true;
		}

	}

	private void validateCustomerUser(TmTerminalExtendVo tmTerminalExtendVo) {
		String userName = tmTerminalExtendVo.getUserName();
		TmUserEntity userEntity = this.findUniqueByProperty(TmUserEntity.class, "userName", userName);
		if(userEntity!=null&&"1".equals(String.valueOf(userEntity.getUserType()))){//如果是客戶用戶
			String sql = "select tc.province,tc.city,tc.area from tm_R_user_customer tr " +
					"join tm_customer tc on tc.id=tr.customer_id " +
					"where user_id=?";
			List<TmCustomerVo> customerVos = this.findBySql(TmCustomerVo.class, sql, userEntity.getId());
			if(CollectionUtil.listNotEmptyNotSizeZero(customerVos)){
				tmTerminalExtendVo.setProvince(customerVos.get(0).getProvince());
				tmTerminalExtendVo.setCity(customerVos.get(0).getCity());
				tmTerminalExtendVo.setArea(customerVos.get(0).getArea());
			}
		}
	}

	private  void saveTerm(TmTerminalForSciEntity tmTerminalForSciEntity,TmTerminalExtendVo tmTerminalExtendVo){

		//用户信息
		UserInfoEntity userInfoEntity = mdmApiService.getUserInfoEntityByPosId(tmTerminalExtendVo.getPosId());
		//当前人信息
		tmTerminalForSciEntity.setCreateName(userInfoEntity.getRealName());
		tmTerminalExtendVo.setCreateName(userInfoEntity.getRealName());
		tmTerminalExtendVo.setUaccount(userInfoEntity.getUserId());
		//保存
//		this.saveOrUpdate(tmTerminalForSciEntity);
		this.save(tmTerminalForSciEntity);

		tmTerminalExtendVo.setTaskId(tmTerminalForSciEntity.getId());
		//保存关系中的门店信息
		List<TmRTermCustPosBGEntity> custPosBGs = getTermPosBgInfo(
				tmTerminalForSciEntity, tmTerminalExtendVo, userInfoEntity);
		if(CollectionUtil.listNotEmptyNotSizeZero(custPosBGs)){
			if (StringUtils.isNotBlank(tmTerminalExtendVo.getPosId())){
				//删除
				String sql = "DELETE FROM TM_R_TERM_CUST_POS_BG WHERE TERMINAL_ID = ? AND POSITION_ID = ?";
				executeSql(sql,tmTerminalForSciEntity.getId(),tmTerminalExtendVo.getPosId());
			}else{
				if (StringUtils.isNotBlank(tmTerminalExtendVo.getCustomerId())){
					String[] customer=tmTerminalExtendVo.getCustomerId().split(",");
					for (int i = 0; i <customer.length ; i++) {
						String customerId = customer[i];
						//删除
						String sql = "DELETE FROM TM_R_TERM_CUST_POS_BG WHERE TERMINAL_ID = ? and customer_id =? AND POSITION_ID is null";
						executeSql(sql,tmTerminalForSciEntity.getId(),customerId);
					}
				}
			}
//			this.getSession().flush();
			//保存
			this.batchSave(custPosBGs);
		}else {
			throw new BusinessException("构建部分关系失败!");
		}
	}
	/**
	 * 添加申请记录
	 */
	private void submitApply(TmTerminalExtendVo tmTerminalExtendVo){
		TmTerminalApproveEntity approveEntity=new TmTerminalApproveEntity();
		approveEntity.setApprovalContent("提交门店申请");//审批意见
		approveEntity.setApprovalType("0");//已提交
		approveEntity.setApprovalDate(new Date());
		approveEntity.setApprovalName(tmTerminalExtendVo.getCreateName());
		approveEntity.setApprovalPosId(tmTerminalExtendVo.getPosId());
		approveEntity.setTerminalId(tmTerminalExtendVo.getTaskId());
		approveEntity.setTerminalName(tmTerminalExtendVo.getTerminalName());
		approveEntity.setUserId(tmTerminalExtendVo.getUaccount());
		approveEntity.setCreateDate(new Date());
		approveEntity.setOperateType("0");
		approveEntity.setCreateName(tmTerminalExtendVo.getCreateName());
		this.save(approveEntity);
	}


	/**
	 * 保存关系.
	 * <AUTHOR>
	 * @param tmTerminalForSciEntity
	 * 		实体对象
	 * @param tmTerminalExtendVo
	 * 		vo对象
	 * @return
	 */
	private List<TmRTermCustPosBGEntity> getTermPosBgInfo(TmTerminalForSciEntity tmTerminalForSciEntity,TmTerminalExtendVo tmTerminalExtendVo,
			UserInfoEntity userInfoEntity) {

		//关系集合
		List<TmRTermCustPosBGEntity> list = new ArrayList<TmRTermCustPosBGEntity>();

		if (String.valueOf(Globals.LOGIN_USER_TYPE_ENTERUSER).equals(tmTerminalExtendVo.getUserType())) {
			//终端，客户，职位关系---针对终端
			TmRTermCustPosBGEntity	tmRTermCustPosBGEntity = new TmRTermCustPosBGEntity();
			//设置职位
			setTmPositionToBGEntity(tmRTermCustPosBGEntity,tmTerminalExtendVo.getPosId());
			//设置所属客户
			setTmCustomerToBGEntity(tmRTermCustPosBGEntity,tmTerminalExtendVo.getCustomerId());
			//设置组织(设置经销商所属组织)
			setOrgIdToBGEntity(tmRTermCustPosBGEntity,tmTerminalExtendVo.getCustomerId());
			//设置门店
			setTmTerminalToBGEntity(tmRTermCustPosBGEntity,tmTerminalForSciEntity.getId());
			//当前创建人
			tmRTermCustPosBGEntity.setCreateName(userInfoEntity.getRealName() +"("+userInfoEntity.getPosName()+")");
			list.add(tmRTermCustPosBGEntity);
		}else if (String.valueOf(Globals.LOGIN_USER_TYPE_DEALERS).equals(tmTerminalExtendVo.getUserType())) {
			//获经销商用户对应的经销商
			getCustomerIdByUserInfo(tmTerminalExtendVo);
			String customerIdTemp = tmTerminalExtendVo.getCustomerId();
			if(!StringUtil.isNotEmpty(customerIdTemp)){
				throw new BusinessException("经销商对应的客户关键值丢失");
			}

			String[] customer = customerIdTemp.split(",");

			for (int i = 0; i < customer.length; i++) {
				String customerId = customer[i];
				if (!StringUtil.isNotEmpty(customerId)) {
					throw new BusinessException("经销商对应的客户关键值丢失");
				}
				//终端，客户，职位关系---针对终端
				TmRTermCustPosBGEntity	tmRTermCustPosBGEntity = new TmRTermCustPosBGEntity();
				//设置所属客户
				setTmCustomerToBGEntity(tmRTermCustPosBGEntity,customerId);
				//设置经销商组织
				setOrgIdToBGEntity(tmRTermCustPosBGEntity,customerId);
				//设置门店
				setTmTerminalToBGEntity(tmRTermCustPosBGEntity,tmTerminalForSciEntity.getId());
				//创建人信息
				tmRTermCustPosBGEntity.setCreateName(userInfoEntity.getRealName() +"("+userInfoEntity.getPosName()+")");
				list.add(tmRTermCustPosBGEntity);
			}
		}else{
			throw new BusinessException("未知的用户类型!");
		}
		return list;
	}

	//设置门店
	private void setTmTerminalToBGEntity(TmRTermCustPosBGEntity tmRTermCustPosBGEntity, String id) {
		TmTerminalEntity tmTerminalEntity = new TmTerminalEntity();
		tmTerminalEntity.setId(id);
		tmRTermCustPosBGEntity.setTmTerminal(tmTerminalEntity);
	}

	//设置经销商组织
	private void setOrgIdToBGEntity(TmRTermCustPosBGEntity tmRTermCustPosBGEntity, String customerId) {
		if(StringUtil.isNotEmpty(customerId)){//客户直接读取需要同过tmorg实体ha....t...读取，以防读取失败
			String sql = "select org_id from tm_customer where id=?";
			List<TmCustomerVo> customerVoList=this.findBySql(TmCustomerVo.class, sql,customerId);
			if(CollectionUtil.listNotEmptyNotSizeZero(customerVoList)){
				tmRTermCustPosBGEntity.setOrgId(customerVoList.get(0).getOrgId());
			}else{
				throw new BusinessException("未找到当前对应经销商对应的组织");
			}
		}
	}

	//设置客户
	private void setTmCustomerToBGEntity(TmRTermCustPosBGEntity tmRTermCustPosBGEntity, String customerId) {
		if(StringUtil.isNotEmpty(customerId)){
			TmCustomerEntity customerEntity = tsVisitDetailService.get(TmCustomerEntity.class,customerId);
			if(customerEntity != null){
				tmRTermCustPosBGEntity.setTmCustomer(customerEntity);
			}else{
				throw new BusinessException("未找到对应经销商");
			}
		}
	}

	//设置职位
	private void setTmPositionToBGEntity(TmRTermCustPosBGEntity tmRTermCustPosBGEntity, String posId) {
		TmPositionEntity tmPosition =tsVisitDetailService.get(TmPositionEntity.class,posId);
		tmRTermCustPosBGEntity.setTmPosition(tmPosition);
	}

	/**
	 * 获取经销商用户对应的经销商信息
	 * @param tmTerminalExtendVo
	 */
	private void getCustomerIdByUserInfo(TmTerminalExtendVo tmTerminalExtendVo) {
		String returnKey = "customerId";
		String sql = " select truc.customer_id as " + returnKey + " from tm_r_user_customer truc where truc.user_id = ? ";
		List<Map<String, Object>> mapLsist = this.findForMapList(sql,tmTerminalExtendVo.getUserId());
		if (CollectionUtil.listNotEmptyNotSizeZero(mapLsist)) {
			if (mapLsist.size() != 1) {
				throw new BusinessException("账号（" + tmTerminalExtendVo.getUserName() + "）读取多个经销商,错误！");
			}
		}else{
			throw new BusinessException("账号（" + tmTerminalExtendVo.getUserName() + "）无对应的经销商!");
		}
		Map<String, Object> map = mapLsist.get(0);
		if(map != null ){
			tmTerminalExtendVo.setCustomerId(OwnBeanUtils.returnNotNullWordValue(String.valueOf(map.get(returnKey))));
		}else{
			throw new BusinessException("账号（" + tmTerminalExtendVo.getUserName() + "）获取对应经销商失败！");
		}
	}

	/**
	 * 保存图片信息.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		终端vo参数
	 */
	private void saveTerminalPicture(TmTerminalExtendVo tmTerminalExtendVo) {
		//设置参数
		TsPictureVo tsPictureVo = new TsPictureVo();
		tsPictureVo.setBusinessId(tmTerminalExtendVo.getTerminalCode());
		tsPictureVo.setImgTypeRemark(tmTerminalExtendVo.getImgTypeRemark());
		tsPictureVo.setPsTime(tmTerminalExtendVo.getPsTime());
		tsPictureVo.setRemark(tmTerminalExtendVo.getRemark());
		tsPictureVo.setImgType(tmTerminalExtendVo.getImgType());
		tsPictureVo.setPhotoName(tmTerminalExtendVo.getPhotoName());
		tsPictureVo.setUaccount(tmTerminalExtendVo.getUaccount());
		tsPictureVo.setImgedate(tmTerminalExtendVo.getImgedate());
		tsPictureService.saveTsPicture(tsPictureVo);
	}
	@Override
	public List<TmTerminalExtendVo> findMyApplyTerm(TmTerminalExtendVo tmTerminalExtendVo, Page page) {
		List<TmTerminalExtendVo> terminalExtendVos = mdmApiDao.findMyApplyTerm(tmTerminalExtendVo, page);
		//设置审批状态
		if (CollectionUtil.listNotEmptyNotSizeZero(terminalExtendVos)) {
			for (TmTerminalExtendVo tmTerminalExtendVo2 : terminalExtendVos) {
				tmTerminalExtendVo2.setStatus(DictUtil.getDictDataValueByCode(PhoneConstantUtil.termianlStatus, tmTerminalExtendVo2.getExtChar7()));
				//查找 待审批人
				List<UserApproveVo>  users=mdmApiDao.getUserListForApprove(tmTerminalExtendVo2.getId(),null);
				Set<String> setM=new HashSet<>();
				for (int i = 0; i <users.size(); i++) {
					setM.add(users.get(i).getFullName());
				}
				tmTerminalExtendVo2.setPendingTrialPerson(StringUtils.join(setM,","));
				Set<String> setC=new HashSet<>();
				List<TmCustomerVo> customerList = tsVisitDetailService.getbelongCust(tmTerminalExtendVo2.getId());
				for (int i = 0; i < customerList.size(); i++) {
					setC.add(customerList.get(i).getCustomerName());
				}
				tmTerminalExtendVo2.setCustomerName(StringUtils.join(setC,","));
				if(null != tmTerminalExtendVo2.getExtChar9()) {
				tmTerminalExtendVo2.setExtChar9(DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel,tmTerminalExtendVo2.getExtChar9()));
			}
		}
		}

		return terminalExtendVos;
	}
	@Override
	public List<TmTerminalExtendVo> findMyApprovalTerm(
			TmTerminalExtendVo tmTerminalExtendVo, Page page) {
		return mdmApiDao.findMyApprovalTerm(tmTerminalExtendVo, page);
	}
	@Override
	public TmTerminalExtendVo findTerminalDetail(TmTerminalExtendVo tmTerminalExtendVo) {
		
		//terminalType终端类型  extChar3位置类型 cooperative合作状态 extChar7审批状态 enableStatus启用状态
		
		String terminalId = tmTerminalExtendVo.getId();
		TmTerminalEditApproveEntity editapprove = mdmApiDao.sercahTmEditApprove(terminalId);
		
		TmTerminalExtendVo vo = mdmApiDao.findTerminalDetail(terminalId);
		if(null !=editapprove){
			String sql = "select * from TM_TERMINAL_APPROVE where APPROVAL_LOG_ID=?";
			List<TmTerminalApproveEntity> ttApproveList = this.findBySql(TmTerminalApproveEntity.class, sql, editapprove.getId());
			vo.setTerminalName(editapprove.getTerminalName());
			vo.setAddress(editapprove.getAddress());
			vo.setChannelType(editapprove.getFirstChannel());
			vo.setExtChar8(editapprove.getSecondChannel());
			vo.setExtChar9(editapprove.getThirdChannel());
			vo.setLinkman(editapprove.getLinkman());
			vo.setLinkmanPhone(editapprove.getLinkmanPhone());
			vo.setExtChar1(editapprove.getAcreage());
			vo.setExtChar2(editapprove.getCash());
			vo.setExtChar3(editapprove.getPosition());
			vo.setExtNumber1(editapprove.getExtnumber1());
			vo.setExtNumber2(editapprove.getExtnumber2());
			vo.setExtNumber3(editapprove.getExtnumber3());
			vo.setExtNumber4(editapprove.getExtnumber4());
			vo.setProvince(editapprove.getProvince());
			vo.setCity(editapprove.getCity());
			vo.setArea(editapprove.getArea());
			vo.setExtChar5(editapprove.getLongitude());
			vo.setExtChar6(editapprove.getLatitude());
//			vo.setExtChar10(editapprove.getBusinessId());
			vo.setExtChar11(editapprove.getTown());
		}
		
		String custname ="";
		String custId="";
		if(null !=editapprove){
			String[] customer = editapprove.getCustomerId().split(",");
			for (int i = 0; i < customer.length; i++) {
				TmCustomerEntity cust = this.get(TmCustomerEntity.class, customer[i]);
				if(i==0) {
			custname+=cust.getCustomerName();
			custId+=cust.getId();
		}else{
					custname += ","+cust.getCustomerName();
					custId += ","+cust.getId();
				}
			}
		}else{
			List<TmCustomerVo> customerList = tsVisitDetailService.getbelongCust(vo.getId());
			for(int i=0;i<customerList.size();i++){
				if(i==0){
					custname += customerList.get(i).getCustomerName();
					custId+=customerList.get(i).getId();
				}else{
					custname += ","+customerList.get(i).getCustomerName();
					custId+=","+customerList.get(i).getId();
				}
			}
		}
		vo.setExtChar15(custname);
		vo.setCustomerId(custId);
		
		//channelType 一级渠道  char8 二级 char9 三级
//		if(null != vo.getExtChar9()){
//			vo.setChannelType();//取第3级渠道
//			String extChar9Value = DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel,vo.getExtChar9());
//			vo.setExtChar9(extChar9Value);
//		}
//		//一级渠道
//		if(StringUtils.isNotBlank(vo.getChannelType())){
//			String chanType = DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel,vo.getChannelType());
//			vo.setChannelType(chanType);
//		}
//		//二级渠道
//		if(StringUtils.isNotBlank(vo.getExtChar8())){
//			String two = DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel,vo.getExtChar8());
//			vo.setExtChar8(two);
//		}
		
		//terminalType终端类型  extChar3位置类型 cooperative合作状态 extChar7审批状态 enableStatus启用状态
//		if(StringUtils.isNotBlank(vo.getTerminalType())){
//			String terminalType = DictUtil.getDictDataValueByCode("terminal_type", vo.getTerminalType());
//			vo.setTerminalType(terminalType);
//		}
//		
//		if(StringUtils.isNotBlank(vo.getExtChar3())){
//			String extChar3 = DictUtil.getDictDataValueByCode("location_type", vo.getExtChar3());
//			vo.setExtChar3(extChar3);
//		}
//		//这个是数据库没得
//		if(StringUtils.isNotBlank(vo.getCooperative())){
//			String cooperative = DictUtil.getDictDataValueByCode("cooperative", vo.getCooperative());
//			vo.setCooperative(cooperative);
//		}
//		
//		if(StringUtils.isNotBlank(vo.getExtChar7())){
//			String extChar7 = DictUtil.getDictDataValueByCode("bpm_status", vo.getExtChar7());
//			vo.setExtChar7(extChar7);
//		}
//		
//		if(vo.getEnableStatus() != null){
//			String enableStatus = DictUtil.getDictDataValueByCode("enable_status", vo.getEnableStatus().toString());
//		}
		//手机端显示ExtChar9为文字extChar9Code位编码（下同）
		if(null != vo.getExtChar9()){
//			vo.setChannelType(DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel,vo.getExtChar9()));//取第3级渠道
			vo.setExtChar9Code(vo.getExtChar9());
			vo.setExtChar9(DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel,vo.getExtChar9()));
			
		}
		if(null != vo.getExtChar3()){
			vo.setExtChar3Code(vo.getExtChar3());
			vo.setExtChar3(DictUtil.getDictDataValueByCode(PhoneConstantUtil.locationType,vo.getExtChar3()));//位置类型
		}
		if(null != vo.getTerminalType()){
			vo.setTerminalTypeCode(vo.getTerminalType());
			vo.setTerminalType(DictUtil.getDictDataValueByCode(PhoneConstantUtil.termianlType,vo.getTerminalType()));//门店类型
		}
		if(null!=vo.getExtChar10()){
			List<TsPictureVo> picList = tsPictureDao.getAllpictrue(vo.getExtChar10(), "50");
			//装载附件路径
			String servers = ResourceUtil.getSysConfigProperty("scitestPath");	
			List<String> pathList = new ArrayList<>();
			vo.setImgPath(pathList);
			for(TsPictureVo picvo : picList){
				String visitPath = servers+ picvo.getImgPath();
				vo.getImgPath().add(visitPath);
			}
		}
		Set<String> setM=new HashSet<>();
		List<UserApproveVo>  users=mdmApiDao.getUserListForApprove(vo.getId(),null);
		for (int i = 0; i <users.size(); i++) {
			setM.add(users.get(i).getFullName());
		}
		vo.setPendingTrialPerson(StringUtils.join(setM,","));
//		vo.setAppUser(users);
		return vo;
	}
	@Override
	public void executeApprovalTerm(TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo) {
		if(StringUtil.isEmpty(tmTerminalForSciWorkFlowVo.getApprovalType())){
			throw new BusinessException("缺少必须的参数approvalType(审批动作类型)");
		}
		if (StringUtil.isNotEmpty(tmTerminalForSciWorkFlowVo.getTaskId())) {
			TmTerminalForSciWorkFlowVo vo = new TmTerminalForSciWorkFlowVo();
			vo.setPosId(tmTerminalForSciWorkFlowVo.getPosId());
			vo.setComment(tmTerminalForSciWorkFlowVo.getComment());
			String[] arr=tmTerminalForSciWorkFlowVo.getTaskId().split(",");
			for (int i = 0; i < arr.length; i++) {
				vo.setTaskId(tmTerminalForSciWorkFlowVo.getTaskId().split(",")[i]);
				vo.setProcessInstanceId(tmTerminalForSciWorkFlowVo.getProcessInstanceId().split(",")[i]);
				//审批
				handlerApproval(tmTerminalForSciWorkFlowVo,arr[i]);
			}
		}
	}
	/**
	 * 处理审批.
	 * <AUTHOR>
	 * @param tmTerminalForSciWorkFlowVo
	 */
	private void handlerApproval(
			TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo,String terminalId) {

		//用户信息
		UserInfoEntity userInfoEntity = mdmApiService
				.getUserInfoEntityByPosId(tmTerminalForSciWorkFlowVo.getPosId());
		String hqledit="from TmTerminalApproveEntity where terminalId=? and approvalType in('0') and operateType='1'";//看是有有过编新增辑项，如有就肯定是编辑审批
		List<TmTerminalApproveEntity> editlist=mdmApiService.findByHql(hqledit,new Object[]{terminalId});
		if(CollectionUtil.listNotEmptyNotSizeZero(editlist)){
			submitEditApprove(tmTerminalForSciWorkFlowVo,userInfoEntity,terminalId);
		}else{
//		if(){门店编辑如果通过不改tm_r_cust_pos_bg
//			return;
//		}审批逻辑以下都要改（看是有有过编辑项，如有就肯定是编辑审批）
			String hql="from TmTerminalApproveEntity where terminalId=? and approvalType in('1','2') and operateType='0'";
			List<TmTerminalApproveEntity> list=mdmApiService.findByHql(hql,new Object[]{terminalId});
			if(CollectionUtil.listNotEmptyNotSizeZero(list)){
				throw new BusinessException("任务已经被审批!");
			}else{
				submitApprove(tmTerminalForSciWorkFlowVo,userInfoEntity,terminalId);
			}
	}

	}
	/**编辑门店添加审批记录*/
	private void submitEditApprove(TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo,UserInfoEntity userInfoEntity,String terminalId){
		
		TmTerminalEditApproveEntity editapprove =  mdmApiDao.sercahTmEditApprove(terminalId);
 		if(editapprove.getIsDrp()!=null &&1==editapprove.getIsDrp()) {//目前只有司机可编辑所属经销商
		//编辑时不更改原来的门店关系
			TmTerminalForSciEntity tmTerminalForSciEntity = new TmTerminalForSciEntity();
			tmTerminalForSciEntity.setId(terminalId);

			TmTerminalExtendVo tmTerminalExtendVo = new TmTerminalExtendVo();
			tmTerminalExtendVo.setCustomerId(editapprove.getCustomerId());
			tmTerminalExtendVo.setCreateName(userInfoEntity.getUserName());
			tmTerminalExtendVo.setUaccount(userInfoEntity.getUserId());
			tmTerminalExtendVo.setTaskId(tmTerminalForSciEntity.getId());


			//保存关系中的门店信息
			String sql = "select id ,TERMINAL_ID terminalId,CUSTOMER_ID customerId,POSITION_ID positionId FROM TM_R_TERM_CUST_POS_BG WHERE TERMINAL_ID = ? ";
			List<TmTermCustPostVo> delList = tsVisitDetailService.findBySql(TmTermCustPostVo.class,sql,new Object[]{terminalId});
			for (int i = 0; i <delList.size() ; i++) {
				//删除
				executeSql("DELETE FROM TM_R_TERM_CUST_POS_BG WHERE id = ?",
						delList.get(i).getId());
			}
			List<TmRTermCustPosBGEntity> custPosBGs = getTermPosBgInfo(
					tmTerminalForSciEntity, tmTerminalExtendVo, userInfoEntity);
			if(CollectionUtil.listNotEmptyNotSizeZero(custPosBGs)) {
				this.getSession().flush();
				this.batchSave(custPosBGs);
			}

		}
		submitEditApproveDetail( tmTerminalForSciWorkFlowVo, userInfoEntity, terminalId,editapprove);
		
	}
	/**
	 * 编辑时添加审批记录
	 */
	private void submitEditApproveDetail(TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo,UserInfoEntity userInfoEntity
			,String terminalId,TmTerminalEditApproveEntity editapprove){
		TmTerminalApproveEntity approveEntity=new TmTerminalApproveEntity();
		approveEntity.setApprovalContent(tmTerminalForSciWorkFlowVo.getComment());//审批意见 1通过 2 驳回
		approveEntity.setApprovalType(tmTerminalForSciWorkFlowVo.getApprovalType());
		approveEntity.setApprovalDate(new Date());
		approveEntity.setApprovalName(userInfoEntity.getRealName());
		approveEntity.setApprovalPosId(tmTerminalForSciWorkFlowVo.getPosId());
		approveEntity.setTerminalId(terminalId);
		approveEntity.setUserId(userInfoEntity.getUserId());
		approveEntity.setOperateType("1");//编辑
		approveEntity.setApprovalLogId(editapprove.getId());
		approveEntity.setCreateDate(new Date());
		tmTerminalService.save(approveEntity);
		TmTerminalForSciEntity tmsci=this.get(TmTerminalForSciEntity.class,terminalId);
		if(tmsci!=null){
			Integer approved = PhoneConstantUtil.TRAVELLEAVE_APPROVED;
			Integer reject = PhoneConstantUtil.TRAVELLEAVE_REJECT;
			if(tmTerminalForSciWorkFlowVo.getApprovalType().equals(approved.toString())){//编辑通过
				tmsci.setTerminalName(editapprove.getTerminalName());
				tmsci.setAddress(editapprove.getAddress());
				tmsci.setChannelType(editapprove.getFirstChannel());
				tmsci.setExtChar8(editapprove.getSecondChannel());
				tmsci.setExtChar9(editapprove.getThirdChannel());
				tmsci.setLinkman(editapprove.getLinkman());
				tmsci.setLinkmanPhone(editapprove.getLinkmanPhone());
				tmsci.setExtChar1(editapprove.getAcreage());
				tmsci.setExtChar2(editapprove.getCash());
				tmsci.setExtChar3(editapprove.getPosition());
				tmsci.setExtNumber1(editapprove.getExtnumber1());
				tmsci.setExtNumber2(editapprove.getExtnumber2());
				tmsci.setExtNumber3(editapprove.getExtnumber3());
				tmsci.setExtNumber4(editapprove.getExtnumber4());
				tmsci.setProvince(editapprove.getProvince());
				tmsci.setCity(editapprove.getCity());
				tmsci.setArea(editapprove.getArea());
				tmsci.setExtChar5(editapprove.getLongitude());
				tmsci.setExtChar6(editapprove.getLatitude());
//				tmsci.setExtChar10(editapprove.getBusinessId());
				tmsci.setCooperative("normal");
				tmsci.setExtChar7(approved.toString());
				tmsci.setUpdateDate(new Date());
				tmsci.setUpdateName(userInfoEntity.getRealName());
				tmsci.setExtChar11(editapprove.getTown());
			}else if(tmTerminalForSciWorkFlowVo.getApprovalType().equals(reject.toString())){//编辑驳回
				tmsci.setCooperative("stop");
				tmsci.setExtChar7(reject.toString());
			}
			this.saveOrUpdate(tmsci);
		}
	}
	
	/**
	 * 添加审批记录
	 */
	private void submitApprove(TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo,UserInfoEntity userInfoEntity,String terminalId){
		TmTerminalApproveEntity approveEntity=new TmTerminalApproveEntity();
		approveEntity.setApprovalContent(tmTerminalForSciWorkFlowVo.getComment());//审批意见 1通过 2 驳回
		approveEntity.setApprovalType(tmTerminalForSciWorkFlowVo.getApprovalType());
		approveEntity.setApprovalDate(new Date());
		approveEntity.setApprovalName(userInfoEntity.getRealName());
		approveEntity.setApprovalPosId(tmTerminalForSciWorkFlowVo.getPosId());
		approveEntity.setTerminalId(terminalId);
		approveEntity.setUserId(userInfoEntity.getUserId());
		TmTerminalForSciEntity tmsci=this.get(TmTerminalForSciEntity.class,terminalId);
		if(tmsci!=null){
			tmsci.setExtChar7(tmTerminalForSciWorkFlowVo.getApprovalType());
			Integer approved = PhoneConstantUtil.TRAVELLEAVE_APPROVED;
			Integer reject = PhoneConstantUtil.TRAVELLEAVE_REJECT;
			if(tmTerminalForSciWorkFlowVo.getApprovalType().equals(approved.toString())){
				tmsci.setCooperative("normal");
			}else if(tmTerminalForSciWorkFlowVo.getApprovalType().equals(reject.toString())){
				tmsci.setCooperative("stop");
			}
			this.updateEntity(tmsci);//更改审批终端
		}else {
			throw new BusinessException("任务对象无效!");
		}
		tmTerminalService.save(approveEntity);
		
	}

	@Override
	public List<TmDictDataForSci> findDictTree(String codetype) {
		// TODO Auto-generated method stub
		return mdmApiDao.findDictTree(codetype);
	}

	@Override
	public Integer findMyApprovalTermNum(TmTerminalExtendVo tmTerminalExtendVo) {
		if (StringUtils.isNotBlank(tmTerminalExtendVo.getPosCode())) {
			TmPositionEntity positionEntity = this.findUniqueByProperty(TmPositionEntity.class,
					"positionCode", tmTerminalExtendVo.getPosCode());
			tmTerminalExtendVo.setOrgId(positionEntity.getTmOrg().getId());
			return mdmApiDao.findMyApprovalTermNum(tmTerminalExtendVo);
		}else{
			throw new BusinessException("岗位编码不能为空");
		}
	}

	@Override
	public List<TmTerminalExtendVo> findTerminalListMain(TmTerminalExtendVo tmTerminalExtendVo, Page page) {
		getCustomerIdByUserInfo(tmTerminalExtendVo);
		String customerId = tmTerminalExtendVo.getCustomerId();
		if(!StringUtil.isNotEmpty(customerId)){
			throw new BusinessException("经销商用户对应的客户关键值丢失");
		}
		TmCustomerEntity tmCustomerEntity = super.get(TmCustomerEntity.class,customerId);
		if(tmCustomerEntity != null){
			tmTerminalExtendVo.setProvince(tmCustomerEntity.getProvince());//省
			tmTerminalExtendVo.setCity(tmCustomerEntity.getCity());//市
//			tmTerminalExtendVo.setArea(tmCustomerEntity.getArea());//区/县
		}else{
			throw new BusinessException("账号（" + tmTerminalExtendVo.getUserName() + "）无对应的经销商");
		}
		//设置字典类型
		setTerminalType(tmTerminalExtendVo);
		//开始查询
		return mdmApiDao.findTerminalListMain(tmTerminalExtendVo,page);
	}

	/**
	 * 获取泰博出行终端信息列表
	 *
	 * @param tmTerminalExtendVo
	 * @param page
	 * @return
	 */
	@Override
	public List<TeboShopResp> findTbcxTerminalListMain(TmTerminalExtendVo tmTerminalExtendVo, Page page) {
		getCustomerIdByUserInfo(tmTerminalExtendVo);
		String customerId = tmTerminalExtendVo.getCustomerId();
		if(!StringUtil.isNotEmpty(customerId)){
			throw new BusinessException("经销商用户对应的客户关键值丢失");
		}
		TmCustomerEntity tmCustomerEntity = super.get(TmCustomerEntity.class,customerId);
		if(tmCustomerEntity == null){
			throw new BusinessException("账号（" + tmTerminalExtendVo.getUserName() + "）无对应的经销商");
		}
		TeboShopReq req = new TeboShopReq();
		req.setKeyWord(tmTerminalExtendVo.getTerminalName());
		req.setPageNum(page.getInt(Page.PAGE));
		req.setPageSize(page.getInt(Page.ROWS));
		List<TeboShopResp> teboShopResps = TeboShopClientUtil.queryShops(req);
		return teboShopResps;
	}

	@Override
	public List<TmTerminalExtendVo> findTerminalListMainXNY(TmTerminalExtendVo tmTerminalExtendVo, Page page) {
		getCustomerIdByUserInfo(tmTerminalExtendVo);
		String customerId = tmTerminalExtendVo.getCustomerId();
		if(!StringUtil.isNotEmpty(customerId)){
			throw new BusinessException("经销商用户对应的客户关键值丢失");
		}
		//设置字典类型
		setTerminalType(tmTerminalExtendVo);
		//开始查询
		return mdmApiDao.findTerminalListMain(tmTerminalExtendVo,page);
	}

	/**
	 * 设置terminalType值
	 * @param vo
	 */
	private void setTerminalType(TmTerminalExtendVo vo){
		String bpmStatusStr = OwnBeanUtils.changeListEntityMorePropertyToString(DictUtil.allDictData.get(SfaGlobals.TT_TERMINAL_TERMINAL_TYPE),"，","dictCode","dictValue");
		vo.setTerminalTypeName(bpmStatusStr.replace("，",","));
	}

	@Override
	public void editTmTerminal(TmTerminalExtendVo tmTerminalExtendVo,TmTerminalEntity terminal) { 
		// TODO Auto-generated method stub
		String sql = "select TERMINAL_ID terminalId,CUSTOMER_ID customerId,POSITION_ID positionId FROM TM_R_TERM_CUST_POS_BG WHERE TERMINAL_ID = ? ";
		List<TmTermCustPostVo> list = tsVisitDetailService.findBySql(TmTermCustPostVo.class,sql,new Object[]{tmTerminalExtendVo.getId()});
//		if(StringUtil.isBlank(list.get(0).getPositionId())&&(null==tmTerminalExtendVo.getIsDrp()||tmTerminalExtendVo.getIsDrp()!=1)){
//			throw new BusinessException("该门店无负责人，不可编辑");
//		}
		if(!CollectionUtil.listNotEmptyNotSizeZero(list)){
			throw new BusinessException("该门店无所属经销商，不可编辑");
		}
		UserInfoEntity userInfoEntity = new UserInfoEntity();

		if(null==tmTerminalExtendVo.getIsDrp()||1!=tmTerminalExtendVo.getIsDrp()) {
			userInfoEntity = mdmApiService.getUserInfoEntityByPosId(tmTerminalExtendVo.getPosId());
		}else{
			TmUserVo userVo = tmUserService.getTmUserVo(tmTerminalExtendVo.getUaccount());
			userInfoEntity.setUserId(userVo.getId());
			userInfoEntity.setUserName(userVo.getUserName());
			userInfoEntity.setRealName(userVo.getFullName());
		}
		Boolean inApproval =false;
		if(!terminal.getTerminalName().equals(tmTerminalExtendVo.getTerminalName())){
			inApproval = true;
		}
		if(null==tmTerminalExtendVo.getIsDrp()||tmTerminalExtendVo.getIsDrp()!=1) {//判断所属经销商是否改变
		if(!list.get(0).getCustomerId().equals(tmTerminalExtendVo.getCustomerId())){
			inApproval = true;
		}
		}else {
			String[] customer = tmTerminalExtendVo.getCustomerId().split(",");
			if(customer.length!=list.size()){
				inApproval = true;
			}else {
				for (String custId : customer) {
					Integer siz = 0;
					for (int i = 0; i < list.size(); i++) {
						if (custId.equals(list.get(i).getCustomerId())) {
							break;
						}
						siz++;
					}
					if (siz == list.size()) {
						inApproval = true;
						break;
					}
				}
			}
		}
		if(("2").equals(terminal.getExtChar7())){
			inApproval = true;
		}
		
		if(inApproval){//从新审批
			terminal.setExtChar7("0");
			terminal.setCooperative(null);
			terminal.setUpdateDate(new Date());
			terminal.setUpdateName(userInfoEntity.getRealName());
			//编辑需审批内容详细
			TmTerminalEditApproveEntity editEntity = new TmTerminalEditApproveEntity();
			editEntity.setCustomerId(tmTerminalExtendVo.getCustomerId());
			editEntity.setTerminalName(tmTerminalExtendVo.getTerminalName());
			editEntity.setCreateDate(new Date());
			editEntity.setCreateName(userInfoEntity.getRealName());
			editEntity.setFirstChannel(tmTerminalExtendVo.getChannelType());
			editEntity.setSecondChannel(tmTerminalExtendVo.getExtChar8());
			editEntity.setThirdChannel(tmTerminalExtendVo.getExtChar9());
			editEntity.setLinkman(tmTerminalExtendVo.getLinkman());
			editEntity.setLinkmanPhone(tmTerminalExtendVo.getLinkmanPhone());
			editEntity.setAcreage(tmTerminalExtendVo.getExtChar1());
			editEntity.setCash(tmTerminalExtendVo.getExtChar2());
			editEntity.setPosition(tmTerminalExtendVo.getExtChar3());
			editEntity.setExtnumber1(tmTerminalExtendVo.getExtNumber1());
			editEntity.setExtnumber2(tmTerminalExtendVo.getExtNumber2());
			editEntity.setExtnumber3(tmTerminalExtendVo.getExtNumber3());
			editEntity.setExtnumber4(tmTerminalExtendVo.getExtNumber4());
			editEntity.setProvince(tmTerminalExtendVo.getProvince());
			editEntity.setCity(tmTerminalExtendVo.getCity());
			editEntity.setArea(tmTerminalExtendVo.getArea());
			editEntity.setLongitude(tmTerminalExtendVo.getExtChar5());
			editEntity.setLatitude(tmTerminalExtendVo.getExtChar6());
			editEntity.setAddress(tmTerminalExtendVo.getAddress());
//			editEntity.setBusinessId(tmTerminalExtendVo.getBusinessId());
			editEntity.setUserId(tmTerminalExtendVo.getUaccount());
			editEntity.setTown(tmTerminalExtendVo.getExtChar11());
			editEntity.setIsDrp(tmTerminalExtendVo.getIsDrp());
			editEntity.setPositionId(tmTerminalExtendVo.getPosId());
			this.saveOrUpdate(editEntity);
			//编辑时新的审批
			TmTerminalApproveEntity approveEntity=new TmTerminalApproveEntity();
			approveEntity.setApprovalContent("提交门店申请");//审批意见
			approveEntity.setApprovalType("0");//已提交
			approveEntity.setApprovalDate(new Date());
			approveEntity.setApprovalName(userInfoEntity.getRealName());
			approveEntity.setApprovalPosId(tmTerminalExtendVo.getPosId());
			approveEntity.setTerminalId(tmTerminalExtendVo.getId());
			approveEntity.setTerminalName(tmTerminalExtendVo.getTerminalName());
			approveEntity.setUserId(userInfoEntity.getUserId());
			approveEntity.setCreateDate(new Date());
			approveEntity.setOperateType("1");
			approveEntity.setCreateName(tmTerminalExtendVo.getCreateName());
			approveEntity.setApprovalLogId(editEntity.getId());
			this.save(approveEntity);
		}else{//直接修改
			terminal.setChannelType(tmTerminalExtendVo.getChannelType());
			terminal.setExtChar8(tmTerminalExtendVo.getExtChar8());
			terminal.setExtChar9(tmTerminalExtendVo.getExtChar9());
			terminal.setLinkman(tmTerminalExtendVo.getLinkman());
			terminal.setLinkmanPhone(tmTerminalExtendVo.getLinkmanPhone());
			terminal.setAddress(tmTerminalExtendVo.getAddress());
			terminal.setProvince(tmTerminalExtendVo.getProvince());
			terminal.setCity(tmTerminalExtendVo.getCity());
			terminal.setArea(tmTerminalExtendVo.getArea());
			terminal.setExtChar5(tmTerminalExtendVo.getExtChar5());
			terminal.setExtChar6(tmTerminalExtendVo.getExtChar6());
			terminal.setExtChar1(tmTerminalExtendVo.getExtChar1());
			terminal.setExtChar2(tmTerminalExtendVo.getExtChar2());
			terminal.setExtChar3(tmTerminalExtendVo.getExtChar3());
			terminal.setExtNumber1(tmTerminalExtendVo.getExtNumber1());
			terminal.setExtNumber2(tmTerminalExtendVo.getExtNumber2());
			terminal.setExtNumber3(tmTerminalExtendVo.getExtNumber3());
			terminal.setExtNumber4(tmTerminalExtendVo.getExtNumber4());
			terminal.setUpdateDate(new Date());
			terminal.setUpdateName(userInfoEntity.getRealName());
//			terminal.setExtChar10(tmTerminalExtendVo.getBusinessId());
			terminal.setExtChar11(tmTerminalExtendVo.getExtChar11());
		}
		
		this.saveOrUpdate(terminal);
		
	}

	@Override
	public List<TmCustomerVo> findTmCustTomerByAllPossible(TmCustomerSfaVo queryTmCustomerVo, Page page) {
		//获取职位id
		String posId = queryTmCustomerVo.getPositionId();
		if(StringUtil.isNotEmpty(posId)){
			//只取positionId,customerName,（android会有冗余数据）
			String sql = "select ORG_ID orgId FROM tm_position WHERE  id = ?";
			List<TmPositionVo> postList = super.findBySql(TmPositionVo.class,sql,posId);
			if(CollectionUtil.listNotEmptyNotSizeZero(postList)){
				queryTmCustomerVo.setOrgId(postList.get(0).getOrgId());
			}else{
				throw new BusinessException("读取职位失败!");
			}
		}
		return mdmApiDao.findCustomerBySearch(queryTmCustomerVo, page);
	}

	@Override
	public CheckTmTerminalExist checkTmTerminal(TmTerminalExtendVo tmTerminalExtendVo) {
		List<TmTerminalExtendVo> list = mdmApiDao.checkTmTerminal(tmTerminalExtendVo);
		Integer b=0;
		if(list.size()>0){
			b=1;
}
		CheckTmTerminalExist check =new CheckTmTerminalExist();
		check.setCheckExist(b);
		return check;
	}

	/**
	 * 获取泰博出行终端信息列表
	 *
	 * @param terminalCode
	 * @return
	 */
	@Override
	public boolean getVipByTerminalCode(String terminalCode) {
		String sql="SELECT a.* FROM TM_TERMINAL a  where a.terminal_code = '"+terminalCode+"'" ;
		List<TmTerminalVoNew> vo  =  this.findBySql(TmTerminalVoNew.class, sql);
		if (CollectionUtil.listEmpty(vo)) {
			throw new BusinessException("终端不存在");
		}
		TmTerminalVoNew tmTerminalVoNew = vo.get(0);
		String teboShopId = tmTerminalVoNew.getTeboShopId();
		if (StringUtil.isEmpty(teboShopId)) {
			return false;
		}
		TeboShopReq req = new TeboShopReq();
		req.setId(Long.valueOf(teboShopId));
		List<TeboShopResp> teboShopResps = TeboShopClientUtil.queryShops(req);
		if (CollectionUtil.listEmpty(teboShopResps)) {
			throw new BusinessException("终端不存在");
		}
		TeboShopResp teboShopResp = teboShopResps.get(0);
		if (Integer.valueOf(1).equals(teboShopResp.getVip())) {
			return true;
		}
		return false;
	}

}
