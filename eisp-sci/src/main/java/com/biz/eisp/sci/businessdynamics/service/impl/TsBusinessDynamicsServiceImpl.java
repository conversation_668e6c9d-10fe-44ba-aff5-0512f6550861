package com.biz.eisp.sci.businessdynamics.service.impl;

import com.biz.eisp.api.synccrms.vo.CrmsNoticeVo;
import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.ComboTree;
import com.biz.eisp.base.common.tag.bean.ComboTreeModel;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.ComboxTreeUtil;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.base.utils.DateUtils;
import com.biz.eisp.mdm.dict.util.DictUtil;
import com.biz.eisp.mdm.dict.vo.TmDictDataVo;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.mdm.org.vo.TmOrgVo;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.sci.api.mdm.service.MdmApiService;
import com.biz.eisp.sci.api.mdm.vo.UserInfoEntity;
import com.biz.eisp.sci.businessdynamics.dao.TsBusinessDynamicsDao;
import com.biz.eisp.sci.businessdynamics.entity.TsBusinessDynamicsEntity;
import com.biz.eisp.sci.businessdynamics.entity.TsBusinessDynamicsShareEntity;
import com.biz.eisp.sci.businessdynamics.service.TsBusinessDynamicsService;
import com.biz.eisp.sci.businessdynamics.vo.BusinessDynamicsAuthorityVo;
import com.biz.eisp.sci.businessdynamics.vo.BusinessDynamicsVo;
import com.biz.eisp.sci.businessdynamics.vo.TsBusinessDynamicsVo;
import com.biz.eisp.sci.notice.entity.TsNoticeRead;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Service("tsBusinessDynamicsService")
@Transactional
public class TsBusinessDynamicsServiceImpl extends BaseServiceImpl implements TsBusinessDynamicsService {
	
	@Autowired
	private TsBusinessDynamicsDao tsBusinessDynamicsDao;
	
	@Autowired
	private MdmApiService mdmService;
	
	
	public List<TsBusinessDynamicsVo> findTsBusinessDynamicsVoList(TsBusinessDynamicsVo tsBusinessDynamicsVo, String orgId, Page page){
		return tsBusinessDynamicsDao.findWebTsBusinessDynamicsVoList(tsBusinessDynamicsVo, page);
	}
	
	public TsBusinessDynamicsVo getTsBusinessDynamicsVo(String id){
		TsBusinessDynamicsVo tsBusinessDynamicsVo = new TsBusinessDynamicsVo();
		tsBusinessDynamicsVo.setId(id);
		
		List<TsBusinessDynamicsVo> lstTsBusinessDynamicsVo = tsBusinessDynamicsDao.findTsBusinessDynamicsVoList(tsBusinessDynamicsVo);
		if(CollectionUtil.listNotEmptyNotSizeZero(lstTsBusinessDynamicsVo)){
			return lstTsBusinessDynamicsVo.get(0);
		}
		return null;
	}
	@Override
	public void saveTsBusinessDynamics(TsBusinessDynamicsVo tsBusinessDynamicsVo){
		TmUserVo userVo = ResourceUtil.getSessionTmUserVo();
		tsBusinessDynamicsVo.setPushDate(DateUtils.getDataString(DateUtils.datetimeFormat));
		TmOrgEntity org=ResourceUtil.getCurrOrg();
		tsBusinessDynamicsVo.setOrgCode(org.getOrgCode());
		tsBusinessDynamicsVo.setOrgName(org.getOrgName());
		tsBusinessDynamicsVo.setCreateName(userVo.getFullName());
		tsBusinessDynamicsVo.setCreateDate(new Date());
		TsBusinessDynamicsEntity tsBusinessDynamicsEntity = new TsBusinessDynamicsEntity();
		if(tsBusinessDynamicsVo.getId()==null||tsBusinessDynamicsVo.getId().equals("")){
			tsBusinessDynamicsVo.setId(null);
		}else {
			String sql="select* from TS_BUSINESS_DYNAMICS where id='"+tsBusinessDynamicsVo.getId()+"'";
			TsBusinessDynamicsVo updateVo=this.findBySql(TsBusinessDynamicsVo.class,sql).get(0);
			tsBusinessDynamicsVo.setReadNum(updateVo.getReadNum());
			tsBusinessDynamicsVo.setShareNum(updateVo.getShareNum());
		}
		BeanUtils.copyProperties(tsBusinessDynamicsVo,tsBusinessDynamicsEntity);
		this.saveOrUpdate(tsBusinessDynamicsEntity);
		tsBusinessDynamicsVo.setId(tsBusinessDynamicsEntity.getId());
	}




    @SuppressWarnings("deprecation")
//	@Override
	public List<BusinessDynamicsVo> findBusinessDynamicsList_1(TsBusinessDynamicsVo vo, Page page) {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, -1);
		vo.setEndDate(cal.getTime());
		vo.setStartDate(new Date());

		final List<BusinessDynamicsAuthorityVo> authorityVoList = tsBusinessDynamicsDao.findBusinessDynamicsListForAuthority(vo);
		final UserInfoEntity userVo = mdmService.getUserInfoEntityByPosId(vo.getPosId());
		final List<TmOrgVo> orgList = mdmService.findSuperiorOrgListByPosId(vo.getPosId(), true);
		final UserInfoEntity userTmp = mdmService.findUserRoleByPosId(vo.getPosId(),DateUtils.dateNow2Str());
		final String roleCode = userTmp!=null?userTmp.getRoleCode():null;
		HashSet<String> includeIds=new HashSet<String>();
		for (BusinessDynamicsAuthorityVo authorityVo : authorityVoList) {
			// 验证是否在发布范围内
			final String org = authorityVo.getReceivingOrgCode();
			if (StringUtils.isNotBlank(org)) {
				if (containsOrgId(orgList, org)) {
					includeIds.add(authorityVo.getId());
				}
			}else{//为空表示包含
				includeIds.add(authorityVo.getId());
			}
			// 验证是否在接受角色内
			final String role = authorityVo.getReceivingRoleCode();
			if (StringUtil.isEmpty(role)) {
				continue;
			}
			if (StringUtil.isEmpty(role)) {
				includeIds.add(authorityVo.getId());
			}else {
				if (Arrays.asList(role.split(",")).contains(roleCode)) {
					includeIds.add(authorityVo.getId());
				} else {
					includeIds.remove(authorityVo.getId());
				}
			}
		}
		vo.setIncludeIds(addIncludeId(includeIds));
		List<BusinessDynamicsVo> resultList = new ArrayList<>();
		if(StringUtils.isNotBlank(vo.getIncludeIds())){
			 resultList = tsBusinessDynamicsDao.findBusinessDynamicsList(vo, page);//可读公告(默认都未读)
		}
		
//		String include="";
//		for(CrmsBusinessDynamicsVo BusinessDynamics : resultList){
//			include += "'"+BusinessDynamics.getId()+"',";
//		}
//		include+="''";
		HashSet<String> includeIds2=new HashSet<String>();
		
		for (BusinessDynamicsVo BusinessDynamicsVo : resultList) {
			includeIds2.add(BusinessDynamicsVo.getId());
		}
		vo.setIncludeIds(addIncludeId(includeIds2));
		vo.setPositionCode(userVo.getPosCode());
		final List<BusinessDynamicsVo> resultredList = tsBusinessDynamicsDao.findreadBusinessDynamicsList(vo);//可读里已读公告
		
		
		for(BusinessDynamicsVo BusinessDynamics : resultList){//已读公告设置
			boolean notRepeat = false;
			for(BusinessDynamicsVo  reBusinessDynamics : resultredList){
				if(BusinessDynamics.getId().equals(reBusinessDynamics.getId())){
					notRepeat=true;
				}
			}
			if(notRepeat){
				BusinessDynamics.setRead("1");
			}
		}
		convertRead(resultList);
//		convertBusinessDynamicsType(resultList);
		return resultList;
	}

	private boolean checkOrgInclude(String orgId ){
		boolean b=false;
		ComboTreeModel comboTreeModel = new ComboTreeModel("id", "orgName", "tmOrgList");
		List<ComboTree> comboTrees = ComboxTreeUtil.currMap.get(ComboxTreeUtil.comboxTag);
		TmOrgVo org=new TmOrgVo();
		org.setId(orgId);
		List<ComboTree>  tmps=ComboxTreeUtil.getComboxByObj(comboTrees, comboTreeModel, org);
		return b;
	}
	/**
	 * 包含id
	 * @param includeIds
	 * @return
	 */
	private String addIncludeId(HashSet<String> includeIds) {
		StringBuilder includeId = new StringBuilder();
		int i=0;
		for (String st:includeIds) {
			if(i!=0){
				includeId.append(",");
			}
			includeId.append("'");
			includeId.append(st);
			includeId.append("'");
			i++;
		}
		return includeId.toString();
	}

	/** 
	 * 是否包含所在部门
	 * <AUTHOR> Wang
	 * @param org
	 */ 
	@SuppressWarnings("deprecation")
	private boolean containsOrgId(List<TmOrgVo> orgList, String org) {
		if (org == null || CollectionUtils.isEmpty(orgList)) {
			return true; 
		}
		
		for (TmOrgVo userOrg : orgList) {
			 if (Arrays.asList(org.split(",")).contains(userOrg.getId())) {
				return true; 
			 }
		}
		return false;
	}

	/** 
	 * 转换read
	 * <AUTHOR> Wang
	 * @param voList
	 */ 
	private void convertRead(final List<? extends BusinessDynamicsVo> voList) {
		for (BusinessDynamicsVo BusinessDynamicsVo : voList) {
			final String dictValue = DictUtil.getDictDataValueByCode("BusinessDynamics_read", BusinessDynamicsVo.getRead());
			BusinessDynamicsVo.setRead(dictValue);
		}
	}
	
	/** 
	 * 转换公告类型
	 * <AUTHOR> Wang
	 * @param voList
	 */ 
	private void convertBusinessDynamicsType(final List<? extends BusinessDynamicsVo> voList) {
		for (BusinessDynamicsVo BusinessDynamicsVo : voList) {
			final String dictValue = DictUtil.getDictDataValueByCode("BusinessDynamics_type", BusinessDynamicsVo.getBusType());
			BusinessDynamicsVo.setBusType(dictValue);
		}
	}

	@Override
	public void checkTheDataHasReaded(List<CrmsNoticeVo> BusinessDynamicsList, CrmsNoticeVo vo) {
		//为空不执行
		if (!CollectionUtil.listNotEmptyNotSizeZero(BusinessDynamicsList)) {
			return ;
		}
		String userName = vo.getCreateName();
		if(!StringUtil.isNotEmpty(userName)){
			throw new BusinessException("登录在账号不能为空");
		}
		String BusinessDynamicsIds = OwnBeanUtils.changeListEntityOnePropertyToString(BusinessDynamicsList,"id",null,true);
		//获取已读数据
		List<BusinessDynamicsVo> resultredList = tsBusinessDynamicsDao.findreadBusinessDynamicsReadList(BusinessDynamicsIds,userName);//可读里已读公告

		//封装字典
		Map<String,TmDictDataVo> dictDataVoMap = OwnBeanUtils.encapsulateDataByMapKeyToKayMapValueToEntity(DictUtil.allDictData.get("BusinessDynamics_read"),true,"dictCode");

		Map<String, BusinessDynamicsVo> stringTMap = new HashMap<String, BusinessDynamicsVo>();
		if (CollectionUtil.listNotEmptyNotSizeZero(resultredList)) {
			stringTMap = OwnBeanUtils.encapsulateDataByMapKeyToKayMapValueToEntity(resultredList,true,"id");
		}
		//循环遍历检查
		for (CrmsNoticeVo crmsBusinessDynamicsVo : BusinessDynamicsList) {
			BusinessDynamicsVo BusinessDynamicsVo = stringTMap.get(crmsBusinessDynamicsVo.getId());
			String readStr = crmsBusinessDynamicsVo.getRead();
			if(BusinessDynamicsVo != null){
				readStr = BusinessDynamicsVo.getRead();
			}
			//获取字典
			TmDictDataVo dictDataVo = dictDataVoMap.get(readStr);
			if(dictDataVo == null ){
				throw new  BusinessException("字典转换失败");
			}
			crmsBusinessDynamicsVo.setRead(dictDataVo.getDictValue());
		}
	}

	@Override
	public boolean signRead(TsBusinessDynamicsVo vo) {
		Assert.notNull(vo.getId());
		UserInfoEntity userVo = mdmService.getUserInfoEntityByPosId(vo.getPosId());
		
		if (hasRead(vo.getId(), userVo.getPosCode())) {
			return false;
		}

		TsNoticeRead entity = new TsNoticeRead();
		entity.setNoticeId(vo.getId());
		entity.setCreateDate(new Date());
		entity.setFullName(userVo.getRealName());
		entity.setUserName(userVo.getUserName());
		entity.setPositionCode(userVo.getPosCode());
		entity.setPositionName(userVo.getPosTypeName());
		save(entity);

		String sql="UPDATE TS_BUSINESS_DYNAMICS set READ_NUM=READ_NUM+1 where id='"+vo.getId()+"'";
		this.executeSql(sql);
		return true;
	}

	/** 
	 *  是否已读
	 * <AUTHOR> Wang
	 */ 
	private boolean hasRead(String BusinessDynamicsId, String posCode) {
//		return getCountForJdbcParam(
//				"SELECT COUNT(1) FROM ts_BusinessDynamics_read WHERE BusinessDynamics_id = ? AND POSITION_CODE = ?", BusinessDynamicsId, posCode) != 0;
		String sql="select r.id from ts_notice_read r  where r.position_code='"+posCode+"' and r.notice_id='"+BusinessDynamicsId+"'";
		List<TsBusinessDynamicsVo> readList=this.findBySql(TsBusinessDynamicsVo.class,sql);
		if(readList!=null&&readList.size()!=0){
			return true;
		}
		return false;
	}
	
	@Override
	public List<ComboTree> findOrgTree(TmOrgVo orgVo, ComboTree comboTree,HttpServletRequest request){
		//找到当前组织的上级是否是事业部
		String sql2 = " select t.* from (select t1.* from tm_org t1 start with t1.id = ? connect by id = prior parent_id)t "+
					 " where t.org_type = ? ";
		
		List<TmOrgVo> xsb = this.findBySql(TmOrgVo.class, sql2, ResourceUtil.getCurrOrg().getId(),"XSB");
		List<TmOrgVo> findList = this.findBySql(TmOrgVo.class, sql2, ResourceUtil.getCurrOrg().getId(),"SYB");

		//返回集合
		List<TmOrgEntity> orgList = new ArrayList<>();
		
		Criterion cr1 = null;
		String selfId = request.getParameter("selfId");
		if(StringUtil.isNotEmpty(selfId)){
			orgList = findByProperty(TmOrgEntity.class,"id",selfId);
		}else if(StringUtil.isNotEmpty(orgVo.getOrgId())){
			cr1 = Restrictions.ne("id", orgVo.getOrgId());
		}
		Criterion cr4 = Restrictions.eq("enableStatus", Globals.ZERO);
		if(StringUtil.isNotEmpty(comboTree.getId())) {
			Criterion cr2 = Restrictions.eq("tmOrg.id", comboTree.getId());
			if(StringUtil.isNotEmpty(cr1)){
				orgList.addAll(findByCriteria(TmOrgEntity.class, cr1, cr2, cr4));
			}else{
				orgList.addAll(findByCriteria(TmOrgEntity.class, cr2, cr4));
			}
		} else {
			Criterion cr3 = null;
			if(StringUtil.isNotEmpty(orgVo.getPid())){
				cr3 = Restrictions.eq("id", orgVo.getPid());
			}else{
				if(xsb.size() > 0){//找到了上级部门，含有销售部
					cr3 = Restrictions.eq("id",xsb.get(0).getId());
				}else{
					if(findList.size() > 0 ){
						cr3 = Restrictions.eq("id",findList.get(0).getId());
					}else{
						cr3 = Restrictions.isNull("tmOrg.id");
					}
				}
			}
			if(StringUtil.isNotEmpty(cr1)){
				orgList.addAll(findByCriteria(TmOrgEntity.class, cr1, cr3, cr4));
			}else{
				orgList.addAll(findByCriteria(TmOrgEntity.class, cr3, cr4));
			}
		}
		
		ComboTreeModel comboTreeModel = new ComboTreeModel("id", "orgName", "tmOrgList");
		List<ComboTree> comboTrees = this.comboTree(orgList, comboTreeModel, null, false);
		return comboTrees;
	}

	@Override
	public List<TsBusinessDynamicsVo> findBusinessDynamicsList(TsBusinessDynamicsVo vo, Page page) {
        return tsBusinessDynamicsDao.findTsBusinessDynamicsVoList(vo,page);
	}


	@Override
    public List<TsBusinessDynamicsVo> findTsBusinessDynamicsList(TsBusinessDynamicsVo vo, Page page) {
		List<TsBusinessDynamicsVo> tbAll=tsBusinessDynamicsDao.findTsBusinessDynamicsVoList(vo,page);
		List<TsBusinessDynamicsVo> bdList=new ArrayList<TsBusinessDynamicsVo>();
		TmUserVo tmUservo=ResourceUtil.getSessionTmUserVo();
		String currentPositionCode=ResourceUtil.getCurrPosition().getPositionCode();
		for(int i=0;i<tbAll.size();i++){
			TsBusinessDynamicsVo subVo=tbAll.get(i);
			List<TmUserVo> scopeList=this.findBdUserInfo(subVo,null);
			for(int k=0;k<scopeList.size();k++){
				if(currentPositionCode.equals(scopeList.get(k).getPositionCode())){
					bdList.add(tbAll.get(i));
					break;
				}
			}

		}

//        return tsBusinessDynamicsDao.findTsBusinessDynamicsVoList(vo,page);
		return bdList;
    }

	@Override
	public List<TmUserVo> findBdUserInfo(TsBusinessDynamicsVo vo, Page page) {
		TsBusinessDynamicsEntity entity=new TsBusinessDynamicsEntity();

		if(StringUtil.isNotEmpty(vo.getId())){
			entity=this.get(TsBusinessDynamicsEntity.class,vo.getId());
		}else {
			throw new BusinessException("信息错误");
		}
		if(StringUtil.isNotEmpty(entity.getReceivingOrgCode())){
			String[] reciveOrgCode=entity.getReceivingOrgCode().split(",");
			String orgIds="(";
			for(int j=0;j<reciveOrgCode.length;j++){
				String orgId="'"+reciveOrgCode[j]+"',";
				orgIds+=orgId;
			}
			orgIds=orgIds.substring(0,orgIds.length()-1)+")";
			vo.setReceivingOrgCode(orgIds);
		}else {
			vo.setReceivingOrgCode(null);
		}

		if(StringUtil.isNotEmpty(entity.getReceivingRoleCode())){
			String[]  roleCOdeArr=entity.getReceivingRoleCode().split(",");
			String roleCOdes="(";
			for(int j=0;j<roleCOdeArr.length;j++){
				String role="'"+roleCOdeArr[j]+"',";
				roleCOdes+=role;

			}
			roleCOdes=roleCOdes.substring(0,roleCOdes.length()-1)+")";
			vo.setReceivingRoleCode(roleCOdes);
		}else {
			vo.setReceivingRoleCode(null);
		}
		return tsBusinessDynamicsDao.findBdUserInfo(vo,page);
	}


	@Override
	public boolean signShare(TsBusinessDynamicsVo vo) {
		Assert.notNull(vo.getId());
		UserInfoEntity userVo = mdmService.getUserInfoEntityByPosId(vo.getPosId());


		TsBusinessDynamicsShareEntity entity = new TsBusinessDynamicsShareEntity();
		entity.setBusinessDynamicsId(vo.getId());
		entity.setCreateDate(new Date());
		entity.setFullName(userVo.getRealName());
		entity.setUserName(userVo.getUserName());
		entity.setPositionCode(userVo.getPosCode());
		entity.setPositionName(userVo.getPosTypeName());
		save(entity);
		String sql="UPDATE TS_BUSINESS_DYNAMICS set SHARE_NUM=nvl(SHARE_NUM,0)+1 WHERE id='"+vo.getId()+"'";
		this.executeSql(sql);
		return true;
	}

	private String getBdIds(TsBusinessDynamicsVo vo){
		final List<BusinessDynamicsAuthorityVo> authorityVoList = tsBusinessDynamicsDao.findBusinessDynamicsListForAuthority(vo);
		final UserInfoEntity userVo = mdmService.getUserInfoEntityByPosId(vo.getPosId());
		final List<TmOrgVo> orgList = mdmService.findSuperiorOrgListByPosId(vo.getPosId(), true);
		final UserInfoEntity userTmp = mdmService.findUserRoleByPosId(vo.getPosId(),DateUtils.dateNow2Str());
		final String roleCode = userTmp!=null?userTmp.getRoleCode():null;
		HashSet<String> includeIds=new HashSet<String>();
		for (BusinessDynamicsAuthorityVo authorityVo : authorityVoList) {
			// 验证是否在发布范围内
			final String org = authorityVo.getReceivingOrgCode();
			if (StringUtils.isNotBlank(org)) {
				if (containsOrgId(orgList, org)) {
					includeIds.add(authorityVo.getId());
				}
			}else{//为空表示包含
				includeIds.add(authorityVo.getId());
			}
			// 验证是否在接受角色内
			final String role = authorityVo.getReceivingRoleCode();
			if (StringUtil.isEmpty(role)) {
				continue;
			}
			if (StringUtil.isEmpty(role)) {
				includeIds.add(authorityVo.getId());
			}else {
				if (Arrays.asList(role.split(",")).contains(roleCode)) {
					includeIds.add(authorityVo.getId());
				} else {
					includeIds.remove(authorityVo.getId());
				}
			}
		}
		return includeIds.toString();
	}

}