package com.biz.eisp.sci.actApply.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.biz.eisp.activiti.runtime.service.TaTaskService;
import com.biz.eisp.activiti.runtime.vo.OperationVo;
import com.biz.eisp.api.act.addressapply.entity.TsActApplyDetailEntity;
import com.biz.eisp.api.act.addressapply.entity.TsActApplyEntity;
import com.biz.eisp.api.act.addressapply.vo.TsActApplyDetailVo;
import com.biz.eisp.api.act.addressapply.vo.TsActApplyVo;
import com.biz.eisp.api.act.addressapply.vo.TsWaitJXSAuditVo;
import com.biz.eisp.api.common.service.TmCommonService;
import com.biz.eisp.api.common.vo.TaCustomProcessVo;
import com.biz.eisp.api.mdm.service.TmCommonMdmService;
import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.api.sfa.door.service.ApiTtDoorApplyExcuteWorkFlowService;
import com.biz.eisp.api.tpm.service.ApiBalanceAllService;
import com.biz.eisp.api.util.BCacheUtil;
import com.biz.eisp.api.util.Globals;
import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.generatednum.num.util.TbNumRuleProvider;
import com.biz.eisp.mdm.customer.vo.TmCustomerVo;
import com.biz.eisp.mdm.dict.util.DictUtil;
import com.biz.eisp.mdm.terminal.vo.TmTerminalVo;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.sci.actApply.dao.TsActAddressAndExecuteDao;
import com.biz.eisp.sci.actApply.enums.AdTypeEnum;
import com.biz.eisp.sci.actApply.service.TsActAddressAndExecuteService;
import com.biz.eisp.sci.actApply.service.TsActConfigService;
import com.biz.eisp.sci.actApply.vo.TmTerminalVoNew;
import com.biz.eisp.sci.actApply.vo.TtHiSuspiciousOpinionsVo;
import com.biz.eisp.sci.api.mdm.entity.TmTerminalForSciEntity;
import com.biz.eisp.sci.api.mdm.vo.AdTypeCountVO;
import com.biz.eisp.sci.api.mdm.vo.TmTerminalExtendVo;
import com.biz.eisp.sci.picture.service.TsPictureService;
import com.biz.eisp.sci.tebo.TeboShopClientUtil;
import com.biz.eisp.sci.tebo.TeboShopReq;
import com.biz.eisp.sci.tebo.TeboShopResp;
import com.biz.eisp.sci.util.AddressParser;
import com.biz.eisp.sci.util.SfaGlobals;
import com.biz.eisp.sci.workflow.TsWorkFlowService;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by clare on 2018/1/8.
 */
@Service("tsActApplyAddRessSelectService")
@Transactional
public class TsActAddressAndExecuteServiceImpl extends BaseServiceImpl implements TsActAddressAndExecuteService {

    /* 日志对象 */
    private static final Logger LOG = LoggerFactory.getLogger(TsActAddressAndExecuteServiceImpl.class);

    private static final String REDIS_ONLY_CODE = "checkTheAdvGetCodeOnly";
    @Autowired
    private ApiTtDoorApplyExcuteWorkFlowService apiTtDoorApplyExcuteWorkFlowService;
    @Autowired
    private TmCommonService tmCommonService;
    @Autowired
    private TmCommonMdmService tmCommonMdmService;
    @Autowired
    private TsActAddressAndExecuteDao tsActAddressAndExecuteDao;
    @Autowired
    private TsWorkFlowService tsWorkFlowService;
    @Autowired
    private TsPictureService tsPictureService;
    @Autowired
    private TaTaskService taTaskService;
    @Autowired
    private TbNumRuleProvider tbNumRuleProvider;
    @Autowired
    private TsActConfigService tsActConfigService;
    @Autowired
    private ApiBalanceAllService apiBalanceAllService;
    @Override
    public List<TsActApplyVo> findMyworkflowList(TsActApplyVo tsActApplyVo) {
        String actType="";
        if (tsActApplyVo.getActType().equals(Globals.acttype.hw.getValue())){
            tsActApplyVo.setActType("BPM010");//户外广告申请执行流程
            actType=Globals.acttype.hw.getValue();
        }else{
            tsActApplyVo.setActType("BPM006");//门头广告执行流程
            actType=Globals.acttype.mt.getValue();
        }
        setBpmStatus(tsActApplyVo);
        List<TsActApplyVo> list=tsActAddressAndExecuteDao.findMyworkflowList(tsActApplyVo);
        //加载图片
        getPictures(list,actType);
        return list;
    }

    /**
     * 设置bpmstatus值
     * @param vo
     */
    private void setBpmStatus(TsActApplyVo vo){
        String bpmStatusStr = OwnBeanUtils.changeListEntityMorePropertyToString(DictUtil.allDictData.get(com.biz.eisp.api.util.Globals.DIC_BPMSTATUS),"，","dictCode","dictValue");
        vo.setBpmStatusStr(bpmStatusStr.replace("，",","));
    }

    /**
     * //加载图片
     * @param list
     * @param actType
     */
    private void getPictures( List<TsActApplyVo> list,String actType){
        for (int i = 0; i <list.size() ; i++) {
            List<TsPictureVo> pics= tsPictureService.getAllPictureVo(list.get(i).getBusinessId(),null,true);
            list.get(i).setPicVoList(pics);
            //白池标  2018.09.14  开始 ######################################################################################################
            list.get(i).setIsUploaded(1);
            if(list.get(i).getUplod1st().equals("0")){//如果存在选址照片小视频没上传，不管在哪个节点都要亮色显示
                list.get(i).setIsUploaded(0);
            } else {
                if(list.get(i).getActivityName() != null){
                    if(list.get(i).getBpmStatus() == 3 || list.get(i).getActivityName().equals("7-市场部后审")){//审批通过  或者在市场部后审节点
                        if(list.get(i).getUplod2ed() == null || list.get(i).getUplod2ed().equals("0")){
                            list.get(i).setIsUploaded(0);
                        }
                    }                }
            }
            //白池标  2018.09.14  结束 ######################################################################################################
            if (Globals.acttype.mt.getValue().equals(actType)) { //门头需要额外获取数据
                List<TsActApplyDetailVo> detailVos = tsActAddressAndExecuteDao.findMtDetails(list.get(i).getId());
                if (CollectionUtil.listNotEmptyNotSizeZero(detailVos)){
                    for (int j = 0; j < detailVos.size(); j++) {
                        List<TsPictureVo> pics_= tsPictureService.getAllPictureVo(detailVos.get(j).getId(),null,true);
                       // List<TsPictureVo> pics_1= tsPictureService.getAllPictureVo(list.get(i).getBusinessId(),null,true);
                       // pics_.addAll(pics_1);
                        detailVos.get(j).setPicVoList(pics_);
                    }
                    list.get(i).setDetailVos(detailVos);
                }
            }
        }

    }
    @Override
    public void saveTsActApplyAddressSelectOutDoor(TsActApplyVo tsActApplyVo) {
        //设置基础参数
        enBaseDataIntoTheTsActApply(tsActApplyVo);
        //数据存储
        saveTsActApplyAddressSelect(tsActApplyVo);

        //保存图片信息
        if (tsActApplyVo.getActType().equals(com.biz.eisp.api.util.Globals.acttype.hw.getValue())) {
            //户外
            if (CollectionUtil.listNotEmptyNotSizeZero(tsActApplyVo.getPicVoList()) || StringUtils.isNotBlank(tsActApplyVo.getPicVoListJson())) {
                tsPictureService.saveTsPictureList(tsActApplyVo.getPicVoList(), tsActApplyVo.getPicVoListJson());
            }
        }

    }
    @Override
    public void saveWorkFlow(TsActApplyVo tsActApplyVo){
        //获取最新流程 提交对象
        TaCustomProcessVo processVo=tsWorkFlowService.getTaCustomProcessVo(tsActApplyVo);
        //提交工作流
        AjaxJson j=tsWorkFlowService.submitTsActApplyAddressSelectBpm(processVo);
        if (!j.isSuccess()){
            throw new BusinessException(j.getMsg());
        }
    }
    /**
     * 设置基础参数
     * @param tsActApplyVo
     */
    private void enBaseDataIntoTheTsActApply(TsActApplyVo tsActApplyVo) {
        if(!StringUtil.isNotEmpty(tsActApplyVo.getActType())){
           throw new BusinessException("活动类型未设置不能提交！");
        }
    }

    /**
     * 数据存储
     * @param tsActApplyVo
     */
    private void saveTsActApplyAddressSelect(TsActApplyVo tsActApplyVo){
        //检查数据正确性
        checkTheDataIsOk(tsActApplyVo);
        //封装数据并保存数据
        enDataAndSaveOrUpdateData(tsActApplyVo);
    }
    @Override
    public TmTerminalVo getTerminalByCode(String tcode) {//在申请中增加真实的店铺联系人信息
        String sql="SELECT a.* FROM TM_TERMINAL a  where a.terminal_code = '"+tcode+"'" ;
        List<TmTerminalVo> vo  =  this.findBySql(TmTerminalVo.class, sql);
        return vo.get(0) ;
    }

    @Override
    public TmTerminalVoNew getTerminalByTeboShopId(String teboShopId, String phone) {
        String sql="SELECT a.* FROM TM_TERMINAL a  where a.tebo_shop_id = '"+teboShopId+"'" ;
        List<TmTerminalVoNew> vo  =  this.findBySql(TmTerminalVoNew.class, sql);
        TmTerminalVoNew terminalVo = null;

        TeboShopReq req = new TeboShopReq();
        req.setId(Long.valueOf(teboShopId));
        List<TeboShopResp> teboShopResps = TeboShopClientUtil.queryShops(req);
        if (teboShopResps == null || teboShopResps.isEmpty()) {
            throw new BusinessException("未找到对应的店铺信息，请检查店铺ID是否正确");
        }
        TeboShopResp teboShopResp = teboShopResps.get(0);
        if (vo == null || vo.isEmpty()) {
            String sqlPhone="SELECT a.* FROM TM_TERMINAL a  where a.LINKMAN_PHONE = '"+phone+"' AND a.TEBO_SHOP_ID IS NULL and (terminal_Type != 'car_icon' or terminal_Type is null) and ssjxs is null " ;
            vo  =  this.findBySql(TmTerminalVoNew.class, sqlPhone);
            if (vo == null || vo.isEmpty()) {
                TmTerminalForSciEntity tmTerminalForSciEntity = new TmTerminalForSciEntity();
                TbNumRuleProvider tbNumRuleProvider=
                        (TbNumRuleProvider) ApplicationContextUtils.getContext().getBean("tbNumRuleProvider");
                String terminalCode=tbNumRuleProvider.getMaxNum(com.biz.eisp.base.common.constant.Globals.AuthTerminal);
                tmTerminalForSciEntity.setTerminalCode(terminalCode);
                tmTerminalForSciEntity.setExtChar7("0");
                tmTerminalForSciEntity.setTerminalType("03");
                tmTerminalForSciEntity.setTerminalName(StringUtil.isBlank(teboShopResp.getShopName())?teboShopResp.getPhoneNumber():teboShopResp.getShopName());
                tmTerminalForSciEntity.setAddress(teboShopResp.getAddress());
                tmTerminalForSciEntity.setLinkman(StringUtil.isBlank(teboShopResp.getShopBossName())?teboShopResp.getPhoneNumber():teboShopResp.getShopBossName());
                tmTerminalForSciEntity.setLinkmanPhone(teboShopResp.getPhoneNumber());
                tmTerminalForSciEntity.setTeboShopId(teboShopId);
                this.save(tmTerminalForSciEntity);
                terminalVo = new TmTerminalVoNew();
                terminalVo.setTerminalCode(tmTerminalForSciEntity.getTerminalCode());
                terminalVo.setLinkmanPhone(tmTerminalForSciEntity.getLinkmanPhone());
            }else {
                terminalVo = vo.get(0);
                String shopName = StringUtil.isBlank(teboShopResp.getShopName())?teboShopResp.getPhoneNumber():teboShopResp.getShopName();
                String shopBossName = StringUtil.isBlank(teboShopResp.getShopBossName())?teboShopResp.getPhoneNumber():teboShopResp.getShopBossName();
                String updateSql="UPDATE TM_TERMINAL SET TEBO_SHOP_ID = '"+teboShopId+"', TERMINAL_NAME = '"+shopName+"', LINKMAN = '"+shopBossName+"', ADDRESS = '"+teboShopResp.getAddress()+"' where ID = '"+terminalVo.getId()+"'";
                this.executeSql(updateSql);
                terminalVo.setAddress(teboShopResp.getAddress());
                terminalVo.setLinkman(shopBossName);
                terminalVo.setLinkmanPhone(teboShopResp.getPhoneNumber());
            }
        }else {
            terminalVo = vo.get(0);
            String shopName = StringUtil.isBlank(teboShopResp.getShopName())?teboShopResp.getPhoneNumber():teboShopResp.getShopName();
            String shopBossName = StringUtil.isBlank(teboShopResp.getShopBossName())?teboShopResp.getPhoneNumber():teboShopResp.getShopBossName();
            String updateSql="UPDATE TM_TERMINAL SET TERMINAL_NAME = '"+shopName+"', LINKMAN = '"+shopBossName+"', ADDRESS = '"+teboShopResp.getAddress()+"' where ID = '"+terminalVo.getId()+"'";
            this.executeSql(updateSql);
            terminalVo.setAddress(teboShopResp.getAddress());
            terminalVo.setLinkman(shopBossName);
            terminalVo.setLinkmanPhone(teboShopResp.getPhoneNumber());
        }
        return terminalVo ;
    }

    private void updateTerminalInfo(String teboShopId, TmTerminalVoNew terminalVo){
        TeboShopReq req = new TeboShopReq();
        req.setId(Long.valueOf(teboShopId));
        List<TeboShopResp> teboShopResps = TeboShopClientUtil.queryShops(req);
        if (teboShopResps == null || teboShopResps.isEmpty()) {
            throw new BusinessException("未找到对应的店铺信息，请检查店铺ID是否正确");
        }
        TeboShopResp teboShopResp = teboShopResps.get(0);
        String shopName = StringUtil.isBlank(teboShopResp.getShopName())?teboShopResp.getPhoneNumber():teboShopResp.getShopName();
        String shopBossName = StringUtil.isBlank(teboShopResp.getShopBossName())?teboShopResp.getPhoneNumber():teboShopResp.getShopBossName();
        String updateSql="UPDATE TM_TERMINAL SET TERMINAL_NAME = '"+shopName+"',SET LINKMAN = '"+shopBossName+"',SET ADDRESS = '"+teboShopResp.getAddress()+"' where ID = '"+terminalVo.getId()+"'";
        this.executeSql(updateSql);
    }

    /**
     * 封装数据并保存数据--及提交流程
     * @param tsActApplyVo
     */
    private void enDataAndSaveOrUpdateData(TsActApplyVo tsActApplyVo) {

        TsActApplyEntity tsActApplyEntity=new TsActApplyEntity();

        try {
            MyBeanUtils.copyBeanNotNull2Bean(tsActApplyVo,tsActApplyEntity);
            tsActApplyEntity.setActCode(tbNumRuleProvider.getMaxNum(Globals.TS_ACT_APPLY));
            tsActApplyEntity.setStatus(Globals.datastatus.add.getValue());
            tsActApplyEntity.setCreateBy(tsActApplyVo.getUserName());
            TmTerminalVoNew vo = getTerminalByTeboShopId(tsActApplyVo.getTeboShopId(), tsActApplyVo.getPhone());
            tsActApplyEntity.setrLinkPhone(vo.getLinkmanPhone());//2019-07-13   增加冗余赋值
            tsActApplyEntity.setTerminalCode(vo.getTerminalCode());
        } catch (Exception e) {
            throw new BusinessException("转换数据出错!");
        }
        this.saveOrUpdate(tsActApplyEntity);
        tsActApplyVo.setId(tsActApplyEntity.getId());
        if (tsActApplyVo.getActType().equals(Globals.acttype.mt.getValue())) {
            //门头
            if (StringUtils.isNotBlank(tsActApplyVo.getDetailJson()) || CollectionUtil.listNotEmptyNotSizeZero(tsActApplyVo.getDetailVos())) {
                //先找到提交人对应的经销商prefix  否则 提交不成功
                TmUserVo tmUserVo =new TmUserVo();
                tmUserVo.setUserName(tsActApplyVo.getUserName());
                tmUserVo.setId(tsActApplyVo.getUserId());
                TmCustomerVo customerVo=tmCommonMdmService.getTmCustomerVoByZzhUser(tmUserVo);
                String prefix="";
                if (customerVo==null)
                {
                    throw new BusinessException("对应经销商未找到!");
                }else{
                    if (StringUtils.isNotBlank(customerVo.getExtChar17())){
                        prefix=customerVo.getExtChar17();
                    }else{
                        throw new BusinessException("对应经销商的规则抬头prefix!");
                    }
                    tsActApplyEntity.setCustomerCode(customerVo.getCustomerCode());
                }
                //ts_act_apply  表增加remarks与adcodes
                StringBuffer remarks = new StringBuffer();
                StringBuffer adcodes = new StringBuffer();
                //解析并返回明细集合
                List<TsActApplyDetailVo> detailVos = returnTsActApplyDetailVoListn(tsActApplyVo);
                for (int i = 0; i < detailVos.size(); i++) {  //ts_act_apply_detail
                    TsActApplyDetailVo vo=detailVos.get(i);
                    TsActApplyDetailEntity detailEntity=new TsActApplyDetailEntity();
                    detailEntity.setHeadId(tsActApplyEntity.getId());
                    detailEntity.setAdCode(tsActConfigService.getAdvCodeForParse(prefix,tsActApplyVo.getAccountCode()));//生成新的广告编码
                    adcodes.append(detailEntity.getAdCode() + " ");//******** bcb
                    //detailEntity.setAdCode(getTheAdvCode(prefix,tsActApplyVo.getAccountCode()));//生成新的广告编码
                    detailEntity.setRemarks(vo.getRemarks() + " ");
                    remarks.append(vo.getRemarks());//******** bcb
                    detailEntity.setStatus(Globals.datastatus.add.getValue());
                    detailEntity.setPlace(vo.getPlace());
                    detailEntity.setAdType(vo.getAdType());
                    this.saveOrUpdate(detailEntity);
                    vo.setId(detailEntity.getId());
                    List<TsPictureVo> picVoList=vo.getPicVoList();
                    if (CollectionUtil.listNotEmptyNotSizeZero(picVoList)) {
                        for (int j = 0; j <picVoList.size() ; j++) {
                            picVoList.get(j).setBusinessId(detailEntity.getId());
                        }
                        //保存门头图片  图片的businnesId是 广告单号数据的id
                        tsPictureService.saveTsPictureList(picVoList, null);
                    }
                }
                //********  增加更新
                this.executeSql("UPDATE ts_act_apply SET ALTERNATIVES = ? ,DADCODES = ? WHERE id = ?",remarks.toString(),adcodes.toString(),tsActApplyEntity.getId());
                tsActApplyVo.setDetailVos(detailVos);
            }

        }
    }

//    //获取编码
//    private String getTheAdvCode(String prefix,String accountCode){
//        try {
//            //调取redis
//            IRedisCacheService redisCacheService = RedisUtils.getIRedisCacheService();
//            //检查是否存在
//            if (StringUtil.isNotEmpty(redisCacheService.getString(REDIS_ONLY_CODE))) {
//                throw new BusinessException("生成门头编码时抢编号失败，请稍后重试,谢谢配合 ~(^.^)~ ");
//            }else{
//                //不存在写入表示当前正在使用
//                redisCacheService.scard(REDIS_ONLY_CODE);
//            }
//            //读取编码
//            String advCode = tsActConfigService.getAdvCodeForParse(prefix,accountCode);
//            //移除检查
//            redisCacheService.remove(REDIS_ONLY_CODE);
//            return advCode;
//        }catch (Exception e){
//            throw new BusinessException(e);
//        }
//    }

    //解析并返回明细集合
    private List<TsActApplyDetailVo> returnTsActApplyDetailVoListn(TsActApplyVo tsActApplyVo) {
        List<TsActApplyDetailVo> tsActApplyDetailVos = new ArrayList<TsActApplyDetailVo>();
        if (CollectionUtil.listNotEmptyNotSizeZero(tsActApplyVo.getDetailVos())){
            tsActApplyDetailVos = tsActApplyVo.getDetailVos();
        }else {
            tsActApplyDetailVos = (List<TsActApplyDetailVo>) JSONArray.parseArray(tsActApplyVo.getDetailJson(),TsActApplyDetailVo.class);
        }
        checkTheDatasIsRepate(tsActApplyDetailVos);
        return tsActApplyDetailVos;
    }

    //检查集合是否重复---前后左右
    private void checkTheDatasIsRepate(List<TsActApplyDetailVo> tsActApplyDetailVos) {
        Map<String,String> map = new HashMap<String,String>();
        for (TsActApplyDetailVo tsActApplyDetailVo : tsActApplyDetailVos) {
            String place = tsActApplyDetailVo.getPlace();
            String targ = map.get(place);
            if(StringUtil.isNotEmpty(targ)){
                throw new BusinessException("广告位置(" + place  + ")不允许重复出现");
            }else{
                if(place.indexOf("车贴") == -1 && place.indexOf("备注注明") == -1){ //20190117  对这两项不进行重复判断
                    map.put(place,place);
                }
            }
        }
    }

    public BigDecimal  getTrialintegral(String customCode){
        TtHiSuspiciousOpinionsVo param = new TtHiSuspiciousOpinionsVo();
        param.setCustomerCode(customCode);
        TtHiSuspiciousOpinionsVo vo = tsActAddressAndExecuteDao.getTrialintegral(param);
        if(vo == null){
            return BigDecimal.ZERO;
        } else {
            return new BigDecimal(vo.getTrialIntegral());
        }
    }

    @Override
    public boolean iscs(String userName) {
        return tsActAddressAndExecuteDao.iscs(userName) == 1 ? true : false;
    }

    @Override
    public List<AdTypeCountVO> findCanSubmitCount(String teboShopId) {
        List<AdTypeCountVO> adTypeCountVOList = new ArrayList<>();
        AdTypeCountVO mtCount = new AdTypeCountVO();
        mtCount.setAdType(AdTypeEnum.MT.getCode());
        mtCount.setAdCount(2);
        adTypeCountVOList.add(mtCount);
        AdTypeCountVO notMtCount = new AdTypeCountVO();
        notMtCount.setAdType(AdTypeEnum.NOT_MT.getCode());
        notMtCount.setAdCount(4);
        adTypeCountVOList.add(notMtCount);
        if (StringUtil.isEmpty(teboShopId)) {
            throw new BusinessException("店铺ID不能为空");
        }
        TeboShopReq req = new TeboShopReq();
        req.setId(Long.valueOf(teboShopId));
        List<TeboShopResp> teboShopResps = TeboShopClientUtil.queryShops(req);
        if (teboShopResps == null || teboShopResps.isEmpty()) {
            throw new BusinessException("未找到对应的店铺信息，请检查店铺ID是否正确");
        }
        //查询店铺对应的终端
        TmTerminalVoNew terminalVo = getTerminalByTeboShopId(teboShopId, teboShopResps.get(0).getPhoneNumber());
        String terminalCode = terminalVo.getTerminalCode();
        List<AdTypeCountVO> result = tsActAddressAndExecuteDao.getAdCount(terminalCode);
        for (AdTypeCountVO adTypeCountVO : result) {
            if (AdTypeEnum.MT.getCode().equals(adTypeCountVO.getAdType())) {
                mtCount.setAdCount(0);
            } else if (AdTypeEnum.NOT_MT.getCode().equals(adTypeCountVO.getAdType())) {
                notMtCount.setAdCount(adTypeCountVO.getAdCount() > 4 ? 0 : 4 - adTypeCountVO.getAdCount());
            }
        }
        return adTypeCountVOList;
    }


    /**
     * 检查数据是否合格
     * @param tsActApplyVo
     */
    private void checkTheDataIsOk(TsActApplyVo tsActApplyVo) {
        //检查不为空
        tmCommonService.checkTheDataIsNotNull(tsActApplyVo,encapsulationCheckData());
        //只有门头才检查活动单号
        if(Globals.acttype.mt.getValue().equals(tsActApplyVo.getActType())){
            //获取活动类型的是否创建活动申请数据
            String isActApply = getIsActApply(tsActApplyVo.getAccountCode());
            TmUserVo tmUserVo =new TmUserVo();
            tmUserVo.setUserName(tsActApplyVo.getUserName());
            tmUserVo.setId(tsActApplyVo.getUserId());
            TmCustomerVo customerVo=tmCommonMdmService.getTmCustomerVoByZzhUser(tmUserVo);
            DateTime dt = BCacheUtil.getCache(customerVo.getCustomerCode());//2019-08-23
            if(dt != null){
                   throw new BusinessException("糟糕，代理商没钱了，门头申请失败！");
            } else {
                //检查可用积分
                checkTheUsedIntegeral(tsActApplyVo,customerVo);
            }

            //if(tsActApplyVo.getAccountCode() != null && tsActApplyVo.getAccountCode().equals("XL000003")){//******** bcb 汽车门头增加数量管控
            //    checkIsEnough(tsActApplyVo);
            //}
            if(SfaGlobals.ONE_STR.equals(isActApply)){//是否需要活动申请
                //检查活动单号
                if(StringUtil.isNotEmpty(tsActApplyVo.getRealActCode())){
                    //检查门头数
                    //checkTheMtIsStandard(tsActApplyVo);
                }else{
                    throw new BusinessException("门头制作申请信息未登记，请经销商联系区域经理解决");
                }
            }
        }
    }
    private final String CHECK_SQL = "select decode(sign(nums - ctlnum),1,1,0) as flag from ( " +
                                        " select count(1) as nums,t.customer_code,b.ctlnum " +
                                        "    from ts_act_apply t, TS_CAR_CTL b " +
                                        "  where t.account_code = b.xcode " +
                                        "    and t.account_code = ? " +
                                        "    and t.customer_code = ? " +
                                        "    group by t.customer_code,b.ctlnum)";
    private void checkIsEnough(TsActApplyVo vo){
        Map<String,Object> map = super.findForMap(CHECK_SQL,vo.getAccountCode(),vo.getCustomerCode());
        if(map != null){
            String flag = OwnBeanUtils.returnNotNullWordValue(String.valueOf(map.get("flag")));
            if (StringUtil.isNotEmpty(flag)) {
                if(flag.equals("1") ){
                    throw new BusinessException("抱歉，该经销商本月可申请数量已经达到上限！");
                } else {
                    return;
                }
            }
        }
        throw new BusinessException("检查汽车门头可做数量失败");
    }

    private void checkTheUsedIntegeral(TsActApplyVo vo ,TmCustomerVo customerVo){
        if(StringUtils.isNotEmpty(vo.getRealActCode())){

            //String realActCode = vo.getRealActCode();
            //TtDealerActApplyEntity doorEntity = this.findUniqueByProperty(TtDealerActApplyEntity.class,"actCode", realActCode);
            //BigDecimal integral = BigDecimal.ZERO;
            //if(doorEntity != null && doorEntity.getApplyAmount() != null ){
            //    integral = doorEntity.getApplyAmount();
            //}
            BigDecimal integral = BigDecimal.ZERO;
            if(!vo.getAccountCode().equals(Globals.ADSTP_CAR)){//非汽车门头才去取  ********
                integral = getTrialintegral(customerVo.getCustomerCode());//获取授信积分2019-08-24
            }
            //

//            TmUserVo tmUserVo =new TmUserVo();
//            tmUserVo.setUserName(vo.getUserName());
//            tmUserVo.setId(vo.getUserId());
//            TmCustomerVo customerVo=tmCommonMdmService.getTmCustomerVoByZzhUser(tmUserVo);
            if (customerVo==null)
            {
                throw new BusinessException("对应经销商未找到!");
            }else {
                BigDecimal restIntegeral = apiBalanceAllService.getOutBalanceRam(customerVo.getCustomerCode(),vo.getAccountCode(), vo.getBusinessId(), integral,0);
                //再减去等待经销商审批的积分  ********    由于生产环境和测试环境  经销商审批节点CODE不同，所以这里通常获取的是空值
                TsWaitJXSAuditVo bean = apiTtDoorApplyExcuteWorkFlowService.getWaitJXSAuditScore(customerVo.getCustomerCode(),vo.getAccountCode());
                if(bean != null && bean.getEblance() != null){
                    restIntegeral = restIntegeral.subtract(bean.getEblance());//减掉经销商待审批积分   2019-07-15
                }
                //存在减两遍的问题   ------------------------------------------

                //经销商细类可用积分
                //经销商的细类可用积分大于等于数据字典里面设置好的细类+积分后才允许提交
                String integeralStr = DictUtil.getDicDataValue("account_code_integeral", vo.getAccountCode());
                if (StringUtils.isNotEmpty(integeralStr)) {//********   逻辑优化一
                    integeralStr = "0";
                }
                //if (StringUtils.isNotEmpty(integeralStr)) {
                BigDecimal integralXl = new BigDecimal(integeralStr);
                if(restIntegeral.add(integralXl).compareTo(BigDecimal.ZERO) < 0){//********   逻辑优化二
                    BCacheUtil.setCache(customerVo.getCustomerCode());
                    throw new BusinessException("糟糕，代理商没钱了，门头申请失败！");
                }
//                    if (integralXl.compareTo(restIntegeral) > 0) {
//                        BCacheUtil.setCache(customerVo.getCustomerCode());
//                        throw new BusinessException("糟糕，代理商没钱了，门头申请失败！");
//                    }
                //}
            }


        }
    }

    //检查门头数
    private void checkTheMtIsStandard(TsActApplyVo tsActApplyVo) {
        //获取活动指定的门头数
        BigDecimal actMtNum = getTheSpecifiedActMtNum(tsActApplyVo.getRealActCode());
        //获取已有的门头数--对应活动的
        BigDecimal existMtNum = getTheExistingActMtNum(tsActApplyVo.getRealActCode());
        //获取本次的门头数量
        BigDecimal thisMtNum = getTheThisActMtNum(tsActApplyVo);
        //总的申请的门头数
        BigDecimal totalMtNum = existMtNum.add(thisMtNum);

        if(totalMtNum.doubleValue() > actMtNum.doubleValue()){
            throw new BusinessException("申请门头数(" + totalMtNum + ")不能大于活动规定门头数(" + actMtNum + ")");
        }
    }

    //获取本次的门头数量
    private BigDecimal getTheThisActMtNum(TsActApplyVo tsActApplyVo) {
        if (!(StringUtils.isNotBlank(tsActApplyVo.getDetailJson()))
                && !(CollectionUtil.listNotEmptyNotSizeZero(tsActApplyVo.getDetailVos()))) {
            throw new BusinessException("无门头明细数据");
        }
        List<TsActApplyDetailVo> detailVos = returnTsActApplyDetailVoListn(tsActApplyVo);
        return new BigDecimal(detailVos.size());
    }

    //获取已有的门头数--当前活动的
    private BigDecimal getTheExistingActMtNum(String realActCode) {
        //ts_act_apply_detail
        String sql = " select count(1) as num from ts_act_apply_detail taad where taad.head_id in ( select taa.id from ts_act_apply taa where taa.real_act_code = ? ) ";
        Map<String,Object> map = super.findForMap(sql,realActCode);
        if(map != null){
            String num = OwnBeanUtils.returnNotNullWordValue(String.valueOf(map.get("num")));
            if (StringUtil.isNotEmpty(num)) {
                return new BigDecimal(num);
            }
        }
        throw new BusinessException("获取对应活动的门头数失败");
    }

    //获取活动指定的门头数
    private BigDecimal getTheSpecifiedActMtNum(String realActCode) {
        String sql = " select tad.DOOR_NUM as doorNum from TT_ACT_DOOR tad where tad.ACT_CODE = ? ";
        Map<String,Object> map = super.findForMap(sql,realActCode);
        if(map != null){
            String doorNumStr = OwnBeanUtils.returnNotNullWordValue(String.valueOf(map.get("doorNum")));
            if (StringUtil.isNotEmpty(doorNumStr)) {
                return new BigDecimal(doorNumStr);
            }
        }
        throw new BusinessException("获取对应活动的门头数失败");
    }

    //获取活动类型的是否创建活动申请数据
    private String getIsActApply(String accountCode) {
        String sql = " select tca.is_Act_Apply as isActApply from tt_cost_account tca where tca.account_code = ? ";
        Map<String,Object> map = super.findForMap(sql,accountCode);
        String isActApply = OwnBeanUtils.returnNotNullWordValue(String.valueOf(map.get("isActApply")));
        if(!StringUtil.isNotEmpty(isActApply)){
            throw new BusinessException("对应的活动类型'是否活动申请'标识丢失");
        }
        return isActApply;
    }

    /**
     * 检查关键字段不能为空---为空则中断
     * @return
     */
    private Map<String, Object> encapsulationCheckData() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("gpsAddress", "当前定位不能为空");
        map.put("accountCode", "活动细类不能为空");
//        map.put("hiddenAddress", "实时定位不能为空");
//        map.put("realActCode", "活动单号");
        return map;
    }

    @Override
    public void saveExecuteBpm(TsActApplyVo tsActApplyVo) {
        if (StringUtil.isNotEmpty(tsActApplyVo.getId())){
            //1更新数据状态
            TsActApplyEntity actApplyEntity=get(TsActApplyEntity.class,tsActApplyVo.getId());
            actApplyEntity.setUpdateName(tsActApplyVo.getFullPathName());
            actApplyEntity.setUpdateDate(new Date());
            actApplyEntity.setLatitudeSg(tsActApplyVo.getLatitudeSg());
            actApplyEntity.setLongitudeSg(tsActApplyVo.getLongitudeSg());
            actApplyEntity.setStatus(Globals.datastatus.update.getValue());
            actApplyEntity.setConstructionAddress(tsActApplyVo.getConstructionAddress());
            if(tsActApplyVo.getConstructionAddress() == null || "".equals(tsActApplyVo.getConstructionAddress().trim())){
                //throw new BusinessException("施工后拍照上传的地址不允许为空，请检查定位是否开启，或者检查该APP是否有定位权限。");
                throw new BusinessException(
                        (Globals.NO_LOCATION_ERROR==null|| "".equals(Globals.NO_LOCATION_ERROR.trim()))?
                                "定位地址不允许为空，请检查手机定位是否开启，再检查本APP是否有定位权限。":
                                Globals.NO_LOCATION_ERROR);
            }
            actApplyEntity.setConstructionUploadRemark(tsActApplyVo.getConstructionUploadRemark());
            actApplyEntity.setHiddenAddress(tsActApplyVo.getHiddenAddress());//clare  181017  将隐藏的地址放到施工后效果上传
            actApplyEntity.setHiddenLatitude(tsActApplyVo.getHiddenLatitude());
            actApplyEntity.setHiddenLongitude(tsActApplyVo.getHiddenLongitude());
            if (StringUtil.isNotBlank(tsActApplyVo.getProvince())) {
                actApplyEntity.setProvince(tsActApplyVo.getProvince());
            }
            if (StringUtil.isNotBlank(tsActApplyVo.getCity())) {
                actApplyEntity.setCity(tsActApplyVo.getCity());
            }
            if (StringUtil.isNotBlank(tsActApplyVo.getArea())) {
                actApplyEntity.setArea(tsActApplyVo.getArea());
            }

            try {
                if (StringUtil.isBlank(tsActApplyVo.getProvince()) ||
                        StringUtil.isBlank(tsActApplyVo.getCity()) ||
                        StringUtil.isBlank(tsActApplyVo.getArea())) {
                    Map<String, String> result = AddressParser.parseAddress(tsActApplyVo.getConstructionAddress());
                    if (StringUtil.isBlank(tsActApplyVo.getProvince())){
                        actApplyEntity.setProvince(result.get("province"));
                    }
                    if (StringUtil.isBlank(tsActApplyVo.getCity())){
                        actApplyEntity.setCity(result.get("city"));
                    }
                    if (StringUtil.isBlank(tsActApplyVo.getArea())){
                        actApplyEntity.setArea(result.get("area"));
                    }
                }
            }catch (Exception e){
                LOG.error("解析地址失败",e);
            }

            //保存图片信息
            if(CollectionUtil.listNotEmptyNotSizeZero(tsActApplyVo.getPicVoList()) || StringUtils.isNotBlank(tsActApplyVo.getPicVoListJson())){
                tsPictureService.saveTsPictureList(tsActApplyVo.getPicVoList(),tsActApplyVo.getPicVoListJson());
            }
            //执行工作流 提交工作流
            submitWorklow(tsActApplyVo);
        }else{
            throw new BusinessException("未找到上传的施工数据");
        }
    }
    //提交工作流执行
    private void submitWorklow(TsActApplyVo tsActApplyVo){
        OperationVo vo=new OperationVo();
        if(StringUtils.isBlank(tsActApplyVo.getTaskId())||StringUtils.isBlank(tsActApplyVo.getProcessInstanceId())){
            throw new BusinessException("流程无效!");
        }
        vo.setTaskId(tsActApplyVo.getTaskId());
        vo.setProcessInstanceId(tsActApplyVo.getProcessInstanceId());
        vo.setComment("同意");
        taTaskService.saveCompelte(vo);
        //更改数据状态
        this.executeSql("UPDATE ts_act_apply SET bpm_status = ?,update_date=sysdate" +
                " WHERE id=?",2,tsActApplyVo.getId());

    }
    @Override
    public void delSelectAddessApply(String id) {
        TsActApplyEntity actApplyEntity=this.get(TsActApplyEntity.class,id);
        if (actApplyEntity!=null){
          if (!(actApplyEntity.getBpmStatus() == 2 || actApplyEntity.getBpmStatus() == 4))
              throw new BusinessException("选址申请已经审批不能删除");
          this.deleteEntityById(TsActApplyEntity.class,id);
        }
    }

    private static String sql_log9 = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,23,?)";
    @Override
    public Integer checkIsRepeat(String teboShopId,String accountCode, boolean hasMt) {
        String sql="SELECT a.* FROM TM_TERMINAL a  where a.tebo_shop_id = '"+teboShopId+"'" ;
        List<TmTerminalVoNew> vo  =  this.findBySql(TmTerminalVoNew.class, sql);
        if (vo == null || vo.isEmpty()) {
            throw new BusinessException("未找到对应的店铺信息，请检查店铺ID是否正确");
        }
        TmTerminalVoNew terminalVo = vo.get(0);
        String terminalCode = terminalVo.getTerminalCode();
        if (StringUtils.isBlank(accountCode))
            throw new BusinessException("门头选址活动细类不能为空!");
        if (tmCommonService.checkDict("SCORE_XL",accountCode)) {
            if (StringUtils.isBlank(terminalCode))
                throw new BusinessException("门头选址终端不能为空!");
            if ("XL000004".equals(accountCode)) {
                Integer mtCount = 0;
                Integer notMtCount = 0;
                boolean cantSubmit = false;
                List<AdTypeCountVO> countVOList = tsActAddressAndExecuteDao.getAdCount(terminalCode);//查询汽车门头可提交数量
                for (AdTypeCountVO countVO : countVOList) {
                    if (AdTypeEnum.MT.getCode().equals(countVO.getAdType()) && hasMt) {
                        mtCount = countVO.getAdCount();
                        cantSubmit = cantSubmit ? cantSubmit : mtCount > 0;
                    }
                    if (AdTypeEnum.NOT_MT.getCode().equals(countVO.getAdType())) {
                        notMtCount = countVO.getAdCount();
                        cantSubmit = cantSubmit ? cantSubmit : notMtCount >= 4;
                    }
                }
                if (cantSubmit) {
                    throw new BusinessException("该终端两年内已制作门头"+mtCount+"个，非门头"+notMtCount+"个本次提交超出广告制作限额，请修改后重新提交。");
                }
            } else {
                //确认店铺类型
                TmTerminalExtendVo oldVo = tsActAddressAndExecuteDao.selectByTerminalCode(terminalCode);
                final Boolean carIconFlag = (oldVo != null && "car_icon".equals(oldVo.getTerminalTypeCode()));

                Map<String, Object> paramMapstrict = new HashMap();
                if (carIconFlag) {
                    paramMapstrict.put("months", 12);
                } else {
                    paramMapstrict.put("months", 24);
                }
                paramMapstrict.put("terminalCode", terminalCode);
                Integer count = tsActAddressAndExecuteDao.checkIsRepeatStrict(paramMapstrict);//白池标  2018年09月19日   针对已记账的店铺，不允许再发起选址。
                if (count == 0) {
                    count = tsActAddressAndExecuteDao.checkIsRepeat(paramMapstrict);
                } else {
                    this.executeSql(sql_log9, terminalCode, accountCode, "2Ys");//2019-10-23进行系统记录，便于后续统计分析
                    throw new BusinessException("您选择的店铺在两年内做过门头并且已报销，请您重新选择一个合理的店铺!");
                }
                if (count != null && count.intValue() > 0) {
                    return 1;
                }
            }
        }
        return 0;
    }

    @Override
    public List<TsActApplyVo> findQyManagerTaskList(TsActApplyVo tsActApplyVo) {
        String actType=Globals.acttype.mt.getValue();
        tsActApplyVo.setActType("BPM006");//门头广告执行流程
        setBpmStatus(tsActApplyVo);
        List<TsActApplyVo> list=tsActAddressAndExecuteDao.findQyManagerTaskList(tsActApplyVo);
        //加载图片
        getPictures(list,actType);
        return list;
    }

    @Override
    public Integer findQyManagerTaskListCount(TsActApplyVo tsActApplyVo) {
        String actType=Globals.acttype.mt.getValue();
        tsActApplyVo.setActType("BPM006");//门头广告执行流程
        setBpmStatus(tsActApplyVo);
        return tsActAddressAndExecuteDao.findQyManagerTaskListCount(tsActApplyVo);
    }

    @Override
    public void qyManagerApprove(TsActApplyVo actApplyVo) {
        TsActApplyEntity entity = this.get(TsActApplyEntity.class, actApplyVo.getId());
        if(entity!=null){
            TmUserEntity userEntity = this.findUniqueByProperty(TmUserEntity.class, "userName", actApplyVo.getUserName());
            if(userEntity==null){
                throw new BusinessException("用户名不存在");
            }
            entity.setManager(userEntity.getFullName()+"("+userEntity.getUserName()+")");//白池标   20180915  增加工号，便于稽查人员识别
            entity.setManagerBpmStatus(String.valueOf(actApplyVo.getBpmStatus()));
            entity.setManagerPassTime(new Date());
            this.updateEntity(entity);
        }else{
            throw new BusinessException("选址id不存在");
        }
    }
    /**
     * 区域经理批量审批
     * */
    private static String sql = "update ts_act_apply set update_date = sysdate,update_name = ?, manager_bpm_status = 3 , manager = ? ,MANAGER_PASS_TIME = sysdate where id in (select id from ads_qyManagerTaskList where yman = ?)";
    private static String sql_log = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,5,?)";
    @Transactional
    @Override
    public boolean qymBatchAudit(String id , String name){
        try {
            this.executeSql(sql,name + "("+id+")",name + "("+id+")" ,id);
            this.executeSql(sql_log,id,id,name);
        }catch (Exception e){
//            return false;
            e.printStackTrace();
            throw new BusinessException("处理失败!");
        }
        return true;
    }
    //private static String sql_reset_0st = "update ts_act_apply set QYMANREJSIGN = 0 where id = ? ";
//    private static String sql_reset_1st = "update ts_act_apply set QYMANREJSIGN = 1 ,QYMANREJDATE = sysdate where ACT_CODE in " +
//            " ( select act_code from ads_insid_actid where insid = ?)";
//    public void setRejectSign(String username , String instid , int status){
//        if (instid != null) {
//            try {
//                if (status == 1) {//驳回就打个标记    白池标  2019.01.16
//                    this.executeSql(sql_reset_1st, instid);
//                } else { //审批通过后，把标记重置一下
//                    this.executeSql(sql_reset_0st, instid);//这里的 instid  传进来的是  ts_act_apply 表的id
//                }
//            } catch (Exception e) {
//                LOG.error("区域经理驳回标记重置失败："+instid + " - " + status);
//            }
//        }
//
//    }

    private static String sql_reset_1st = "update ts_act_apply set MANAGER_BPM_STATUS = -1 ,MANAGER_PASS_TIME = sysdate , MANAGER = ? where ACT_CODE in " +
            " ( select act_code from ads_insid_actid where insid = ?)";
    public void setRejectSign(String username , String instid ){
        if (instid != null) {
            try {//驳回就打个标记    白池标  2019.01.16
                    this.executeSql(sql_reset_1st,username, instid);
            } catch (Exception e) {
                LOG.error("区域经理驳回标记重置失败："+instid);
            }
        }

    }

    /**
     * 判断文件是否存在   2018年09月11日
     * */
    @Override
    public boolean checkFilesIsUploaded(List<TsPictureVo> list){
        if(list != null) {
            for(TsPictureVo bean : list){
                if(bean.getPlace().equals("设计稿")){
                    continue;
                } else {
                    String filePath = ResourceUtil.getSysConfigProperty(com.biz.eisp.api.util.Globals.SFAPATH) + bean.getImgPath();//获得绝对路径
                    try {
                        File file = new File(filePath);
                        if (!(file.isFile() && file.exists())) { //判断文件是否存在
                            return false;
                        }
                    } catch (Exception e){
                        return false;
                    }
                }
            }
            return true;
        }else{
            return false;
        }
    }
}
