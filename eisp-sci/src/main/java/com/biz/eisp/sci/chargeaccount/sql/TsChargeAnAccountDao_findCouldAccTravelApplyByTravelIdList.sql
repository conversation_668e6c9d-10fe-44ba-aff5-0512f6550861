select
 t2.*
from (
  select
    t1.id,
    t1.applyDate,
    t1.startAddress ,
    t1.travelDestination ,
    sum(t1.amount) as amount,
    sum(t1.ivcNum) as ivcNum
  from (
    select
      tt.id as id,
      Tt.Apply_Date as applyDate,
      tt.start_address as startAddress,
      Tt.Travel_Destination as travelDestination,
      tcaa.id as detailId,
      TCAA.AMOUNT as amount,
      TCAA.IVC_NUM as ivcNum
    from ts_travel tt
    left join TS_CHARGE_AN_ACCOUNT tcaa on TCAA.OTHER_ID = tt.id
    where Tt.Username = :travelHeadVo.userName
    and Tt.Bpm_Status = 7
    <#if travelHeadVo.accountStatus ?exists && travelHeadVo.accountStatus ?length gt 0>
      and nvl(Tt.Account_Status,1) in ( '1','4','5' )
    </#if>
    <#if travelHeadVo.travelDestination ?exists && travelHeadVo.travelDestination ?length gt 0>
      AND Tt.Travel_Destination like '%' || :travelHeadVo.travelDestination || '%'
    </#if>
  ) t1
  where t1.detailId is not null
  GROUP by
  t1.id,
  t1.applyDate,
  t1.startAddress ,
  t1.travelDestination
) t2
order by t2.applyDate desc