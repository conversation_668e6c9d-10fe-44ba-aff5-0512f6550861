package com.biz.eisp.sci.extend.controller;

import com.biz.eisp.api.mdm.vo.TmUseSfaQueryVo;
import com.biz.eisp.api.synccrms.service.CrmsAdsNoticeService;
import com.biz.eisp.api.synccrms.vo.CrmsHeadVo;
import com.biz.eisp.api.synccrms.vo.CrmsNoticeVo;
import com.biz.eisp.api.synccrms.vo.CrmsTsNoticeVo;
import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.mdm.position.entity.TmPositionEntity;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.sci.extend.service.WorkNoticeVisitService;
import com.biz.eisp.sci.extend.vo.WorkNoticeVisitVo;
import com.biz.eisp.sci.pi.entity.UserAccount;
import com.biz.eisp.sci.pi.pimpl.vo.UserAcc;
import com.biz.eisp.sci.pi.pimpl.vo.UserAccountVo;
import com.biz.eisp.sci.pi.util.json.Head;
import com.biz.eisp.sci.pi.util.json.ResponseBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping("/workNoticeVisitController")
public class WorkNoticeVisitController {

    @Autowired
    private WorkNoticeVisitService workNoticeVisitService;
    @Autowired
    private CrmsAdsNoticeService crmsAdsNoticeService;

    @RequestMapping(params = "getWorkNoticeVisitRefreshVo")
    @ResponseBody
    public ResponseBean getWorkNoticeVisitRefreshVo(TmUserVo tmUserVo) {

        //workNoticeVisitController.do?getWorkNoticeVisitRefreshVo&userName=admin

        WorkNoticeVisitVo vo = new WorkNoticeVisitVo();
        Head head = new Head();
        head.setCode(Globals.RETURN_SUCCESS);
        head.setMessage("操作成功");
        try {
            vo = workNoticeVisitService.getWorkNoticeVisitRefreshVo(tmUserVo);
            //        获取最新一条公告
            CrmsTsNoticeVo crmsTsNoticeVo = new CrmsTsNoticeVo();
            List<CrmsNoticeVo> noticeList = crmsAdsNoticeService.findNoticeList(crmsTsNoticeVo, null);
            if (StringUtil.isNotEmpty(noticeList) && noticeList.size() > 0) {
                vo.setNoticeId(noticeList.get(0).getId());
                vo.setNoticeTitle(noticeList.get(0).getNoticeTitle());
                vo.setNoticeContent(noticeList.get(0).getNoticeContent());
            }
        } catch (BusinessException be) {
            be.printStackTrace();
            head.setCode(Globals.RETURN_FAIL);
            head.setMessage(be.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            head.setCode(Globals.RETURN_FAIL);
            head.setMessage("操作失败");
        }
        ResponseBean responseBean = new ResponseBean();
        responseBean.setHead(head);
        responseBean.setBusinessObject(vo);
        return responseBean;
    }


    /**
     * 查询下属出勤
     * @return
     * @url : workNoticeVisitController.do?findStaffAttendance
     */
    @RequestMapping(params = "findStaffAttendance")
    @ResponseBody
    public ResponseBean findStaffAttendance(WorkNoticeVisitVo noticeVisitVo){
        ResponseBean json = new ResponseBean();
        Page page = setPage(noticeVisitVo.getPage(), noticeVisitVo.getRows());
        Head head = new Head();
        head.setCode(Globals.RETURN_SUCCESS);
        String title = "获取数据";
        head.setMessage( title + "为空");
        try {
            //读取当前登录人
            List<WorkNoticeVisitVo> noticeVisitVos =  workNoticeVisitService.findStaffAttendance(noticeVisitVo);
            json.setBusinessObject(noticeVisitVos);
            if (CollectionUtil.listNotEmptyNotSizeZero(noticeVisitVos)) {
                head.setMessage( title + "成功");
            }
        }catch (Exception e){
            e.printStackTrace();
            head.setCode(Globals.RETURN_FAIL);
            if(StringUtil.isNotEmpty(e.getMessage())){
                head.setMessage(title + "失败:" + e.getMessage());
            }
        }
        json.setHead(head);
        return json;
    }

    private Page setPage(String pageStr,String rowsStr){
        Page page = new Page();
        if(!StringUtil.isNotEmpty(pageStr)){
            pageStr = "1";
        }
        if(!StringUtil.isNotEmpty(rowsStr)){
            rowsStr = "5";
        }
        page.setPage(pageStr);
        page.setRows(rowsStr);
        return page;
    }


}
