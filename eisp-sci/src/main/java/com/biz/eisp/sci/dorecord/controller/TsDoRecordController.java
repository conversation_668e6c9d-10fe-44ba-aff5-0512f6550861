package com.biz.eisp.sci.dorecord.controller;

import com.biz.eisp.api.sfa.dorecord.service.DoRecordService;
import com.biz.eisp.api.sfa.dorecord.vo.TsDoRecordVo;
import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.sci.pi.util.json.Head;
import com.biz.eisp.sci.pi.util.json.ResponseBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by clare on 2018/5/11.
 */
@Controller
@RequestMapping("/tsDoRecordController")
public class TsDoRecordController extends BaseController {

    @Autowired
    private DoRecordService doRecordSaveService;

    /**
     * 保存菜单点击记录
     * @param vo
     * @return
     * @url : tsDoRecordController.do?saveTheDoRecord
     */
    @RequestMapping(params = "saveTheDoRecord")
    @ResponseBody
    public ResponseBean saveTheDoRecord(TsDoRecordVo vo) {
        ResponseBean json = new ResponseBean();
        Head head=new Head();
        head.setCode(Globals.RETURN_FAIL);
        String title = "保存";
        head.setMessage(title + "失败");
        try {
            doRecordSaveService.saveTheDoRecord(vo);
            head.setMessage(title + "成功");
            head.setCode(Globals.RETURN_SUCCESS);
        } catch(Exception e) {
            e.printStackTrace();
            String msgTemp = e.getMessage();
            if (StringUtil.isNotEmpty(msgTemp)) {
                head.setMessage(head.getMessage() + ":" + msgTemp);
            }
        }
        json.setHead(head);
        return json;
    }
}
