package com.biz.eisp.sci.workplan.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.sci.api.mdm.service.MdmApiService;
import com.biz.eisp.sci.api.mdm.vo.UserInfoEntity;
import com.biz.eisp.sci.workplan.dao.TsWorkPlanSummaryDao;
import com.biz.eisp.sci.workplan.entity.TsNfSummaryEntity;
import com.biz.eisp.sci.workplan.service.TsNfSummaryService;
import com.biz.eisp.sci.workplan.transformer.TsNfSummaryVoToEntity;
import com.biz.eisp.sci.workplan.vo.TsNfSummaryVo;

@Service("tsNfSummaryService")
@Transactional
public class TsNfSummaryServiceImpl extends BaseServiceImpl implements TsNfSummaryService{

	@Autowired
	private TsWorkPlanSummaryDao tsWorkPlanSummaryDao;
	@Autowired
	private MdmApiService mdmApiService;
	
	@Override
	public List<TsNfSummaryVo> findTsNfSummaryPhone(TsNfSummaryVo tsNfSummaryVo) {
		// TODO Auto-generated method stub
		UserInfoEntity userEntity = mdmApiService.getUserInfoEntityByPosId(tsNfSummaryVo.getPosId());
		tsNfSummaryVo.setPositionCode(userEntity.getPosCode());
		return tsWorkPlanSummaryDao.findTsNfSummaryPhone(tsNfSummaryVo);
	}
	@Override
	public String saveTsNfSummaryPhone(TsNfSummaryVo tsNfSummaryVo) {
		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat sf2 = new SimpleDateFormat("yyyy-MM-dd");
		
		
		tsNfSummaryVo.setCreateDate(new Date());
		//保存时summaryDate格式yyyy-MM-dd HH:mm:ss需转换成yyyy-MM-dd
		tsNfSummaryVo.setSummaryDate(sf2.format(tsNfSummaryVo.getCreateDate()));
		
		UserInfoEntity userEntity = mdmApiService.getUserInfoEntityByPosId(tsNfSummaryVo.getPosId());
		tsNfSummaryVo.setPositionCode(userEntity.getPosCode());
		
		List<TsNfSummaryVo> summaryList = tsWorkPlanSummaryDao.findTsNfSummaryPhone(tsNfSummaryVo);
		if(summaryList.size()>0){
			return "请勿重复提个人任务";
		}
		TsNfSummaryEntity entity = new TsNfSummaryVoToEntity().apply(tsNfSummaryVo);
		entity.setUserName(userEntity.getUserName());
		entity.setFullName(userEntity.getRealName());
		entity.setPositionCode(userEntity.getPosCode());
		entity.setPositionName(userEntity.getPosName());
		entity.setPositionLevel(userEntity.getPositionLevel());
		entity.setOrgCode(userEntity.getDepartCode());
		entity.setOrgName(userEntity.getDepartName());
		entity.setSalOrgCode(userEntity.getSalOrgCode());
		entity.setSalOrgName(userEntity.getSalOrgName());
		entity.setCreateName(entity.getFullName());
		save(entity);
		return "提交成功";
	}

	
}
