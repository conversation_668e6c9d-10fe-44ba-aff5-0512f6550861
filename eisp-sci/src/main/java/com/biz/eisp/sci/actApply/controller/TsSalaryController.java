package com.biz.eisp.sci.actApply.controller;

import com.alibaba.fastjson.JSONObject;
import com.biz.eisp.api.act.addressapply.vo.TsActApplyVo;
import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.util.Md5EncryptionAndDecryption;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.mdm.terminal.vo.TmTerminalVo;
import com.biz.eisp.sci.actApply.service.TsActApplyService;
import com.biz.eisp.sci.actApply.vo.SaQueryConVo;
import com.biz.eisp.sci.actApply.vo.SalaryVo;
import com.biz.eisp.sci.api.sfa.service.ApiTsRRoleImeiStatusWebService;
import com.biz.eisp.sci.html5.controller.Html5Controller;
import com.biz.eisp.sci.html5.service.Html5ervice;
import com.biz.eisp.sci.html5.vo.DBean;
import com.biz.eisp.sci.pi.util.json.Head;
import com.biz.eisp.sci.pi.util.json.ResponseBean;
import com.biz.eisp.sci.visitdetail.vo.TsVisitDetailNewVo;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiUserGetRequest;
import com.dingtalk.api.request.OapiUserGetuserinfoRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.dingtalk.api.response.OapiUserGetuserinfoResponse;
import com.taobao.api.ApiException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Scope("prototype")
@Controller
@RequestMapping("/tsSalaryController")
public class TsSalaryController extends BaseController{

    @Autowired
    private Html5ervice h5serive;

    private static String TOKEN = null;

    @Autowired
    private ApiTsRRoleImeiStatusWebService log;

    private static final Logger logger = Logger.getLogger(TsSalaryController.class);

    private static final int MAX_ENTRIES = 1001;
    private static Map<String , DBean> USER_CACHE = new LinkedHashMap<String , DBean>(MAX_ENTRIES, .75F, true) {
        protected boolean removeEldestEntry(Map.Entry eldest) {
            return size() > MAX_ENTRIES;
        }
    };

    private static  String  LOG_ERR = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,109,?)";
    private static  String  LOG_INFO = "insert into bcb_log (var1st,var2ed,type,oper) values (?,?,111,?)";

    @RequestMapping(params = "goSalary")
    public ModelAndView goSalary(SaQueryConVo vo, HttpServletRequest request) {
//        ModelAndView view = null;
//        SalaryVo beans = new SalaryVo();
//        beans = h5serive.checkPwd(vo);
//        if(beans.getWorkcode() == null){
//            if(beans.getSubcompanyid1() != null && !"".equals(beans.getSubcompanyid1())){
//                if(com.biz.eisp.api.util.Globals.SalarySubcompany.indexOf("," + beans.getSubcompanyid1() + ",") == -1){
//                   beans.setSubcompanyid1("0");
//                }
//            }
//            view = new ModelAndView("com/biz/eisp/sci/salary/setSaPassword");
//            view.addObject("ssocode",vo.getSsocode());
//            view.addObject("workcode",vo.getWorkcode());
//            view.addObject("id",vo.getId());
//        } else {
//            view = new ModelAndView("com/biz/eisp/sci/salary/tsSaQuery");
//        }
//        view.addObject("beans",beans);
//        return view;
        ModelAndView view = new ModelAndView("com/biz/eisp/sci/salary/tsSaQuery");
        SalaryVo beans = new SalaryVo();
        view.addObject("beans",beans);
        return view;
    }

    @RequestMapping(params = "goPassword")
    public ModelAndView goPassword(SaQueryConVo vo, HttpServletRequest request) {
        ModelAndView view = new ModelAndView("com/biz/eisp/sci/salary/setSaPassword");
        view.addObject("ssocode",vo.getSsocode());
        view.addObject("workcode",vo.getWorkcode());
        view.addObject("id",vo.getId());
        if(com.biz.eisp.api.util.Globals.SalarySubcompany.indexOf("," + vo.getSubcompanyid1() + ",") != -1){
            view.addObject("error","1");
        } else {
            view.addObject("error","0");
        }
        return view;
    }

    /**
     * 初始化设置密码界面
     * @return
     */
    @RequestMapping(params ="setPassword")
    public ModelAndView setPassword(SaQueryConVo vo,HttpServletRequest request, HttpServletResponse response) {

        SalaryVo beans = new SalaryVo();
        String userId = vo.getWorkcode();
        if(vo.getPassword() != null && !"".equals(vo.getPassword()) && vo.getPassword1() != null && !"".equals(vo.getPassword1())){
            if(vo.getPassword().equals(vo.getPassword1())){
                if(TOKEN != null && !"".equals(TOKEN)){
                    vo.setWorkcode(userId);
                    h5serive.setPassword(vo);
                    beans.setSymbol("1");
                    beans.setErrorInfo("  工资条查询密码设置成功！");
                } else {
                    beans.setSymbol("0");
                    beans.setErrorInfo("系统异常！");
                }
            } else {
                beans.setSymbol("0");
                beans.setErrorInfo("两次密码输入不一致，请重新输入！");
            }
        } else {
            beans.setSymbol("0");
            beans.setErrorInfo("信息未填写完整，请重新输入！");
        }
        ModelAndView view = null;
        if(beans.getSymbol().equals("1")){
            view = new ModelAndView("com/biz/eisp/sci/salary/tsSaQuery");
            view.addObject("bean",beans);
        } else {
            view = new ModelAndView("com/biz/eisp/sci/salary/setSaPassword");
            view.addObject("ssocode",vo.getSsocode());
            view.addObject("workcode",vo.getWorkcode());
        }
        return view;
    }

    /**
     * 初始化查询界面
     * @return
     */
    @RequestMapping(params ="doQuery")
    public ModelAndView doQuery(SaQueryConVo vo,HttpServletRequest request, HttpServletResponse response) {
        ModelAndView view=new ModelAndView("com/biz/eisp/sci/salary/tsSalaryDetail");
        String userId = null;
        String mobile = null;
        DBean  bean = new DBean();
        String ssocode = vo.getSsocode();

        SalaryVo beans = new SalaryVo();
        if(ssocode != null && USER_CACHE.containsKey(ssocode)){
            bean = USER_CACHE.get(ssocode);
            userId = bean.getWorkcode();
        } else {
            if(TOKEN != null && !"".equals(TOKEN)){
                userId = getUserid(TOKEN, ssocode);
            }
            if(userId == null || "".equals(userId)){
                TOKEN = getToken();
                userId = getUserid(TOKEN, ssocode);
            }
            if(userId == null || "".equals(userId)){
                beans.setSymbol("0");
                beans.setErrorInfo("您的授权已失效，请退出并重新点击账号管理！");

                return view;
            }
            bean = getWorkcode(userId,TOKEN);//根据userID获取工号                    ===========================
            userId = bean.getWorkcode();
        }
        if(userId != null){
            log.record(userId,":"+bean.getPhoneNumber()+":"+bean.getUserid());

            if(userId.length() < 5 || userId.length() > 8){
                beans.setSymbol("0");
                beans.setErrorInfo("您在钉钉的工号："+userId+"，与实际不符，操作失败！");

                return view;
            } else {
                if(!USER_CACHE.containsKey(ssocode)){
                    USER_CACHE.put(ssocode, bean);
                }
            }
        } else {
            beans.setSymbol("0");
            beans.setErrorInfo("工号获取失败，请您退出后再进来试试...");
            return view;
        }
        vo.setWorkcode(userId);
        h5serive.executeSql(LOG_INFO,vo.getWorkcode(),vo.getYear()+"-"+vo.getMonth(),vo.getWorkcode());
        String pwd =  Md5EncryptionAndDecryption.encryPwd(vo.getPassword());


        String subComp = h5serive.getSubCompCode(userId);
        String str = null;
        if(subComp != null && subComp.equals("405")){
            if(bean.getTitle().equals("1")){
                str = "pass";//吴山基地的
            }

        }
        if(str == null ) {
            str = h5serive.oaUserAuth(userId,pwd); //===================此处对于   工人是不需要的
        }
        if(str != null && !"".equals(str)){
             beans = h5serive.getSalaryInfo(vo);

             if(beans == null){
                 beans = new SalaryVo();
                 beans.setSymbol("0");
                 beans.setSetPwd("0");
                 beans.setErrorInfo("未查询到当月" + vo.getYear() + "-" + vo.getMonth() + "工资记录！");

             } else {
                 if(beans.getWorkcode() != null && !"".equals(bean.getWorkcode())){
                     beans.setSymbol("1");
                     beans.setSetPwd("0");
                 } else {
                     beans.setSymbol("0");
                     beans.setErrorInfo("未查询到当月"+vo.getYear()+"-"+vo.getMonth()+"工资记录！");
                     beans.setSetPwd("0");
                 }
             }
        } else {
            beans = h5serive.checkPwd(vo);
            beans.setSetPwd("0");
            if(beans.getWorkcode() == null){
                beans.setSymbol("0");
                beans.setErrorInfo("抱歉，您尚未设置查询密码，请设置！");
                beans.setSetPwd("1");
            } else {
                beans.setSymbol("0");
                beans.setErrorInfo("抱歉，查询密码不正确，请重新输入！");
            }
        }
        beans.setSsocode(vo.getSsocode());
        beans.setWorkcode(userId);
        view.addObject("bean",beans);
        return view;
    }

    /**
     * 获得TOKEN
     * */
    private  String getToken (){
        String token = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest req = new OapiGettokenRequest();
            req.setAppkey("dinghjum3hjhwusyjchu");
            req.setAppsecret("M_tkWAsEX9MB1LjKKQJSPIyiu4BzX8muVMqk7YZbRv2WfZKP1_AYRqMZ9WkLSE5m");
            req.setHttpMethod("GET");
            OapiGettokenResponse rsp = client.execute(req);
            JSONObject firstJson = JSONObject.parseObject(rsp.getBody());
            token = firstJson.getString("access_token");
        } catch (ApiException e) {
            h5serive.executeSql(LOG_ERR,"dd_app_info",e.getErrMsg(),"system");
        }
        return token;
    }

    /**
     * 获得TOKEN
     * */
    private String getUserid (String token ,String ssocode){
        String userId = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/getuserinfo");
            OapiUserGetuserinfoRequest req = new OapiUserGetuserinfoRequest();
            req.setCode(ssocode);
            req.setHttpMethod("GET");
            OapiUserGetuserinfoResponse response = client.execute(req,token);
            userId = response.getUserid();
        } catch (ApiException e) {
            h5serive.executeSql(LOG_ERR,"dd_app_info",e.getErrMsg(),"system");
        }
        return userId==null?"":userId;
    }

    private DBean getWorkcode(String userid , String token){
        DBean bean = new DBean();
        String workcode = null;
        String mobile = null;
        String title = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/get");
            OapiUserGetRequest req = new OapiUserGetRequest();
            req.setUserid(userid);
            req.setHttpMethod("GET");
            OapiUserGetResponse rsp = client.execute(req, token);
            System.out.println(rsp.getBody());
            JSONObject firstJson = JSONObject.parseObject(rsp.getBody());
            workcode = firstJson.getString("jobnumber");
            mobile = firstJson.getString("mobile");
            title = firstJson.getString("title");
            if(title == null || !title.endsWith("工")){
                title = "0";
            } else {
                title = "1";
            }
            bean.setUserid(userid);
            bean.setWorkcode(workcode==null?"":workcode);
            bean.setPhoneNumber(mobile==null?"":mobile);
            bean.setTitle(title==null?"":title);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        return  bean;
    }

}
