package com.biz.eisp.sci.train.service.impl;

import com.biz.eisp.api.common.exception.OperateException;
import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.api.train.entity.TsTrainActCheckEntity;
import com.biz.eisp.api.train.vo.TsTrainActCheckVo;
import com.biz.eisp.api.util.Globals;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.sci.picture.dao.TsPictureDao;
import com.biz.eisp.sci.train.dao.TsTrainActCheckAuditDao;
import com.biz.eisp.sci.train.service.TsTrainActCheckAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TsTrainActCheckAuditServiceImpl extends BaseServiceImpl implements TsTrainActCheckAuditService {

    @Autowired
    private TsTrainActCheckAuditDao tsTrainActCheckAuditDao;

    @Autowired
    private TsPictureDao tsPictureDao;

    @Override
    public List<TsTrainActCheckVo> findTsTrainActCheckList(TsTrainActCheckVo tsTrainActCheckVo, Page page) {
        return tsTrainActCheckAuditDao.findTrainActCheckList(tsTrainActCheckVo, page);
    }

    @Override
    public void saveTsTrainActCheckAudit(TsTrainActCheckVo tsTrainActCheckVo) {
        if (StringUtil.isEmpty(tsTrainActCheckVo) || StringUtil.isEmpty(tsTrainActCheckVo.getId())) {
//            参数为空
            throw new OperateException("参数为空");
        }

        TsTrainActCheckEntity tsTrainActCheckEntity = get(TsTrainActCheckEntity.class, tsTrainActCheckVo.getId());
        if (StringUtil.isEmpty(tsTrainActCheckEntity)) {
//            未找到记录
            throw new OperateException("未找到记录");
        }

        if (Globals.BPM_STATUS_DOING != tsTrainActCheckEntity.getBpmStatus()) {
//             不是待审批状态
            throw new OperateException("不是待审批状态的检查申请不可审批");
        }
        tsTrainActCheckEntity.setBpmStatus(Globals.BPM_STATUS_PASS);
        tsTrainActCheckEntity.setAuditDate(new Date());
        tsTrainActCheckEntity.setAuditUserName(ResourceUtil.getSessionUserName().getUserName());
        saveOrUpdate(tsTrainActCheckEntity);
    }

    @Override
    public List<TsPictureVo> findTsPictureVoList(TsPictureVo tsPictureVo, Page page) {
        if (StringUtil.isEmpty(tsPictureVo) || StringUtil.isEmpty(tsPictureVo.getId())) {
            List<TsPictureVo> emptyList = new ArrayList<>();
            return emptyList;
        }
        return tsTrainActCheckAuditDao.findTsPictureVoList(tsPictureVo, page);
    }
}
