package com.biz.eisp.sci.picture.service;

import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.api.picture.vo.TsPictureVoT;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;

import java.util.List;


public interface TsPictureService extends BaseService{
	/**
	 * 方法说明
	 * @param tsPictureVo
	 * @return List<TsPictureVo>
	 */
	public List<TsPictureVo> findTsPictureVoList(TsPictureVo tsPictureVo, Page page);

	/**
	 * 方法说明
	 * @param tsPictureVo
	 * @return List<TsPictureVo>
	 */
	public List<TsPictureVo> findTsPictureVoListByAdCode(TsPictureVo tsPictureVo, Page page);

	public List<TsPictureVo> findTsPictureVoListByHW(String hw);

	public List<TsPictureVo> findTsPictureVoListByHWHis(String hw);

	public int delSGImg(String mainid,String id, String bussid,String oper);

	public List<TsPictureVoT> findTsPictureVoListByUname(String username);


	/**门头选址照片列表*/
	public List<TsPictureVoT> findTsPictureVoListMTXZ();
	/**门头施工照片列表*/
	public List<TsPictureVoT> findTsPictureVoListMTSG();
	/**
	 * 方法说明
	 * @param id
	 * @return TsPictureVo
	 */
	public TsPictureVo getTsPictureVo(String id);
	
	/**
	 * 保存图片信息.
	 * <AUTHOR>
	 * @param tsPictureVo
	 * 		图片vo对象
	 */
	public void saveTsPicture(TsPictureVo tsPictureVo);


	public List<String> getAllPicture(String businessId,String imageType);

	/**
	 * 业务数据上传是保存图片数据
	 * @param tsPictureVoList
	 * @param tsPicVoListJson
	 */
	public void saveTsPictureList(List<TsPictureVo> tsPictureVoList,String tsPicVoListJson);

	/**
	 * 刷新有文件的图片数据
	 * @param tsPictureVo
	 */
	public void updateHaveFilePic(TsPictureVo tsPictureVo);

	/**
	 *
	 * @param businessId
	 * @param imageType
	 * @param isPath 是否加入访问抬头链接
	 * @return
	 */
	public List<TsPictureVo> getAllPictureVo(String businessId,String imageType,boolean isPath);

	/**
	 * 根据外键删除图片数据
	 * @param businessId
	 */
	public void delByBusinessId(String businessId,Integer type);

	public void setAdType(String type,String adCode);

	void setMtAdType(String type , String adCode);
}
