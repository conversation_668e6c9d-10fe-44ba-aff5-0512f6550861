package com.biz.eisp.sci.operationManual.service.impl;

import com.biz.eisp.api.common.exception.OperateException;
import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.ComboTree;
import com.biz.eisp.base.common.tag.bean.ComboTreeModel;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.mdm.org.vo.TmOrgVo;
import com.biz.eisp.sci.operationManual.dao.TsOperationManualDao;
import com.biz.eisp.sci.operationManual.entity.TsOperationManualEntity;
import com.biz.eisp.sci.operationManual.service.TsOperationManualService;
import com.biz.eisp.sci.operationManual.vo.TsOperationManualVo;
import com.biz.eisp.sci.picture.entity.TsPictureEntity;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Service
public class TsOperationManualServiceImpl extends BaseServiceImpl implements TsOperationManualService {

    @Autowired
    private TsOperationManualDao tsOperationManualDao;

    @Override
    public List<TsOperationManualVo> findTsOperationManualList(TsOperationManualVo tsOperationManualVo, Page page) {
        List<TsOperationManualVo> tsOperationManualList = tsOperationManualDao.findTsOperationManualList(tsOperationManualVo, page);
        for (TsOperationManualVo vo :
                tsOperationManualList) {
            if (StringUtil.isNotEmpty(vo.getReceivingOrgCode())) {
                String orgNamesByCodes = getOrgNamesByCodes(vo.getReceivingOrgCode());
                vo.setReceivingOrgNames(orgNamesByCodes);
            }
            if (StringUtil.isNotEmpty(vo.getReceivingRoleCode())) {
                String roleNamesByCodes = getRoleNamesByCodes(vo.getReceivingRoleCode());
                vo.setReceivingRoleNames(roleNamesByCodes);
            }
        }
        return tsOperationManualList;
    }

    @Override
    public TsOperationManualVo findTsOperationManualById(String id) {
        if (StringUtil.isEmpty(id)) {
            throw new OperateException("参数为空");
        }
        TsOperationManualEntity entity = get(TsOperationManualEntity.class, id);
        if (StringUtil.isEmpty(entity)) {
            throw new OperateException("未找到记录");
        }
        TsOperationManualVo vo = new TsOperationManualVo();
        vo.setId(entity.getId());
        vo.setManualTitle(entity.getManualTitle());
        vo.setManualContent(entity.getManualContent());
        vo.setPushDate(entity.getPushDate());
        vo.setCreateName(entity.getCreateName());
        return vo;
    }

    @Override
    public List<TsOperationManualVo> findTsOperationManualListByUser(TsOperationManualVo tsOperationManualVo, Page page) {

        String orgId = ResourceUtil.getSessionTmUserVo().getOrgId();
        String tmRoleString = ResourceUtil.getTmRoleString();
        List<TsOperationManualVo> tsOperationManualList = tsOperationManualDao.findTsOperationManualListByUser(orgId, tmRoleString);
        return tsOperationManualList;
    }

    @Override
    public void saveTsOperationManual(TsOperationManualVo tsOperationManualVo) {
        if (StringUtil.isEmpty(tsOperationManualVo)) {
            throw new OperateException("参数为空");
        }
        if (StringUtil.isNotEmpty(tsOperationManualVo.getId())) {
            //修改
            TsOperationManualEntity tsOperationManualEntity = get(TsOperationManualEntity.class, tsOperationManualVo.getId());
            tsOperationManualEntity.setManualTitle(tsOperationManualVo.getManualTitle());
            tsOperationManualEntity.setManualContent(tsOperationManualVo.getManualContent());
            tsOperationManualEntity.setReceivingOrgCode(tsOperationManualVo.getReceivingOrgCode());
            tsOperationManualEntity.setReceivingRoleCode(tsOperationManualVo.getReceivingRoleCode());
            tsOperationManualEntity.setViewStartDate(tsOperationManualVo.getViewStartDate());
            tsOperationManualEntity.setViewEndDate(tsOperationManualVo.getViewEndDate());
            tsOperationManualEntity.setCreateUserName(ResourceUtil.getSessionUserName().getUserName());
            this.saveOrUpdate(tsOperationManualEntity);
        } else {
            //新增
            TsOperationManualEntity tsOperationManualEntity = new TsOperationManualEntity();
            try {
                MyBeanUtils.copyBeanNotNull2Bean(tsOperationManualVo, tsOperationManualEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
            tsOperationManualEntity.setStatus(com.biz.eisp.api.util.Globals.datastatus.add.getValue());
            tsOperationManualEntity.setCreateUserName(ResourceUtil.getSessionUserName().getUserName());
            tsOperationManualEntity.setPushDate(new Date());
            tsOperationManualEntity.setEnableStatus(1);
            this.save(tsOperationManualEntity);
        }
    }

    @Override
    public void deleteTsOperationManual(TsOperationManualVo tsOperationManualVo) {
        if (StringUtil.isEmpty(tsOperationManualVo) || StringUtil.isEmpty(tsOperationManualVo.getId())) {
            throw new OperateException("参数为空");
        }
        TsOperationManualEntity tsOperationManualEntity = get(TsOperationManualEntity.class, tsOperationManualVo.getId());
        if (StringUtil.isEmpty(tsOperationManualEntity)) {
            throw new OperateException("未找到记录");
        }
        tsOperationManualEntity.setStatus(com.biz.eisp.api.util.Globals.datastatus.del.getValue());
        this.saveOrUpdate(tsOperationManualEntity);
    }

    @Override
    public TsOperationManualVo findTsOperationManualVoById(String id) {
        TsOperationManualEntity tsOperationManualEntity = get(TsOperationManualEntity.class, id);
        TsOperationManualVo tsOperationManualVo = new TsOperationManualVo();
        try {
            MyBeanUtils.copyBeanNotNull2Bean(tsOperationManualEntity, tsOperationManualVo);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("数据转换错误");
        }
        if (StringUtil.isNotEmpty(tsOperationManualVo.getReceivingOrgCode())) {
            String orgNamesByCodes = getOrgNamesByCodes(tsOperationManualVo.getReceivingOrgCode());
            tsOperationManualVo.setReceivingOrgNames(orgNamesByCodes);
        }
        if (StringUtil.isNotEmpty(tsOperationManualVo.getReceivingRoleCode())) {
            String roleNamesByCodes = getRoleNamesByCodes(tsOperationManualVo.getReceivingRoleCode());
            tsOperationManualVo.setReceivingRoleNames(roleNamesByCodes);
        }
        return tsOperationManualVo;
    }

    @Override
    public List<ComboTree> findOrgTree(TmOrgVo orgVo, ComboTree comboTree, HttpServletRequest request) {
        //返回集合
        List<TmOrgEntity> orgList = super.loadAll(TmOrgEntity.class);

        ComboTreeModel comboTreeModel = new ComboTreeModel("id", "orgName", "tmOrgList");
        List<ComboTree> comboTrees = this.comboTree(orgList, comboTreeModel, null, false);
        return comboTrees;
    }

    @Override
    public List<TsPictureEntity> findFilesList(TsOperationManualVo tsOperationManualVo) {
        if (StringUtil.isEmpty(tsOperationManualVo) || StringUtil.isEmpty(tsOperationManualVo.getId())) {
            return null;
        }
        String hql = " from TsPictureEntity where businessId=? and imgType='60'";
        return findByHql(hql, new Object[]{tsOperationManualVo.getId()});
    }


    private String getOrgNamesByCodes(String codes) {
        String orgNames = "";
        if (StringUtil.isNotEmpty(codes)) {
            List<String> codeList = Arrays.asList(codes.split(","));
            String receivingOrgCodes = stringListToString(codeList);
            List<Map<String, String>> orgNameByCode = tsOperationManualDao.findOrgNameByCode(receivingOrgCodes);
            for (Map<String, String> map :
                    orgNameByCode) {
                String orgName = map.get("ORGNAME");
                orgNames += orgName + ",";
            }
            orgNames = orgNames.substring(0, orgNames.length() - 1);
        } else {
            return "";
        }
        return orgNames;
    }

    private String getRoleNamesByCodes(String codes) {
        String names = "";
        if (StringUtil.isNotEmpty(codes)) {
            List<String> codeList = Arrays.asList(codes.split(","));
            String roleCodes = stringListToString(codeList);
            List<Map<String, String>> roleNameByCode = tsOperationManualDao.findRoleNameByCode(roleCodes);
            for (Map<String, String> map :
                    roleNameByCode) {
                String orgName = map.get("ROLENAME");
                names += orgName + ",";
            }
            String result = names.substring(0, names.length() - 1);
            return result;
        } else {
            return "";
        }
    }

    private String stringListToString(List<String> list) {
        String string = "";
        for (String str :
                list) {
            string += "'" + str + "'" + ",";
        }
        String result = string.substring(0, string.length() - 1);
        return result;
    }

}
