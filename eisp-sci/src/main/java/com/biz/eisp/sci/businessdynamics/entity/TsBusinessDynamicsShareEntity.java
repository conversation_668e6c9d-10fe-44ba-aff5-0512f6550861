package com.biz.eisp.sci.businessdynamics.entity;

import com.biz.eisp.base.common.identity.IdEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/** 
 * 业务动态分享.
 * <AUTHOR>
 */
@Entity
@Table(name = "ts_business_dynamics_share", schema = "")
public class TsBusinessDynamicsShareEntity extends IdEntity implements java.io.Serializable {
	
	/**
	 * 描述.
	 */
	private static final long serialVersionUID = -5395498782850559108L;

	
	/** 业务动态编号*/
	private String BusinessDynamicsId;
	
	/** 用户名称 */
	private String userName;
	
	/** 真实名称 */
	private String fullName;
	
	/** 职位编码 */
	private String positionCode;
	
	/** 职位名称 */
	private String positionName;
	
	/** 创建时间 */
	private Date createDate;


	@Column(name ="business_dynamics_id")
	public String getBusinessDynamicsId() {
		return BusinessDynamicsId;
	}

	public void setBusinessDynamicsId(String businessDynamicsId) {
		BusinessDynamicsId = businessDynamicsId;
	}

	@Column(name ="user_Name")
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	@Column(name ="full_name")
	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Column(name ="position_code")
	public String getPositionCode() {
		return positionCode;
	}

	public void setPositionCode(String positionCode) {
		this.positionCode = positionCode;
	}

	@Column(name ="position_name")
	public String getPositionName() {
		return positionName;
	}

	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

	@Column(name ="create_date")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
}
