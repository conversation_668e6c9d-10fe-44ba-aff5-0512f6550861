package com.biz.eisp.sci.businessdynamics.controller;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.base.common.jsonmodel.ComboTree;
import com.biz.eisp.base.common.service.TbAttachmentService;
import com.biz.eisp.base.common.util.*;
import com.biz.eisp.mdm.dict.service.TmDictDataService;
import com.biz.eisp.mdm.dict.vo.TmDictDataVo;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.mdm.org.vo.TmOrgVo;
import com.biz.eisp.mdm.role.vo.TmRoleVo;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.sci.businessdynamics.entity.TsBusinessDynamicsEntity;
import com.biz.eisp.sci.businessdynamics.service.TsBusinessDynamicsRoleService;
import com.biz.eisp.sci.businessdynamics.service.TsBusinessDynamicsService;
import com.biz.eisp.sci.businessdynamics.vo.TsBusinessDynamicsVo;
import com.biz.eisp.sci.notice.entity.TsNoticeEntity;
import com.biz.eisp.sci.notice.service.TsNoticeRoleService;
import com.biz.eisp.sci.notice.transformer.TsNoticeEntityToTsNoticeVo;
import com.biz.eisp.sci.pi.util.json.ResponseBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.base.utils.UploadFile;
import com.biz.eisp.sci.notice.service.TsNoticeService;
import com.biz.eisp.sci.notice.vo.TsNoticeVo;
import com.biz.eisp.sci.picture.entity.TsPictureEntity;

/**
 * 公告web端控制器.
 * <AUTHOR>
 * @version v1.0
 */
@Scope("prototype")
@Controller
@RequestMapping("/tsBusinessDynamicsVoWebController")
public class TsBusinessDynamicsVoWebController extends BaseController {
	
//	@Autowired
//	private TsNoticeService tsNoticeService;
//	@Autowired
//	private TsNoticeRoleService tsNoticeRoleService;
	@Autowired
    private TbAttachmentService tbAttachmentService;
	@Autowired
	TsBusinessDynamicsService businessDynamicsService;
	@Autowired
	TsBusinessDynamicsRoleService businessDynamicsRoleService;
	@Autowired
	private TmDictDataService dictDataService;


	/**
	 * 公告管理列表 页面跳转
	 * <AUTHOR>
	 * @return
	 */
	@RequestMapping(params = "goTsBusinessDynamicsMain")
	public ModelAndView goTsNoticeMain() {
		ModelAndView view = new ModelAndView("com/biz/eisp/sci/businessdynamics/tsBusinessDynamicsMain");
		return view;
	}
	
	/**
	 * 公告管理操作表单 页面跳转.
	 * <AUTHOR>
	 * @param businessDynamicsVo
	 * 		公告vo
	 * @return
	 */
	@RequestMapping(params = "goTsBusinessDynamicsForm")
	public ModelAndView goTsBusinessDynamicsForm(TsBusinessDynamicsVo businessDynamicsVo) {
		ModelAndView view = new ModelAndView("com/biz/eisp/sci/businessdynamics/tsBusinessDynamicsForm");
		List<TsPictureEntity> pics=new ArrayList<>();
		//编辑
		if (StringUtil.isNotEmpty(businessDynamicsVo.getId())) {
			TsBusinessDynamicsEntity entity=businessDynamicsService.get(TsBusinessDynamicsEntity.class,businessDynamicsVo.getId());
			 BeanUtils.copyProperties(entity,businessDynamicsVo);
			String hql=" from TsPictureEntity where businessId=? and imgType='67'";
			pics=businessDynamicsService.findByHql(hql,new Object[]{businessDynamicsVo.getId()});
		}
		List<TmDictDataVo> bdTypes=findBusinessDynamicsType();
		view.addObject("bdTypes",bdTypes);
		view.addObject("pics", pics);
		view.addObject("vo", businessDynamicsVo);
		return view;
	}

	/**
	 * web端获取公告信息.
	 * <AUTHOR>
	 * 		vo对象
	 * @param request	
	 * 		请求对象
	 * @return
	 */
	@RequestMapping(params = "findTsBusinessDynamicsList")
	@ResponseBody
	public DataGrid findTsBusinessDynamicsList(TsBusinessDynamicsVo businessDynamicsVo, HttpServletRequest request) {
		Page page = new EuPage(request);
		List<TsBusinessDynamicsVo> result=null;
		String orgId = (String) request.getParameter("parent_id");
        String sql="select org_name orgName,org_code orgCode from tm_org where id='"+orgId+"'";
        List<TsBusinessDynamicsVo> orgInfo=businessDynamicsService.findBySql(TsBusinessDynamicsVo.class,sql);
        if(orgInfo!=null&&orgInfo.size()!=0){
            businessDynamicsVo.setOrgName(orgInfo.get(0).getOrgName());
        }

		try {
//			result = businessDynamicsService.findBusinessDynamicsList(businessDynamicsVo, page);
			result = businessDynamicsService.findBusinessDynamicsList(businessDynamicsVo, page);
		} catch (Exception e) {
			e.printStackTrace();
			throw new BusinessException("获取业务动态信息失败,"+e.getMessage());
		}
		return new DataGrid(result, page);
	}	
	/**
	 * 保存公告信息.
	 * <AUTHOR>
	 * 		vo对象
	 * @param request	
	 * 		请求对象
	 * @return
	 */
	@RequestMapping(params = "saveBusinessDynamics")
	@ResponseBody
	public AjaxJson saveBusinessDynamics(TsBusinessDynamicsVo vo,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			businessDynamicsService.saveTsBusinessDynamics(vo);
			j.setFlagId(vo.getId());
			
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			throw new BusinessException("保存公告信息失败,"+e.getMessage());
		}
		return j;
	}
	@RequestMapping(
			params = {"goOrgMainSelectToPosGrid"}
	)
	public ModelAndView goOrgSelectMain(HttpServletRequest request) {
		return new ModelAndView("com/biz/eisp/sci/notice/tmOrgSelectMain");
	}

	@RequestMapping(params = {"findOrgMainGrid"})
	@ResponseBody
	public List<ComboTree> findOrgMainGrid(HttpServletRequest request,ComboTree comboTree,TmOrgVo orgVo,String notid) {
		List<ComboTree> comboTrees = businessDynamicsService.findOrgTree(orgVo, comboTree, request);
		if (StringUtils.isNotBlank(notid)){
			TsBusinessDynamicsEntity entity=businessDynamicsService.get(TsBusinessDynamicsEntity.class,notid);
			String orgre=entity.getReceivingOrgCode();
			if (StringUtils.isNotBlank(orgre)){
				Map<String,String> map = OwnBeanUtils.changeStringArrayToMap(orgre.split(","));
				ComboxCheck(comboTrees,map);
				/*String[] arr=orgre.split(",");
				for (int i = 0; i <arr.length ; i++) {
					ComboxCheck(comboTrees,arr[i]);
				}*/
			}
		}
		return comboTrees;
	}
    private void ComboxCheck(List<ComboTree> comboTrees, Map<String, String> map){
		for (int i = 0; i < comboTrees.size(); i++) {
			ComboTree comboTree=comboTrees.get(i);
			if (OwnBeanUtils.ONE_STR.equals(map.get(comboTree.getId()))){
				comboTree.setChecked(true);
			}else{
				comboTree.setChecked(false);
				List<ComboTree> children=comboTree.getChildren();
				if (CollectionUtil.listNotEmptyNotSizeZero(children)){
					ComboxCheck(children,map);
				}
			}

		}
	}
	@RequestMapping(params = {"goPosMainSelectToRole"})
	public ModelAndView goPosMainSelectToRole(HttpServletRequest request,String receivingOrgCode) {
		request.setAttribute("receivingOrgCode",receivingOrgCode);
		return new ModelAndView("com/biz/eisp/sci/notice/tmRoleSelectMain");
	}
	@RequestMapping(params = {"findRoleByPosMainGrid"})
	@ResponseBody
	public List<ComboTree> findRoleByPosMainGrid(HttpServletRequest request,String notid,TmRoleVo roleVo,String receivingOrgCode) {
		List<ComboTree> comboTrees=new ArrayList<>();

		if (StringUtils.isNotBlank(receivingOrgCode)){
			String arr[]=receivingOrgCode.split(",");
			String tmp="";
			for (int i = 0; i <arr.length ; i++) {
				if(i==0){
					tmp+="'"+arr[i]+"'";
				}else{
					tmp+=",";
					tmp+="'"+arr[i]+"'";
				}
			}
			receivingOrgCode=tmp;
		}
		List<TmRoleVo> list=businessDynamicsRoleService.getRoleByPosIds(roleVo,receivingOrgCode);
		if (CollectionUtil.listNotEmptyNotSizeZero(list)){
			for (int i = 0; i < list.size(); i++) {
				TmRoleVo rvo=list.get(i);
				ComboTree tree=new ComboTree();
				tree.setId(rvo.getRoleCode());
				tree.setText(rvo.getRoleName());
				comboTrees.add(tree);
			}
		}
		if (StringUtils.isNotBlank(notid)){
			TsBusinessDynamicsEntity entity=businessDynamicsService.get(TsBusinessDynamicsEntity.class,notid);
			String orgre=entity.getReceivingRoleCode();
			if (StringUtils.isNotBlank(orgre)){
				String[] arr=orgre.split(",");
				for (int i = 0; i <arr.length ; i++) {
					for (int j = 0; j <comboTrees.size() ; j++) {
						if (arr[i].equals(comboTrees.get(j).getId())){
							comboTrees.get(j).setChecked(true);
						}
					}
				}
			}
		}
		return comboTrees;
	}
	@RequestMapping(params = {"deleteAll"})
	@ResponseBody
	public AjaxJson deleteAll(String ids){
		AjaxJson j=new AjaxJson();
		try {
			if (StringUtils.isNotBlank(ids)) {
              String[] arr=ids.split(",");
                for (int i = 0; i <arr.length ; i++) {
					businessDynamicsService.deleteEntityById(TsBusinessDynamicsEntity.class,arr[i]);
                }
            }
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("删除失败");
		}

		return j;
	}
	
	
	
	
	
	
	/**
	 *
	 * @param request
	 * @return
	 */
	@RequestMapping(params = "saveProductfile", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson saveProductfile(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			TsPictureEntity entity = new TsPictureEntity();
			String val = request.getParameter("file_upload");
			entity.setBusinessId(val);
			entity.setImgType("67");
			entity.setStatus("009");
			entity.setPsTime(new Date());
			UploadFile uploadFile = new UploadFile(request, entity);
			uploadFile.setExtend("extChar1");
			uploadFile.setRealPath("imgPath");
			uploadFile.setFileName("imgTypeRemark");
			uploadFile.setObject(entity);
			uploadFile.setRename(false);
			this.tbAttachmentService.uploadFile(uploadFile);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("上传失败");
		}

		return j;
		
	}
	@RequestMapping(params = "delPic", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson delPic(String id){
		AjaxJson j = new AjaxJson();
		String message = "";
		try {
			TsPictureEntity picImage = tbAttachmentService.get(TsPictureEntity.class, id);
			String picName = picImage.getImgTypeRemark();
			// -----------删除硬盘上的图片
			String saveFilePath = ResourceUtil.getSysConfigProperty("basePath");
			saveFilePath = saveFilePath + File.separator + picImage.getImgPath();
			File imgpng = new File(saveFilePath);
			if (imgpng.exists()) {
				imgpng.delete();
				message = "删除附件成功";
			} else {
				message = "未找到附件,删除数据成功";
			}
			tbAttachmentService.deleteEntityById(TsPictureEntity.class, id);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			message =  "删除附件失败";
		}
		j.setMsg(message);
		return j;
	}

	private List<TmDictDataVo>  findBusinessDynamicsType(){
		List<TmDictDataVo> bdTypes=dictDataService.findVoByType("business_dynamics_type");
		return  bdTypes;

	}

	/**
	 * 公告管理操作表单 页面跳转.
	 * <AUTHOR>
	 * @param businessDynamicsVo
	 * 		公告vo
	 * @return
	 */
	@RequestMapping(params = "goTsBusinessDynamicsDetail")
	public ModelAndView goTsBusinessDynamicsDetail(TsBusinessDynamicsVo businessDynamicsVo,HttpServletRequest request) {
		ModelAndView view = new ModelAndView("com/biz/eisp/sci/businessdynamics/tsBusinessDynamicsDetail");
		List<TsPictureEntity> pics=new ArrayList<>();
		//编辑
		if (StringUtil.isNotEmpty(businessDynamicsVo.getId())) {
			TsBusinessDynamicsEntity entity=businessDynamicsService.get(TsBusinessDynamicsEntity.class,businessDynamicsVo.getId());
			BeanUtils.copyProperties(entity,businessDynamicsVo);
			String hql=" from TsPictureEntity where businessId=? and imgType='67'";
			pics=businessDynamicsService.findByHql(hql,new Object[]{businessDynamicsVo.getId()});
		}
		List<TmDictDataVo> bdTypes=findBusinessDynamicsType();
		view.addObject("bdTypes",bdTypes);
		view.addObject("pics", pics);
		view.addObject("vo", businessDynamicsVo);
		return view;
	}

    /**
     * web端获取公告信息.
     * <AUTHOR>
     * 		vo对象
     * @param request
     * 		请求对象
     * @return
     */
    @RequestMapping(params = "findTsBusinessDynamicsUserList")
    @ResponseBody
    public DataGrid findTsBusinessDynamicsUserList(TsBusinessDynamicsVo businessDynamicsVo, HttpServletRequest request) {
        Page page = new EuPage(request);
        List<TmUserVo> result=null;
        try {
            result = businessDynamicsService.findBdUserInfo(businessDynamicsVo, page);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取信息失败,"+e.getMessage());
        }
        return new DataGrid(result, page);
    }


}
