select * from
(
	(select 
	tc.CUSTOMER_CODE as customerCode,
	tc.CUSTOMER_NAME as customerName,
	tc.ADDRESS as realaddress,
	tc.id as customerRealId,
	tc.EXT_CHAR_5 as realLongitude,
  tc.EXT_CHAR_6 as realLatitude,
	(select GET_DISTANCE(tc.EXT_CHAR_5, tc.EXT_CHAR_6, '${vist.realLongitude}','${vist.realLatitude}')  from dual) as deviation,
	'1' as customerType
	from  TM_CUSTOMER tc 
	where 1=1
	<#if vist.customerName ?exists&&vist.customerName ?length gt 0>
		AND tc.CUSTOMER_NAME LIKE '%${vist.customerName}%'
	</#if>
	<#if vist.provinceName ?exists&&vist.provinceName ?length gt 0>
		AND tc.PROVINCE = '${vist.provinceName}'
	</#if>
	<#if vist.cityName ?exists&&vist.cityName ?length gt 0>
		AND tc.CITY = '${vist.cityName}'
	</#if>
	<#if vist.countyName ?exists&&vist.countyName ?length gt 0>
		AND to_char(tc.AREA) = '${vist.countyName}'
	</#if>
	and tc.ENABLE_STATUS='0'
	)
	union
	(select
	tt.TERMINAL_CODE as customerCode,
	tt.TERMINAL_NAME as customerName,
	tt.ADDRESS as realaddress,
	tt.id as customerRealId,
	tt.EXT_CHAR_5 as realLongitude,
	tt.EXT_CHAR_6 as realLatitude,
	(select GET_DISTANCE(tt.EXT_CHAR_5, tt.EXT_CHAR_6, '${vist.realLongitude}','${vist.realLatitude}')  from dual) as deviation,
	'2' as customerType
	from TM_TERMINAL tt 
	where 1=1
	<#if vist.customerName ?exists&&vist.customerName ?length gt 0>
		AND tt.TERMINAL_NAME LIKE '%${vist.customerName}%'
	</#if>
	<#if vist.provinceName ?exists&&vist.provinceName ?length gt 0>
		AND tt.PROVINCE = '${vist.provinceName}'
	</#if>
	<#if vist.cityName ?exists&&vist.cityName ?length gt 0>
		AND tt.CITY = '${vist.cityName}'
	</#if>
	<#if vist.countyName ?exists&&vist.countyName ?length gt 0>
		AND tt.AREA = '${vist.countyName}'
	</#if>
	and tt.ENABLE_STATUS='0'
	and tt.EXT_CHAR_7<>'2'
	)

)where 1=1

order by deviation asc