package com.biz.eisp.sci.actApply.enums;

public enum MaterialActPhotoTypeEnum {
    DETAIL_MATERIAL(1, "详情物料"),
    ACTIVITY_APPLICATION(2, "物料结算清单凭证"),
    ACTIVITY_APPLICATION_VIDEO(3, "物料盘点视频"),
    ACTIVITY_PHOTO(4, "活动照片"),
    POSTER_DESIGN_DRAFT(5, "海报设计稿"),
    BANNER_DESIGN_DRAFT(6, "条幅设计稿");

    private final int code;
    private final String desc;

    MaterialActPhotoTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getBeforeTypeStr() {
        return POSTER_DESIGN_DRAFT.getCode() + "," + BANNER_DESIGN_DRAFT.getCode();
    }

    public static String getAfterTypeStr() {
        return DETAIL_MATERIAL.getCode() + "," + ACTIVITY_APPLICATION.getCode() + "," + ACTIVITY_APPLICATION_VIDEO.getCode() + "," + ACTIVITY_PHOTO.getCode();
    }
}