package com.biz.eisp.sci.picture.dao;

import com.biz.eisp.api.picture.vo.TsPictureVo;
import com.biz.eisp.api.picture.vo.TsPictureVoT;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.sci.picture.entity.TsPictureEntity;

import java.util.List;

/**
 * dao类说明
 * <AUTHOR>
 */
@InterfaceDao
public interface TsPictureDao {
	/**
	 * 方法说明
	 * @param vo
	 * @param page
	 * @result List<TsPictureVo>
	 */
	@ResultType(TsPictureVo.class)
	@Arguments({"vo", "page"})
	public List<TsPictureVo> findTsPictureVoList(TsPictureVo vo, Page page);

	/**
	 * 方法说明
	 * @param vo
	 * @param page
	 * @result List<TsPictureVo>
	 */
	@ResultType(TsPictureVo.class)
	@Arguments({"vo", "page"})
	public List<TsPictureVo> findTsPictureVoListByAdCode(TsPictureVo vo, Page page);

	@ResultType(TsPictureVo.class)
	@Arguments({"hw"})
	public List<TsPictureVo> findTsPictureVoListByHW1(String hw);

	@ResultType(TsPictureVo.class)
	@Arguments({"hw"})
	public List<TsPictureVo> findTsPictureVoListByHW1His(String hw);
	/**
	 * 方法说明
	 * @param tsPicture
	 * @result List<TsPictureVo>
	 */
	@ResultType(TsPictureVo.class)
	@Arguments({"tsPicture"})
	public List<TsPictureVo> findTsPictureVoList(TsPictureVo tsPicture);

	@ResultType(TsPictureVoT.class)
	@Arguments({"username"})
	public List<TsPictureVoT> findTsPictureVoListByUname(String username);

	@ResultType(TsPictureVoT.class)
	public List<TsPictureVoT> findTsPictureVoListMTXZ();

	@ResultType(TsPictureVoT.class)
	public List<TsPictureVoT> findTsPictureVoListMTSG();
	/**
	 * 
	 * @param ids
	 * @return
	 */
	@ResultType(TsPictureVo.class)
	@Arguments({"ids","imgtype"})
	public  List<TsPictureVo> getAllpictrue(String ids,String imgtype);

	/**
	 *
	 * @param ids
	 * @return
	 * @sql : TsPictureDao_getAllpictrueByIdsUseIn.sql
	 */
	@ResultType(TsPictureVo.class)
	@Arguments({"ids","imgtype"})
	public  List<TsPictureVo> getAllpictrueByIdsUseIn(String ids,String imgtype);

	/**
	 * 查询2017/10/18后的图片数据
	 * @param tsPicture
	 * @return
	 */
	@ResultType(TsPictureEntity.class)
	@Arguments({"tsPicture"})
	public  List<TsPictureEntity> findFilePic(TsPictureVo tsPicture);

	/**
	 * 根据外键删除图片数据
	 * @param businessId
	 */
	@Arguments({"businessId"})
	public void delByBusinessId(String businessId);
}
