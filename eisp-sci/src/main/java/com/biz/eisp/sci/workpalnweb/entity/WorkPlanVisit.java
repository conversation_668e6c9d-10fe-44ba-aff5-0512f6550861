package com.biz.eisp.sci.workpalnweb.entity;

import com.biz.eisp.base.exporter.annotation.Excel;

/*
 * 导入拜访参考次数表信息
 */
public class WorkPlanVisit {
	

	//id
	private long id;
	/**职位编码*/
	@Excel(exportName = "职位编码")
	private String positionCode;
	/**职位名称*/
	@Excel(exportName = "职位名称")
	private String positionName;
	/**客户类型*/
	@Excel(exportName = "客户类型")
	private String customerType;
	/**拜访参考次数*/
	@Excel(exportName = "拜访参考次数")
	private String visitNums;
	//销售部
	private String salName;
	//组织名称
	private String orgName;
	//人员名称
	private String fullName;
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getPositionCode() {
		return positionCode;
	}
	public void setPositionCode(String positionCode) {
		this.positionCode = positionCode;
	}
	public String getPositionName() {
		return positionName;
	}
	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}
	public String getCustomerType() {
		return customerType;
	}
	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}
	public String getVisitNums() {
		return visitNums;
	}
	public void setVisitNums(String visitNums) {
		this.visitNums = visitNums;
	}
	public String getSalName() {
		return salName;
	}
	public void setSalName(String salName) {
		this.salName = salName;
	}

	public String getFullName() {
		return fullName;
	}
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	
	
	
}
