package com.biz.eisp.sci.workpalnweb.entity;

import javax.persistence.Transient;

import com.biz.eisp.base.exporter.annotation.Excel;

//@Entity
//@Table(name = "ts_visit_detail", schema = "")
public class WorkPlanVisitEntity {
//	年月
	@Excel(exportName="年月")
	private String yearMonth;
	//销售部
	@Excel(exportName="销售部")
	private String salName;
//	组织
	@Excel(exportName="组织")
	private String orgName;
	//组织编码
	private  String orgCode;
//	人员名称
	@Excel(exportName="人员名称 ")
	private String fullName;
//	职位
	@Excel(exportName="职位名称")
	private String positionName;
	/**出勤天数*/
	@Excel(exportName="出勤天数")
	private java.lang.Integer onAttendance1;	
	/**未出勤天数*/
	@Excel(exportName="未出勤天数")
	private java.lang.Integer onAttendance2;	
	/**迟到天数*/
	@Excel(exportName="迟到次数 ")
	private java.lang.Integer onAttendance3;	
	/**早退天数*/
	@Excel(exportName="早退次数")
	private java.lang.Integer onAttendance4;	
	/**正常出勤天数*/
	@Excel(exportName="正常次数")
	private java.lang.Integer onAttendance5;
	/**请假天数*/
	@Excel(exportName="请假天数")
	private java.lang.String onAttendance6;
//	/**出差天数*/
//	@Excel(exportName="出差天数")
//	private java.lang.String onAttendance7;
//	客户类型
	private String customerType;
//	客户总数
	@Excel(exportName="经销商拜访总数")
	private String customerNums;
//	拜访考核次数
	@Excel(exportName="经销商拜访考核次数")
	private String visitNums;
//	计划外拜访次数
	@Excel(exportName="经销商拜访频次")
	private String visitOutNums;
//	计划外实际拜访次数
	@Excel(exportName="经销商完整拜访次数")
	private String visitOutRealNums;
////	拜访达成率
	private String visitVate;
//	计划外完成率
	@Excel(exportName="经销商完整拜访率")
	private String visitOutComNums;
//	拜访达成率
	@Excel(exportName="经销商拜访考核达成率")
	private String normalVisitVate;
//	拜访考核次数
	//	客户总数
	@Excel(exportName="门店拜访总数")
	private String customerNums1;
	@Excel(exportName="门店拜访考核次数")
	private String visitNums1;
//	计划外拜访次数
	@Excel(exportName="门店拜访频次")
	private String visitOutNums1;
//	计划外实际拜访次数
	@Excel(exportName="门店完整拜访次数")
	private String visitOutRealNums1;
////	拜访达成率
//	@Excel(exportName="门店计划完成率")
//	private String visitVate1;
//	计划外完成率
	@Excel(exportName="门店完整拜访率")
	private String visitOutComNums1;
//	拜访达成率
	@Excel(exportName="门店拜访考核达成率")
	private String normalVisitVate1;
//	新增门店数量
	@Excel(exportName="新增门店数量")
	private String terminalNums;
//	工作计划与总结次数
	@Excel(exportName="工作计划与总结次数")
	private String workPlanNums;
//	个人任务提报次数
	@Excel(exportName="个人任务提报次数")
	private String summaryNums;
//	个人任务提报次数
	@Excel(exportName="场景快照提报次数")
	private String photoNums;
	//	客户姓名
	private String customerName;
//	客户编码
	private String customerCode;
//	客户地址
	private String customerAdds;
	
	//职位编码
	private String positionCode;
	//客户类型	
	
	
	public String getCustomerName() {
		return customerName;
	}
	public String getPositionCode() {
		return positionCode;
	}
	public void setPositionCode(String positionCode) {
		this.positionCode = positionCode;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getCustomerAdds() {
		return customerAdds;
	}
	public void setCustomerAdds(String customerAdds) {
		this.customerAdds = customerAdds;
	}
	public String getSalName() {
		return salName;
	}
	public void setSalName(String salName) {
		this.salName = salName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getFullName() {
		return fullName;
	}
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getYearMonth() {
		return yearMonth;
	}
	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}
	
	
	public String getCustomerNums1() {
		return customerNums1;
	}
	public void setCustomerNums1(String customerNums1) {
		this.customerNums1 = customerNums1;
	}
	@Transient
	public java.lang.Integer getOnAttendance1() {
		return onAttendance1;
	}
	
	@Transient
	public void setOnAttendance1(java.lang.Integer onAttendance1) {
		this.onAttendance1 = onAttendance1;
	}

	@Transient
	public java.lang.Integer getOnAttendance2() {
		return onAttendance2;
	}

	@Transient
	public void setOnAttendance2(java.lang.Integer onAttendance2) {
		this.onAttendance2 = onAttendance2;
	}

	@Transient
	public java.lang.Integer getOnAttendance3() {
		return onAttendance3;
	}

	@Transient
	public void setOnAttendance3(java.lang.Integer onAttendance3) {
		this.onAttendance3 = onAttendance3;
	}

	@Transient
	public java.lang.Integer getOnAttendance4() {
		return onAttendance4;
	}

	@Transient
	public void setOnAttendance4(java.lang.Integer onAttendance4) {
		this.onAttendance4 = onAttendance4;
	}

	@Transient
	public java.lang.Integer getOnAttendance5() {
		return onAttendance5;
	}

	@Transient
	public void setOnAttendance5(java.lang.Integer onAttendance5) {
		this.onAttendance5 = onAttendance5;
	}

	@Transient
	public String getOnAttendance6() {
		return onAttendance6;
	}

	@Transient
	public void setOnAttendance6(String onAttendance6) {
		this.onAttendance6 = onAttendance6;
	}
	
//	@Transient
//	public String getOnAttendance7() {
//		return onAttendance7;
//	}
//
//	@Transient
//	public void setOnAttendance7(String onAttendance7) {
//		this.onAttendance7 = onAttendance7;
//	}
	
	public String getCustomerType() {
		return customerType;
	}
	
	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}
	public String getCustomerNums() {
		return customerNums;
	}
	public void setCustomerNums(String customerNums) {
		this.customerNums = customerNums;
	}
	public String getVisitNums() {
		return visitNums;
	}
	public void setVisitNums(String visitNums) {
		this.visitNums = visitNums;
	}
	public String getVisitOutNums() {
		return visitOutNums;
	}
	public void setVisitOutNums(String visitOutNums) {
		this.visitOutNums = visitOutNums;
	}
	public String getVisitOutRealNums() {
		return visitOutRealNums;
	}
	public void setVisitOutRealNums(String visitOutRealNums) {
		this.visitOutRealNums = visitOutRealNums;
	}
	public String getVisitOutComNums() {
		return visitOutComNums;
	}
	public void setVisitOutComNums(String visitOutComNums) {
		this.visitOutComNums = visitOutComNums;
	}
	public String getVisitVate() {
		return visitVate;
	}
	public void setVisitVate(String visitVate) {
		this.visitVate = visitVate;
	}
	public String getPositionName() {
		return positionName;
	}
	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

public String getNormalVisitVate() {
		return normalVisitVate;
	}
	public void setNormalVisitVate(String normalVisitVate) {
		this.normalVisitVate = normalVisitVate;
	}
	public String getSummaryNums() {
		return summaryNums;
	}
	public void setSummaryNums(String summaryNums) {
		this.summaryNums = summaryNums;
	}
	public String getWorkPlanNums() {
		return workPlanNums;
	}
	public void setWorkPlanNums(String workPlanNums) {
		this.workPlanNums = workPlanNums;
	}
	public String getTerminalNums() {
		return terminalNums;
	}
	public void setTerminalNums(String terminalNums) {
		this.terminalNums = terminalNums;
	}
	public String getPhotoNums() {
		return photoNums;
	}
	public void setPhotoNums(String photoNums) {
		this.photoNums = photoNums;
	}
	public String getVisitNums1() {
		return visitNums1;
	}
	public void setVisitNums1(String visitNums1) {
		this.visitNums1 = visitNums1;
	}
	public String getVisitOutNums1() {
		return visitOutNums1;
	}
	public void setVisitOutNums1(String visitOutNums1) {
		this.visitOutNums1 = visitOutNums1;
	}
	public String getVisitOutRealNums1() {
		return visitOutRealNums1;
	}
	public void setVisitOutRealNums1(String visitOutRealNums1) {
		this.visitOutRealNums1 = visitOutRealNums1;
	}
//	public String getVisitVate1() {
//		return visitVate1;
//	}
//	public void setVisitVate1(String visitVate1) {
//		this.visitVate1 = visitVate1;
//	}
	public String getVisitOutComNums1() {
		return visitOutComNums1;
	}
	public void setVisitOutComNums1(String visitOutComNums1) {
		this.visitOutComNums1 = visitOutComNums1;
	}
	public String getNormalVisitVate1() {
		return normalVisitVate1;
	}
	public void setNormalVisitVate1(String normalVisitVate1) {
		this.normalVisitVate1 = normalVisitVate1;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
}
