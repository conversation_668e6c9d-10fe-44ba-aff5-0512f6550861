package com.biz.eisp.sci.extend.service.impl;

import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.sci.extend.dao.RecentNoticeDao;
import com.biz.eisp.sci.extend.service.RecentNoticeService;
import com.biz.eisp.sci.notice.vo.TsNoticeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RecentNoticeServiceImpl extends BaseServiceImpl implements RecentNoticeService {

    @Autowired
    private RecentNoticeDao recentNoticeDao;

    @Override
    public TsNoticeVo getRecentNotice(String userName) {
        List<TsNoticeVo> recentNotice = recentNoticeDao.getRecentNotice(userName);
        if (StringUtil.isEmpty(recentNotice) || recentNotice.size() == 0) {
            return new TsNoticeVo();
        }
        return recentNotice.get(0);
    }
}
