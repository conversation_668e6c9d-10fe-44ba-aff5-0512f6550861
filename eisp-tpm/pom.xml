<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.biz.eisp</groupId>
		<artifactId>eisp-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>eisp-tpm</artifactId>
	<packaging>war</packaging>
	<version>${project.parent.version}</version>
	<name>eisp-tpm</name>
	<url>http://www.biz-united.com.cn/</url>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.4.3</version>
				<configuration>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.1.1</version>
				<configuration>
					<failOnMissingWebXml>false</failOnMissingWebXml>
					<archiveClasses>false</archiveClasses>
					<attachClasses>true</attachClasses>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.sql</include>
					<include>**/*.ftl</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
	</build>

	<dependencies>
		<!-- mdm 
		<dependency>
			<groupId>com.biz.eisp</groupId>
			<artifactId>eisp-mdm</artifactId>
			<version>${eisp.mdm}</version>
			<type>war</type>
		</dependency>-->
		<dependency>
			<groupId>com.biz.eisp</groupId>
			<artifactId>eisp-mdm</artifactId>
			<version>${eisp.mdm}</version>
			<type>war</type>
		</dependency>
		<dependency>
			<groupId>com.biz.eisp</groupId>
			<artifactId>eisp-mdm</artifactId>
			<version>${eisp.mdm}</version>
			<classifier>classes</classifier>
		</dependency>
		<dependency>
			<groupId>com.jamesmurty.utils</groupId>
			<artifactId>java-xmlbuilder</artifactId>
			<version>0.4</version>
		</dependency>
		<!-- web依赖包 -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jsp-api</artifactId>
			<version>2.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/cn.jpush.api/jpush-client -->
		<dependency>
			<groupId>cn.jpush.api</groupId>
			<artifactId>jpush-client</artifactId>
			<version>3.2.9</version>
		</dependency>
		<dependency>
			<groupId>com.biz.eisp</groupId>
			<artifactId>eisp-api</artifactId>
			<version>${project.parent.version}</version>
			<classifier>classes</classifier>
		</dependency>

		<dependency>
			<groupId>com.biz.eisp</groupId>
			<artifactId>eisp-api</artifactId>
			<type>war</type>
			<version>${project.parent.version}</version>
		</dependency>

		<dependency>
			<groupId>dingtalk</groupId>
			<artifactId>dingtalk</artifactId>
			<version>1.0.0</version>
		</dependency>
	</dependencies>

</project>
