import com.biz.eisp.base.utils.NumberUtils;
import org.codehaus.stax2.ri.typed.NumberUtil;

import java.lang.reflect.Field;
import java.text.NumberFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by liukai on 2017/7/4.
 */
public class TestMain {

    public static boolean isDouble(String input){
        Matcher mer = Pattern.compile("^[-\\+]?[.\\d]*$").matcher(input);
        return mer.find();
    }

    public static <T> void getDataField(T data) throws IllegalArgumentException, IllegalAccessException{
        Class clazz = data.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for(Field field : fields){
            String name = field.getName();
            if(name.equals("id")){
                field.setAccessible(true);
//               field.setInt(data,0);
                field.set(data,"kklkkllklk");

//                field.setInt(data,29);
            }
        }
    }

    public static void main(String args []) {
        System.out.println(TestMain.isDouble("123"));
    }
}
