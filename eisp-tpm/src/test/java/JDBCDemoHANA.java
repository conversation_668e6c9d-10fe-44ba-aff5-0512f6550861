import java.sql.*;

/**
 * Created by liukai on 2017/7/6.
 */
public class JDBCDemoHANA {

    public static void main(String[] args) {
        Connection connection = null;
        try {
            connection = DriverManager.getConnection(
                    "************************************************", "SAPABAP1", "jlb#JLB123");
        } catch (SQLException e) {
            e.printStackTrace();
            System.err.println("Connection Failed. User/Passwd Error?");
            return;
        }
        if (connection != null) {
            try {
                System.out.println("Connection to HANA successful!");
                Statement stmt = connection.createStatement();
                ResultSet resultSet = stmt.executeQuery("Select 'hello world' from dummy");
                resultSet.next();
                String hello = resultSet.getString(1);
                System.out.println(hello);
            } catch (SQLException e) {
                System.err.println("Query failed!");
            }
        }
    }
}
