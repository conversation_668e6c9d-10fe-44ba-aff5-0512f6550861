package com.biz.eisp.tpm.act.photowall.dao;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.log.vo.TmLogVo;

import com.biz.eisp.tpm.act.photowall.vo.PhotoTypeVo;
import com.biz.eisp.tpm.act.photowall.vo.PhotoWallVo;
import com.biz.eisp.tpm.act.photowall.vo.PictureVo;


import java.util.List;

/**
 * Created by renkaiwei  on 2017/7/10 0010.
 */
@InterfaceDao
public interface PhotoWallDao {
    /**
     * 分页查询活动照片信息
     * @param photoWallVo
     * @param page
     * @return
     */
    @Arguments({"photoWallVo","typeNames","orgId","userId","page"})
    @ResultType(PhotoWallVo.class)
    List<PhotoWallVo> findPhotoWallList(PhotoWallVo photoWallVo, String typeNames, String orgId,String userId, Page page);

    /**
     * 查询照片信息
     * @param displayTypeName
     * @return
     */
    @Arguments({"id","displayTypeName","seasonNum"})
    @ResultType(PictureVo.class)
    List<PictureVo> getAttachmentList(String id,String displayTypeName,String seasonNum);

    /**
     * 照片日志信息
     * @param vo
     * @param page
     * @return
     */
    @Arguments({"vo","page"})
    @ResultType(TmLogVo.class)
    List<TmLogVo> findLogs(PhotoWallVo vo, Page page);

    /**
     * 照片分类
     * @param id
     * @return
     */
    @Arguments({"id","terminalName","costAccountName"})
    @ResultType(PhotoTypeVo.class)
    List<PhotoTypeVo> getTypeList(String id,String terminalName,String costAccountName);

    /**
     * 根据业务id查询照片信息
     * @param bussId
     * @return
     */
    @Arguments({"id"})
    @ResultType(PictureVo.class)
    List<PictureVo> getListByBusinesId(String bussId);

    /**
     * 获取活动照片信息
     * @param id
     * @return
     */
    @Arguments({"id"})
    @ResultType(PhotoWallVo.class)
    List<PhotoWallVo> getPhotoList(String id);

    /**
     * 导出照片信息
     * @param vo
     * @return
     */
    @Arguments({"vo","orgId","userId"})
    @ResultType(PhotoWallVo.class)
    List<PhotoWallVo> findExportOutInfo(PhotoWallVo vo,String orgId,String userId);

    /**
     * 获取备注跟地址信息
     * @param id
     * @return
     */
    @Arguments({"id"})
    @ResultType(PhotoWallVo.class)
    PhotoWallVo getAddressAndRemarkInfo(String id);

    /**
     * 根据类型修改第一次照片状态可用
     * @param
     */
    @Arguments({"photoWallVo","typeNames","orgId","userId",})
    void updateStatus(PhotoWallVo photoWallVo, String typeNames, String orgId,String userId);
}
