package com.biz.eisp.tpm.accounting.service.accountingOpt;

import com.biz.eisp.api.sap.vo.TsapAccountingItemVo;
import com.biz.eisp.tpm.accounting.entity.TtAccountingEntity;
import com.biz.eisp.tpm.accounting.vo.TtVoucherVo;

import java.math.BigDecimal;

/**
 * SAP Item 数据构造工厂
 * Created by liukai on 2017/9/15.
 */
public class SapItemDataFactory {

    /**
     * 上账实体
     */
    private TtAccountingEntity accountingEntity;
    /**
     * 凭证实体
     */
    private TtVoucherVo voucherVo;

    public SapItemDataFactory(TtAccountingEntity accountingEntity, TtVoucherVo voucherVo) {
        this.accountingEntity = accountingEntity;
        this.voucherVo = voucherVo;
    }

    public TsapAccountingItemVo createTsapAccountingItemVo(String type) {
        if ("0".equals(type)) { //结案行项目vo对象
            return createType0();
        } else if ("1".equals(type)) { //税金行项目vo对象
            return createType1();
        } else if ("2".equals(type)) { //上账行项目vo对象
            return createType2();
        } else if ("3".equals(type)) { //进项税行项目vo对象
            return createType3();
        } else if ("4".equals(type)) { //反结案 结案行项目vo对象
            return createType4();
        } else if ("5".equals(type)) { //反结案 上账行项目vo对象
            return createType5();
        }
        return null;
    }

    /**
     * 结案行项目vo对象
     *
     * @return
     */
    private TsapAccountingItemVo createType0() {
        TsapAccountingItemVo itemVo = new TsapAccountingItemVo();

        BigDecimal auditAmount = voucherVo.getAuditAmount() == null ? BigDecimal.ZERO : voucherVo.getAuditAmount();
        itemVo.setJAJE(auditAmount);
        itemVo.setHDKJKM(voucherVo.getAuditAccountCode());
        itemVo.setJFPKM("40");
        itemVo.setHWB(voucherVo.getRemark());
        //设置成本中心
        itemVo.setCBZX(voucherVo.getCostCenter());
        itemVo.setSQYSKM(accountingEntity.getFinancialAccountCode());
        itemVo.setZYSMS(accountingEntity.getFinancialAccountName());
        itemVo.setSQKH(accountingEntity.getCustomerCode());
        itemVo.setHDNY(accountingEntity.getYearMonth());
        itemVo.setJABH(accountingEntity.getAuditCode());
        itemVo.setSZKM(accountingEntity.getAccountingCode());
        itemVo.setFPHM(voucherVo.getInvoiceNum());
        itemVo.setKUNNR(accountingEntity.getCustomerCode());
        itemVo.setFPHH(voucherVo.getLineNum());

        //当税码字段值为DD（待抵扣进项税）时，该字段值为空，选择其他税码时，增值税类别写死为J008
        if ("DD".equals(voucherVo.getTaxNum())) {
            itemVo.setZZS("");
        } else {
            itemVo.setZZS("J008");
        }
        itemVo.setCRMBM(voucherVo.getVoucherCode());
        itemVo.setZFLKEY(voucherVo.getFlagKey());
        itemVo.setITEMTYPE("0");

        return itemVo;
    }

    /**
     * 税金行项目vo对象
     *
     * @return
     */
    private TsapAccountingItemVo createType1() {
        TsapAccountingItemVo itemVo = new TsapAccountingItemVo();

        BigDecimal taxAmount = voucherVo.getTaxAmount() == null ? BigDecimal.ZERO : voucherVo.getTaxAmount();
        itemVo.setJAJE(taxAmount);
        itemVo.setJFPKM("40");
        itemVo.setHWB(voucherVo.getRemark());
        itemVo.setHDKJKM(voucherVo.getTaxAccountCode());
        itemVo.setSZKM(accountingEntity.getAccountingCode());
        //当税码字段值为DD（待抵扣进项税）时，该字段值为空，选择其他税码时，增值税类别写死为J008
        if ("DD".equals(voucherVo.getTaxNum())) {
            itemVo.setZZS("");
        } else {
            itemVo.setZZS("J008");
        }
        itemVo.setCRMBM(voucherVo.getVoucherCode());
        itemVo.setZFLKEY(voucherVo.getFlagKey());
        itemVo.setITEMTYPE("1");

        return itemVo;
    }

    /**
     * 上账行项目vo对象
     *
     * @return
     */
    private TsapAccountingItemVo createType2() {
        TsapAccountingItemVo itemVo = new TsapAccountingItemVo();

        BigDecimal accountAmount = voucherVo.getAccountingAmount() == null ? BigDecimal.ZERO : voucherVo.getAccountingAmount();
        itemVo.setJAJE(accountAmount);
        itemVo.setJFPKM("11");
        itemVo.setHDKJKM(voucherVo.getCustomerAccountCode());
        itemVo.setKUNNR(voucherVo.getAccountingCustomerCode());
        itemVo.setHWB(voucherVo.getRemark());
        itemVo.setSZKM(accountingEntity.getAccountingCode());
        itemVo.setCRMBM(voucherVo.getVoucherCode());
        itemVo.setZFLKEY(voucherVo.getFlagKey());
        itemVo.setJABH(accountingEntity.getAuditCode());
        itemVo.setITEMTYPE("2");

        return itemVo;
    }

    /**
     * 进项税行项目vo对象
     *
     * @return
     */
    private TsapAccountingItemVo createType3() {
        TsapAccountingItemVo itemVo = new TsapAccountingItemVo();

        BigDecimal inputTaxAmount = voucherVo.getInputTaxAmount() == null ? BigDecimal.ZERO : voucherVo.getInputTaxAmount();
        itemVo.setJAJE(inputTaxAmount);
        itemVo.setJFPKM("50");
        itemVo.setHDKJKM(voucherVo.getInputTaxAccountCode());
        itemVo.setHWB(voucherVo.getRemark());
        itemVo.setSZKM(accountingEntity.getAccountingCode());
        itemVo.setCRMBM(voucherVo.getVoucherCode());
        itemVo.setZFLKEY(voucherVo.getFlagKey());
        itemVo.setJABH(accountingEntity.getAuditCode());
        itemVo.setKUNNR(accountingEntity.getCustomerCode());
        itemVo.setITEMTYPE("3");

        return itemVo;
    }

    /**
     * 反结案 结案行项目vo对象
     *
     * @return
     */
    private TsapAccountingItemVo createType4() {
        TsapAccountingItemVo itemVo = new TsapAccountingItemVo();

        BigDecimal auditAmount = voucherVo.getAuditAmount() == null ? BigDecimal.ZERO : voucherVo.getAuditAmount();
        itemVo.setJAJE(auditAmount.abs());
        itemVo.setHDKJKM(voucherVo.getAuditAccountCode());
        itemVo.setJFPKM("50");
        itemVo.setHWB(voucherVo.getRemark());
        //设置成本中心
        itemVo.setCBZX(voucherVo.getCostCenter());
        itemVo.setSQYSKM(accountingEntity.getFinancialAccountCode());
        itemVo.setZYSMS(accountingEntity.getFinancialAccountName());
        itemVo.setSQKH(accountingEntity.getCustomerCode());
        itemVo.setHDNY(accountingEntity.getYearMonth());
        itemVo.setJABH(accountingEntity.getAuditCode());
        itemVo.setSZKM(accountingEntity.getAccountingCode());
        itemVo.setKUNNR(accountingEntity.getCustomerCode());
        itemVo.setFPHH(voucherVo.getLineNum());
        itemVo.setCRMBM(voucherVo.getVoucherCode());
        itemVo.setZFLKEY(voucherVo.getFlagKey());

        return itemVo;
    }

    /**
     * 反结案 上账行项目vo对象
     *
     * @return
     */
    private TsapAccountingItemVo createType5() {
        TsapAccountingItemVo itemVo = new TsapAccountingItemVo();

        BigDecimal accountAmount = voucherVo.getAccountingAmount() == null ? BigDecimal.ZERO : voucherVo.getAccountingAmount();
        itemVo.setJAJE(accountAmount.abs());
        itemVo.setJFPKM("01");
        itemVo.setHDKJKM(voucherVo.getCustomerAccountCode());
        itemVo.setKUNNR(voucherVo.getAccountingCustomerCode());
        itemVo.setHWB(voucherVo.getRemark());
        itemVo.setSZKM(accountingEntity.getAccountingCode());
        itemVo.setCRMBM(voucherVo.getVoucherCode());
        itemVo.setZFLKEY(voucherVo.getFlagKey());

        return itemVo;
    }
}
