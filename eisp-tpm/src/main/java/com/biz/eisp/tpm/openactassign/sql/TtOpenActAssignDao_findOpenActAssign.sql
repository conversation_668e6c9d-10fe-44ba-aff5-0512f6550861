 select t.*,cost.ACCOUNT_NAME accountName
    from ts_assign_task t,TS_ACT_APPLY ts,TT_COST_ACCOUNT cost
    WHERE t.STATUS <> 0
    AND t.ACT_CODE=ts.ACT_CODE
    AND ts.ACCOUNT_CODE=cost.ACCOUNT_CODE
    <#if vo.actCode ?exists&&vo.actCode ?length gt 0>
      AND t.act_code like '%${vo.actCode}%'
    </#if>
    <#if vo.assignUser ?exists&&vo.assignUser ?length gt 0>
      AND t.assign_user like '%${vo.assignUser}%'
    </#if>
    <#if vo.advName ?exists&&vo.advName ?length gt 0>
      AND t.adv_name like '%${vo.advName}%'
    </#if>
     <#if vo.terminalName ?exists&&vo.terminalName ?length gt 0>
          AND ts.TERMINAL_NAME LIKE '%${vo.terminalName}%'
        </#if>
    <#if vo.assignUser ?exists&&vo.assignUser ?length gt 0>
      AND t.assign_user like '%${vo.assignUser}%'
    </#if>
    <#if vo.actType ?exists&&vo.actType ?length gt 0>
      AND t.act_type= ${vo.actType}
    </#if>
    <#if vo.receiveStatus ?exists&&vo.receiveStatus ?length gt 0>
      AND t.receive_status= ${vo.receiveStatus}
    </#if>
    <#if vo.accountName ?exists&&vo.accountName ?length gt 0>
        AND cost.ACCOUNT_NAME =:vo.accountName
     </#if>
    <#if vo.starttimeStr ?exists&&vo.starttimeStr ?length gt 0>
      AND   to_char(t.starttime,'yyyy-MM-dd')>= '${vo.starttimeStr}'
    </#if>

    <#if vo.endtimeStr ?exists&&vo.endtimeStr ?length gt 0>
      AND  to_char(t.endtime,'yyyy-MM-dd') <= '${vo.endtimeStr}'
    </#if>
    ORDER BY t.RECEIVE_STATUS ASC , t.TASK_CODE DESC

