SELECT oaa.id,oaa.CREATE_DATE, taa.ACT_CODE,taa.ADV_CODE as unit_code,taa.ADV_NAME as unit_name,oaa.AUDIT_AMOUNT,taa.ACCOUNT_CODE,tca.ACCOUNT_NAME,
  oaa.TITLE,oaa.AUDIT_DATE,oaa.START_DATE,oaa.END_DATE,oaa.UPDATE_DATE,oaa.UPDATE_NAME,oaa.AUDIT_CODE,
  oaa.BPM_STATUS
from  TS_ACT_APPLY taa LEFT JOIN
  TT_OPEN_AD_AUDIT oaa on taa.ACT_CODE=oaa.ACT_CODE
  LEFT JOIN TT_COST_ACCOUNT tca on taa.ACCOUNT_CODE=tca.ACCOUNT_CODE
  WHERE taa.BPM_STATUS =3 and taa.ACT_TYPE=1

  <#if vo.actCode ?exists&&vo.actCode ?length gt 0>
    AND taa.act_code like '%${vo.actCode}%'
  </#if>

 <#if vo.unitName ?exists&&vo.unitName ?length gt 0>
  AND taa.ADV_NAME like '%${vo.unitName}%'
</#if>

 <#if vo.auditCode ?exists&&vo.auditCode ?length gt 0>
  AND oaa.AUDIT_CODE like '%${vo.auditCode}%'
</#if>

<#if vo.bpmStatus ?exists&&vo.bpmStatus ?length gt 0>

<#if vo.bpmStatus==122>
 AND oaa.BPM_STATUS is null
</#if>

<#if vo.bpmStatus!=122>
  AND oaa.BPM_STATUS = ${vo.bpmStatus}
  </#if>

</#if>

  order by taa.ACT_CODE DESC







