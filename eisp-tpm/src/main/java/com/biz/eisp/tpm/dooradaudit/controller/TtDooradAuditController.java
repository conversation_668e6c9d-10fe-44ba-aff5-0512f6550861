package com.biz.eisp.tpm.dooradaudit.controller;

import com.biz.eisp.api.act.addressapply.vo.TsActApplyVo;
import com.biz.eisp.api.act.openadbase.vo.TsActCheckVo;
import com.biz.eisp.api.tpm.entity.TtAttachmentEntity;
import com.biz.eisp.api.tpm.vo.TtAttachmentVo;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.base.utils.UploadFile;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.mdm.user.entity.TmUserEntity;
import com.biz.eisp.mdm.user.vo.TmUserVo;
import com.biz.eisp.tpm.dooradaudit.service.TtDooradAuditService;
import com.biz.eisp.tpm.dooradaudit.vo.TtDoorAdAuditVo;
import com.biz.eisp.tpm.dooradaudit.vo.TtDoorDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope("prototype")
@Controller
@RequestMapping("/ttDooradAuditController")
public class TtDooradAuditController extends BaseController {
    @Autowired
    private TtDooradAuditService ttDooradAuditService;

    @RequestMapping(params = "ttDooradAuditMain")
    public ModelAndView ttdooradAuditMain(){
        ModelAndView view=new ModelAndView("com/biz/eisp/tpm/dooradaudit/ttDooradAuditMain");
        return view;
    }

    /**
     * 获取核销列表信息
     * @param vo
     * @param request
     * @return
     */
    @RequestMapping(params = "findDoorAdAuditList")
    @ResponseBody
    public DataGrid findDoorAdAuditList(TtDoorAdAuditVo vo, HttpServletRequest request) {
        Page page = new EuPage(request);
        List<TtDoorAdAuditVo> result = ttDooradAuditService.findDoorAdAuditList(vo,page);
        return new DataGrid(result, page);
    }

    /**
     * 导出excel
     * @param vo
     * @param request
     * @param response
     */
    @SuppressWarnings("deprecation")
    @RequestMapping(params = "exportXls")
    public void exportXls(TtDoorAdAuditVo vo, HttpServletRequest request, HttpServletResponse response){
        List<TtDoorAdAuditVo> result = ttDooradAuditService.findDoorAdAuditList(vo,null);
        this.doExportXls(response,request,result,TtDoorAdAuditVo.class,"户外广告核销信息");
    }

    @RequestMapping(params = "dooradForm")
    public ModelAndView dooradForm(){
        ModelAndView view=new ModelAndView("com/biz/eisp/tpm/dooradaudit/ttDooradForm");
        view.addObject("actCode","#####");
        return view;
    }

    /**
     * 获取核销列表信息
     * @param vo
     * @param request
     * @return
     */
    @RequestMapping(params = "findActCheckList")
    @ResponseBody
    public DataGrid findActCheckList(TtDoorDataVo vo, HttpServletRequest request) {
        Page page = new EuPage(request);
        List<TtDoorDataVo> result = ttDooradAuditService.findTtDoorDataVoList(vo,page);
        return new DataGrid(result, page);
    }

    @RequestMapping(params = "saveDooradAudit")
    @ResponseBody
    public AjaxJson saveDooradAudit(TtDoorAdAuditVo vo, HttpServletRequest request){
        AjaxJson ajaxJson=new AjaxJson();
        if(vo!=null){
            if(vo.getApplyIds()!=null){
                vo.setApplyIds("("+vo.getApplyIds().substring(0,vo.getApplyIds().length()-1)+")");
            }
        }
        try{
            ttDooradAuditService.savedoorAdAudit(vo);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        }catch (Exception e){
            e.printStackTrace();
            ajaxJson.setMsg(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @RequestMapping(params = "findTsActApplyList")
    @ResponseBody
    public DataGrid findTsActApplyList(TsActApplyVo vo,HttpServletRequest request){
        Page page = new EuPage(request);
        List<TsActApplyVo> result = ttDooradAuditService.findTsActApplyList(vo,page);
        return new DataGrid(result, page);
    }


    @RequestMapping(params = "findTtDoorDataVoList")
    @ResponseBody
    public DataGrid findTtDoorDataVoList(TtDoorDataVo vo,HttpServletRequest request){
        Page page = new EuPage(request);
        List<TtDoorDataVo> result;
        if(vo==null||vo.getActCode()==null||vo.getActCode().equals("")){
            result=null;
        }else{
            result = ttDooradAuditService.findTtDoorDataVoList(vo,page);
            List<TtDoorAdAuditVo> volist=new ArrayList<TtDoorAdAuditVo>();
            String sql="select * from TT_DOOR_AD_AUDIT where status=1 and act_Code like '%"+vo.getActCode()+"%'";
            volist=ttDooradAuditService.findBySql(TtDoorAdAuditVo.class,sql);
            if(volist!=null&&volist.size()>0&&result!=null){
                String excutCodes="";
                for (int i=0;i<volist.size();i++){
                    excutCodes+=volist.get(i).getActExcuteCode();
                }

                for(int h=0;h<volist.size();h++){
                    for(int j=0;j<result.size();j++){
                        if(volist.get(h).getActExcuteCode().contains(result.get(j).getExcudeCode())){
                            result.remove(j);
                        }

//                        if(excutCodes.contains(result.get(j).getExcudeCode())){
//                            result.remove(j);
//                        }
                    }
                }

            }
        }
        return new DataGrid(result, page);
    }


    /**
     * 保存附件.
     * <AUTHOR>
     * @param ttAttachmentVo 附件对象
     * @param request 请求对象
     * @param response 响应对象
     * @return
     */
    @RequestMapping(params = "saveFiles")
    @ResponseBody
    public AjaxJson saveFiles(TtAttachmentVo ttAttachmentVo, HttpServletRequest request, HttpServletResponse response) {
        AjaxJson j = new AjaxJson();
        try {
            TtAttachmentEntity ta = new TtAttachmentEntity();
            ta.setBusinessId(ttAttachmentVo.getBusinessId());
            ta.setOtherCode(ttAttachmentVo.getOtherCode());
            ta.setRemark(ttAttachmentVo.getRemark());
            UploadFile uploadFile = new UploadFile(request, ta);
            ta.setSubclassName(TtAttachmentEntity.class.getName());
            uploadFile.setCusPath("resources/upload");
            uploadFile.setExtend("extend");
            uploadFile.setTitleField("attachmentTitle");
            uploadFile.setRealPath("realPath");
            uploadFile.setObject(ta);
            uploadFile.setRename(true);

            ttDooradAuditService.uploadFile(uploadFile);

            List<TtAttachmentVo> myAttachmentList = ttDooradAuditService.findAttachMent(ttAttachmentVo);
            Map<String, Object> attributes = new HashMap<String, Object>();
            attributes.put("url", ta.getRealPath());
            attributes.put("fileKey", ta.getId());
            attributes.put("code", ta.getOtherCode());
            attributes.put("name", ta.getAttachmentTitle());
            attributes.put("delurl", "taAttachmentController.do?delObjFile&fileKey=" + ta.getId());
            attributes.put("myAttachmentList", myAttachmentList);
            j.setAttributes(attributes);
            j.setMsg("文件添加成功");
        } catch(Exception e) {
            e.printStackTrace();
        }
        return j;
    }


    @RequestMapping(params = "deleteDoorAudit")
    @ResponseBody
    public AjaxJson deleteDoorAudit(TtDoorAdAuditVo vo, HttpServletRequest request){
        AjaxJson ajaxJson=new AjaxJson();
        if(vo!=null){
            if(vo.getId()!=null){
                vo.setId("("+vo.getId().substring(0,vo.getId().length()-1)+")");
            }
        }
        try{
            ttDooradAuditService.deleteDoorAudit(vo.getId());
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        }catch (Exception e){
            e.printStackTrace();
            ajaxJson.setMsg(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }



}
