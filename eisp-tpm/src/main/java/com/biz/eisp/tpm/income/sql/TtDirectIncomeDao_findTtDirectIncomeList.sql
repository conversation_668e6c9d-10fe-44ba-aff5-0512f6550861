SELECT
  t.ID AS ID,
  t.OR<PERSON>_CODE AS orgCode,
  t.ORG_NAME AS orgName,
  t.ORG_LEVEL AS orgLevel,
  t.INTO_ORG_CODE AS intoOrgCode,
  t.INTO_ORG_NAME AS intoOrgName,
  t.INTO_ORG_LEVEL AS intoOrgLevel,
  t.<PERSON>EG<PERSON>_DATE AS beginDate,
  t.END_DATE AS endDate,
  t.REMARK AS remark,
  t.CREATE_DATE AS createDate,
  t.CREATE_NAME AS createName,
  t.UPDATE_DATE AS updateDate,
  t.UPDATE_NAME AS updateName,
  t.ENABLE_STATUS AS enableStatus
FROM tt_direct_income t
WHERE 1=1

    <#if ttDirectIncome.orgCode ?exists&&ttDirectIncome.orgCode ?length gt 0>
        AND t.org_code like '%${ttDirectIncome.orgCode}%'
    </#if>
    <#if ttDirectIncome.orgCodes ?exists&&ttDirectIncome.orgCodes ?length gt 0>
        and t.org_Code in (select org_code from tm_org start with org_code in(${ttDirectIncome.orgCodes}) connect by prior id = parent_id)
    </#if>
    <#if ttDirectIncome.intoOrgCode ?exists&&ttDirectIncome.intoOrgCode ?length gt 0>
        AND t.into_Org_Code like '%${ttDirectIncome.intoOrgCode}%'
    </#if>
    <#if ttDirectIncome.intoOrgCodes ?exists&&ttDirectIncome.intoOrgCodes ?length gt 0>
        and t.into_Org_Code in (select org_code from tm_org start with org_code in(${ttDirectIncome.intoOrgCodes}) connect by prior id = parent_id)
    </#if>

    <#if ttDirectIncome.activityDate_begin ?exists&& ttDirectIncome.activityDate_begin ?length gt 0>
        AND t.begin_Date >= '${ttDirectIncome.activityDate_begin}'
    </#if>

    <#if ttDirectIncome.activityDate_end ?exists&& ttDirectIncome.activityDate_end ?length gt 0>
        AND end_Date <= '${ttDirectIncome.activityDate_end}'
    </#if>

    <#if ttDirectIncome.createName ?exists&&ttDirectIncome.createName ?length gt 0>
        AND t.create_Name like '%${ttDirectIncome.createName}%'
    </#if>


    <#if ttDirectIncome.createDate_begin ?exists&& ttDirectIncome.createDate_begin ?length gt 0>
        AND to_char(t.create_Date,'yyyy-MM-dd HH:mm:ss') >= '${ttDirectIncome.createDate_begin}'
    </#if>

    <#if ttDirectIncome.createDate_end ?exists&& ttDirectIncome.createDate_end ?length gt 0>
        AND to_char(t.create_Date,'yyyy-MM-dd HH:mm:ss') <= '${ttDirectIncome.createDate_end}'
    </#if>

    AND t.Enable_Status = 0
    ORDER BY create_Date DESC

