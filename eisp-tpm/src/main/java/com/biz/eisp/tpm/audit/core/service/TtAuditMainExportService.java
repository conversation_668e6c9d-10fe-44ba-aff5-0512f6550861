package com.biz.eisp.tpm.audit.core.service;

import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.tpm.audit.core.vo.TtAuditPictureVo;
import com.biz.eisp.tpm.audit.core.vo.TtAuditTerminalExportVo;
import com.biz.eisp.tpm.audit.core.vo.TtAuditVo;

import java.util.List;

/**
 * 核销导出service
 *
 * <AUTHOR>
 * @create 2017-08-25 11:59
 **/
public interface TtAuditMainExportService extends BaseService{

    /**
     * 结案客户导出查询的
     *
     * @param ttAuditVo
     *
     *
     * @return
     *
     */
    List<TtAuditVo> findTtAuditVoList(TtAuditVo ttAuditVo);

    /**
     * 结案门店导出查询的
     *
     * @param ttAuditTerminalExportVo
     *
     * @return
     */
    List<TtAuditTerminalExportVo> findTtAuditTerminalVoList(TtAuditTerminalExportVo ttAuditTerminalExportVo);

    /**
     * 根据auditId和门店查询图片列表
     *
     * @param auditId 结案子单ID
     *
     * @param terminalCode 门店编码
     *
     * @return  返回图片列表
     */
    List<TtAuditPictureVo>  findAuditTerminalPictureList(String auditId, String terminalCode);


}
