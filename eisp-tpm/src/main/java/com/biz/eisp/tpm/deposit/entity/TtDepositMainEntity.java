package com.biz.eisp.tpm.deposit.entity;

import com.biz.eisp.base.common.identity.IdEntity;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.log.curd.Loggerable;
import com.biz.eisp.log.curd.ModifyObject;
import com.biz.eisp.mdm.dict.util.DictUtil;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 奶粉押金管理实体
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "TT_DEPOSIT_MAIN")
public class TtDepositMainEntity extends IdEntity implements java.io.Serializable, Loggerable {
    private static final long serialVersionUID = 8693977935367288805L;


    /**
     * 开始时间
     */
    private String beginDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 产品层级
     */
    private String productLevel;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 财年
     */
    private String finacialYear;

    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人
     */
    private String updateName;
    /**
     * 修改时间
     */
    private Date updateDate;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 启用状态
     */
    private Integer enableStatus;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 押金金额
     */
    private BigDecimal depositAmount;
    /**
     * 协议金额
     */
    private BigDecimal protocolAmount;
    /**
     * 支付方式
     */
    private String paymentCode;
    /**
     * 逻辑删除状态
     */
    private Integer delStatus;

    /**
     * 产品返利标准
     */
    private BigDecimal productRebateStandard;


    @Column(name = "END_DATE", nullable = false, length = 20)
    public String getEndDate() {
        return this.endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Column(name = "BEGIN_DATE", nullable = false, length = 20)
    public String getBeginDate() {
        return this.beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    @Column(name = "PRODUCT_LEVEL", nullable = true, length = 20)
    public String getProductLevel() {
        return this.productLevel;
    }

    public void setProductLevel(String productLevel) {
        this.productLevel = productLevel;
    }

    @Column(name = "PRODUCT_NAME", nullable = false, length = 200)
    public String getProductName() {
        return this.productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "PRODUCT_CODE", nullable = false, length = 50)
    public String getProductCode() {
        return this.productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    @Column(name = "CUSTOMER_NAME", nullable = false, length = 200)
    public String getCustomerName() {
        return this.customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    @Column(name = "CUSTOMER_CODE", nullable = false, length = 20)
    public String getCustomerCode() {
        return this.customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    @Column(name = "FINACIAL_YEAR", nullable = true, length = 20)
    public String getFinacialYear() {
        return this.finacialYear;
    }

    public void setFinacialYear(String finacialYear) {
        this.finacialYear = finacialYear;
    }

    @Column(name = "REMARK", nullable = true, length = 200)
    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "UPDATE_NAME", nullable = true, length = 100)
    public String getUpdateName() {
        return this.updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Column(name = "UPDATE_DATE", nullable = true)
    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Column(name = "CREATE_NAME", nullable = true, length = 100)
    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Column(name = "CREATE_DATE", nullable = true)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "ENABLE_STATUS", nullable = true)
    public Integer getEnableStatus() {
        return this.enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    @Column(name = "AMOUNT", nullable = true)
    public BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "DEPOSIT_AMOUNT", nullable = true)
    public BigDecimal getDepositAmount() {
        return this.depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    @Column(name = "PROTOCOL_AMOUNT", nullable = true)
    public BigDecimal getProtocolAmount() {
        return this.protocolAmount;
    }

    public void setProtocolAmount(BigDecimal protocolAmount) {
        this.protocolAmount = protocolAmount;
    }

    @Column(name = "DEL_STATUS", nullable = true)
    public Integer getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(Integer delStatus) {
        this.delStatus = delStatus;
    }

    @Column(name = "PRODUCT_REBATE_STANDARD", nullable = true)
    public BigDecimal getProductRebateStandard() {
        return productRebateStandard;
    }

    public void setProductRebateStandard(BigDecimal productRebateStandard) {
        this.productRebateStandard = productRebateStandard;
    }

    @Column(name = "PAYMENT_CODE", nullable = true)
    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    /**
     * 新增日志
     *
     * @return
     */
    @Override
    public String addLogContent() {
        String enableStatus = null;
        if (this.enableStatus != null) {
            enableStatus = DictUtil.getDictDataValueByCode("enable_status", this.enableStatus.toString());
        } else {
            enableStatus = "空";
        }

        String remark = "";
        if (StringUtils.isNotBlank(this.getRemark())) {
            remark += this.getRemark();
        } else {
            remark = "空";
        }


        //code--改成名字
        //code查询数据库，把名字查询出来
        String content =
                "字段[<b>客户名称</b>]:" + this.customerName + "</br>" +
                        "字段[<b>产品系列</b>]:" + this.productName + "</br>" +
                        "字段[<b>开始时间</b>]:" + this.beginDate + "</br>" +
                        "字段[<b>备注</b>]:" + remark + "</br>" +
                        "字段[<b>结束时间</b>]:" + this.endDate + "</br>" +
                        "字段[<b>协议金额</b>]:" + this.protocolAmount + "</br>" +
                        "字段[<b>押金金额</b>]:" + this.depositAmount + "</br>" +
                        "字段[<b>启用状态</b>]:" + enableStatus + "</br>" +
                        "字段[<b>产品返利标准</b>]:" + this.productRebateStandard;
        return content;
    }

    /**
     * 修改的日志
     *
     * @param modifyFieldList
     * @return
     */
    @Override
    public String updateLogContent(List<ModifyObject> modifyFieldList) {
        String logContent = "";
        for (ModifyObject obj : modifyFieldList) {
            //当前改变的值 enableStatus
            String fieldName = obj.getFieldName();

            if (StringUtil.equals("customerName", fieldName)) {
                logContent += "字段[<b>客户名称</b>]:";
                logContent += "原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
            }

            if (StringUtil.equals("remark", fieldName)) {
                logContent += "字段[<b>备注</b>]:";
                if (StringUtil.isEmpty(obj.getOldVal())) {
                    logContent += "原值[" + "空" + "],修改为[" + obj.getNowVal() + "]</br>";
                } else if (StringUtil.isEmpty(obj.getNowVal())) {
                    logContent += "原值[" + obj.getOldVal() + "],修改为[" + "空" + "]</br>";
                } else {
                    logContent += "原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
                }

            }

            if (StringUtil.equals("enableStatus", fieldName)) {
                logContent += "字段[<b>启用状态</b>]：";
                String old = "";
                String now = "";
                old = DictUtil.getDictDataValueByCode("enable_status", obj.getOldVal().toString());
                now = DictUtil.getDictDataValueByCode("enable_status", obj.getOldVal().toString());
                logContent += "原值[" + old + "],修改为[" + now + "]</br>";
            }

            if (StringUtil.equals("depositAmount", fieldName)) {
                logContent += "字段[<b>押金金额</b>]：原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
            }
            if (StringUtil.equals("protocolAmount", fieldName)) {
                logContent += "字段[<b>协议金额</b>]：原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
            }
            if (StringUtil.equals("beginDate", fieldName)) {
                logContent += "字段[<b>开始时间</b>]：原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
            }
            if (StringUtil.equals("endDate", fieldName)) {
                logContent += "字段[<b>结束时间</b>]：原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
            }
            if (StringUtil.equals("productRebateStandard", fieldName)) {
                logContent += "字段[<b>产品返利标准</b>]：原值[" + obj.getOldVal() + "],修改为[" + obj.getNowVal() + "]</br>";
            }
        }
        return logContent;
    }

    /**
     * 日志名称
     *
     * @return
     */
    @Override
    public String businessDesc() {
        return "奶粉押金管理";
    }


}
