package com.biz.eisp.tpm.workflow.listener;

import com.biz.eisp.activiti.common.WorkFlowGlobals;
import com.biz.eisp.activiti.designer.businessconf.entity.TaProcessVariableEntity;
import com.biz.eisp.activiti.designer.processconf.entity.TaProcessNodeEntity;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.mdm.actrole.service.TmActRoleService;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import com.biz.eisp.tpm.act.core.entity.TtActActivitiHeadEntity;
import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色下所有职位会签
 * Created by liukai on 2017/7/26.
 */
public class AllPositionByRoleSignListener implements TaskListener {

    //工作流角色管理Service
    public TmActRoleService tmActRoleService;
    private HistoryService historyService;

    public AllPositionByRoleSignListener() {
        tmActRoleService = (TmActRoleService) ApplicationContextUtils.getContext()
                .getBean("tmActRoleService");
        historyService = ApplicationContextUtils.getContext().getBean(HistoryService.class);
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        //如果不是审批通过，就直接跳过
        String approveOpt = (String)delegateTask.getVariable("approveOpt");
        if(approveOpt == null) {
            return;
        }
        if(!approveOpt.equals(WorkFlowGlobals.OPT_PASS)) {
            return;
        }

        String processInstanceId = delegateTask.getProcessInstanceId();

        //获取业务Key
        String businessKey = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult().getBusinessKey();

        TtActActivitiHeadEntity headEntity = tmActRoleService.get(TtActActivitiHeadEntity.class, businessKey);
        //获取flagKey
        String flagKey = headEntity.getFlagKey();

        //获取下一个节点任务key
        String nextNodeTaskDefinitionKey = this.getNextNodeTaskDefinitionKey(delegateTask.getTaskDefinitionKey());

        //查找会签角色编码
        String roleKey = this.getRoleKey(headEntity.getId(), nextNodeTaskDefinitionKey);

        List<TmPositionVo> voList = tmActRoleService.findPositionList(roleKey);

        List <String> personList = new ArrayList<String>();
        for(TmPositionVo vo : voList) {
            personList.add(vo.getPositionCode());
        }
        //设置会签信息
        delegateTask.setVariable("persons", personList);
    }

    /**
     * 获取下一个节点编码
     * @return
     */
    private String getNextNodeTaskDefinitionKey(String currTaskDefinitionKey) {
        TaProcessNodeEntity nodeEntity = tmActRoleService.findUniqueByProperty(TaProcessNodeEntity.class,
                "processNodeCode", currTaskDefinitionKey);
        int currIndex = nodeEntity.getSort();
        String hql = "from TaProcessNodeEntity where taProcessEntity.id = ? AND sort = ?";
        List <TaProcessNodeEntity> list = tmActRoleService.findByHql(hql, nodeEntity.getTaProcessEntity().getId(), currIndex + 1);
        if(CollectionUtil.listNotEmptyNotSizeZero(list)) {
            return list.get(0).getProcessNodeCode();
        } else {
            throw new BusinessException("流程节点配置顺序错误，请联系管理员配置");
        }
    }

    /**
     * 获取角色变量
     * @param processId
     * @param processNodeKey
     * @return
     */
    private String getRoleKey(String processId, String processNodeKey) {
        String sql = "SELECT v.* FROM ta_base_business_obj o " +
                "       LEFT JOIN ta_process_busiobj_config c ON o.process_busiobj_config_id = c.id " +
                "       LEFT JOIN ta_process_variable v ON v.process_id = c.process_id " +
                "       LEFT JOIN ta_process_node n ON n.id = v.process_node_id " +
                " WHERE o.id = ? AND n.process_node_code = ?";

        List <TaProcessVariableEntity> list = tmActRoleService.findBySql(TaProcessVariableEntity.class,
                sql, processId, processNodeKey);

        if(CollectionUtil.listNotEmptyNotSizeZero(list)) {
            return list.get(0).getProcessVariableKey();
        } else {
            throw new BusinessException("没有配置会签角色变量，请联系管理员配置");
        }
    }
}
