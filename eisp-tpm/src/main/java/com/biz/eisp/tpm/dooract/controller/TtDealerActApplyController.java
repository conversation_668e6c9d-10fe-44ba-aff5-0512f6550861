package com.biz.eisp.tpm.dooract.controller;

import com.biz.eisp.api.act.areaactapply.service.TtAreaActApplyService;
import com.biz.eisp.api.act.areaactapply.vo.AreaActApplyvo;
import com.biz.eisp.api.act.costaccount.service.TtApiCostAccountService;
import com.biz.eisp.api.act.costaccount.vo.TtApiCostAccountVo;
import com.biz.eisp.api.act.dealeractapply.entity.TtDealerActApplyEntity;
import com.biz.eisp.api.act.dealeractapply.service.TtDealerActApplyService;
import com.biz.eisp.api.act.dealeractapply.vo.DealerActApplyvo;
import com.biz.eisp.api.util.Globals;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.tpm.balance.service.BalanceAllService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

@Controller
@RequestMapping("ttDealerActApplyController")
public class TtDealerActApplyController extends BaseController {

    private static final Logger logger = Logger.getLogger(TtDealerActApplyController.class);

    @Autowired
    private TtDealerActApplyService dealerActApplyService;

    @Autowired
    private TtAreaActApplyService areaActApplyService;

    @Autowired
    private BalanceAllService balanceAllService;

    @Autowired
    private TtApiCostAccountService ttApiCostAccountService;


    /**
     * 经销商活动申请信息 页面跳转
     *
     * @return
     */
    @RequestMapping(params = "dealerActApplyMain")
    public ModelAndView dealerActApplyMain() {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/dealeractapply/ttDealerActApplyMain");
        return view;
    }


    /**
     * 经销商活动申请信息列表
     *
     * @param dealerActApplyvo
     * @param request
     * @return
     */
    @RequestMapping(params = "findDealerActApplyList")
    @ResponseBody
    public DataGrid findDealerActApplyList(DealerActApplyvo dealerActApplyvo, HttpServletRequest request) {
        Page page = new EuPage(request);
        dealerActApplyvo.setIsAreaAct(0);
        List<DealerActApplyvo> result = dealerActApplyService.findDealerActApplyvoList(dealerActApplyvo, page);
        return new DataGrid(result, page);
    }

    /**
     * 经销商活动申请信息维护页面
     *
     * @return
     */
    @RequestMapping(params = "goDealerActApplyForm")
    public ModelAndView goDealerActApplyForm(DealerActApplyvo actApplyvo, HttpServletRequest request) throws Exception {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/dealeractapply/ttDealerActApplyForm");
        String id = request.getParameter("id");

        DealerActApplyvo dealerActApplyvo = new DealerActApplyvo();
        if (id == null || id.equals("")) {
            dealerActApplyvo.setStartDate(new Date());
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.YEAR,1);
            dealerActApplyvo.setEndDate(c.getTime());
        }
        if (id != null && !id.equals("")) {
            List<TtDealerActApplyEntity> entitys = dealerActApplyService.findByProperty(TtDealerActApplyEntity.class, "id", id);
            if (entitys != null && entitys.size() != 0) {
                MyBeanUtils.copyBean2Bean(dealerActApplyvo, entitys.get(0));
            }
        }

        //获取本单金额
        Map map = new HashMap();


        try {
            String customerCode = dealerActApplyService.getCustomerInfo().getCustCode();

            //找到细类---分了产品组的
            TtApiCostAccountVo apiCostAccountVo = new TtApiCostAccountVo();
            apiCostAccountVo.setCustomerCode(customerCode);
            apiCostAccountVo.setIsActApply(Globals.ONE);
            List<TtApiCostAccountVo> actCostAccounts = ttApiCostAccountService.findTtApiCostAccountVoList(apiCostAccountVo);
            view.addObject("costAccounts", actCostAccounts);

            //HashMap<String, BigDecimal> priceMap = balanceAllService.getPriceMap(customerCode);
//        BigDecimal bigDecimal = balanceAllService.getCurrentBalance(priceMap,businessKey);
//        map.put("本单金额",bigDecimal);

            //获取经销商积分余额
            Map<String, BigDecimal> decimalMap = balanceAllService.getCashBalancePlusOtherBalanceMap(customerCode,null);
            map.put("经销商积分余额", decimalMap.get("cashBalance"));

            //未报销金额
            map.put("未报销金额", decimalMap.get("otherBalance"));

            //CRMS待报销积分
            BigDecimal crmsmoney = balanceAllService.getCrmBalance(customerCode);
            map.put("CRMS待报销积分", crmsmoney);

            //广告系统累计待报销积分
            String businessKey = "";

            BigDecimal ggjf = balanceAllService.getTmpBalance(customerCode, "1",businessKey);
            map.put("广告系统累计待报销积分", ggjf);

            dealerActApplyvo.setFundBalance(decimalMap.get("cashBalance"));
            dealerActApplyvo.setCrmsBlance(crmsmoney);
            dealerActApplyvo.setEblance(ggjf);
            dealerActApplyvo.setOtherBalance(decimalMap.get("otherBalance"));

        } catch (BusinessException e) {
            e.printStackTrace();
        } finally {
            view.addObject("vo", dealerActApplyvo);
            view.addObject("isAreaAct", actApplyvo.getIsAreaAct());
            return view;
        }

//        view.addObject("vo", dealerActApplyvo);
//        view.addObject("isAreaAct",actApplyvo.getIsAreaAct());
//        return view;
    }


    /**
     * 保存经销商活动申请信息
     *
     * @param dealerActApplyvo
     * @param request
     * @return
     */
    @RequestMapping(params = "saveDealerActApply")
    @ResponseBody
    public AjaxJson saveDealerActApply(DealerActApplyvo dealerActApplyvo, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();

        try {
            dealerActApplyService.saveDealerActApplyvo(dealerActApplyvo);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setSuccess(false);
            if (StringUtil.isNotEmpty(e.getMessage())) {
                ajaxJson.setMsg(e.getMessage());
            }
        }
        return ajaxJson;

    }

    /**
     * 提交经销商活动申请信息
     *
     * @param dealerActApplyvo
     * @return
     */
    @RequestMapping(params = "submitDealerActApply")
    @ResponseBody
    public AjaxJson submitDealerActApply(DealerActApplyvo dealerActApplyvo) {
        //ttDealerActApplyController.do?submitDealerActApply
        AjaxJson ajaxJson = new AjaxJson();
        ajaxJson.setSuccess(true);
        ajaxJson.setMsg("操作成功");
        try {
            dealerActApplyService.submitDealerActApply(dealerActApplyvo);
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setSuccess(false);
            if (StringUtil.isNotEmpty(e.getMessage())) {
                ajaxJson.setMsg(e.getMessage());
            } else {
                ajaxJson.setMsg("操作失败");
            }
        }
        return ajaxJson;

    }

    /**
     * 撤回经销商活动申请信息
     *
     * @param dealerActApplyvo
     * @return
     */
    @RequestMapping(params = "revokeDealerActApply")
    @ResponseBody
    public AjaxJson revokeDealerActApply(DealerActApplyvo dealerActApplyvo) {
        //ttDealerActApplyController.do?revokeDealerActApply
        AjaxJson ajaxJson = new AjaxJson();
        ajaxJson.setSuccess(true);
        ajaxJson.setMsg("操作成功");
        try {
            dealerActApplyService.revokeDealerActApply(dealerActApplyvo);
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setSuccess(false);
            if (StringUtil.isNotEmpty(e.getMessage())) {
                ajaxJson.setMsg(e.getMessage());
            } else {
                ajaxJson.setMsg("操作失败");
            }
        }
        return ajaxJson;

    }


    /**
     * 删除经销商活动申请信息
     *
     * @param ids
     * @param request
     * @return
     */
    @RequestMapping(params = "deleteDealerActApply")
    @ResponseBody
    public AjaxJson deleteDealerActApply(String ids, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            dealerActApplyService.deleteDealerActApplyvo(ids);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        } catch (Exception e) {
            e.printStackTrace();
            ajaxJson.setMsg(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;

    }

    /**
     * 导出excel
     *
     * @param dealerActApplyvo
     * @param request
     * @param response
     */
    @SuppressWarnings("deprecation")
    @RequestMapping(params = "exportXls")
    public void exportXls(DealerActApplyvo dealerActApplyvo, HttpServletRequest request, HttpServletResponse response) {
        dealerActApplyvo.setIsAreaAct(0);
        List<DealerActApplyvo> result = dealerActApplyService.findDealerActApplyvoList(dealerActApplyvo, null);
        this.doExportXls(response, request, result, DealerActApplyvo.class, "经销商活动申请");
    }


    /**
     * 停用活动
     *
     * @param ids
     * @param request
     * @return
     */
    @RequestMapping(params = "stopActApply")
    @ResponseBody
    public AjaxJson stopActApply(DealerActApplyvo actApplyvo, String ids, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        json.setSuccess(false);
        String title = "停用";
        json.setMsg(title + "失败");
        try {
            dealerActApplyService.stopActApply(actApplyvo, ids);
            json.setSuccess(true);
            json.setMsg(title + "成功");
        } catch (Exception e) {
            e.printStackTrace();
            if (StringUtil.isNotEmpty(e.getMessage())) {
                json.setMsg(json.getMsg() + ":" + e.getMessage());
            }
        }
        return json;
    }


}
