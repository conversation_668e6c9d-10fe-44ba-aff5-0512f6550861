package com.biz.eisp.tpm.openadaudit.vo;

import com.biz.eisp.activiti.runtime.vo.MyInstanceVo;
import com.biz.eisp.base.exporter.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class TtOpenAdAuditVo extends MyInstanceVo implements Serializable {
    /**审批状态*/
    private Integer bpmStatus ;
    /**标题*/
    @Excel(exportName = "标题",orderNum = "2",exportFieldWidth = 20)
    private String title ;
    /**活动编号*/
    @Excel(exportName = "活动编号",orderNum = "3",exportFieldWidth = 20)
    private String  actCode ;
    /**核销单号*/
    @Excel(exportName = "核销单号",orderNum = "4",exportFieldWidth = 20)
    private String  auditCode ;
    /**单位编码*/
    @Excel(exportName = "单位编码",orderNum = "5",exportFieldWidth = 20)
    private String unitCode ;
    /**单位名*/
    @Excel(exportName = "单位名",orderNum = "5",exportFieldWidth = 20)
    private String unitName ;
    /**核销日期*/
    @Excel(exportName = "核销日期",orderNum = "6",exportFieldWidth = 20)
    private Date auditDate ;
    /**开始日期*/
    @Excel(exportName = "开始日期",orderNum = "7",exportFieldWidth = 20)
    private Date startDate ;
    /**结束日期*/
    @Excel(exportName = "结束日期",orderNum = "8",exportFieldWidth = 20)
    private Date endDate ;
    /**核销金额*/
    @Excel(exportName = "核销金额",orderNum = "9",exportFieldWidth = 20)
    private BigDecimal auditAmount ;
    /**瑕疵率*/
    private BigDecimal  flawChance;
    /**尺寸*/
    private String adSize ;
    /**材料*/
    private String material ;
    /**周围环境*/
    @Excel(exportName = "周围环境",orderNum = "13",exportFieldWidth = 20)
    private String surroundings ;
    /**创建日期*/
    @Excel(exportName = "创建日期",orderNum = "14",exportFieldWidth = 20)
    private Date createDate ;
    /**创建人*/
    @Excel(exportName = "创建人",orderNum = "15",exportFieldWidth = 20)
    private String createName ;
    /**更新日期*/
    @Excel(exportName = "更新日期",orderNum = "16",exportFieldWidth = 20)
    private Date updateDate ;
    /**更新人*/
    @Excel(exportName = "更新人",orderNum = "17",exportFieldWidth = 20)
    private String  updateName ;

    private String id ;
    /**检查申请id*/
    private String applyIds;

    private String actType;
    @Excel(exportName = "广告类型",orderNum = "19",exportFieldWidth = 20)
    private String adType;

    private String accountCode;
    private String accountName;

    public Integer getBpmStatus() {
        return bpmStatus;
    }

    public void setBpmStatus(Integer bpmStatus) {
        this.bpmStatus = bpmStatus;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getActCode() {
        return actCode;
    }

    public void setActCode(String actCode) {
        this.actCode = actCode;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getAuditAmount() {
        return auditAmount;
    }

    public void setAuditAmount(BigDecimal auditAmount) {
        this.auditAmount = auditAmount;
    }

    public BigDecimal getFlawChance() {
        return flawChance;
    }

    public void setFlawChance(BigDecimal flawChance) {
        this.flawChance = flawChance;
    }

    public String getAdSize() {
        return adSize;
    }

    public void setAdSize(String adSize) {
        this.adSize = adSize;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getSurroundings() {
        return surroundings;
    }

    public void setSurroundings(String surroundings) {
        this.surroundings = surroundings;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getApplyIds() {
        return applyIds;
    }

    public void setApplyIds(String applyIds) {
        this.applyIds = applyIds;
    }

    public String getAdType() {
        return adType;
    }

    public void setAdType(String adType) {
        this.adType = adType;
    }

    public String getActType() {
        return actType;
    }

    public void setActType(String actType) {
        this.actType = actType;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
