SELECT
y.ID AS  id,
y.YEAR as year,
y.MONTH as month,
y.BEGIN_DATE as beginDate,
y.END_DATE as endDate,
y.YEAR_MONTH as yearMonth
FROM TT_FINANCIAL_YEAR y

WHERE 1=1
and  (
      to_char(y.begin_date,'yyyy-MM-dd') >= '${vo.beginDate}'

      and to_char(y.end_date,'yyyy-MM-dd') <= '${vo.endDate}'

      or '${vo.beginDate}' between to_char(y.begin_date,'yyyy-MM-dd') and  to_char(y.end_date,'yyyy-MM-dd')
      or '${vo.endDate}' between to_char(y.begin_date,'yyyy-MM-dd') and  to_char(y.end_date,'yyyy-MM-dd')

     )

ORDER  BY y.YEAR_MONTH asc
