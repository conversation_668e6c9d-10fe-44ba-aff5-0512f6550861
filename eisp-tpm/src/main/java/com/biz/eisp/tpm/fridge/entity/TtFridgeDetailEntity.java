package com.biz.eisp.tpm.fridge.entity;

import com.biz.eisp.base.common.identity.IdEntity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 冰柜明细
 * <AUTHOR>
 * @version v1.0
 */
@Entity
@Table(name = "tt_fridge_detail")
public class TtFridgeDetailEntity extends IdEntity implements java.io.Serializable {
	private static final long serialVersionUID = -1281210164181253040L;
	
	/**冰柜编码*/
	private String fridgeCode;
	private String fridgeDetailCode;
	/**年月*/
	private String yearMonth;
	
	/**返利金额*/
	private BigDecimal rebateAmount;

	/**已申请金额*/
	private BigDecimal applyAmount;
	
	//状态:0未提交 1部分返利 2全部返利
	private Integer rebateStatus;

	//客户
	private String customerCode;
	private String customerName;

	private String remark;
	
	/**创建人职位*/
	private String createPost;
	private String updatePost;

	private String createName;
	private Date createDate;
	private String updateName;
	private Date updateDate;


	//冗余字段
	private String orgCode;
	private String orgName;
	private String orgType;
	//活动细类
	private String costAccountCode;
	private String costAccountName;
	//活动大类
	private String costTypeCode;
	private String costTypeName;
	//费用科目
	private String accountCode;
	private String accountName;

	private String terminalCode;
	private String terminalName;
	private String positionName;
	private String positionCode;



	@Column(name = "customer_code",nullable = true,length = 32)
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	@Column(name = "customer_name",length = 100)
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	@Column(name ="rebate_status",nullable=true)
	public Integer getRebateStatus() {
		return rebateStatus;
	}
	public void setRebateStatus(Integer rebateStatus) {
		this.rebateStatus = rebateStatus;
	}
	@Column(name ="FRIDGE_CODE",nullable=true,length=32)
	public String getFridgeCode(){
		return this.fridgeCode;
	}
	public void setFridgeCode(String fridgeCode){
		this.fridgeCode = fridgeCode;
	}
	@Column(name ="YEAR_MONTH",nullable=true,length=20)
	public String getYearMonth(){
		return this.yearMonth;
	}
	public void setYearMonth(String yearMonth){
		this.yearMonth = yearMonth;
	}
	@Column(name ="REBATE_AMOUNT",nullable=true,length=10)
	public BigDecimal getRebateAmount(){
		return this.rebateAmount;
	}
	public void setRebateAmount(BigDecimal rebateAmount){
		this.rebateAmount = rebateAmount;
	}
	@Column(name ="APPLY_AMOUNT",nullable=true,length=10)
	public BigDecimal getApplyAmount(){
		return this.applyAmount;
	}
	public void setApplyAmount(BigDecimal applyAmount){
		this.applyAmount = applyAmount;
	}
	@Column(name ="REMARK",nullable=true,length=500)
	public String getRemark(){
		return this.remark;
	}
	public void setRemark(String remark){
		this.remark = remark;
	}
	@Column(name ="CREATE_NAME",nullable=true,length=32)
	public String getCreateName(){
		return this.createName;
	}
	public void setCreateName(String createName){
		this.createName = createName;
	}
	@Column(name ="CREATE_POST",nullable=true,length=32)
	public String getCreatePost(){
		return this.createPost;
	}
	public void setCreatePost(String createPost){
		this.createPost = createPost;
	}
	@Column(name ="CREATE_DATE",nullable=true)
	public Date getCreateDate(){
		return this.createDate;
	}
	public void setCreateDate(Date createDate){
		this.createDate = createDate;
	}
	@Column(name ="UPDATE_NAME",nullable=true,length=32)
	public String getUpdateName(){
		return this.updateName;
	}
	public void setUpdateName(String updateName){
		this.updateName = updateName;
	}
	@Column(name ="UPDATE_POST",nullable=true,length=32)
	public String getUpdatePost(){
		return this.updatePost;
	}
	public void setUpdatePost(String updatePost){
		this.updatePost = updatePost;
	}
	@Column(name ="UPDATE_DATE",nullable=true)
	public Date getUpdateDate(){
		return this.updateDate;
	}
	public void setUpdateDate(Date updateDate){
		this.updateDate = updateDate;
	}

	@Column(name ="org_code",nullable=true)
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	@Column(name ="org_name",nullable=true)
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	@Column(name ="org_type",nullable=true)
	public String getOrgType() {
		return orgType;
	}
	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}

	@Column(name ="cost_account_code",nullable=true)
	public String getCostAccountCode() {
		return costAccountCode;
	}
	public void setCostAccountCode(String costAccountCode) {
		this.costAccountCode = costAccountCode;
	}

	@Column(name ="cost_account_name",nullable=true)
	public String getCostAccountName() {
		return costAccountName;
	}
	public void setCostAccountName(String costAccountName) {
		this.costAccountName = costAccountName;
	}

	@Column(name ="cost_type_code",nullable=true)
	public String getCostTypeCode() {
		return costTypeCode;
	}
	public void setCostTypeCode(String costTypeCode) {
		this.costTypeCode = costTypeCode;
	}

	@Column(name ="cost_type_name",nullable=true)
	public String getCostTypeName() {
		return costTypeName;
	}
	public void setCostTypeName(String costTypeName) {
		this.costTypeName = costTypeName;
	}

	@Column(name ="account_code",nullable=true)
	public String getAccountCode() {
		return accountCode;
	}
	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	@Column(name ="account_name",nullable=true)
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	@Column(name ="terminal_code",nullable=true)
	public String getTerminalCode() {
		return terminalCode;
	}
	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	@Column(name ="terminal_name",nullable=true)
	public String getTerminalName() {
		return terminalName;
	}
	public void setTerminalName(String terminalName) {
		this.terminalName = terminalName;
	}

	@Column(name ="position_name",nullable=true)
	public String getPositionName() {
		return positionName;
	}
	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}

	@Column(name ="position_code",nullable=true)
	public String getPositionCode() {
		return positionCode;
	}
	public void setPositionCode(String positionCode) {
		this.positionCode = positionCode;
	}
	@Column(name ="fridge_detail_code",nullable=true)
	public String getFridgeDetailCode() {
		return fridgeDetailCode;
	}

	public void setFridgeDetailCode(String fridgeDetailCode) {
		this.fridgeDetailCode = fridgeDetailCode;
	}
}
