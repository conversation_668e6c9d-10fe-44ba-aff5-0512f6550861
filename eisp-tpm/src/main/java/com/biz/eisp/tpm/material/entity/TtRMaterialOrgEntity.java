package com.biz.eisp.tpm.material.entity;

import com.biz.eisp.base.common.identity.IdEntity;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**   
 * @Title: Entity
 * @Description: 总部物料组织权限明细实体
 * <AUTHOR>
 * @date 2017-06-24 10:19:41
 * @version V1.0   
 *
 */
@Entity
@Table(name = "TT_R_MATERIAL_ORG", schema = "")
@SuppressWarnings("serial")
public class TtRMaterialOrgEntity extends IdEntity implements java.io.Serializable {
	/**修改时间*/
	private Date updateDate;
	/**修改人*/
	private String updateName;
	/**创建人*/
	private String createName;
	/**创建时间*/
	private Date createDate;
	/**启用状态*/
	private Integer enableStatus;
	/**是否包含下级*/
	private Integer hasIncludeChild;
	/**关系类型 0表示允许1表示排除*/
	private Integer relationType;
	/**组织名称*/
	private String orgName;
	/**组织编码*/
	private String orgCode;
	/**物料表名称*/
	private String materialName;
	/**物料表编码*/
	private String materialCode;
	/**物料表id*/
	private String materialId;
	
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  修改时间
	 */
	@Column(name ="UPDATE_DATE",nullable=true,length=20)
	public Date getUpdateDate(){
		return this.updateDate;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  修改时间
	 */
	public void setUpdateDate(Date updateDate){
		this.updateDate = updateDate;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  修改人
	 */
	@Column(name ="UPDATE_NAME",nullable=true,length=20)
	public String getUpdateName(){
		return this.updateName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  修改人
	 */
	public void setUpdateName(String updateName){
		this.updateName = updateName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  创建人
	 */
	@Column(name ="CREATE_NAME",nullable=true,length=20)
	public String getCreateName(){
		return this.createName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  创建人
	 */
	public void setCreateName(String createName){
		this.createName = createName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  创建时间
	 */
	@Column(name ="CREATE_DATE",nullable=true,length=20)
	public Date getCreateDate(){
		return this.createDate;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  创建时间
	 */
	public void setCreateDate(Date createDate){
		this.createDate = createDate;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  启用状态
	 */
	@Column(name ="ENABLE_STATUS",nullable=true)
	public Integer getEnableStatus(){
		return this.enableStatus;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  启用状态
	 */
	public void setEnableStatus(Integer enableStatus){
		this.enableStatus = enableStatus;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  是否包含下级
	 */
	@Column(name ="HAS_INCLUDE_CHILD",nullable=true)
	public Integer getHasIncludeChild(){
		return this.hasIncludeChild;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  是否包含下级
	 */
	public void setHasIncludeChild(Integer hasIncludeChild){
		this.hasIncludeChild = hasIncludeChild;
	}
	/**
	 *方法: 取得java.lang.Integer
	 *@return: java.lang.Integer  关系类型
	 */
	@Column(name ="RELATION_TYPE",nullable=false)
	public Integer getRelationType(){
		return this.relationType;
	}

	/**
	 *方法: 设置java.lang.Integer
	 *@param: java.lang.Integer  关系类型
	 */
	public void setRelationType(Integer relationType){
		this.relationType = relationType;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织名称
	 */
	@Column(name ="ORG_NAME",nullable=false,length=100)
	public String getOrgName(){
		return this.orgName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织名称
	 */
	public void setOrgName(String orgName){
		this.orgName = orgName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  组织编码
	 */
	@Column(name ="ORG_CODE",nullable=false,length=32)
	public String getOrgCode(){
		return this.orgCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  组织编码
	 */
	public void setOrgCode(String orgCode){
		this.orgCode = orgCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  物料表名称
	 */
	@Column(name ="MATERIAL_NAME",nullable=false,length=100)
	public String getMaterialName(){
		return this.materialName;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  物料表名称
	 */
	public void setMaterialName(String materialName){
		this.materialName = materialName;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  物料表编码
	 */
	@Column(name ="MATERIAL_CODE",nullable=false,length=50)
	public String getMaterialCode(){
		return this.materialCode;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  物料表编码
	 */
	public void setMaterialCode(String materialCode){
		this.materialCode = materialCode;
	}
	/**
	 *方法: 取得java.lang.String
	 *@return: java.lang.String  物料表id
	 */
	@Column(name ="MATERIAL_ID",nullable=false,length=32)
	public String getMaterialId(){
		return this.materialId;
	}

	/**
	 *方法: 设置java.lang.String
	 *@param: java.lang.String  物料表id
	 */
	public void setMaterialId(String materialId){
		this.materialId = materialId;
	}
}
