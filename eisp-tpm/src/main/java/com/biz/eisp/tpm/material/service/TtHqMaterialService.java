package com.biz.eisp.tpm.material.service;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.log.vo.TmLogVo;
import com.biz.eisp.tpm.material.entity.TtHqMaterialEntity;
import com.biz.eisp.tpm.material.vo.TtHqMaterialVo;

import java.util.List;

/**
 * Created by 肖胜 on 2017/6/24.
 */
public interface TtHqMaterialService extends BaseService{
    /**
     * 分页查询物料列表
     * @param ttHqMaterialVo
     * @param page
     * @return
     */
    List<TtHqMaterialVo> findHqMaterialList(TtHqMaterialVo ttHqMaterialVo, Page page);

    /**
     * 物料列表 报表
     * @param ttHqMaterialVo
     * @param page
     * @return
     */
    List<TtHqMaterialVo> findHqMaterialExportList(TtHqMaterialVo ttHqMaterialVo, Page page);
    /**
     *  保存总部物料
     * @param ttHqMaterialVo
     */
    void saveMaterial(TtHqMaterialVo ttHqMaterialVo);

    /**
     * 删除总部物料
     * @param ids
     */
    void doBatchDel(String ids);
    /**
     * 查询总部物料操作日志
     * @param vo
     * @param page
     * @return
     */
    List<TmLogVo> findLogs(TtHqMaterialVo vo, Page page);

    /**
     * 根据时间跟物料编码查询物料信息
     * @param beginDate
     * @param materialCode
     * @return
     */
    List<TtHqMaterialEntity> selectByBeginDateAndMaterialCode(String orgCode,String beginDate,String endDate,Double bearCost, String materialCode);
}
