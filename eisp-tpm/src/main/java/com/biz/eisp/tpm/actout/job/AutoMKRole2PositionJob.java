package com.biz.eisp.tpm.actout.job;

import com.biz.eisp.api.picture.vo.TsPictureVoT;
import com.biz.eisp.api.taskjob.entity.ScheduleJob;
import com.biz.eisp.api.taskjob.utils.ScheduleUtils;
import com.biz.eisp.api.util.OwnDateUtils;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.base.utils.DateUtils;
import com.biz.eisp.tpm.actout.service.TnAdsSmartService;
import com.biz.eisp.tpm.actout.service.TtActOutService;
import com.biz.eisp.tpm.actout.vo.TtPosRoleVo;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import java.util.Date;
import java.util.List;

/**
 * 白池标    自动为空角色职位赋予默认区域经理角色
 * 2019.01.18
 * */
public class AutoMKRole2PositionJob implements Job{
	
	/* 日志对象 */
    private static final Logger LOG = LoggerFactory.getLogger(AutoMKRole2PositionJob.class);


	private TnAdsSmartService smartService;

	@Override
	public void execute(JobExecutionContext context){
		ScheduleJob scheduleJob = (ScheduleJob)context.getMergedJobDataMap().get(ScheduleUtils.JOB_PARAM_KEY);
		String jobName = scheduleJob.getJobName();
		String jobGroup = scheduleJob.getJobGroup();
		String jobClass = scheduleJob.getJobClass();
		LOG.info("任务["+jobName+"]成功运行——开始  " + DateUtils.format(new Date(), OwnDateUtils.FORMATTER_YMDHMS));
		try {
			ApplicationContext acontext = ApplicationContextUtils.getContext();
			smartService = acontext.getBean(TnAdsSmartService.class);//注入service
			List<TtPosRoleVo> list = null;
			try {
				list = smartService.findPosTaskList();
				if (list != null && list.size() > 0) {
					smartService.createRelationShip(list);
				}
			}catch (Exception e){
				e.printStackTrace();
			}
		} catch (Exception e) {
			LOG.error("任务[" + jobName + "]异常",e);
			e.printStackTrace();
		}
		LOG.info("任务["+jobName+"]成功运行——结束  " + DateUtils.format(new Date(), OwnDateUtils.FORMATTER_YMDHMS));
	}
}
