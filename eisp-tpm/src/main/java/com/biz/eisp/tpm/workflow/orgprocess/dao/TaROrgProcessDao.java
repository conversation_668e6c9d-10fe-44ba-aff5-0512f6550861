package com.biz.eisp.tpm.workflow.orgprocess.dao;

import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.interfacedao.annotation.Arguments;
import com.biz.eisp.base.interfacedao.annotation.InterfaceDao;
import com.biz.eisp.base.interfacedao.annotation.ResultType;
import com.biz.eisp.tpm.workflow.orgprocess.vo.TaROrgProcessVo;

import java.util.List;

/**
 * 组织关系流程Dao
 * Created by wa<PERSON><PERSON><PERSON> on 2017/8/5.
 */
@InterfaceDao
public interface TaROrgProcessDao {
    /**
     * 查询组织关系流程数据
     *
     * @param page
     * @return
     */
    @Arguments({"taROrgProcessVo", "page"})
    @ResultType(TaROrgProcessVo.class)
    public List<TaROrgProcessVo> findTaROrgProcessListDao(TaROrgProcessVo taROrgProcessVo, Page page);

    /**
     * 查询组织关系流程数据 --导出
     *
     * @return
     */
    @Arguments({"taROrgProcessVo"})
    @ResultType(TaROrgProcessVo.class)
    public List<TaROrgProcessVo> findTaROrgProcessExportListDao(TaROrgProcessVo taROrgProcessVo);

}
