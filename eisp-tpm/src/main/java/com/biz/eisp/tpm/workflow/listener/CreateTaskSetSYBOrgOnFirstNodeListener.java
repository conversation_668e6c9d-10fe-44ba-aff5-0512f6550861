package com.biz.eisp.tpm.workflow.listener;

import com.biz.eisp.api.mdm.service.TmOrgApiService;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.mdm.org.vo.TmOrgVo;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设置提交人的事业部组织  {针对流程中突然插入其他组织的审批人}
 * <AUTHOR>
 *
 */
public class CreateTaskSetSYBOrgOnFirstNodeListener implements TaskListener {

    private static final long serialVersionUID = 7930090167155459394L;

    private TmOrgApiService tmOrgApiService;

    public CreateTaskSetSYBOrgOnFirstNodeListener() {
        tmOrgApiService = ApplicationContextUtils.getContext().getBean(TmOrgApiService.class);
    }

    @Override
    @Transactional
    public void notify(DelegateTask delegateTask) {
        //设置提交人的组织  {针对流程中突然插入其他组织的审批人}
        String orgId = (String) delegateTask.getVariable("submitOrgId");
        if(StringUtil.isEmpty(orgId)){
            orgId = ResourceUtil.getCurrPosition().getOrgId(); //当前组织ID
        }

        TmOrgVo orgVo = tmOrgApiService.getSYBOrgVo(orgId);

        if(orgVo == null) {
            throw new BusinessException("未找到发起人所属事业部，请联系管理员配置");
        }

        delegateTask.setVariable("sybOrgCode", orgVo.getOrgCode());
    }
}

