package com.biz.eisp.tpm.workflow.listener;

import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设置提交人的组织  {针对流程中突然插入其他组织的审批人}
 * <AUTHOR>
 *
 */
public class CreateTaskSetOrgOnFirstNodeListener implements TaskListener {

    /**
     *
     */
    private static final long serialVersionUID = 7930090167155459394L;

    @Override
    @Transactional
    public void notify(DelegateTask delegateTask) {
        //设置提交人的组织  {针对流程中突然插入其他组织的审批人}
        String orgId = (String) delegateTask.getVariable("submitOrgId");
        if(StringUtil.isEmpty(orgId)){
            orgId = ResourceUtil.getCurrPosition().getOrgId(); //当前组织ID
        }
        delegateTask.setVariable("submitOrgId", orgId);
    }
}

