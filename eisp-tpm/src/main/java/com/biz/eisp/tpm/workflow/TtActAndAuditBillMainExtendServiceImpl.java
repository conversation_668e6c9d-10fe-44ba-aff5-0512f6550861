package com.biz.eisp.tpm.workflow;

import com.biz.eisp.base.common.service.TtActAndAuditBillMainExtendService;
import com.biz.eisp.base.common.vo.BaseVo;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by liukai on 2017/8/4.
 */
@Service("ttActAndAuditBillMainExtendService")
@Transactional
public class TtActAndAuditBillMainExtendServiceImpl extends BaseServiceImpl implements TtActAndAuditBillMainExtendService {
    @Override
    public String getBusinessKeyByBusinessObjId(String businessObjId) {
        return null;
    }

    @Override
    public String getBusinessKeyByTtAuditBillHeaderId(String businessId) {
        return null;
    }

    @Override
    public List<BaseVo> findTtActByBusinessKey(String businessKey) {
        return null;
    }
}
