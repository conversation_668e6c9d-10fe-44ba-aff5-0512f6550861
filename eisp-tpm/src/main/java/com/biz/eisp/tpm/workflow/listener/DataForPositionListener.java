package com.biz.eisp.tpm.workflow.listener;

import com.biz.eisp.api.act.addressapply.vo.TsActApplyVo;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import com.biz.eisp.tpm.act.core.entity.TtActActivitiHeadEntity;
import com.biz.eisp.tpm.act.sfa.service.TtApplyExcuteWorkFlowService;
import com.biz.eisp.tpm.workflow.listener.dao.TtBpmAuditDao;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;

import java.util.List;

/**
 * 根据业务数据 申请人账号查找对应的岗位监听器.
 * 逻辑：
 * <AUTHOR>
 * @version v1.0
 */
public class DataForPositionListener implements TaskListener {

	//工作流角色管理Service
	private TtBpmAuditDao ttBpmAuditDao;
	private TaskService taskService;
	private TtApplyExcuteWorkFlowService ttApplyExcuteWorkFlowService;
	public DataForPositionListener() {
		ttBpmAuditDao = ApplicationContextUtils.getContext().getBean(TtBpmAuditDao.class);
		taskService = ApplicationContextUtils.getContext().getBean(TaskService.class);
		ttApplyExcuteWorkFlowService=ApplicationContextUtils.getContext().getBean(TtApplyExcuteWorkFlowService.class);
	}

	@Override
	public void notify(DelegateTask delegateTask) {
		String businessKey=delegateTask.getExecution().getBusinessKey();//获取流程业务体字段
		//获取审批信息
		TtActActivitiHeadEntity headEntity = ttApplyExcuteWorkFlowService.get(TtActActivitiHeadEntity.class, businessKey);
		String flagKey = headEntity.getFlagKey();
		TsActApplyVo vo=ttApplyExcuteWorkFlowService.getApplyVoForWorkFlow(flagKey);
		if (vo!=null){
			List<TmPositionVo> pList=	ttApplyExcuteWorkFlowService.findPositionByUserName(vo.getCreateBy(),"");
			try {
				delegateTask.setAssignee(pList.get(0).getPositionCode());
			} catch (Exception e) {
				throw new BusinessException("申请人无岗位");
			}
		}else{
			throw new BusinessException("无法找到对应审核申请人岗位");
		}

	}

}
