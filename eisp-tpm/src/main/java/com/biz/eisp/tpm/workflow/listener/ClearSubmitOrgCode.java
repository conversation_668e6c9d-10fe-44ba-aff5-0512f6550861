package com.biz.eisp.tpm.workflow.listener;

import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.apache.commons.lang3.StringUtils;

/**
 * 清除设置的提交人组织
 * <AUTHOR>
 */
public class ClearSubmitOrgCode implements TaskListener {
    @Override
    public void notify(DelegateTask dt) {
        String orgId = (String) dt.getVariable("submitOrgId");
        if(StringUtils.isNotBlank(orgId)){
            dt.setVariable("submitOrgId","");
        }
    }
}
