package com.biz.eisp.tpm.budget.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.biz.eisp.activiti.runtime.service.TaProcessInstanceService;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.core.service.impl.BaseServiceImpl;
import com.biz.eisp.log.entity.TmLogEntity;
import com.biz.eisp.log.vo.OperationType;
import com.biz.eisp.mdm.dict.util.DictUtil;
import com.biz.eisp.mdm.org.entity.TmOrgEntity;
import com.biz.eisp.tpm.budget.account.entity.TtFinancialAccountEntity;
import com.biz.eisp.tpm.budget.bill.entity.TtBudgetBillEntity;
import com.biz.eisp.tpm.budget.bill.service.TtBudgetBillAdjustService;
import com.biz.eisp.tpm.budget.bill.service.TtBudgetBillService;
import com.biz.eisp.tpm.budget.bill.service.TtBudgetDetailService;
import com.biz.eisp.tpm.budget.bill.transformer.TtBudgetBillEntityToTtBudgetBillVo;
import com.biz.eisp.tpm.budget.bill.transformer.TtBudgetBillVoToTtBudgetBillEntity;
import com.biz.eisp.tpm.budget.bill.vo.TtBudgetBillVo;
import com.biz.eisp.tpm.budget.bill.vo.TtBudgetDetailVo;
import com.biz.eisp.tpm.budget.core.BudgetManager;
import com.biz.eisp.tpm.budget.fyear.service.TtFinancialYearService;
import com.biz.eisp.tpm.budget.fyear.vo.TtFinancialYearVo;
import com.biz.eisp.tpm.budget.period.vo.TtBudgetPeriodVo;
import com.biz.eisp.api.util.Globals;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

/** 
 * 费用预算：预算调整service实现
 * <p>
 * <AUTHOR>
 * @version v1.0
 */
@Service("ttBudgetBillAdjustService")
@Transactional
public class TtBudgetBillAdjustServiceImpl extends BaseServiceImpl implements TtBudgetBillAdjustService{
	
	//调入
	private static Integer ADJUST_IN = 1;
	//调出
	private static Integer ADJUST_OUT = -1;
	//预算类型
	private static String BUDGET_BUSINESS_ADJUST = "40";	
	
	//预算明细service
	@Autowired
	private TtBudgetDetailService ttBudgetDetailService;
	
	//预算
	@Autowired
	private TtBudgetBillService billService;
	
	//财年service
	@Autowired
	private TtFinancialYearService yearService;
	
	//预算管理
	@Autowired
	private BudgetManager budgetManager;
	
	@Autowired
	private TaProcessInstanceService taProcessInstanceService;
	
	
	public TtBudgetPeriodVo createBudgetPeriodVo(TtBudgetPeriodVo vo){
		
		String year = vo.getYear();
		String month = vo.getMonth();
		String orgCode = vo.getOrgCode();
		String accountCode = vo.getAccountCode();
		TmOrgEntity orgEntity = this.findUniqueByProperty(TmOrgEntity.class,"orgCode",orgCode);
		TtFinancialAccountEntity accountEntity = this.findUniqueByProperty(TtFinancialAccountEntity.class, "accountCode", accountCode);
		
		//计算余额
		BigDecimal balance = budgetManager.getBudgetBalance(year, month, orgCode, accountCode);
		
		vo.setOrgId(orgEntity.getId());
		vo.setOrgName(orgEntity.getOrgName());
		vo.setOrgCode(orgEntity.getOrgCode());
		vo.setAccountName(accountEntity.getAccountName());
		
		vo.setBalance(balance);
		return vo;
	}
	
	//查询：通过年+季度+组织+费用类型，计算可以调出的金额
	@Override
	public BigDecimal getBudgetOutAmount(TtBudgetPeriodVo vo) {
		String year = vo.getYear();
		String month = vo.getMonth();
		String orgCode = vo.getOrgCode();
		String accountCode = vo.getAccountCode();
		//余额
		BigDecimal balanceAmount = budgetManager.getBudgetBalance(year, month, orgCode, accountCode);
			
		return balanceAmount;
	}
	
	//保存-费用预算调整数据保存
	@Override
	public void saveBudgetBillBalance(String outJson,String inJson) {
		//调出
		TtBudgetBillVo outBill = this.parseBudgetBillJson(outJson);
		//调入
		TtBudgetBillVo inBill = this.parseBudgetBillJson(inJson);
		
		TtBudgetBillEntity entityOut = new TtBudgetBillVoToTtBudgetBillEntity(this).apply(outBill);
		TtBudgetBillEntity entityIn = new TtBudgetBillVoToTtBudgetBillEntity(this).apply(inBill);
		
		TmOrgEntity orgEntity = this.findUniqueByProperty(TmOrgEntity.class, "orgCode", entityOut.getOrgCode());
		//构建工作流相关信息：走调入方，调出方可以根据relationId获取到
		
		
		//检查调出方的余额是否够用
		String year = entityOut.getYear();
		String month = entityOut.getMonth();
		String orgCode = entityOut.getOrgCode();
		String accountCode = entityOut.getAccountCode();
		TtBudgetPeriodVo quaryVo = new TtBudgetPeriodVo();
		quaryVo.setYear(year);
		quaryVo.setMonth(month);
		quaryVo.setOrgCode(orgCode);
		quaryVo.setAccountCode(accountCode);
		BigDecimal balanceAmount = billService.getBudgetBalance(quaryVo);
		
		//调出金额
		BigDecimal outAmount = entityOut.getAdjustAmount();
		
		if(balanceAmount != null && outAmount != null){
			if(outAmount.compareTo(balanceAmount) > 0){
				throw new BusinessException("调出金额大约可用余额，不能进行预算调整");
			}
		}else{
			throw new BusinessException("数据错误");
		}
		
		
		
		//预算调整
		entityOut.setBillType(Globals.BUDGET_BILL_ADJ);
		entityIn.setBillType(Globals.BUDGET_BILL_ADJ);

		//保存预算,调出方
		this.saveOrUpdate(entityOut);

		//将调出方的id与调入关联起来
		entityIn.setRelationId(entityOut.getId());
		this.saveOrUpdate(entityIn);

		entityOut.setRelationId(entityIn.getId());
		this.saveOrUpdate(entityOut);

		//预算调整自定义
		this.saveLogIn(entityIn);
		this.saveLogOut(entityOut);

		//保存预算明细表:调出
		this.saveBudgetDetail(entityOut, entityOut.getAdjustFlag());
		this.saveBudgetDetail(entityIn, entityIn.getAdjustFlag());
	}
	
	//查询-根据id查询出调出预算和调入预算
	@Override
	public TtBudgetBillVo[] getBudgetAdjustsById(String businessObjId) {
		//根据调入预算id查询
		TtBudgetBillEntity budgetInEntity = this.get(TtBudgetBillEntity.class, businessObjId);
		//调出预算id
		String outId = budgetInEntity.getRelationId();
		//调出预算实体
		TtBudgetBillEntity budgetOutEntity = this.get(TtBudgetBillEntity.class, outId);
		
		//调入预算vo
		TtBudgetBillVo budgetInVo = new TtBudgetBillEntityToTtBudgetBillVo().apply(budgetInEntity);
		//调出预算vo
		TtBudgetBillVo budgetOutVo = new TtBudgetBillEntityToTtBudgetBillVo().apply(budgetOutEntity);
		
		//返回结果集
		TtBudgetBillVo[] result = new TtBudgetBillVo[2];
		result[0] = budgetInVo;
		result[1] = budgetOutVo;
		
		return result;
	}
	
	/**
	 * 费用预算：解析-调整的json数据
	 * <AUTHOR>
	 * @param jsonData
	 */
	private TtBudgetBillVo parseBudgetBillJson(String jsonData){
		JSONObject json = JSON.parseObject(jsonData);
		
		//调入调出标识
		int adjustFlag = json.getInteger("adjustFlag");
		//年
		String year = json.getString("year");
		//月份
		String month = json.getString("month");
		//组织id
		String orgId = json.getString("orgId");
		//费用类型编码
		String accountCode = json.getString("accountCode");
		//期初金额
		BigDecimal periodAmount = json.getBigDecimal("periodAmount");
		//可用余额
		BigDecimal beforeAdjustBalance = json.getBigDecimal("beforeAdjustBalance");
		//调整金额
		BigDecimal adjustAmount = json.getBigDecimal("adjustAmount");
		//调整后金额
		BigDecimal afterAdjustBalance = json.getBigDecimal("afterAdjustBalance");
		
		String remark = json.getString("remark");
		
		TtBudgetBillVo billVo = new TtBudgetBillVo();
		//预算调整
		billVo.setBillType(Globals.BUDGET_BILL_ADJ);
		billVo.setYear(year);
		billVo.setMonth(month);
		
		TmOrgEntity orgEntity = this.get(TmOrgEntity.class, orgId);
		billVo.setOrgCode(orgEntity.getOrgCode());
		billVo.setOrgName(orgEntity.getOrgName());
		
		TtFinancialAccountEntity accountEntity = this.findUniqueByProperty(TtFinancialAccountEntity.class, "accountCode", accountCode);
		billVo.setAccountCode(accountEntity.getAccountCode());
		billVo.setAccountName(accountEntity.getAccountName());
		
		billVo.setPeriodAmount(periodAmount);
		billVo.setBeforeAdjustBalance(beforeAdjustBalance);
		billVo.setAfterAdjustBalance(afterAdjustBalance);
		billVo.setAdjustAmount(adjustAmount);
		
		billVo.setAdjustFlag(adjustFlag);
		
		billVo.setRemark(remark);
		
		return billVo;
	}

	/**
	 * 费用预算：构建-预算明细
	 * <AUTHOR>
	 * @param entity
	 * @param adjustFlag
	 */
	private void saveBudgetDetail(TtBudgetBillEntity entity, Integer adjustFlag) {
		TtBudgetDetailVo vo = new TtBudgetDetailVo();
		
		if(adjustFlag == ADJUST_IN){//调入
			vo.setAmount(entity.getAdjustAmount());
		}else if(adjustFlag == ADJUST_OUT){//调出
			vo.setAmount(BigDecimal.ZERO.subtract(entity.getAdjustAmount()));//调出方是负数
		}
		//预算调整
		vo.setBusinessTypeCode(Globals.BUDGET_BILL_ADJ);//预算调整
		vo.setBusinessTypeName(DictUtil.getDictDataValueByCode("budget_business_type", BUDGET_BUSINESS_ADJUST));
		
		//费用类型编码
		vo.setAccountCode(entity.getAccountCode());
		vo.setAccountName(entity.getAccountName());
		
		//组织编码
		vo.setOrgCode(entity.getOrgCode());
		//组织名称
		vo.setOrgName(entity.getOrgName());
		//季度
		vo.setMonth(entity.getMonth());
		//年
		vo.setYear(entity.getYear());
		vo.setYearMonth(entity.getYear() + "-" + entity.getMonth());
		//凭证编码
		vo.setVoucherCode(entity.getBudgetCode());
		
		String year = entity.getYear();
		String month = entity.getMonth();
		TtFinancialYearVo fYear = yearService.getTtFinancialYearByYearMonth(year, month);
		
		if(fYear == null){
			throw new BusinessException("根据年:"+year+",月:"+month+"没有找到对应的财年");
		}
		vo.setBudgetSourceId(entity.getId());
		//保存明细
		ttBudgetDetailService.saveTtBudgetDetailNoValidate(vo);
	}

	//校验-预算调整中选择的年+季度+部门+费用类型是否在预算中
	@Override
	public void validateChooseBudgetIsExistence(TtBudgetPeriodVo vo) {
		if(StringUtils.isNotBlank(vo.getOrgId())){
			TmOrgEntity orgEntity = this.get(TmOrgEntity.class, vo.getOrgId());
			String orgCode = orgEntity.getOrgCode();
			String year = vo.getYear();
			String month = vo.getMonth();
			String accountCode = vo.getAccountCode();
			
			String sql = " select count(1) from tt_budget_period t "+
			             " where t.year = ? and t.month = ? and t.org_code = ? and t.account_code = ? ";
			Long num = this.getCountForJdbcParam(sql, year,month,orgCode,accountCode);
			if(num == 0){
				TtFinancialAccountEntity account = this.findUniqueByProperty(TtFinancialAccountEntity.class, "accountCode", accountCode);
				throw new BusinessException("所选择的年：" + vo.getYear() + ",月份：" + vo.getMonth() + ",组织：" + orgEntity.getOrgName()
				+ ",预算科目:" + account.getAccountName() + "没有存在预算中，请重新选择");
			}
		}
	}
	

	/**
	 * 预算调整日志自定义
	 * @param entityIn  调入方
	 * <AUTHOR>
	 */
	private void saveLogIn(TtBudgetBillEntity entityIn) {
		Date createDate = new Date();
		TtFinancialAccountEntity costEntity = billService.findUniqueByProperty(TtFinancialAccountEntity.class, "accountCode", entityIn.getAccountCode());
		String content = "";
		content +=  "<br>字段[<b>预算编码</b>]:" + entityIn.getBudgetCode() + "</br>" +
					"字段[<b>预算类型</b>]:预算调整" + "</br>" +
					"字段[<b>年</b>]:" + entityIn.getYear() + "</br>" +
					"字段[<b>月</b>]:" + entityIn.getMonth() + "</br>" +
					"字段[<b>组织名称</b>]:" + entityIn.getOrgName() + "</br>" +
					"字段[<b>预算科目</b>]:" + costEntity.getAccountName() + "</br>" +
					"字段[<b>期初金额</b>]:" + entityIn.getPeriodAmount() + "</br>" +
					"字段[<b>调整前余额</b>]:" + entityIn.getBeforeAdjustBalance() + "</br>" +
					"字段[<b>调整后余额</b>]:" + entityIn.getAfterAdjustBalance() + "</br>" +
					"字段[<b>调增金额</b>]:" + entityIn.getAdjustAmount() + "</br>";

		if (StringUtil.isNotEmpty(entityIn.getRemark())) {
			content += "字段[<b>备注</b>]:" + entityIn.getRemark() + "</br>";
		}else{
			content += "字段[<b>备注</b>]:空</br>";
		}
		content += "字段[<b>调整标志</b>]:调增<br>";
		TmLogEntity log = new TmLogEntity();
		log.setContent(content);
		log.setOperationType(OperationType.INSERT.toString());
		log.setBusinessDesc("预算调整");
		log.setBusinessId(entityIn.getId());
		log.setPositionName(ResourceUtil.getCurrPosition().getPositionName());
		this.save(log);
	}
	/**
	 * 预算调整日志自定义<p>
	 * @param entityOut 调出方
	 * <AUTHOR>
	 */
	private void saveLogOut(TtBudgetBillEntity entityOut) {
		Date createDate = new Date();
		TtFinancialAccountEntity costEntity = billService.findUniqueByProperty(TtFinancialAccountEntity.class, "accountCode", entityOut.getAccountCode());
		String content = "";
		content +=  "<br>字段[<b>预算编码</b>]:" + entityOut.getBudgetCode() + "</br>" +
					"字段[<b>预算类型</b>]:预算调整" + "</br>" +
					"字段[<b>年</b>]:" + entityOut.getYear() + "</br>" +
					"字段[<b>月</b>]:" + entityOut.getMonth() + "</br>" +
					"字段[<b>组织名称</b>]:" + entityOut.getOrgName() + "</br>" +
					"字段[<b>预算科目</b>]:" + costEntity.getAccountName() + "</br>" +
					"字段[<b>期初金额</b>]:" + entityOut.getPeriodAmount() + "</br>" +
					"字段[<b>调整前余额</b>]:" + entityOut.getBeforeAdjustBalance() + "</br>" +
					"字段[<b>调整后余额</b>]:" + entityOut.getAfterAdjustBalance() + "</br>" +
					"字段[<b>调减金额</b>]:" + entityOut.getAdjustAmount() + "</br>";

		if (StringUtil.isNotEmpty(entityOut.getRemark())) {
			content += "字段[<b>备注</b>]:" + entityOut.getRemark() + "</br>";
		}else{
			content += "字段[<b>备注</b>]:空</br>";
		}
		content += "字段[<b>调整标志</b>]:调减<br>";
		TmLogEntity log = new TmLogEntity();
		log.setContent(content);
		log.setOperationType(OperationType.INSERT.toString());
		log.setBusinessDesc("预算调整");
		log.setBusinessId(entityOut.getId());
		log.setPositionName(ResourceUtil.getCurrPosition().getPositionName());
		this.save(log);
	}

}
