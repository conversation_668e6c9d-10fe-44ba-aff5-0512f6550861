package com.biz.eisp.tpm.trainadbaseaudit.service;

import com.biz.eisp.api.activiti.vo.KeyindicatorBPM009Vo;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.service.BaseService;
import com.biz.eisp.tpm.act.core.vo.TtActBillMainWorkFlowVo;
import com.biz.eisp.tpm.trainadbaseaudit.vo.TtTrainAdBaseAuditVo;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * 高铁普列核销申请工作流审批Service
 * Created by xuduan on 2018/01/05.
 */
public interface TtTrainAuditWorkFlowService extends BaseService {


    /**
     * 查询工作流业务数据，提交时的活动列表
     *
     * @param ttProductActVo
     * @param flagKey
     * @param page
     * @return
     */
    public List<TtTrainAdBaseAuditVo> findTtProductActWorkFlowList(TtTrainAdBaseAuditVo ttProductActVo, String flagKey, Page page);

    /**
     * 查询工作流业务数据，提交时的活动列表 导出Excel
     *
     * @param ttProductActVo
     * @param flagKey
     * @return
     */
    public List<TtTrainAdBaseAuditVo> findTtProductActExcelWorkFlowList(TtTrainAdBaseAuditVo ttProductActVo, String flagKey);

    /**
     * 查询高铁普列广告核报数据
     *
     * @param businessObjId
     * @param flagKey
     * @return
     */
    public List<KeyindicatorBPM009Vo> findKeyindicatorBPM009(String businessObjId, String flagKey);

    /**
     * 查询工作流日志列表
     *
     * @param vo
     * @return
     */
    public List<TtActBillMainWorkFlowVo> findProductActWorkflowLogList(TtActBillMainWorkFlowVo vo);

    /**
     * 额外跳转
     * @param businessObjId
     * @param isKeyIndicators
     * @param type
     * @return
     */
    public ModelAndView getExtraWorkFlowModelAndView(String businessObjId, String isKeyIndicators, String type);


}
