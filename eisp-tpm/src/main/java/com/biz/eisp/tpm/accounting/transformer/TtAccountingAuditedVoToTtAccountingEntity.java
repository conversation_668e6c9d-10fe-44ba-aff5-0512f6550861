package com.biz.eisp.tpm.accounting.transformer;

import com.biz.eisp.base.common.util.ResourceUtil;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.generatednum.num.util.TbNumRuleProvider;
import com.biz.eisp.tpm.accounting.entity.TtAccountingEntity;
import com.biz.eisp.tpm.accounting.service.TtAccountingService;
import com.biz.eisp.tpm.accounting.vo.TtAccountingAuditedVo;
import com.biz.eisp.tpm.audit.core.entity.TtAuditEntity;
import com.biz.eisp.api.util.Globals;
import com.google.common.base.Function;

/**
 * Created by liukai on 2017/8/15.
 */
public class TtAccountingAuditedVoToTtAccountingEntity
        implements Function <TtAccountingAuditedVo,TtAccountingEntity> {

    private TtAccountingService ttAccountingService;

    private TbNumRuleProvider tbNumRuleProvider;

    public TtAccountingAuditedVoToTtAccountingEntity(TtAccountingService ttAccountingService) {
        this.ttAccountingService = ttAccountingService;
        this.tbNumRuleProvider = ApplicationContextUtils.getContext().getBean(TbNumRuleProvider.class);
    }

    @Override
    public TtAccountingEntity apply(TtAccountingAuditedVo vo) {
        TtAccountingEntity entity = new TtAccountingEntity();

        TtAuditEntity auditEntity = ttAccountingService.get(TtAuditEntity.class, vo.getId());

        this.copyProperties(auditEntity, entity);

        return entity;
    }

    private void copyProperties(TtAuditEntity auditEntity, TtAccountingEntity entity) {
        entity.setHandleStatus(0);
        entity.setSupplyProductCode(auditEntity.getPremiumProductCode());
        entity.setSupplyProductName(auditEntity.getPremiumProductName());
        entity.setCreatePositionCode(ResourceUtil.getCurrPosition().getPositionCode());
        entity.setCreatePositionName(ResourceUtil.getCurrPosition().getPositionName());
        entity.setCreateOrgCode(ResourceUtil.getCurrOrg().getOrgCode());
        entity.setCreateOrgName(ResourceUtil.getCurrOrg().getOrgName());
//        entity.setGroupNo(vo);
        entity.setSybOrgCode(auditEntity.getBusinessUnitCode());
        entity.setSybOrgName(auditEntity.getBusinessUnitName());
        entity.setAuditCreateName(auditEntity.getCreateName());
//        entity.setActCreateName();
        entity.setAuditPositionCode(auditEntity.getPositionCode());
        entity.setAuditPositionName(auditEntity.getPositionName());
        entity.setAccountingStatus(0);
        entity.setAccountingAmount(null);
        entity.setAuditAmount(auditEntity.getRealAuditAmount());
        entity.setActAmount(auditEntity.getPlanAmount());
        entity.setCostClassifyCode(auditEntity.getCostClassifyCode());
        entity.setCostClassifyName(auditEntity.getCostClassifyName());
        entity.setPaymentCode(auditEntity.getPaymentCode());
        entity.setPaymentName(auditEntity.getPaymentName());
        entity.setActModeCode(auditEntity.getActModeCode());
        entity.setActModeName(auditEntity.getActModeName());
        entity.setActTypeCode(auditEntity.getActTypeCode());
        entity.setActTypeName(auditEntity.getActTypeName());
        entity.setFinancialAccountCode(auditEntity.getFinancialAccountCode());
        entity.setFinancialAccountName(auditEntity.getFinancialAccountName());
        entity.setCostAccountCode(auditEntity.getCostAccountCode());
        entity.setCostAccountName(auditEntity.getCostAccountName());
        entity.setCostTypeCode(auditEntity.getCostTypeCode());
        entity.setCostTypeName(auditEntity.getCostTypeName());
        entity.setOrgCode(auditEntity.getOrgCode());
        entity.setOrgName(auditEntity.getOrgName());
        entity.setErpCode(auditEntity.getErpCode());
        entity.setCustomerCode(auditEntity.getCustomerCode());
        entity.setCustomerName(auditEntity.getCustomerName());
        entity.setYearMonth(auditEntity.getYearMonth());
//        entity.setActPositionCode();
//        entity.setActPositionName();
        entity.setBeginDate(auditEntity.getBeginDate());
        entity.setEndDate(auditEntity.getEndDate());
        entity.setActName(auditEntity.getActName());
        entity.setActCode(auditEntity.getActCode());
        entity.setAuditCode(auditEntity.getAuditCode());
//        entity.setAccountingCode();
        entity.setProductCode(auditEntity.getProductCode());
        entity.setProductName(auditEntity.getProductName());
        entity.setAccountingedAmount(auditEntity.getAccountingAmount());
        entity.setSapCostCenter(auditEntity.getSapCostCenter());

        entity.setAccountingCode(tbNumRuleProvider.getMaxNum(Globals.TT_ACCOUNTING_CODE));
    }
}
