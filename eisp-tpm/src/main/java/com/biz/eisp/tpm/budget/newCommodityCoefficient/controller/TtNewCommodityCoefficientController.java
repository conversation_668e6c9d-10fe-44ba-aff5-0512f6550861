package com.biz.eisp.tpm.budget.newCommodityCoefficient.controller;

import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.jsonmodel.AjaxJson;
import com.biz.eisp.base.common.jsonmodel.DataGrid;
import com.biz.eisp.base.core.page.EuPage;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.base.utils.DateUtils;
import com.biz.eisp.tpm.budget.newCommodityCoefficient.entity.TtNewCommodityCoefficientEntity;
import com.biz.eisp.tpm.budget.newCommodityCoefficient.service.TtNewCommodityCoefficientService;
import com.biz.eisp.tpm.budget.newCommodityCoefficient.transformer.TtNewCommodityCoefficientEntityToTtNewCommodityCoefficientVo;
import com.biz.eisp.tpm.budget.newCommodityCoefficient.vo.TtNewCommodityCoefficientVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 货补系数配置Controller
 * Created by wangjie on 2017/9/7.
 * ttNewCommodityCoefficientController.do?goTtNewCommodityCoefficientMain
 */
@Scope("prototype")
@Controller
@RequestMapping("/ttNewCommodityCoefficientController")
public class TtNewCommodityCoefficientController extends BaseController {
    @Autowired
    private TtNewCommodityCoefficientService ttNewCommodityCoefficientService;


    /**
     * 跳转到货补系数配置主页
     *
     * @param vo
     * @param request
     * @return
     */
    @RequestMapping(params = "goTtNewCommodityCoefficientMain")
    public ModelAndView goTtNewCommodityCoefficientMain(TtNewCommodityCoefficientVo vo, HttpServletRequest request) {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/budget/newCommodityCoefficient/ttNewCommodityCoefficientMain");
        String list = "ttNewCommodityCoefficientController.do?findTtNewCommodityCoefficientList";
        view.addObject("url", list);
        view.addObject("nowYearMonth", DateUtils.yyyy_MM.format(new Date()));
        return view;
    }

    /**
     * 跳转到货补系数配置编辑页面
     *
     * @param vo
     * @param request
     * @return
     */
    @RequestMapping(params = "goTtNewCommodityCoefficientForm")
    public ModelAndView goTtNewCommodityCoefficientForm(TtNewCommodityCoefficientVo vo, HttpServletRequest request) {
        ModelAndView view = new ModelAndView("com/biz/eisp/tpm/budget/newCommodityCoefficient/ttNewCommodityCoefficientForm");
        if (StringUtils.isNotBlank(vo.getId())) {
            TtNewCommodityCoefficientEntity entity = ttNewCommodityCoefficientService.get(TtNewCommodityCoefficientEntity.class, vo.getId());
            TtNewCommodityCoefficientVo commodityCoefficientVo = new TtNewCommodityCoefficientEntityToTtNewCommodityCoefficientVo().apply(entity);
            view.addObject("commodityCoefficientVo", commodityCoefficientVo);
        }
        return view;
    }

    /**
     * 货补系数配置请求所有数据
     *
     * @param vo
     * @param request
     * @return
     */
    @RequestMapping(params = "findTtNewCommodityCoefficientList")
    @ResponseBody
    public DataGrid findTtNewCommodityCoefficientList(TtNewCommodityCoefficientVo vo, HttpServletRequest request) {
        Page page = new EuPage(request);
        List<TtNewCommodityCoefficientVo> incomeStatementsVos = ttNewCommodityCoefficientService.findTtNewCommodityCoefficientList(vo, page);
        return new DataGrid(incomeStatementsVos, page);
    }

    /**
     * 保存或编辑货补系数配置
     *
     * @return
     */
    @RequestMapping(params = "saveTtNewCommodityCoefficient")
    @ResponseBody
    public AjaxJson saveTtNewCommodityCoefficient(TtNewCommodityCoefficientVo vo) {
        AjaxJson aj = new AjaxJson();
        try {
            ttNewCommodityCoefficientService.saveTtNewCommodityCoefficient(vo);
        } catch (Throwable t) {
            aj.setMsg(t.getMessage());
            aj.setSuccess(false);
            t.printStackTrace();
        }
        return aj;
    }

    /**
     * 删除货补系数配置
     *
     * @return
     */
    @RequestMapping(params = "deleteTtNewCommodityCoefficient")
    @ResponseBody
    public AjaxJson deleteTtNewCommodityCoefficient(String id) {
        AjaxJson aj = new AjaxJson();
        try {
            ttNewCommodityCoefficientService.deleteTtNewCommodityCoefficient(id);
        } catch (Throwable t) {
            aj.setMsg(t.getMessage());
            aj.setSuccess(false);
            t.printStackTrace();
        }
        return aj;
    }

    /**
     * 货补系数配置导出
     *
     * @param vo
     * @param request
     * @param response
     */
    @RequestMapping(params = "exportXls")
    public void exportRebatePoolXls(TtNewCommodityCoefficientVo vo, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<TtNewCommodityCoefficientVo> ttNewCommodityCoefficientList = ttNewCommodityCoefficientService.findTtNewCommodityCoefficientList(vo, null);
            doExportXls(response, request, ttNewCommodityCoefficientList, TtNewCommodityCoefficientVo.class, "货补系数配置");
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("货补系数配置导出异常！请联系管理员");
        }
    }

}

