package com.biz.eisp.tpm.workflow.orgposition.entity;

import com.biz.eisp.base.common.identity.IdEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 *流程与关键指标Entity
 */
@Entity
@Table(name = "ta_processkey_keyindicator", schema = "")
public class TaProcessKeyKeyindicatorEntity extends IdEntity implements Serializable {
    /**
     * 流程key
     */
    private String processKey;
    /**
     * 关键指标编码
     */
    private String keyindicator;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createDate;
    @Column(name = "PROCESS_KEY ", nullable = true)
    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }
    @Column(name = "KEYINDICATOR ", nullable = true)
    public String getKeyindicator() {
        return keyindicator;
    }

    public void setKeyindicator(String keyindicator) {
        this.keyindicator = keyindicator;
    }
    @Column(name = "CREATE_NAME", nullable = true)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    @Column(name = "CREATE_DATE ", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
