package com.biz.eisp.tpm.workflow.orgposition.transformer;

import com.biz.eisp.base.common.util.MyBeanUtils;
import com.biz.eisp.tpm.material.entity.TtHqMaterialEntity;
import com.biz.eisp.tpm.material.vo.TtHqMaterialVo;
import com.biz.eisp.tpm.workflow.orgposition.entity.OrgPositionEntity;
import com.biz.eisp.tpm.workflow.orgposition.vo.OrgPositionVo;
import com.google.common.base.Function;

/**
 * 组织职位关系实体转vo
 */
public class OrgPositionEntityToOrgPositionVo implements Function<OrgPositionEntity, OrgPositionVo> {
    @Override
    public OrgPositionVo apply(OrgPositionEntity entity) {
        OrgPositionVo vo = new OrgPositionVo();
        try {
            MyBeanUtils.copyBeanNotNull2Bean(entity, vo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return vo;
    }
}
