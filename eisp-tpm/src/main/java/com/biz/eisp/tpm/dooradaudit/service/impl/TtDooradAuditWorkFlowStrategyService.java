package com.biz.eisp.tpm.dooradaudit.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.biz.eisp.activiti.designer.businessconf.entity.TaProcessVariableEntity;
import com.biz.eisp.activiti.runtime.vo.TaProcessThemeVo;
import com.biz.eisp.base.common.exception.BusinessException;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.base.utils.ApplicationContextUtils;
import com.biz.eisp.mdm.actrole.service.TmActRoleService;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import com.biz.eisp.tpm.act.core.entity.TtActActivitiHeadEntity;
import com.biz.eisp.tpm.act.core.service.TtActWorkFlowService;
import com.biz.eisp.tpm.act.core.service.TtActWorkFlowStrategyService;
import com.biz.eisp.tpm.act.core.vo.TtActWorkFlowInfoVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 户外广告核销工作流提交操作策略。
 *
 * <AUTHOR>
 * @create 2018-01-05 下午1:41
 */
public class TtDooradAuditWorkFlowStrategyService extends TtActWorkFlowStrategyService {
    @Override
    public String getCopyHiDataProcName() {
        //保存业务数据到历史表中
        return "pt_hi_door_act_audit";
    }

    @Override
    public void initWorkFlow(TtActWorkFlowService ttActWorkFlowService, TtActWorkFlowInfoVo ttActWorkFlowInfoVo, TaProcessThemeVo vo) {
        JSONObject obj = JSONObject.parseObject(vo.getParams());
        String processKey = obj.getString("processKey");
        //开发过程默认使用test流程
        ttActWorkFlowInfoVo.setProcessKey(processKey);
        ttActWorkFlowInfoVo.setProcessEndServiceName("ttDooradAuditProcessEndListener");
        ttActWorkFlowInfoVo.setHeadCode(processKey);
        ttActWorkFlowInfoVo.setHeadName("门头广告核销申请");
        ttActWorkFlowInfoVo.setProcessTitle(vo.getName());
        ttActWorkFlowInfoVo.setProcessDetail(vo.getDetail());
    }

    @Override
    public ModelAndView getWorkFlowModelAndView(TtActWorkFlowService ttActWorkFlowService, String businessObjId, String isKeyIndicators, String type) {
        if(StringUtil.isNotBlank(isKeyIndicators) && "true".equals(isKeyIndicators)) {
            TtActActivitiHeadEntity headEntity = ttActWorkFlowService.get(TtActActivitiHeadEntity.class, businessObjId);
            String keyIndicatorsCode = ttActWorkFlowService.getKeyIndicators(headEntity.getHeadCode());
//            ModelAndView modelAndView = new ModelAndView("com/biz/eisp/tpm/act/policy/keyindicator/keyindicator_" + keyIndicatorsCode);
            ModelAndView  modelAndView = new ModelAndView("com/biz/eisp/tpm/keyindicator/keyindicator_P07");
            modelAndView.addObject("flagKey", headEntity.getFlagKey());
            return modelAndView;
        } else {
            TtActActivitiHeadEntity headEntity = ttActWorkFlowService.get(TtActActivitiHeadEntity.class, businessObjId);
            String keyIndicatorsCode = ttActWorkFlowService.getKeyIndicators(headEntity.getHeadCode());
            boolean hiddenCustomerYearTaskAmount = "P05".equals(keyIndicatorsCode) ? false : true;
            ModelAndView modelAndView = new ModelAndView("com/biz/eisp/tpm/act/policy/ttProductActMain_workFlow");
            modelAndView.addObject("flagKey", headEntity.getFlagKey());
            modelAndView.addObject("hiddenCustomerYearTaskAmount", hiddenCustomerYearTaskAmount);
            return modelAndView;
        }
    }

    @Override
    public void updateBpmStatus(TtActWorkFlowService ttActWorkFlowService, String flagKey, String ids, String closeWorkflow) {
//        String sql = "UPDATE tt_door_ad_audit a SET bpm_status = 2,update_date=sysdate WHERE " + this.getIds(ids);
        String sql = "UPDATE tt_door_ad_audit a SET bpm_status = 2,update_date=sysdate WHERE id= '"+ ids+"'";
        ttActWorkFlowService.executeSql(sql);
    }



    /**
     * 获取要更新的id sql
     * @param ids
     * @return
     */
    private String getIds(String ids) {
        if(StringUtils.isBlank(ids)) {
            return "1 = 2";
        }

        String [] splitArr = ids.split(",");

        String sqlCondition = " 1=1 "; //要返回的sql条件

        int separateNum = 3; //划分条件的数量，oracle是1000，这里设置为1000
        int indexVal = 0;
        String sql = "";
        boolean isFirst = true;
        for(String val : splitArr) {
            indexVal++;
            sql += "'" + val + "'" + ",";
            if(separateNum == indexVal) {
                indexVal = 0;
                if(isFirst) {
                    sqlCondition += " AND (a.audit_Code IN (" + sql.substring(0, sql.length() - 1) + ")";
                    isFirst = false;
                } else {
                    sqlCondition += " OR a.audit_Code IN (" + sql.substring(0, sql.length() - 1) + ")";
                }
                sql = "";
            }
        }

        if(separateNum != indexVal && indexVal != 0) {
            if(isFirst) {
                sqlCondition += " AND (a.audit_Code IN (" + sql.substring(0, sql.length() - 1) + ")";
            } else {
                sqlCondition += " OR a.audit_Code IN (" + sql.substring(0, sql.length() - 1) + ")";
            }
        }

        return sqlCondition + ")";
    }

    public Map<String, Object> buildStartWorkFlowVariables(TtActWorkFlowService ttActWorkFlowService,
                                                           String businessKey) {
        TtActActivitiHeadEntity headEntity = ttActWorkFlowService.get(TtActActivitiHeadEntity.class, businessKey);

        if("NF001".equals(headEntity.getHeadCode())) {
            List <String> personList = getSignPositionList(ttActWorkFlowService, headEntity.getFlagKey());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("persons", personList);
            return map;
        }

        if("NF023".equals(headEntity.getHeadCode())
                || "NF024".equals(headEntity.getHeadCode())
                || "NF025".equals(headEntity.getHeadCode())
                || "NF026".equals(headEntity.getHeadCode())) {
            List <String> personList = getRelationAndRoleList(ttActWorkFlowService,
                        headEntity.getFlagKey(), headEntity.getHeadCode());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("persons", personList);
            return map;
        }
        return null;
    }

    /**
     * 查询产品系列会签职位
     * @param ttActWorkFlowService
     * @param flagKey
     * @return
     */
    private List <String> getSignPositionList(TtActWorkFlowService ttActWorkFlowService, String flagKey) {
        String sql = "SELECT * FROM (" +
                "SELECT p.product_attribute4 AS position_code FROM tm_product p WHERE p.product_level = '04' START WITH p.product_code IN (" +
                "       SELECT a.product_code FROM tt_hi_product_act a WHERE a.flag_key = ? AND a.product_level_flag = 'Y'" +
                ") CONNECT BY PRIOR p.id= p.parent_id" +
                " UNION " +
                "SELECT p.product_attribute4 AS position_code FROM tm_product p WHERE p.product_code IN (" +
                " SELECT dmg.group_id FROM tt_hi_product_act a" +
                "       LEFT JOIN dms_XPS_MATERIAL dp ON a.product_code = dp.matnr" +
                "       left join dms_mara_group dmg on dmg.id = dp.group_id " +
                " WHERE a.flag_key = ? AND a.product_level_flag = 'N'" +
                ")" +
                " UNION " +
                "SELECT p.product_attribute4 AS position_code FROM tm_product p WHERE p.product_code IN (" +
                " SELECT dmg.group_id FROM tt_hi_product_act a" +
                "       LEFT JOIN dms_XPS_MATERIAL dp ON instr(a.product_code,dp.matnr) > 0" +
                "       left join dms_mara_group dmg on dmg.id = dp.group_id " +
                " WHERE a.flag_key = ? AND a.product_level_flag = 'H'" +
                ")" +
                ") t";

        List<TmPositionVo> list = ttActWorkFlowService.findBySql(TmPositionVo.class, sql, flagKey, flagKey, flagKey);

        if(CollectionUtil.listNotEmptyNotSizeZero(list)) {
            List <String> personList = new ArrayList<String>();
            for(TmPositionVo vo : list){
                if(StringUtils.isBlank(vo.getPositionCode())) {
                    throw new BusinessException("产品系列没有配置职位，请联系管理员配置");
                }
                personList.add(vo.getPositionCode());
            }
            return personList;
        } else {
            throw new BusinessException("没有找到产品系列对应的职位，请联系管理员配置");
        }
    }

    /**
     * 获取产品与职位关系，并且通过角色过滤后的职位数据
     * @param ttActWorkFlowService
     * @param flagKey
     * @param processKey
     * @return
     */
    private List <String> getRelationAndRoleList(TtActWorkFlowService ttActWorkFlowService, String flagKey, String processKey) {
        List <String> personList = getSignPositionByRelationList(ttActWorkFlowService, flagKey);

        //查找会签角色编码
        String roleKey = this.getRoleKey(ttActWorkFlowService, processKey);

        //角色对应的职位
        List <String> actRoleList = this.findPositionListByRoleCode(roleKey);

        List <String> approveList = new ArrayList<String>();

        //销售部，角色都存在的职位
        Map<String, String> map = new HashMap<String, String>(); //用于过滤重复职位编码
        for(String productPositionVo : personList) {
            for(String acrRolePositionVo : actRoleList) {
                if(acrRolePositionVo.equals(productPositionVo)) {
                    if(!map.containsKey(acrRolePositionVo)) {
                        approveList.add(acrRolePositionVo);
                        map.put(acrRolePositionVo, acrRolePositionVo);
                    }
                }
            }
        }

        if(CollectionUtil.listEmpty(approveList)) {
            throw new BusinessException("没有找到产品系列对应的职位，请联系管理员配置");
        }

        return approveList;
    }

    /**
     * 查找会签的所有职位
     * @param ttActWorkFlowService
     * @param flagKey
     * @return
     */
    private List <String> getSignPositionByRelationList(TtActWorkFlowService ttActWorkFlowService, String flagKey) {
        //查询角色下的所有职位
        String sql = "SELECT distinct position_code FROM ta_r_product_position WHERE product_code IN (" +
                "SELECT p.product_code FROM tm_product p WHERE p.product_level = '04' START WITH p.product_code IN (" +
                "       SELECT a.product_code FROM tt_hi_product_act a WHERE a.flag_key = ? AND a.product_level_flag = 'Y'" +
                ") CONNECT BY PRIOR p.id= p.parent_id" +
                " UNION " +
                "SELECT p.product_attribute4 AS position_code FROM tm_product p WHERE p.product_code IN (" +
                " SELECT dmg.group_id FROM tt_hi_product_act a" +
                "       LEFT JOIN dms_XPS_MATERIAL dp ON a.product_code = dp.matnr" +
                "       left join dms_mara_group dmg on dmg.id = dp.group_id " +
                " WHERE a.flag_key = ? AND a.product_level_flag = 'N'" +
                ")" +
                " UNION " +
                "SELECT p.product_attribute4 AS position_code FROM tm_product p WHERE p.product_code IN (" +
                " SELECT dmg.group_id FROM tt_hi_product_act a" +
                "       LEFT JOIN dms_XPS_MATERIAL dp ON instr(a.product_code,dp.matnr) > 0" +
                "       left join dms_mara_group dmg on dmg.id = dp.group_id " +
                " WHERE a.flag_key = ? AND a.product_level_flag = 'H'" +
                ")" +
                ") ";

        List<TmPositionVo> list = ttActWorkFlowService.findBySql(TmPositionVo.class, sql, flagKey, flagKey, flagKey);

        if(CollectionUtil.listNotEmptyNotSizeZero(list)) {
            List <String> personList = new ArrayList<String>();
            for(TmPositionVo vo : list){
                if(StringUtils.isBlank(vo.getPositionCode())) {
                    throw new BusinessException("产品系列没有配置职位，请联系管理员配置");
                }
                personList.add(vo.getPositionCode());
            }
            return personList;
        } else {
            throw new BusinessException("没有找到产品系列对应的职位，请联系管理员配置");
        }
    }

    /**
     * 获取角色变量
     * @param ttActWorkFlowService
     * @param processKey
     * @return
     */
    private String getRoleKey(TtActWorkFlowService ttActWorkFlowService, String processKey) {
        String sql = " SELECT pv.* FROM ta_process_variable pv " +
                    "       LEFT JOIN ta_process_node pn ON pv.process_node_id = pn.id " +
                    "       LEFT JOIN ta_process p ON p.id = pn.process_id " +
                    " WHERE p.process_key = ? AND pn.sort = 1";

        List <TaProcessVariableEntity> list = ttActWorkFlowService.findBySql(TaProcessVariableEntity.class, sql, processKey);

        if(CollectionUtil.listNotEmptyNotSizeZero(list)) {
            if(list.size() > 1) {
                throw new BusinessException(processKey + "第一个节点找到多个变量");
            }
            TaProcessVariableEntity entity = list.get(0);
            return entity.getProcessVariableKey();
        } else {
            throw new BusinessException(processKey + "第一个节点没有配置变量");
        }
    }

    /**
     * 查询角色对应的职位
     * @return
     */
    public List<String> findPositionListByRoleCode(String roleKey) {
        TmActRoleService tmActRoleService = ApplicationContextUtils.getContext().getBean(TmActRoleService.class);

        List<TmPositionVo> positionList = tmActRoleService.findPositionList(roleKey);

        List <String> personList = new ArrayList<String>();

        if (CollectionUtil.listNotEmptyNotSizeZero(positionList)) {
            for(TmPositionVo positionVo : positionList) {
                personList.add(positionVo.getPositionCode());
            }
        } else {
            throw new BusinessException("角色编码" + roleKey + "未找到审批职位，请联系系统管理员");
        }

        return personList;
    }
}
