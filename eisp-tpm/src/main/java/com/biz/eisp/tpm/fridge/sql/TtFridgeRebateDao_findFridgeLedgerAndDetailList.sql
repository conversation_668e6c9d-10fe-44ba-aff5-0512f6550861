select
t3.id as id,
t3.fridge_code as fridgeCode,
t3.customer_code as customerCode,
t3.customer_name as customerName,
t2.purchase_type as purchaseType,
nvl(t1.APPLY_AMOUNT,0) applyAmount,
t3.rebate_status as rebateStatus,
t3.year_month as yearMonth
from tt_fridge_detail t3
left join tt_fridge_main t2 on t2.fridge_code = t3.fridge_code
left join TT_FRIDGE_REBATE t1 on t1.fridge_code = t3.fridge_code and t1.year_month  =t3.year_month
where 1=1
  <#if customerCode ?exists&&customerCode ?length gt 0>
       and t3.customer_code = '${customerCode}'
  </#if>
  <#if purchaseType ?exists&&purchaseType ?length gt 0>
       and t2.purchase_type = '${purchaseType}'
  </#if>
  <#if yearMonth ?exists&&yearMonth ?length gt 0>
       and t3.year_month <= '${yearMonth}'
  </#if>

order by t3.year_month asc