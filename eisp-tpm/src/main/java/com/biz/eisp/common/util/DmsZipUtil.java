package com.biz.eisp.common.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: 压缩文件工具类
 */
public class DmsZipUtil {
	public static boolean zipCompress(List<File> srcFiles, String desFile) {
        boolean isSuccessful = false;

        try {
        	BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(desFile));
        	ZipOutputStream zos = new ZipOutputStream(bos);

        	for (int i = 0; i < srcFiles.size(); i++) {
        		String entryName = srcFiles.get(i).getName();

        		// 创建Zip条目
        		ZipEntry entry = new ZipEntry(entryName);
        		zos.putNextEntry(entry);

        		BufferedInputStream bis = new BufferedInputStream(new FileInputStream(srcFiles.get(i)));

        		byte[] b = new byte[1024];

        		while (bis.read(b, 0, 1024) != -1) {
        			zos.write(b, 0, 1024);
        		}
        		bis.close();
        		zos.closeEntry();
        	}

        	zos.flush();
        	zos.close();
        	isSuccessful = true;
        } catch (IOException e) {
        }

        return isSuccessful;
	}

}
