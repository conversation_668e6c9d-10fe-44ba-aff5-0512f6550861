package com.biz.eisp.common.util;

import com.biz.eisp.api.common.constant.DmsConstant;
import com.biz.eisp.api.common.util.CompareUtil;
import com.biz.eisp.api.common.vo.DmsImgVo;
import com.biz.eisp.base.common.util.ResourceUtil;

/**
 * 获取图片地址地址
 *
 * <AUTHOR>
 */
public class ImgRealUrlUtil {

    private static final String uploadpath = ResourceUtil.getSysConfigProperty("uploadpath");
    private static final String nginx_url = ResourceUtil.getSysConfigProperty("url_nginx");

    /**
     * 获取原地址
     *
     * @param fileName 文件名
     * @param suffix   后缀名
     * @return
     */
    public static String getRelativePath(String fileName, String suffix, String size) {
        String backstr = "";
        if (CompareUtil.eq(DmsConstant.IMGSIZE.small.getV(), size)) {
            //获取45*45的缩略图地址
            backstr = uploadpath + "/" + fileName + DmsConstant.IMGSIZE.small.getV() + "." + suffix;
        } else if (CompareUtil.eq(DmsConstant.IMGSIZE.medium.getV(), size)) {
            //获取220*220的缩略图地址
            backstr = uploadpath + "/" + fileName + DmsConstant.IMGSIZE.medium.getV() + "." + suffix;
        } else {
            backstr = uploadpath + "/" + fileName + "." + suffix;
        }
        return backstr;
    }


    /**
     * 得到图片的基地址
     *
     * @return
     */
    public static String getPicUrlBath() {
//		ResourceBundle bunder = ResourceBundle.getBundle("sysconfig");
        return nginx_url;
    }

    public static String getAbsolutePicUrl(DmsImgVo img, String size) {
        //添加一张缩略图url
        String picBath = ImgRealUrlUtil.getPicUrlBath();
        if (img != null) {
            String picUrl = ImgRealUrlUtil.getRelativePath(img.getFileName(), img.getExtend(), size);
            return picBath + picUrl;
        } else {
            return "resources/shop/images/dengkang-load" + size + ".png";
        }
    }


}
