/**
 * 描述.
 */
package com.biz.eisp.core.rule.vo;

import java.math.BigDecimal;

/**
 * 返利结果 数据vo
 * 
 * <AUTHOR>
 * @version v1.0
 */
public class DmsRebRtVo {
	/** 物料编码 */
	private String matnr;
	/** 计算值 如金额或者数量 */
	private BigDecimal caVal;

	/**
	 * getter 物料编码
	 * 
	 * @return matnr
	 */
	public String getMatnr() {
		return matnr;
	}

	/**
	 * setter 物料编码
	 * 
	 */
	public void setMatnr(String matnr) {
		this.matnr = matnr;
	}

	/**
	 * getter 计算值如金额或者数量
	 * 
	 * @return caVal
	 */
	public BigDecimal getCaVal() {
		return caVal;
	}

	/**
	 * setter 计算值如金额或者数量
	 * 
	 */
	public void setCaVal(BigDecimal caVal) {
		this.caVal = caVal;
	}
}
