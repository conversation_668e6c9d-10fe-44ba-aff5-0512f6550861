<?xml version="1.0" encoding="UTF-8"?>
<!--
table :
	class    : 实体类的类名
	key      : 可以指定某一列的code为key，可以指定多个，中间用逗号分隔；如果指定，系统会根据key的值来决定数据的存储方式，新增或是修改
	startRow : 从数据文件中的第几行开始读取数据(1...)
columns:
	tableName: 动态配置表名，将自动根据配置项加载模板
column :
	id       : 列号（1...）
	code     : 实体类中的属性名
	type     : 数据类型 char|number|date
	length   : 数据最大长度，如果是数值，则包括小数点及正负号
	format   : 在数据类型为 number|date 时有效；date类型时也可不输入，系统会自动尝试所有格式；如果是number类型，则必须输入，以便区分或转换为long和double
	required : 值是否必须
	mask     : 使用正则表达式验证
	value    : 可直接指定列的的值，如：value="123"; 也可加入系统变量或当前变量，如：value="${0}-${_emp.empName}-${sort}" , 结果："001-系统管理员-1";
目前系统支持的变量：
_user       : TmUser
_currDate   : String
_currTime   : String
_currMillis : String
_uuid       : String
-->
<table class="com.biz.eisp.tpm.act.policy.vo.TtProductActVo"
	   key="customerCode"
	   startRow="2"
	   eventHandler="com.biz.eisp.tpm.act.policy.service.TtProductActHandler"
	   template-name="产品活动申请导入模板">
	<columns>
		<column title="客户编码" type="char" length="50" required="true" code="customerCode"></column>
		<column title="产品政策编号" type="char" length="50" required="true" code="productPolicyCode"></column>
		<column title="活动开始时间(格式2017-01-01)" type="char" length="20" required="true" code="beginDate"></column>
		<column title="活动结束时间(格式2017-01-01)" type="char" length="20" required="true" code="endDate"></column>
		<column title="目标销量1（EA）" type="char" length="10" required="true" code="targetQuantityStr"></column>
		<column title="目标销售额1（元）" type="char" length="20" required="true" code="targetAmountStr"></column>
		<column title="关联考核产品销量（EA）" type="char" length="10" code="relationProductQuantityStr"></column>
		<column title="关联考核产品销售额（元）" type="char" length="20" code="relationProductAmountStr"></column>
		<column title="目标销量2（EA）" type="char" length="10" code="targetQuantity2Str"></column>
		<column title="目标销售额2（元）" type="char" length="20" code="targetAmount2Str"></column>
		<column title="货补产品编码" type="char" length="50" code="supplyProductCode"></column>
		<column title="折合供价" type="char" length="50" code="rebatePriceStr"></column>
		<column title="终端供价" type="char" length="50" code="terminalPriceStr"></column>
		<column title="终端执行方式" type="char" length="50" code="executionMode"></column>
		<column title="备注" type="char" length="50" code="remark"></column>
	</columns>
</table>
