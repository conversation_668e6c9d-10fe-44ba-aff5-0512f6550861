<?xml version="1.0" encoding="UTF-8"?>

<table class="com.biz.eisp.tpm.act.orgcost.vo.TtTemporaryTravelDetailVo" key="createDate" startRow="2"
	eventHandler="com.biz.eisp.tpm.act.orgcost.controller.TtActBillTravelDetailHandler" template-name="差旅明细导入模版">
	<columns>
		<column title="开始时间(格式xxxx-xx-xx)"  type="char" required="true" length="100" code="beginDate"/>
		<column title="结束时间(格式xxxx-xx-xx)"  type="char" required="true" length="100" code="endDate"/>
		<column title="地点"  type="char" required="true" length="100" code="address"/>
		<column title="交通费"  type="char" length="32" code="trafficExpense"/>
		<column title="伙食补贴"  type="char" length="32" code="mealAllowance"/>
		<column title="小票补贴"  type="char" length="32" code="smallTicketAllowance"/>
		<column title="住宿费"  type="char" length="32" code="hotelExpense"/>
		<column title="电话费"  type="char" length="32" code="phoneExpense"/>
		<column title="其他费用"  type="char" length="32" code="otherExpense"/>
		<column title="进项税抵扣"  type="char" length="32" code="incomeTaxDeduction"/>
		<column title="费用摘要"  type="char" length="100" code="expenseSummary"/>
	</columns>
	
</table>
 
 