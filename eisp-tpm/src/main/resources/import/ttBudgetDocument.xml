<?xml version="1.0" encoding="UTF-8"?>
<!--
table :
	class    : 实体类的类名
	key      : 可以指定某一列的code为key，可以指定多个，中间用逗号分隔；如果指定，系统会根据key的值来决定数据的存储方式，新增或是修改
	startRow : 从数据文件中的第几行开始读取数据(1...)
columns:
	tableName: 动态配置表名，将自动根据配置项加载模板
column :
	id       : 列号（1...）
	code     : 实体类中的属性名
	type     : 数据类型 char|number|date
	length   : 数据最大长度，如果是数值，则包括小数点及正负号
	format   : 在数据类型为 number|date 时有效；date类型时也可不输入，系统会自动尝试所有格式；如果是number类型，则必须输入，以便区分或转换为long和double
	required : 值是否必须
	mask     : 使用正则表达式验证
	value    : 可直接指定列的的值，如：value="123"; 也可加入系统变量或当前变量，如：value="${0}-${_emp.empName}-${sort}" , 结果："001-系统管理员-1";
目前系统支持的变量：
_user       : TmUser
_currDate   : String
_currTime   : String
_currMillis : String
_uuid       : String
-->

<table class="com.biz.eisp.tpm.budgetdoc.vo.TtBudgetDocumentImpVo"
       key="id"
       startRow="2"
       eventHandler="com.biz.eisp.tpm.budgetdoc.service.impl.TtBudgetDocumentImpHandler"
       template-name="预算入账导入模板">

    <columns>
        <column title="公司代码" type="char" length="100" required="true" code="bukrs"></column>
        <column title="凭证日期(使用文本格式2018-01-01)" type="char" length="100" required="true" code="bldatStr"></column>
        <column title="过账日期(使用文本格式2018-01-01)" type="char" length="100" required="true" code="budatStr"></column>
        <column title="抬头文本" type="char" length="100" required="true" code="bktxt"></column>

        <column title="货币" type="char" length="100" required="true" dictCode="dict_currency_type" code="waers"></column>
        <column title="汇率" type="char" length="100"  code="kursf"></column>


        <column title="发票页数" type="char" length="100" required="true" code="attPages"></column>



        <column title="借/贷" type="char" length="100" required="true" code="shkzg" dictCode="dict_shkzg"></column>
        <column title="科目" type="char" length="100" required="true" code="hkont"></column>
        <column title="往来单位" type="char" length="100" code="zwldw"></column>

        <column title="税码" type="char" length="100"  code="mwskz"></column>
        <column title="金额" type="char" length="100" required="true" code="wrbtr"></column>
        <column title="本位币金额" type="char" length="100"  code="dmbtr"></column>

        <column title="税额" type="char" length="100"  code="taxv"></column>
        <column title="行项目文本" type="char" length="100"  code="sgtxt"></column>
        <column title="反记账" type="char" length="100"  code="xnegp"></column>
        <column title="成本中心" type="char" length="100" code="kostl"></column>


        <column title="原因代码" type="char" length="100" code="rstgr" dictCode="rsgtr"></column>
        <column title="贸易伙伴" type="char" length="100"  code="vbund"></column>
        <column title="订单" type="char" length="100"  code="aufnr"></column>
        <column title="利润中心" type="char" length="100"  code="prctr"></column>

        <column title="工作分解结构元素" type="char" length="100"  code="posid"></column>




    </columns>
</table>
