<?xml version="1.0" encoding="UTF-8"?>
<!--
table :
	class    : 实体类的类名
	key      : 可以指定某一列的code为key，可以指定多个，中间用逗号分隔；如果指定，系统会根据key的值来决定数据的存储方式，新增或是修改
	startRow : 从数据文件中的第几行开始读取数据(1...)
columns:
	tableName: 动态配置表名，将自动根据配置项加载模板
column :
	id       : 列号（1...）
	code     : 实体类中的属性名
	type     : 数据类型 char|number|date 
	length   : 数据最大长度，如果是数值，则包括小数点及正负号
	format   : 在数据类型为 number|date 时有效；date类型时也可不输入，系统会自动尝试所有格式；如果是number类型，则必须输入，以便区分或转换为long和double
	required : 值是否必须
	mask     : 使用正则表达式验证
	value    : 可直接指定列的的值，如：value="123"; 也可加入系统变量或当前变量，如：value="${0}-${_emp.empName}-${sort}" , 结果："001-系统管理员-1";
目前系统支持的变量：
_user       : TmUser
_currDate   : String
_currTime   : String
_currMillis : String
_uuid       : String
-->

<table class="com.biz.eisp.tpm.budget.target.vo.TtChanllIncomeTargetVo" 
	   startRow="2"
	   eventHandler="com.biz.eisp.tpm.budget.target.service.impl.TtChanllIncomeTargetHandler" 
	   template-name="挑战版收入预算"
	   batches="999999999">
	
	<columns>
		<column title="年月" type="char" length="32" required="true" code="yearMonth"></column>
		<column title="组织编码" type="char" length="32" required="true" code="orgCode"></column>
		<column title="组织名称" type="char" length="100" code="orgName"></column>
		
		
		<column title="产品层级编码" type="char" length="32" required="true" code="productCode"></column>
		<column title="产品层级名称" type="char" length="100" code="productName"></column>
		
		
		<column title="客户编码" type="char" length="32" required="true" code="customerCode"></column>
		<column title="客户名称" type="char" length="100" code="customerName"></column>
		
		
		<column title="金额(元)" type="char" length="32" required="true" code="amount"></column>
	</columns>
</table>
 
