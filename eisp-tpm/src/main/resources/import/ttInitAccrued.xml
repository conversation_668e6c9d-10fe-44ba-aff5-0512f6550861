<?xml version="1.0" encoding="UTF-8"?>
<!--
table :
	class    : 实体类的类名
	key      : 可以指定某一列的code为key，可以指定多个，中间用逗号分隔；如果指定，系统会根据key的值来决定数据的存储方式，新增或是修改
	startRow : 从数据文件中的第几行开始读取数据(1...)
columns:
	tableName: 动态配置表名，将自动根据配置项加载模板
column :
	id       : 列号（1...）
	code     : 实体类中的属性名
	type     : 数据类型 char|number|date
	length   : 数据最大长度，如果是数值，则包括小数点及正负号
	format   : 在数据类型为 number|date 时有效；date类型时也可不输入，系统会自动尝试所有格式；如果是number类型，则必须输入，以便区分或转换为long和double
	required : 值是否必须
	mask     : 使用正则表达式验证
	value    : 可直接指定列的的值，如：value="123"; 也可加入系统变量或当前变量，如：value="${0}-${_emp.empName}-${sort}" , 结果："001-系统管理员-1";
目前系统支持的变量：
_user       : TmUser
_currDate   : String
_currTime   : String
_currMillis : String
_uuid       : String
-->

<table class="com.biz.eisp.tpm.accrued.vo.TtInitAccruedVo"
       key="name"
       startRow="2"
       eventHandler="com.biz.eisp.tpm.accrued.service.impl.TtInitAccruedHandler"
       template-name="往期奶粉预期管理模板">

    <columns>
        <column title="预提年月" type="char" length="10" required="true" code="accruedYearMonth"></column>
        <column title="活动年月" type="char" length="10" required="true" code="actYearMonth"></column>
        <column title="预提类型" type="char" length="10" required="true" code="accruedType" dictCode="accrued_type"></column>
        <column title="往期预提类型" type="char" length="10" required="true" code="accruedDataType" dictCode="milk_init_accrued_type"></column>
        <column title="产品编码" type="char" length="32" code="productCode" required="true"></column>
        <!--<column title="产品名称" type="char" length="100" code="productName" required="true"></column>-->
        <column title="组织编码" type="char" length="32" code="orgCode" required="true"></column>
        <!--<column title="组织名称" type="char" length="100" code="orgName" required="true"></column>-->
        <column title="SAP成本中心" type="char" length="50" code="costCenter" required="true"></column>
        <column title="客户编码" type="char" length="50" code="customerCode" required="true"></column>
        <!--<column title="客户名称" type="char" length="200" code="customerName" required="true"></column>-->
        <column title="财务科目erp编码" type="char" length="32" code="finacialAccountCode" required="true"></column>
        <column title="预算科目编码" type="char" length="32" code="finacialCode" required="true"></column>
        <!--<column title="预算科目名称" type="char" length="50" code="finacialName" required="true"></column>-->
        <column title="活动大类编码" type="char" length="32" code="costTypeCode" required="true"></column>
        <!--<column title="活动大类名称" type="char" length="50" code="costTypeName" required="true"></column>-->
        <column title="活动细类编码" type="char" length="32" code="costAccountCode" required="true"></column>
        <!--<column title="活动细类名称" type="char" length="50" code="costAccountName" required="true"></column>-->
        <column title="活动开始时间" type="char" length="20" required="true" code="beginDate" width="30"></column>
        <column title="活动结束时间" type="char" length="20" required="true" code="endDate" width="30"></column>
        <column title="支付方式" type="char" length="20" required="true" code="paymentCode" width="30" dictCode="payment_type"></column>
        <column title="预提金额" type="char" length="20" required="true" code="accruedAmount" width="30"></column>
        <column title="备注" type="char" length="200" required="false" code="remark" width="30"></column>
    </columns>
</table>