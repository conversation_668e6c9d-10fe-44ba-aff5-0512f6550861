<?xml version="1.0" encoding="UTF-8"?>
<!--
table :
	class    : 实体类的类名
	key      : 可以指定某一列的code为key，可以指定多个，中间用逗号分隔；如果指定，系统会根据key的值来决定数据的存储方式，新增或是修改
	startRow : 从数据文件中的第几行开始读取数据(1...)
columns:
	tableName: 动态配置表名，将自动根据配置项加载模板
column :
	id       : 列号（1...）
	code     : 实体类中的属性名
	type     : 数据类型 char|number|date 
	length   : 数据最大长度，如果是数值，则包括小数点及正负号
	format   : 在数据类型为 number|date 时有效；date类型时也可不输入，系统会自动尝试所有格式；如果是number类型，则必须输入，以便区分或转换为long和double
	required : 值是否必须
	mask     : 使用正则表达式验证
	value    : 可直接指定列的的值，如：value="123"; 也可加入系统变量或当前变量，如：value="${0}-${_emp.empName}-${sort}" , 结果："001-系统管理员-1";
目前系统支持的变量：
_user       : TmUser
_currDate   : String
_currTime   : String
_currMillis : String
_uuid       : String
-->

<table class="com.biz.eisp.tpm.act.bkw.vo.TtActBkwMainVo" 
	   key="id" 
	   startRow="2"
	   eventHandler="com.biz.eisp.tpm.act.bkw.service.impl.TtActBkwMainEventHandler" 
	   template-name="宝库旺导入模板">
	
	<columns>
		<column title="流程类型编码" type="char" length="100" required="true" code="actTypeCode" dictCode="bkw_act_type"></column>
		<!-- <column title="流程类型名称" type="char" length="100" code="actTypeName"></column> -->

		<column title="活动名称" type="char" length="100" required="true" code="actName"></column>
		<column title="活动大类编码" type="char" length="100" required="true" code="costTypeCode"></column>

		<!-- <column title="活动大类名称" type="char" length="100" code="costTypeName"></column> -->
		<column title="活动细类编码" type="char" length="100" required="true" code="costAccountCode"></column>
		<!-- <column title="活动细类名称" type="char" length="100" code="costAccountName"></column> -->
		<column title="开始时间(yyyy-mm-dd)" type="char" length="100" required="true" code="beginDate"></column>
		<column title="结束时间(yyyy-mm-dd)" type="char" length="100" required="true" code="endDate"></column>
		<column title="客户编码" type="char" length="100" code="customerCode"></column>
		<!-- <column title="客户名称" type="char" length="100" code="customerName"></column> -->
		<column title="产品层级编码 " type="char" length="100" code="productLevelCode"></column>
		<!-- <column title="产品层级名称" type="char" length="100" code="productLevelName"></column> -->
		<column title="费用金额" type="number" length="100" required="true" code="amount"></column>
		<column title="预估销售额" type="number" length="100"  required="true" code="estimateSale"></column>
		<!-- <column title="支付方式编码" type="char" length="100" required="true" code="paymentCode"></column> -->
		<column title="支付方式" type="char" required="true" length="100" code="paymentCode" dictCode="payment_type"></column>
		<column title="文本描述" type="char" length="100"  code="remark"></column>
	</columns>
</table>
 