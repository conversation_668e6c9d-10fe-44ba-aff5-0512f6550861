body {
	#background-color: #282828;
	padding: .5rem;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.title {
	font-size: 28px;
	text-align: center;
	color: #fff;
	margin-bottom: 30px;
}

.toolbar {
	text-align: center;
}

.toolbar-2 {
	text-align: center;
	margin-top: 30px;
	padding: 30px;
	background-color: rgba(255,255,255,0.05);
}




.btn {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	padding: 1rem 2rem;
	background-color: rgba(255,255,255,0.2);;
	color: #fff;
	border: 0;
	
	transition: all 200ms ease;
}
.btn.btn-round {
	border-radius: 10px;
}
.btn:focus {
	outline: none;
}
.btn:active {
	box-shadow: inset 0 0 0 1000px rgba(0,0,0,0.1);
}

.btn-blue {
	background-color: #005db9;
}
.btn-red {
	background-color: #ad0052;
}
.btn-green {
	background-color: #00947e;
}
.btn-black {
	background-color: #444;
}



.btn-border {
	display: inline-block;
	margin: 5px;
}
.btn-border	.btn {
	display: block;
	margin: 2px;
}
	
.btn-border.btn-round {
	border-radius: 10px;
}
.btn-border.btn-round .btn {
	border-radius: 10px;
}



.ref {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	position: fixed;
	z-index: 10000;
	bottom: 0;
	left: 0;
	width: 100%;
	text-align: center;
	color: #fff;
	padding: 1rem 0;
	background-image: linear-gradient(135deg, rgba(60, 88, 173, 0.8), rgba(41, 152, 172, 0.8));
	-webkit-backdrop-filter: blur(10px);
	backdrop-filter: blur(10px);
}
.ref a {
	color: #fff;
}