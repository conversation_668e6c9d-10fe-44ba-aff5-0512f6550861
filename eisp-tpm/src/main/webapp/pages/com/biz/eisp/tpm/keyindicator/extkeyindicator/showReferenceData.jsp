<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<div id = "showReferenceData" style="clear:both; margin-bottom: 5px; margin-left: 5px;background-color: white !important;">
</div>
<script>
    $(function () {
        getShowReferenceData();
    });

    function getShowReferenceData(){
        var thisData = {
            businessKey : getBusinessKey(),
            flagKey : getFlagKey()
        }
        var url = "ttActApplyExcuteWorkFlowController.do?getShowReferenceData";
        var d = ajaxPost(thisData,url);

        if(d.success){
            var html = returnShowReferenceDataHtml(d.obj);
            $('#showReferenceData').html(html)
        }
    }


    function returnShowReferenceDataHtml(objs) {
        //var symbol = "";
       // for(var obj in objs){
       //     if(obj == 'SYMBOL'){
       //         symbol = objs[obj];
       //     }
        //}
        var html = "";
        for(var obj in objs){
            //if(symbol == 'NUMS'){
            //   html = '<label>' + objs[obj] + '</label>';
            //} else {
                if(html != ''){
                    html += "\t|\t"
                }
                //if(objs[obj] == 'SCORE'){
                //    continue;
                //}
                if(obj == '可用积分' || obj == '本单实报'){
                    html += '<label>' + obj + ':<span style="font-size: 15px;color: red;">' + objs[obj] + '</span></label>';
                } else if(obj == '报销比例') {
                    html += '<label>' + obj + ':<span style="font-size: 15px;color: darkblue;">' + objs[obj] + '%</span></label>';
                } else {
                    html += '<label>' + obj + ':<span style="font-size: 15px;color: darkblue;">' + objs[obj] + '</span></label>';
                }
            //}
        }
        html = html.replace("<label>SYMBOL:<span style=\"font-size: 15px;color: darkblue;\">SCORE</span></label>	|","");
        return html;
    }
</script>