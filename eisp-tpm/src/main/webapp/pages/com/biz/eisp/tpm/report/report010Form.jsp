<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>客户换户关系</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="report010Controller.do?savefindReport010"
             refresh="true">
    <!-- id -->
    <input name="id" type="hidden" value="${report010Vo.id}"/>

    <div class="form">
        <label class="Validform_label" name="productName">原户名称: </label>
        <input datatype="*" id="origCusName" name="origCusName" value="${report010Vo.origCusName}" class="inputxt"
               readonly="readonly"/>
        <input id="origCusCode" name="origCusCode" value="${report010Vo.origCusCode}" type="hidden"/>
        <span style="color: red">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" name="choose" icon="icon-search"
           onClick="openOriginalCustomerSelect()"></a>
        <span class="Validform_checktip" id="errorPro"></span>
    </div>


    <div class="form">
        <label class="Validform_label" name="productName">新户名称: </label>
        <input datatype="*" id="newCusName" name="newCusName" value="${report010Vo.newCusName}" class="inputxt"
               readonly="readonly"/>
        <input id="newCusCode" name="newCusCode" value="${report010Vo.newCusCode}" type="hidden"/>
        <span style="color: red">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" name="choose" icon="icon-search"
           onClick="openNewCustomerSelect()"></a>
        <span class="Validform_checktip" id="errorPro"></span>
    </div>

    <%--<div class="form">--%>
        <%--<label id="newCusSAPDate" class="Validform_label">新户SAP第一次发票记账日期: </label>--%>
        <%--<input class="Wdate" name="newCusSAPDate" value="${report010Vo.newCusSAPDate}"--%>
               <%--onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"--%>
               <%--readonly="readonly"--%>
        <%--/>--%>
        <%--<span style="color: red;">*</span>--%>
    <%--</div>--%>


    <div class="form">
        <label class="Validform_label">状态：</label>
        <select name="status" dataType="*" value="${report010Vo.origCusTesk}">
            <option value="">--请选择--</option>
            <c:forEach items="${enableStatus}" var="c">
                <option value="${c.dictCode}"
                        <c:if test="${report010Vo.status == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">是否承接原户任务：</label>
        <select name="origCusTesk" dataType="*" value="${report010Vo.origCusTesk}">
            <option value="">--请选择--</option>
            <c:forEach items="${nooryes}" var="c">
                <option value="${c.dictCode}"
                        <c:if test="${report010Vo.origCusTesk == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">是否承接原户去年销量：</label>
        <select name="origCusSales" dataType="*" value="${report010Vo.origCusSales}">
            <option value="">--请选择--</option>
            <c:forEach items="${nooryes}" var="c">
                <option value="${c.dictCode}"
                        <c:if test="${report010Vo.origCusSales == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>

</t:formvalid>
</body>
</html>

<script type="text/javascript">
    $(".Validform_label").css("width","200px");
    $('.form > input[type!="hidden"]').css("margin-left","80px");
    $('.form > select').css("margin-left","80px");

    //只能输入数字，或者保留两位小数
    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    }

    $(document).ready(function () {
        $("#cbproductCode").combobox({
            onChange: function (newValue, oldValue) {
                load();
            }
        });
    });

    //弹框选原客户
    function openOriginalCustomerSelect() {
        safeShowDialog({
            content: "url:tmCusNoConditionApiController.do?goTmCusNoConditionApiMain&singleSelect=true",
            lock: true,
            title: "选择客户",
            width: 500,
            height: 450,
            left: '85%',
            cache: false,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tmCustomerList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#origCusCode").val("");
                    $("#origCusName").val("");
                    return true;
                }
                $("#origCusCode").val(rowsData.customerCode);
                $("#origCusName").val(rowsData.customerName);
                return true;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    //弹框选新客户
    function openNewCustomerSelect() {
        safeShowDialog({
            content: "url:tmCusNoConditionApiController.do?goTmCusNoConditionApiMain&singleSelect=true",
            lock: true,
            title: "选择客户",
            width: 500,
            height: 450,
            left: '85%',
            cache: false,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tmCustomerList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#newCusCode").val("");
                    $("#newCusName").val("");
                    return true;
                }
                $("#newCusCode").val(rowsData.customerCode);
                $("#newCusName").val(rowsData.customerName);
                return true;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>