<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>返利标准信息</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <style type="text/css">
    .line-title {
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
        }
    </style>
    <style>
	    #content .inputxt,#steps form select{width:250px;}
	    
        #formobj {
            padding: 0 5px;
        }

        #steps form div.form {
            float: left;
            width: 300px;
            min-height:22px;
        }

        #steps form div.form-2 {
            min-width: 700px;
        }

        #steps form div.form-2 textarea {
            width:100%;
        }
        
        #steps form div.form .Validform_label {
            width: 85px;
            margin-right: 5px;
            font-weight:normal;
        }
        
        #steps form div.form .Validform_label input[type="radio"],#steps form div.form .Validform_label input[type="checkbox"]{
        	position:absolute;
        	left:3px;
        	top:3px;
        	width:15px;
        	height:15px;
        }

        .formDiv {
            float: left;
        }

        .line-title {
            font-size: 14px;
            padding: 5px;
            /* background:#b4d2ea; */
        }
		
		.form_label{float:left;padding-right: 20px;line-height:26px;}
        .form_label input[type="radio"] {
            float: left;
            margin: 5px 0 0 0px;
            width: 17px;
            height: 17px;
        }

        .promotionForm_tem {
            margin: 10px;
            padding: 5px;
            border: 1px solid #a5aeb6;
            background: #f5f5f5;
            line-height: 30px;
        }

        .promotionForm_tem input {
            margin: 0 10px;
            width: 60px;
            border: 0;
        }
        .promotionArea{border: 1px solid #ddd;background: #fff;min-height: 80px;padding:5px;width: 310px;}
        .promotionArea a{margin-right:5px;}
        .selectArea{background:#ffc6f2 !important;}
        
    </style>
</head>
<body scroll="yes">

    <div region="center" fit="true">
        <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
                     action="rebateStandController.do?saveTdStRebate">
            <div>
               <input id="backstandProduct" name="backstandProduct"  type="hidden">
                <input name="id" type="hidden" value="${rebVo.id}"> 
                <div class="form">
                    <label class="Validform_label">返利组:</label>
                    <t:dictSelect field="rebGpCode" type="select" defaultVal="${rebVo.rebGpCode}"
                     typeGroupCode="rebate_group" ></t:dictSelect>
                    <span style="color: red">*</span>
                </div>
                <div class="form">
                    <label class="Validform_label">标准类型${rebVo.rebGpStand }:</label>
                    <t:dictSelect field="rebGpStand" type="select" typeGroupCode="rebate_st_type" defaultVal="${rebVo.rebGpStand }"  ></t:dictSelect>
                    <span style="color: red">*</span>
                </div>
                <div class="form">
                    <label class="Validform_label">标准值:</label>
                    <input id="rebGpCt"  name="rebGpCt" type="text"
                     class="inputxt" value="${rebVo.rebGpCt}"  datatype="n">
                    <span style="color: red">*</span>
                </div>
                <div class="form">
                    <label class="Validform_label">有效时间:</label>
                     <input name="gpStart" class="inputxt Wdate" value="${rebVo.gpStart}" style="width: 119px;"
                   onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" readonly/>
            -
            <input name="gpEnd" class="inputxt Wdate" value="${rebVo.gpEnd}" style="width: 119px;"
                   onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" readonly/>
                     
                     <span style="color: red">*</span>
                </div>
		<div class="form">
            <label class="Validform_label">返利对象:</label>
            <span>
				<div>
					<a iconcls="icon-append" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
                             onclick="openProduct('product')">添加单品</a>
					<a iconcls="icon-remove" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
                             onclick="areaDelete('product')">删除</a>
				</div>
				<div class="promotionArea" id="product"></div>
			</span>
        </div>
        <div class="form">
			<span>
				<div>
					<a iconcls="icon-append" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
                              onclick="openSeries('series')">添加系列</a>
					<a iconcls="icon-remove" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
                              onclick="areaDelete('series')">删除</a>
				</div>
				<!-- <textarea rows="5" cols="30" id="includeCust"></textarea> -->
				<div class="promotionArea" id="series"></div>
			</span>
        </div>
            </div>
            
        </t:formvalid>
    </div>
</body>
<script src="resources/laytpl/laytpl.js"></script>
<script src="resources/tools/Map.js"></script>
<script type="text/html" id="areaBlockTpl">
    <a iconcls="icon-ok"
       class="easyui-linkbutton l-btn l-btn-plain"
       plain="true" onclick="selectArea(this,'{{d.stProduct}}','{{d.stPtName}}','{{d.proType}}','{{d.aeraname}}')" data-id="{{d.stProduct}}">
    <span class="l-btn-left">
    <span class="l-btn-text icon-ok l-btn-icon-left">{{d.stPtName}}</span>
    </span>
    </a>
</script>
<script type="text/javascript">
//产品
var productMap = new Map();
//系列
var seriesMap = new Map();
//公共的map集合定义
var mapList = {
    productMap: productMap,
    seriesMap: seriesMap
};
//返回产品json
var rstProductList = [];
$(function(){
	var bkstpro=${rebVo.backstandProduct};
	if(bkstpro!=null&&bkstpro!=undefined&&bkstpro!=""&&bkstpro!="null")
	initPro(bkstpro);
});
function initPro(backstandProduct){
	$(backstandProduct).each(function(eindex, tempv) {
		
		if(tempv.proType == 0){//商品
			var tempx ={
					fullName : "",
					id : "",
					proType : "",
					aeraname : ""
				};
			tempx.fullName = tempv.stPtName;
			tempx.id = tempv.stProduct;
			tempx.proType = tempv.proType;
			tempx.aeraname = 'product';
			putAreaBlock('product', tempx, "id",  0);
			var map = mapList["productMap"];
			map.put(tempx.id, tempx.name);
		}
		if(tempv.proType == 1){//系列
			var tempx ={
					text : "",
					id : "",
					proType : "",
					aeraname : ""
				};
			tempx.text = tempv.stPtName;
			tempx.id = tempv.stProduct;
			tempx.proType = tempv.proType;
			tempx.aeraname = 'series';
			putAreaBlock('series', tempx, "id",  1);
			var map = mapList["seriesMap"];
			map.put(tempx.id, tempx.name);
		}
	});
}
//选择产品
function openProduct(containerElement) {
	$.dialog({
		title : "产品管理",
		content : "url:tdProductController.do?goNoGiftProduct",
		lock : true,
		width : "500",
		height : "400",
		zIndex : 10000,
		parent : windowapi,
		ok : function() {
			iframe = this.iframe.contentWindow;
			var rows = iframe.$("#noGiftProductList").datagrid(
					'getSelections');
			//            console.log("选中的产品", rows);
			putAreaBlock(containerElement, rows, "id",  0);
		},
		cancelVal : '关闭',
		cancel : true
	});
}
//选择系列
function openSeries(containerElement) {
	$.dialog({
		title : "系列管理",
		content : "url:tdMaterialController.do?tmProductSeriesChoose",
		lock : true,
		width : "500",
		height : "400",
		zIndex : 10000,
		parent : windowapi,
		ok : function() {
			iframe = this.iframe.contentWindow;
			var rows = iframe.$("#prdList").datagrid('getSelections');
			//            console.log("选中的系列", rows);
			putAreaBlock(containerElement, rows, "id", 1);
		},
		cancelVal : '关闭',
		cancel : true
	});
}
function putAreaBlock(containerElement, json, id, type) {
	var map = mapList[containerElement + "Map"];
	$(json).each(function(e, v) {
		var areaBlockTpl = $("#areaBlockTpl").html();
		var x ={
			stPtName : "",
			stProduct : "",
			proType : "",
			aeraname : ""
		};
		x.stProduct = v.id;
		x.proType = type;
		x.aeraname = containerElement;
		if(type == 0){
			x.stPtName = v.fullName;
		}
		if(type == 1){
			x.stPtName = v.text;
		}
		//判断是否存在
		var flag =map.containsKey(v.id);
		if (!flag) {
			map.put(v.id, v.name);
			laytpl(areaBlockTpl).render(x, function(html) {
				$("#" + containerElement).append(html);
			});
			rstProductList.push(x);
		} else {
			tip("已经选择了" + x.stPtName);
		}
	});
	inputrstProductList(rstProductList);
}
function areaDelete(id) {
	var map = mapList[id + "Map"];
	var obj = $("#" + id + " .selectArea");
	obj.each(function(index, object) {
		//var key = object.dataset;
		var data_id = $(object).attr("data-id");
		map.remove(data_id);
		var conProTemp = [];
		$(rstProductList).each(function(i, l) {
			if (data_id == l.id) {

			} else {
				conProTemp.push(l);
			}
		});
		rstProductList = conProTemp;
	});
	inputrstProductList(rstProductList);
	$("#" + id + " .selectArea").remove();
}

//返利产品选中
function selectArea(obj, id, name, type, aeraname) {
  $(obj).toggleClass("selectArea");
}
function inputrstProductList(productlist){
	$("#backstandProduct").val(JSON.stringify(productlist));
}
</script>
</html>
