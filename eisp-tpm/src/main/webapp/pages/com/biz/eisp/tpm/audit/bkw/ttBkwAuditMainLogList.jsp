<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="myRunningProcessList" title="发起的流程" actionUrl="${url}"
                    fitColumns="true" idField="id" fit="true" queryMode="group">
            <t:dgCol title="编号" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="流程编号" field="processInstanceId" hidden="true"  width="80"></t:dgCol>
            <t:dgCol title="流程Key" field="processDefinitionKey" width="80"></t:dgCol>
            <t:dgCol title="流程名称" field="processDefinitionName" width="120"></t:dgCol>
            <t:dgCol title="流程标题" field="processTitle" width="120"></t:dgCol>
            <t:dgCol title="发起人" field="startPositionCode" hidden="true" ></t:dgCol>
            <t:dgCol title="开始时间" field="startTime"  width="120"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime" width="120"></t:dgCol>
            <t:dgCol title="耗时" field="spendTimes"  width="120"></t:dgCol>
            <t:dgCol title="流程状态" field="runtimeStatus"  width="80" replace="处理中_doing,完成_completed,驳回_reject,流程追回_recover"></t:dgCol>
            <t:dgToolBar icon="icon-look" id="processView" url="taTaskController.do?goInstanceHandleTabForm&isView=true&isReadFlag=false" funname="view" params="processInstanceId" title="查看" height="500" width="1200"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>

