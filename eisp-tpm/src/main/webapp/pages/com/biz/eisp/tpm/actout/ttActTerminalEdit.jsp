<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>终端管理</title>
    <t:base type="jquery,easyui,tools"></t:base>
    <style>
        #steps form div.form {float:left;width:325px !important;min-height:26px;padding-left:115px !important;}
        #steps form div.form .Validform_label {width:115px;margin-right:5px;}
        .formDiv {float:left;}
    </style>
</head>
<body>
<input type="hidden" name="hwId" value="${hwId}"/>
<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"  action="ttActOutUploadController.do?saveTmTerminal&hwId=${hwId}">
    <!-- 自定义表单 begin -->

                <input type="hidden" name="id" value="${terminalVo.id}"/>

                <div class="form">
                    <label class="Validform_label" name="terminalCode">终端编号: </label>
                    <input id="terminalCode" name="terminalCode" value="${terminalVo.terminalCode}"  readonly="readonly" class="inputxt" onClick="choose(this);" />不可修改！
                </div>
                <div class="form">
                    <label class="Validform_label" name="terminalName">店铺名称（现名）: </label>
                    <input id="terminalName1" name="terminalName1" disabled="disabled" readonly="readonly" class="inputxt"  value="${terminalVo.terminalName}" />
                </div>
                <div class="form">
                    <label class="Validform_label" name="terminalName"><span style="color:red">店铺名称（拟改）: </span></label>
                    <input id="terminalName" name="terminalName" class="inputxt"  value="${terminalVo.terminalName}" />
                </div>
                <div class="form">
                    <label class="Validform_label" name="linkman">店铺老板（现名）: </label>
                    <input id="linkman1" name="linkman1" readonly="readonly" class="inputxt" value="${terminalVo.linkman}"  disabled="disabled"/>
                </div>
                <div class="form">
                    <label class="Validform_label" name="linkman"><span style="color:red">店铺老板（拟改）:</span> </label>
                    <input id="linkman" name="linkman"  class="inputxt" value="${terminalVo.linkman}"  />
                </div>
                <div class="form">
                    <label class="Validform_label" name="linkmanPhone">店老板手机: </label>
                    <input id="linkmanPhone" name="linkmanPhone" readonly="readonly" class="inputxt" value="${terminalVo.linkmanPhone}"  disabled="disabled"/>
                </div>
                <div class="form">
                    <label class="Validform_label" name="RLinkPhone"><span style="color:red">店老板手机(招牌上):</span> </label>
                    <input id="rLinkmanPhone" name="RLinkPhone"  class="inputxt" value="${terminalVo.RLinkPhone}"  datatype= "/^(1[0-9]{10})|(0\d{2,3}-\d{7,8})$/"/>
                </div>
    <div class="form">
        一人多店时，请填写实际显示在招牌上的手机号码。
    </div>
    <div class="form">
        <label class="Validform_label" name="createName">创建人: </label>
        <input id="createName" name="createName" readonly="readonly" class="inputxt" value="${terminalVo.createName}" disabled="disabled" style="width: 160px !important;"/>
    </div>
    <div class="form">
        <label class="Validform_label" name="createDate">创建时间: </label>
        <input id="createDate" name="createDate" readonly="readonly" class="inputxt" value="${terminalVo.createDate}" disabled="disabled" style="width: 160px !important;"/>
    </div>
    <div class="form">
        <label class="Validform_label" name="updateName">最后修改人: </label>
        <input id="updateName" name="updateName" readonly="readonly" class="inputxt" value="${terminalVo.updateName}"  disabled="disabled" style="width: 160px !important;"/>
    </div>
    <div class="form">
        <label class="Validform_label" name="updateDate">最后修改时间: </label>
        <input id="updateDate" name="updateDate" readonly="readonly" class="inputxt" value="${terminalVo.updateDate}"  disabled="disabled" style="width: 160px !important;" />
    </div>

    <!-- 自定义表单 end-->
</t:formvalid>

</body>
<script type="text/javascript">

</script>
</html>
