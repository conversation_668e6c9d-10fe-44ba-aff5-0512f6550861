<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="border-left:0px">
        <t:datagrid name="ttAuditQuotaList" fit="true" fitColumns="true" singleSelect="true"
                    title="结案子单"
                    queryMode = "group"
                    actionUrl="ttAuditQuotaController.do?findTtAuditQuotaList&billMainId=${billMainId}"
                    idField="id"
                    autoLoadData="true"
                    onLoadSuccess="loadTotal">

            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="结案主单ID" field="billMainId" hidden="true"></t:dgCol>

            <t:dgCol title="部门编码"  field="orgCode" hidden = "true"></t:dgCol>
            <t:dgCol title="活动发布要求" field="actDeployRequire" hidden="true"></t:dgCol>
            <t:dgCol title="结案主单主键" field="businessKey" hidden="true"></t:dgCol>

            <t:dgCol title="审批状态"  field="bpmStatus" query="true" dictionary="bpm_status" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="活动类型"  field="actModeCode" dictionary="audit_act_mode_type" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="客户名称"  field="customerName" query="true" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="活动名称"  field="actName" query = "true" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="活动编号"  field="actCode" ></t:dgCol>
            <t:dgCol title="是否到门店"  field="containTerminal" dictionary="yesorno" query ="true" ></t:dgCol>
            <t:dgCol title="部门名称"  field="orgName"  ></t:dgCol>






            <t:dgCol title="产品名称"  field="productName" query="true"></t:dgCol>
            <t:dgCol title="活动大类"  field="costTypeName" query = "true"></t:dgCol>
            <t:dgCol title="活动细类"  field="costAccountName" query = "true"></t:dgCol>
            <t:dgCol title="活动开始时间" field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="活动申请数量"  field="planQuantity"></t:dgCol>
            <t:dgCol title="活动申请金额"  field="planAmount"></t:dgCol>
            <t:dgCol title="超额核销比(%)"  field="overAuditScale" ></t:dgCol>
            <t:dgCol title="申请结案数量 "  field="applyAuditQuantity" ></t:dgCol>
            <t:dgCol title="申请结案金额"  field="applyAuditAmount" ></t:dgCol>
            <t:dgCol title="归属事业部"  field="businessUnitName"></t:dgCol>
            <t:dgCol title="审核结案数量"  field="auditQuantity" editor="{type:'numberbox',options:{precision:1,min:0}}"></t:dgCol>
            <t:dgCol title="审核结案金额"  field="auditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
            <t:dgCol title="最终结案金额"  field="realAuditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>


            <t:dgCol title="支付方式"   field="paymentCode" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="货补产品"  field="premiumProductName"></t:dgCol>
            <t:dgCol title="结案状态"  field="auditStatus"  dictionary="audit_bkw_status"></t:dgCol>
            <t:dgCol title="结案申请单号" field="billCode" ></t:dgCol>
            <t:dgCol title="结案明细编码"  field="auditCode" ></t:dgCol>
            <t:dgCol title="附件数量" field="fileNumber" ></t:dgCol>
            <t:dgCol title="备注" field="workFlowRemark"  editor="{type:'text'}"></t:dgCol>



            <t:dgToolBar title="门店结案金额录入"   url=""  funname="goSelectQuotaTerminalAdd"  icon="icon-add"   ></t:dgToolBar>
            <t:dgToolBar title="保存" icon="icon-save" onclick="saveQuotaAuditForm()" ></t:dgToolBar>
            <t:dgToolBar title="附件查看"  icon="icon-look" url="" funname="fileUpload"></t:dgToolBar>

            <t:dgToolBar title="查看客户图片"  url=""  funname="detailPicture"  icon="icon-look"  width = "1000" height = "500"></t:dgToolBar>
            <t:dgToolBar title="客户导出" icon="icon-dataOut" url="ttAuditMainExportController.do?exportCustomerXls" funname="excelCustomerExport"></t:dgToolBar>
            <t:dgToolBar title="门店导出" icon="icon-dataOut" url="ttAuditMainExportController.do?exportTerminalIncludeImgXls" funname="excelTerminalExport"></t:dgToolBar>
            <t:dgToolBar title="移除" icon="icon-remove" url="" funname="deleteALLAuditSelect"></t:dgToolBar>
            <t:dgToolBar title="流程日志" icon="icon-log"  url="ttAuditMainLogController.do?goTtAuditMainLogMainByAuditId"  funname="detailLog1" width="1200" height="600"></t:dgToolBar>
            <t:dgToolBar title="日志" url="tmLogController.do?goTmLogDetailMain"  funname="detailLog1"  icon="icon-log"  width = "1000" height = "500"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        //绑定当行点击事件
        $('#ttAuditQuotaList').datagrid({
            onClickRow: function(index,row){
                    editRow(index,row);
            }
        });
        $("#ttAuditQuotaList_toolbar_div").parent().append("&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
        $("#ttAuditQuotaList_toolbar_div").remove();
    })
    function loadTotal(){
        var queryParams = $('#ttAuditQuotaList').datagrid('options').queryParams;
        $('#ttAuditQuotaListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttAuditQuotaController.do?getRealAuditAmountByTotal&billMainId=${billMainId}";
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("最终结案金额汇总："+d.obj);
            }
        }
        });
    }
    //保存行编辑数据
    function saveQuotaAuditForm(){
        var rows=$("#ttAuditQuotaList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditQuotaList").datagrid("getRowIndex",row);
            $("#ttAuditQuotaList").datagrid('endEdit', rowIndex);
        });

        var updated=$("#ttAuditQuotaList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttAuditQuotaController.do?saveWorkFlowQuotaAuditByRows",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated),billMainId:'${billMainId}'},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    newTip(data.msg,"info");
                    $("#ttAuditQuotaList").datagrid("reload");
                }else {
                    newTip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditQuotaList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });

    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditQuotaList").datagrid('getColumnFields',true).concat($("#ttAuditQuotaList").datagrid('getColumnFields'));
        var subStr  = "30";
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var containTerminal = row.containTerminal;
            var actModeCode = row.actModeCode;
            if((fields[i] == "auditQuantity")&&(actModeCode=="product_act_type"||actModeCode=="ad_act_type"||actModeCode=="longterm_act_type")){
                col.editor = null;
            }
            if ((containTerminal== "1")&&(fields[i] == "auditAmount"||fields[i] == "auditQuantity")){
                col.editor = null;
            }
        }
        $("#ttAuditQuotaList").datagrid('beginEdit', index);

        var editors=$("#ttAuditQuotaList").datagrid('getEditors',index);
        $.each(editors,function (index1,editor){

            if(editor.type=="combobox"){
                if(editor.field=="auditStatus"){
                    $(editor.target).focus();
                    $(editor.target).combobox('reload',"tmTableConfigController.do?dictCombox&dictCode=audit_bkw_status");
                }
            }
            if(editor.field=="auditAmount"){
                editor.target.bind('change',function () {
                    var str = editor.target.val();
                    editors[index1+1].target.val(str);
                });
            }
            if(editor.field=="realAuditAmount"){
                editor.target.bind('focus',function () {
                    var str = editors[index1-1].target.val();
                    editor.target.val(str);
                });

            }
        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }
    //跳转选择门店结案的页面
    function goSelectQuotaTerminalAdd(){
        var ttAuditQuotaTarget = $("#ttAuditQuotaList").datagrid(
            "getSelected");
        if (ttAuditQuotaTarget == null ||  ttAuditQuotaTarget== "") {
            newTip("请选择费用结案子单");
            return false;
        }
        var subStr  = "30";
        var containTerminal = ttAuditQuotaTarget.containTerminal;
        if (containTerminal == "0"){
            newTip("该费用结案子单不能到门店，不能选择门店");
            return false;
        }
        $.dialog({
            title: "添加活动门店",
            content: "url:ttAuditTerminalController.do?goAuditTerminalWorkflowMain&auditId="+ttAuditQuotaTarget.id,
            lock: true,
            width: "1500",
            height: "500",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: function () {
                $("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }
    //客户导出
    function excelCustomerExport() {

        var queryParams = $('#ttAuditQuotaList').datagrid('options').queryParams;
        $('#' + 'ttAuditQuotaList' + 'tb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';
        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#' + 'ttAuditQuotaList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = "ttAuditMainExportController.do?exportCustomerXls&billMainId=${billMainId}";
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.location.href = tagetUrl + encodeURI(fields + params);
    }
    //门店导出
    function excelTerminalExport() {

        var auditMainTarget = $("#ttAuditQuotaList").datagrid("getSelected");

        if (auditMainTarget == null ||  auditMainTarget== "") {
            newTip("请选择一条结案明细申请");
            return false;
        }
        var containTerminal = auditMainTarget.containTerminal;

        if (containTerminal == "0"){
            newTip("该结案的活动不到门店，不能导出！");
            return false;
        }
        var tagetUrl = "ttAuditMainExportController.do?exportTerminalXls&auditId="+auditMainTarget.id;
        $.messager.confirm("确认",'是否导出图片?',function(r){
            if(r){
                tagetUrl = "ttAuditMainExportController.do?exportTerminalIncludeImgXls&auditId="+auditMainTarget.id;
            }
            //菜单id
            var accessEntry=$("#accessEntry").val();
            if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
                tagetUrl+="&accessEntry="+accessEntry;
            }
            window.location.href = tagetUrl;
        });
    }
    //附件上传
    function fileUpload() {
        var auditQuotaList = $("#ttAuditQuotaList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){newTip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){newTip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        //openwindow("附件上传", url,"ttAuditQuotaList", 600, 400);
        safeShowDialog({
            title: "附件查看",
            content: "url:"+url,
            lock: true,
            width: "600",
            height: "400",
            zIndex: 100000000,
            parent: windowapi,
            ok: function () {
                return true;
            },
            cancelVal: false,
            cancel: function () {
                $("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }
    function detailPicture(){
        var auditQuotaList = $("#ttAuditQuotaList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){newTip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){newTip("请选择一条数据","error");return false;}
        var id = auditQuotaList[0].id;
        var containTerminal = auditQuotaList[0].containTerminal;
        var actModeCode = auditQuotaList[0].actModeCode;
        if(containTerminal == "1"){
            newTip("到门店活动，请点击查看门店结案金额查看照片","error");return false;
        }
        if(auditQuotaList[0].actModeCode == 'product_act_type'){
            newTip("产品活动没有查看图片","error");return false;
        }

        if(auditQuotaList[0].actModeCode == 'ad_act_type'){
            newTip("广告费活动没有查看图片","error");return false;
        }
        var url = "ttAuditQuotaPictureController.do?goActPhotoWall&auditId="+id;
        //openwindow("查看图片", url,"ttAuditQuotaList", 1000, 600);
        safeShowDialog({
            title: "查看图片",
            content: "url:"+url,
            lock: true,
            width: "1000",
            height: "600",
            zIndex: 100000000,
            parent: windowapi,
            ok: function () {
                return true;
            },
            cancelVal: false,
            cancel: function () {
                //$("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }


    /**
     * 查看日志事件打开窗口
     */
    function detailLog1(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择查看项目','error');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再查看','error');
            return;
        }
        url += '&load=detail&id=' + rowsData[0].id;
        openwindow(title, url, id, width, height);
    }
    function openwindow(title, url, name, width, height){
        gridname = name;
        width = width || 'auto';
        height = height || 'auto';
        safeShowDialog({
            content: 'url:' + url,
            zIndex:100000,
            title: title,
            cache: false,
            lock: true,
            width: width,
            height: height
        });
    }
    //删除
    function deleteALLAuditSelect() {
        var ids = [];
        var rows = $("#ttAuditQuotaList").datagrid('getSelections');
        if (rows.length > 0) {
            $.messager.confirm("确定","你确定移除该结案明细数据吗？", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        async : false,
                        url : "ttAuditQuotaController.do?deleteTtQuotaAudit",
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                loadTotal();
                                $("#ttAuditQuotaList").datagrid('reload');
                                $("#ttAuditQuotaList").datagrid('unselectAll');
                                ids = '';
                                newTip(msg,'info');
                            }else{
                                newTip(msg,'error');
                                return;
                            }
                        }
                    });
                }
            });
        } else {
            newTip("请选择需要删除的数据","error");
        }
    }
</script>