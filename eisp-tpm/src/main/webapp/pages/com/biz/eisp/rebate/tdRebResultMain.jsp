<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true" id="rebateListtb_r" >
    <div region="center" style="padding:1px;">
        <t:datagrid name="dtRebateList" checkbox="true" fitColumns="false" title="客户返利报表"
                    actionUrl="rebResultController.do?findTdRebResultList" idField="id" fit="true" queryMode="group" pageSize="30">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="经销商编码" field="kunnr" query="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="经销商名称" field="kunnrName" queryMode="single" query="true"  width="70"></t:dgCol>
            <t:dgCol title="返利名称" field="rebName" query="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="返利组" field="rebGroup"  queryMode="single" query="true" dictionary="rebate_group" width="60" ></t:dgCol>
            <t:dgCol title="返利周期" field="timeType" queryMode="single"  dictionary="dic_time_type" query="true" width="120"></t:dgCol>
            <t:dgCol title="返利值" field="num" queryMode="single"  width="70"></t:dgCol>
            <t:dgCol title="单位" field="unit"  queryMode="single"  width="70"></t:dgCol>
            <t:dgCol title="返利计算时间" field="cacuTime" queryMode="single" query="true" formatter="yyyy-MM-dd"   width="130"></t:dgCol>
             <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut"
                         url="rebResultController.do?exportRebResultXls"
                         funname="excelExport" ></t:dgToolBar>
        </t:datagrid>

    </div>
</div>

<script>
$(function(){
	$("#rebateListtb_r").find("input[name='cacuTime']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
});
	
</script>

