<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report024List"  title="活动照片统计"
                    pagination="true" autoLoadData="true" actionUrl="report024Controller.do?findReportList" idField="id" fit="true">
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="用户名" field="userName" width="150" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="司机编码" field="drpCode" width="150" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="终端编码" field="terminalCode" width="150" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="终端名称" field="terminalName" width="150" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="手机型号" field="cellPhoneModel" width="150" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="是否丢失" field="businessId" width="150" sortable="false" dictionary="nooryes" query="true"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" width="150" queryMode="group" formatter="yyyy-MM-dd HH:mm:ss"  sortable="false" query="true"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report024Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>

    </div>
</div>
<script>
    $(function () {

        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });

        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });

        $("#report024Listtb_r").find("input[name='billingDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report024Listsearch() {
        var orgCode = $("#report024Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report024Listtb_r").find("input[name='yearMonth']").val();



        var queryParams = $("#report024List").datagrid('options').queryParams;
        $("#report024Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report024List").datagrid({url:'report024Controller.do?findReportList'});
    }

</script>
