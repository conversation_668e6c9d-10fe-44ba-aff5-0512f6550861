<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>产品政策计算</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
<div region="center" fit="true">
<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" action="ttProductCalculateController.do?doTtProductCalculate">
    <div class="form">
        <label class="Validform_label">事业部:</label>
        <select name="orgCode">
            <c:forEach items="${orgList}" var="i">
                <option value="${i.orgCode}">${i.orgName}</option>
            </c:forEach>
        </select>
    </div>

    <div class="form">
        <label class="Validform_label">核销年月:</label>

        <input type="text" id="yearMonth" name="yearMonth"
               onfocus="WdatePicker({dateFmt:'yyyy-MM'})"
               readonly="readonly" class="Wdate" style="width: 100px; " />
    </div>
</div>
</t:formvalid>
</div>

</body>
</html>

<script type="text/javascript">

</script>
