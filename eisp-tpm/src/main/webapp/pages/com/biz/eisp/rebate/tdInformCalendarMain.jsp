<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid  name="tdInformCalendarList" checkbox="true" fitColumns="true" title="信息建设费日历管理"
                    singleSelect="false" actionUrl="informCalendarController.do?findTdInformCalendarList" idField="id" fit="true" queryMode="group" pageSize="30">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年份" field="year" query="true"></t:dgCol>
            <t:dgCol title="月份" field="month" query="true"></t:dgCol>
            <t:dgCol title="季度" field="season"></t:dgCol>
            <t:dgCol title="应上传天数" field="uploadDay"></t:dgCol>
            <t:dgCol title="实际上传天数" field="actualDay"></t:dgCol>
            <t:dgToolBar title="新增" operationCode="add" icon="icon-add" url="informCalendarController.do?goInformCalendarForm" funname="add" height="450" width="450"></t:dgToolBar>
            <t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="informCalendarController.do?goInformCalendarForm" funname="update" height="450" width="450"></t:dgToolBar>
            <t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="" funname="deleteInformCalendar()" ></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="informCalendarController.do?exportInformCalendarXls" funname="excelExport" ></t:dgToolBar>

        </t:datagrid>

    </div>
</div>

<script type="text/javascript">
 /** 删除 */
 function deleteInformCalendar(){
	 debugger;
	 var ids="";
	 var selectData = $("#tdInformCalendarList").datagrid('getSelections');
	 if(selectData.length>0){
		 for(var i=0;i<selectData.length;i++){
			 if(ids.length>0){
				 ids+=","+selectData[i].id;
			 }else{
				 ids+=selectData[i].id;
			 }
		 }
	 }else{
		 tip("请选择需要删除数据");
		 return;
	 }
	 $.messager.progress({text:'数据操作中~~~~'});
	 $.post("informCalendarController.do?deleteInformCalendar",
			 { "id": ids },function(data){
			  $.messager.progress('close');
		       tip(data.msg);
		       reloadTable();
			   }, "json");
 }

 </script>
