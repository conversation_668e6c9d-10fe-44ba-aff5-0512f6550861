<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<div id="tmCostAccountSelectedMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
	    <t:datagrid name="ttCostAccountSelectedList" pagination="false" fitColumns="true" title="已关联科目" queryMode = "group" singleSelect="false"
	    onLoadSuccess="loadElecteGrid"  idField="id" actionUrl="${datagridUrl }">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="费用类型名称" field="costTypeName"></t:dgCol>
	        <t:dgCol title="费用类型编码" field="costTypeCode" hidden="true"></t:dgCol>
	        <t:dgCol title="费用科目名称" field="accountName"></t:dgCol>
	        <t:dgCol title="费用科目编码" field="accountCode" hidden="true"></t:dgCol>
	    	<t:dgToolBar title="移除" icon="icon-remove"  onclick="removeAccount()"></t:dgToolBar>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
	//移除
	function removeAccount(){
		var checkListTarget =  $("#ttCostAccountSelectedList").datagrid("getSelections");
		if(checkListTarget==null || checkListTarget==""){
			tip("请至少选择一条数据");
			return false;
		}
		var selectRows = [];
		//选中数据加入数组
		for (var i = 0; i < checkListTarget.length; i++) {
			selectRows.push(checkListTarget[i]);
		}
		for (var i = 0; i < selectRows.length; i++) {
			var checkRowIndex = $("#ttCostAccountSelectedList").datagrid("getRowIndex",selectRows[i]);
			//移除该数据
			$("#ttCostAccountSelectedList").datagrid("deleteRow",checkRowIndex);
		}
		loadElecteGrid();
	}
	//加载待选角色
	function loadElecteGrid(){
		//加载待选列表
		var excludeId = "'-1'";//默认一个值
		var checkedTarget = $("#ttCostAccountSelectedList").datagrid("getRows");
		if(checkedTarget != null && checkedTarget != ""){
			excludeId = "";
			for(var i = 0;i<checkedTarget.length;i++){
				if(excludeId != ""){
					excludeId+=",";
				}
				excludeId += "'"+checkedTarget[i].id+"'";
			}
		}
		$('#tmCostAccountList').datagrid({
			queryParams: {
				excludeId: excludeId
			}
		});
	}
</script> 