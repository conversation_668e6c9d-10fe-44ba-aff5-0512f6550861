<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<link rel="stylesheet" href="resources/codemirror/lib/codemirror.css">
<link rel="stylesheet" href="resources/codemirror/theme/eclipse.css">
<style>
    .CodeMirror { height: auto; border: 1px solid #ddd; }
    .CodeMirror-scroll { max-height: 390px; }
    .CodeMirror pre { padding-left: 7px; line-height: 1.25; }
</style>

<div fit="true">
    <textarea id="javaScriptContent"></textarea>
</div>

<script src="resources/codemirror/lib/codemirror.js" type="text/javascript" charset="utf-8"></script>
<script src="resources/codemirror/mode/javascript/javascript.js"></script>
<script>

    //获取父窗口
    var json_$ = getSafeJq();
    //获取取值控件的id
    var hiddenJavascriptEditorGetId = json_$("#hiddenJavascriptEditorGetId").val();

    var parentJavaScriptContent = json_$("#" + hiddenJavascriptEditorGetId).val();

    document.getElementById("javaScriptContent").value = parentJavaScriptContent || "";
    var editor = CodeMirror.fromTextArea(document.getElementById("javaScriptContent"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/typescript"
    });

    editor.on("change", function (instance, object) {
        console.log(editor.getValue());
        document.getElementById("javaScriptContent").value = editor.getValue();
    });

</script>

