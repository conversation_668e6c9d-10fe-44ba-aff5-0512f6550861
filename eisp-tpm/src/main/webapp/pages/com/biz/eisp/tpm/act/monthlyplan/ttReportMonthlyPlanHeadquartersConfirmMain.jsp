<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}

	.qf{/* 区分--qufen-qf-- =.=!! */}

	.ownTabs li {
		margin: -4px 4px 0 0;
		height: 25px;
		float : left;
	}
	.ownTabs li a.tabs-inner {
		height: 25px;
		line-height: 39px;
		background: #6c94c2;
		border-color: #6c94c2;
		color: #fff;
		transition: .4s;
		filter:none;
	}

	.ownTabs li.tabs-selected a.tabs-inner,.ownTabs li:hover a.tabs-inner {
		background: #ececec;
		color:#000000;
		filter:none;
	}
	.ownTabs li.tabs-selected a.tabs-close,.ownTabs li:hover a.tabs-close {
		display: block;
	}
	.ownTabs li.tabs-selected a.tabs-inner {
		font-weight: bold;
		outline: none;
	}

	.ownTabs li a.tabs-inner {
		display: inline-block;
		text-decoration: none;
		margin: 0;
		padding: 0 10px;
		height: 25px;
		line-height: 25px;
		text-align: center;
		white-space: nowrap;
		border-width: 1px;
		border-style: solid;
		-moz-border-radius: 5px 5px 0 0;
		-webkit-border-radius: 5px 5px 0 0;
		border-radius: 5px 5px 0 0;
	}
	ul li {
		list-style: none;
	}
	ul {
		display: block;
		list-style-type: disc;
		-webkit-margin-before: 0em;
		-webkit-margin-after: 0em;
		-webkit-margin-start: 0px;
		-webkit-margin-end: 0px;
		-webkit-padding-start: 0px;
	}
</style>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'north',title:'月度计划总部审核'" style="margin: 3px 0px 1px 1px;height:118px;background-color: EEEEEE;" >
		<div class="qf panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" id="headQuartersSubmit" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-ok" onClick="submitForm()">提交</a>
							<a href="#" id="headQuartersUpdateEdit" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-edit" onClick="changeUpdate()">指定修改</a>
							<%--<a href="#" id="headQuartersDataOut" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-dataOut" onClick="ownExcelExport()">导出</a>--%>
							<a href="#" id="headQuartersDataIn" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-dataIn" onClick="ownImportDataByXml()">导入</a>
							<a href="#" id="outAsSalesDepart" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-dataOut" onClick="ownExcelExportByUrl('ttMonthlyPlanHeadquartersConfirmController.do?exportXlsXSB')">按销售部导出</a>
							<a href="#" id="outAsProduct" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-dataOut" onClick="ownExcelExportByUrl('ttMonthlyPlanHeadquartersConfirmController.do?exportXlsProduct')">按产品别导出</a>
							<a href="#" id="outAsDeatilData" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-dataOut" onClick="ownExcelExportByUrl('ttMonthlyPlanHeadquartersConfirmController.do?exportXlsDetailData')">按详细数据导出</a>
							<a href="#" id="outAsDetal" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-dataOut" onClick="ownExcelExportByUrl('ttMonthlyPlanHeadquartersConfirmController.do?exportXlsDetail')">导出明细</a>
							<div style="color:red;float: right;margin-top: 5px;margin-left: 50px;">
								<label><b>销售额汇总:</b>&nbsp;</label>
								<span id="showTotalAmount" >***</span>
								&nbsp;&nbsp;元&nbsp;
							</div>
						</span>
						<span style="float:right">
							<a href="#" class="easyui-linkbutton" iconcls="icon-search" onclick="ttMonthlyPlanGatherListSearchFunction()">查询</a>
							<a href="#" class="easyui-linkbutton" iconcls="icon-reload" onclick="searchReset()">重置</a>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search" style=" height: 49px;">
		            	<form>
		            		<div>
		            			<label>计划年份</label>
		            			<input type="text" name="year" id="year" class="Wdate"
								onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){chooseYearAndMonthSearch(); }})" readonly="readonly" />
		            			<span style="color:red;">*</span>
		            		</div>
		            		<div>
		            			<label>计划月份</label>
		            			<input type="text" name="month" id="month" class="Wdate"
								onclick="WdatePicker({dateFmt:'MM',onpicked:function(){ chooseYearAndMonthSearch(); }})" readonly="readonly" />
		            			<span style="color:red;">*</span>
		            		</div>
		            		<div>
		            			<label>组织</label>
		            			<%--<input type="text" name="orgCode" id="orgCode" lass="inuptxt" />--%>
								<%--<t:inputSelect id="orgCode" name="orgCode" method="" code=""--%>
								<%--<t:dictSelect field=""--%>
								<%--<t:comboBox name="" url=""--%>
									<select name="orgCode" id="orgCode">
										<option value="">--请选择--</option>
										<c:forEach items="${ttMonthlyPlanVos}" var="ttMonthlyPlanVo">
											<option value="${ttMonthlyPlanVo.orgCode}">${ttMonthlyPlanVo.orgName}</option>
										</c:forEach>
									</select>
		            		</div>
		            		<div>
		            			<label>客户编码</label>
		            			<input type="text" name="customerCode" id="customerCode" lass="inuptxt" onkeyup="value=value.replace(/[^a-z^A-Z^0-9]/g,'')"/>
		            		</div>
		            		<div>
		            			<label>客户名称</label>
		            			<input type="text" name="customerName" id="customerName" lass="inuptxt" />
		            		</div>
							<div>
								<label title="产品编号">产品编号</label>
								<input style="width: 100px" type="text" name="productCode" id="productCode" class="inuptxt" onkeyup="value=value.replace(/[^a-z^A-Z^0-9]/g,'')" >
							</div>
							<div>
								<label title="产品名称">产品名称</label>
								<input style="width: 100px" type="text" name="productName" id="productName" class="inuptxt" >
							</div>
							<label id="productNotice" title="注*：按产品查询时，不会对销售部别数据产生影响" style="color: red;"><%--&nbsp;&nbsp;注*&lt;%&ndash;<img src="resources/img/annotation.png">&ndash;%&gt;--%></label>
							<label id="custNotice" title="注*：按客户/组织查询时，不会对产品别数据和详细数据产生影响" style="color: red;"><%--&nbsp;&nbsp;注*&lt;%&ndash;<img src="resources/img/annotation.png">&ndash;%&gt;--%></label>
		            	</form>
	            		<div class="top_tip" style="margin-top: 10px;background-color: EEEEEE;" >
							<ul class="ownTabs" id="ulTemp">
								<input type="hidden" value="ttMonthlyPlanGatherList" id="tabs-sel" hidden="hidden" />
								<li  class="tabs-selected" data-id="ttMonthlyPlanGatherList"><a class="tabs-inner" href="javascript:void(0)"  ><span class="tabs-title">销售部别数据</span><span class="tabs-icon"></span></a></li>
								<li  data-id="ttMonthlyPlanProductList" id="priceGuide"><a class="tabs-inner" href="javascript:void(0)" ><span class="tabs-title">产品别数据</span><span class="tabs-icon"></span></a></li>
								<li data-id="ttMonthlyPlanHeadDetailList"><a class="tabs-inner" href="javascript:void(0)" ><span class="tabs-title">详细数据</span><span class="tabs-icon"></span></a></li>
							</ul>
		            	</div>
					</div>
				</div>
				<div class="datagrid-view" >
				</div>
			</div>
		</div>
	</div>
	<div id="dataInfo" data-options="region:'center'" class="tabs-wrap" style="padding: 0px 1px 1px 1px;">
		<t:datagrid name="ttMonthlyPlanGatherList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
					autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanGatherList" onLoadSuccess="monthlyPlanGatheronLoadSuccessFun" >
			<t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" ></t:dgCol>
			<t:dgCol title="销售部名称" field="salesDepartName" sortable="false" ></t:dgCol>
			<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="环比（元）" field="sequential" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="差异额（元）" field="differenceAmount" sortable="false" ></t:dgCol>
			<t:dgCol title="状态" field="bpmStatus" dictionary="monthlyPlan_Bpm_status" sortable="false" ></t:dgCol>
		</t:datagrid>
		<t:datagrid name="ttMonthlyPlanProductList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
					autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanProductList" onLoadSuccess="monthlyPlanProductonLoadSuccessFun" >
			<t:dgCol title="主键" field="id" hidden="true" ></t:dgCol>
			<t:dgCol title="年" field="year" hidden="true" sortable="false" ></t:dgCol>
			<t:dgCol title="月" field="month" hidden="true" sortable="false" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" ></t:dgCol>
			<t:dgCol title="产品编号" field="productCode" sortable="false" ></t:dgCol>
			<t:dgCol title="产品名称" field="productName" sortable="false" ></t:dgCol>
			<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="环比（元）" field="sequential" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="差异额（元）" field="differenceAmount" sortable="false" ></t:dgCol>
			<t:dgCol title="吨位" field="tonnage" sortable="false" ></t:dgCol>
		</t:datagrid>
		<t:datagrid name="ttMonthlyPlanHeadDetailList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
					autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findttAddMonthlyPlanHeadquartersDetailList" onLoadSuccess="monthlyPlanHeadDetailonLoadSuccessFun" >
			<t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
			<t:dgCol title="年" field="year" hidden="true" sortable="false" ></t:dgCol>
			<t:dgCol title="月" field="month" hidden="true" sortable="false" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" ></t:dgCol>
			<t:dgCol title="销售部编码" field="salesDepartCode" hidden="true" sortable="false" ></t:dgCol>
			<t:dgCol title="销售部名称" field="salesDepartName" sortable="false" ></t:dgCol>
			<t:dgCol title="产品编号" field="productCode" sortable="false" ></t:dgCol>
			<t:dgCol title="产品名称" field="productName" sortable="false" ></t:dgCol>
			<t:dgCol title="计划销量（EA）" field="planSales" sortable="false" ></t:dgCol>
			<t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false" ></t:dgCol>
			<t:dgCol title="吨位" field="tonnage" sortable="false" ></t:dgCol>
			<t:dgCol title="单品销售额（元）" field="totalPlanSales" sortable="false" ></t:dgCol>
			<t:dgCol title="操作" field="opt" sortable="false" ></t:dgCol>
			<t:dgFunOpt title="查看" funname="operationDetail(salesDepartCode,productCode,yearMonth)"></t:dgFunOpt>
		</t:datagrid>
	</div>
</div>

<%--隐藏ul--%>
<%--<div hidden="hidden" id="ulDiv">
	<ul class="ownTabs" id="ulTemp">
		<li  class="tabs-selected" data-id="ttMonthlyPlanGatherList"><a class="tabs-inner" href="javascript:void(0)"  ><span class="tabs-title">销售部汇总</span><span class="tabs-icon"></span></a></li>
		<li  data-id="ttMonthlyPlanProductList" id="priceGuide"><a class="tabs-inner" href="javascript:void(0)" ><span class="tabs-title">总部产品汇总</span><span class="tabs-icon"></span></a></li>
		<li data-id="ttMonthlyPlanHeadDetailList"><a class="tabs-inner" href="javascript:void(0)" ><span class="tabs-title">详情</span><span class="tabs-icon"></span></a></li>
	</ul>
</div>--%>
<%@include file="/pages/com/biz/eisp/tpm/common/operation.jsp"%>
<script type="text/javascript">
    var searchSingle = 0;
    var isAutoInit = 0;
    var pageListMenu = new pageListMenuList();
    var chooseUseSearchType = 2;
    $(document).ready(function(){
        //放入datagrid tital
//		initGetUlDivOutInDatagridTitle();

        initSomePropertyEvent();
//        $('.tabs-selected').click();
        addInputEvent();
        searchReset();
    });

    function defaultYear(){
        return '${year}'
    }
    function defaultMonth(){
        return '${month}'
    }
    function initInputValue(){
        $("#year").val(defaultYear());
        $("#month").val(defaultMonth());
    }

    //放入datagrid tital
    function initGetUlDivOutInDatagridTitle(){
        $('div[class="panel datagrid"] div[class="panel-header"] div[class="panel-title"]').html($('#ulDiv').html());
	}

    //给input标签添加时间
    function addInputEvent(){
        //去除所有回车监控
        $('#ttReportMonthlyPlanMain .datagrid-toolbar-search input').removeAttr("onkeypress").removeAttr("onkeydown");
//        onkeypress="EnterPress(event)" onkeydown="EnterPress(event)
        //添加回车监控
        $('#ttReportMonthlyPlanMain .datagrid-toolbar-search input[readonly != "readonly" ][disabled != "disabled"] ').attr("onkeypress","EnterPress(event)").attr("onkeydown","EnterPress(event)");
//        $('#ttReportMonthlyPlanMain .datagrid-toolbar-search select').change(function(){EnterPress(event)});
    }
    //初始绑定点击事件
	function initSomePropertyEvent(){
			 $("#ulTemp li").click(function() {
			 $(".tabs-selected").removeClass("tabs-selected");
			 $(this).addClass("tabs-selected");
			 /*var obj = $('#' + $(this).attr("data-id")).parents('div[class="panel datagrid"]');
			$('div[class="panel datagrid"]').hide();
			obj.show();*/
			$("#tabs-sel").val($(this).attr("data-id"));
            changeDatagridShow();
			changeSearchParamsShow();
		});
	}
	//改变显示
	function changeDatagridShow(){
        var obj = $('#' + returnTabsSelectedVal()).parents('div[class="panel datagrid"]');
        $('div[class="panel datagrid"]').hide();
        obj.show();
	}

	function returnTabsSelectedVal(){
        return $("#tabs-sel").val();
	}

	function pageListMenuList(){
	    this.oneList = "ttMonthlyPlanGatherList";
        this.twoList = "ttMonthlyPlanProductList";
        this.threeList = "ttMonthlyPlanHeadDetailList";
	}

	function changeSearchParamsShow() {
		var key = returnTabsSelectedVal();
//        orgCode,customerCode,customerName,productCode,productName,sqlbuilder,year,month,productCode,productName
//        orgCode,customerCode,customerName,productCode,productName
        var showStr = "";
        var hideStr = "";
        var labelShowStr = "";
        var labelHideStr = "";
		switch (key){
			case pageListMenu.oneList:
                showStr = "orgCode,customerCode,customerName";
                hideStr = "productCode,productName";
                labelShowStr = "custNotice";
                labelHideStr = "productNotice";
                break;
            case pageListMenu.twoList:
                hideStr = "orgCode,customerCode,customerName";
                showStr = "productCode,productName";
                labelShowStr = "productNotice";
                labelHideStr = "custNotice";
                break;
            case pageListMenu.threeList:
                hideStr = "orgCode,customerCode,customerName";
                showStr = "productCode,productName";
                labelShowStr = "productNotice";
                labelHideStr = "custNotice";
                break;
			default : tip("?????---咋回事？"); return ;
		}
        changeHideOrShow(hideStr,showStr);
        changeHideOrShowLabel(labelHideStr,labelShowStr);
        reloadTtMonthlyPlanListDatagridChooseDoFun(key);
    }

    //选择处理方法
    function reloadTtMonthlyPlanListDatagridChooseDoFun(key) {
        if(chooseUseSearchType == 1){
            reloadTtMonthlyPlanListDatagrid(key);
        }else if(chooseUseSearchType == 2){
            reloadTtMonthlyPlanListDatagridTwo(key);
        }else{
            tip("选取失败");
        }
    }

    //单个页面读取
    var nTemp = 0;
    function reloadTtMonthlyPlanListDatagridTwo(grid){
        if (searchSingle == 0 && nTemp != 1){
            nTemp = 1;
            ttMonthlyPlanGatherListSearchFunction();
        }else{
            nTemp = 0;
            searchSingle = 0;
            getAllTotalAmount(grid);
        }
    }
	//全页页面读取
    function reloadTtMonthlyPlanListDatagrid(grid){
        if (searchSingle == 0 && nTemp != 1){
            nTemp = 1;
            if(isAutoInit == 0){
                ttMonthlyPlanGatherListSearchFunction();
                isAutoInit = 1;
            }else{
                $('#' + grid).datagrid("reload");
            }
        }else{
            nTemp = 0;
            searchSingle = 0;
            getAllTotalAmount(grid);
        }
    }
    //汇总销售额
    function getAllTotalAmount(grid){
        var thisDatas = $('#' + grid).datagrid('options').queryParams
        var url = "ttMonthlyPlanHeadquartersConfirmController.do?getAllTotalAmount";
        var d = ajaxPost(thisDatas,url)
        if (d.success){
            $('#showTotalAmount').html(accounting.formatMoney(d.obj,""));
            return true;
        }
        tip(d.msg);
        return false;
    }
	function changeHideOrShow(hide,show) {
	    var hides = hide.split(",");
		for (var i in hides){
		    $('#' + hides[i]).parent('div').hide();
            $('#' + hides[i]).val('');
		}
        var shows = show.split(",");
        for (var i in shows){
            $('#' + shows[i]).parent('div').show();
        }
    }
    function changeHideOrShowLabel(hide,show) {
        var hides = hide.split(",");
        for (var i in hides){
            $('#' + hides[i]).hide();
        }
        var shows = show.split(",");
        for (var i in shows){
            $('#' + shows[i]).show();
        }
    }
    /**
     * 提交审批 XXX
     */
    function submitForm() {
        var year = $('#year').val();
        if (year == '') {
            tip("填写年份");
            return ;
        }
        var month = $('#month').val();
        if (month == '') {
            tip("填写月份");
            return ;
        }
        var thisData = {
            year : year,
            month : month
		}
		//先检查是否有销售部未创建的--及继续
		checkTheDataIsHasNotCreateMonthlyPlanDataAndContinue(thisData);
    }
    //先检查是否有销售部未创建的--及继续
    function checkTheDataIsHasNotCreateMonthlyPlanDataAndContinue(thisData){
		var tips = returnConfirmTips(thisData);
		if(tips == ''){
            continueSubmit(thisData);
		}else{
            getSafeJq().dialog.confirm(tips + "是否继续?", function(r) {
                setTimeout(function(){ continueSubmit(thisData); },0);
            });
		}

	}

	function returnConfirmTips(thisData){
        var showMsg = checkTheDataIsHasNotCreateMonthlyPlanDatasSale(thisData);
        if(checkIsNotUndefinedAndNullAndNullValue(showMsg)){
            return showMsg;
        }
        return '';
	}


	//检查还有几个销售部
    function checkTheDataIsHasNotCreateMonthlyPlanDatasSale(thisData){
        var url = "ttMonthlyPlanHeadquartersConfirmController.do?checkTheDataIsHasNotCreateMonthlyPlanDatasSale";
        var d = ajaxPost(thisData,url)
        if (d.success){
            return d.obj;
        }
        return '';
	}

	//继续提交
	function continueSubmit(thisData){
        if(checkZBIsCouldSubmit(thisData)){
            starSubmit(thisData);
        }
	}
    function checkZBIsCouldSubmit(thisDatas){
        var url = "ttMonthlyPlanWorkFlowController.do?checkZBIsCouldSubmit";
        var d = ajaxPost(thisDatas,url)
        if (d.success){
            return true;
        }
        tip(d.msg);
        return false;
	}
	function starSubmit(thisData){
        var data = changeDataToUrlData(thisData);
        $.dialog({
            title: "提交流程",
            content: "url:ttMonthlyPlanWorkFlowController.do?goTtMonthlyPlanSubmitMain" + data,
            lock: true,
            width: "1200",
            height: "550",
//            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                $('#btn_sub', this.iframe.contentWindow.document).click();
                return false;
            },
            cancelVal: '关闭',
            cancel: true
        });
	}
	//改变编辑状态
	function changeUpdate(){
		createwindowExt("修改编辑状态","ttMonthlyPlanHeadquartersConfirmController.do?goChangeUpdateStatus","1200","650",{
	    	 button : [
	 	            {name : "关闭",
		            callback : function() {
		            	return true;
	            }}
	   	 ] });
	}

    //打开明细--行级
    function rowLeveloperationDetail(index,rowData){
        var data = rowData;
        var salesDepartCode = data.salesDepartCode;
        var productCode = data.productCode;
        var yearMonth = data.yearMonth;
        createwindowExt("查看明细","ttMonthlyPlanHeadquartersConfirmController.do?gotTtAddMonthlyPlanHeadquartersDetailList"
            + "&salesDepartCode=" + salesDepartCode + "&productCode=" + productCode
            + "&yearMonth=" + yearMonth,"1000","650",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }
    //打开明细--列级
    function operationDetail(salesDepartCode,productCode,yearMonth){
        var salesDepartCode = salesDepartCode;
        var productCode = productCode;
        var yearMonth = yearMonth;
        createwindowExt("查看明细","ttMonthlyPlanHeadquartersConfirmController.do?gotTtAddMonthlyPlanHeadquartersDetailList"
            + "&salesDepartCode=" + salesDepartCode + "&productCode=" + productCode
            + "&yearMonth=" + yearMonth,"1000","650",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }
	function chooseYearAndMonthSearch(){
		var year = $('#year').val();
		if (year == '') {
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			return ;
		}
		ttMonthlyPlanGatherListSearchFunction();
	}
	//回车监控
    function EnterPress(e) {
        var e = e || window.event;
        if(e.keyCode == 13) {
            ttMonthlyPlanGatherListSearchFunction();
        }
    }
	//查询
	function ttMonthlyPlanGatherListSearchFunction(){
	    debugger;
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var searchData = {
            year : year,
            month : month
		}

        searchSingle = 1;

		//选择查询方式
		if (chooseUseSearchType == 1){
            searchAllList(searchData);
		}else if(chooseUseSearchType == 2){
            chooseSearchSingleList(searchData);
		}else{
		    tip("查询失败，请不要乱动全局变量，谢谢！");
		    return ;
		}
	}

	function chooseSearchSingleList(searchData){
        var key = returnTabsSelectedVal();
		switch (key){
			case pageListMenu.oneList://查询销售部汇总
                ttMonthlyPlanGatherListSearchStar(searchData);
			    break;
			case pageListMenu.twoList://查询产品汇总
                ttMonthlyPlanProductListSearchStar(searchData);
			    break;
            case pageListMenu.threeList://查询详细数据
                ttMonthlyPlanHeadDetailListSearchStar(searchData);
                break;
			default : tip("?????---咋回事？"); return ;
		}
	}

    /**
	 * 同事查询所有列表数据
     */
	function searchAllList(searchData){
        //查询销售部汇总
        ttMonthlyPlanGatherListSearchStar(searchData);
        //查询产品汇总
        ttMonthlyPlanProductListSearchStar(searchData);
        //查询详细数据
        ttMonthlyPlanHeadDetailListSearchStar(searchData);
	}

	//查询销售部汇总
	function ttMonthlyPlanGatherListSearchStar(searchData){
        searchData.customerCode = $('#customerCode').val()
        searchData.customerName = $('#customerName').val();
        searchData.orgCode = $('#orgCode').val();
        var url = "ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanGatherList";
        sharingSearch("ttMonthlyPlanGatherList",url,searchData)
	}
    //查询产品汇总
    function ttMonthlyPlanProductListSearchStar(searchData){
        searchData.productCode = $('#productCode').val()
        searchData.productName = $('#productName').val();
        var url = "ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanProductList";
        sharingSearch("ttMonthlyPlanProductList",url,searchData)
    }
    //查询详细数据
    function ttMonthlyPlanHeadDetailListSearchStar(searchData){
        searchData.productCode = $('#productCode').val()
        searchData.productName = $('#productName').val();
        var url = "ttMonthlyPlanHeadquartersConfirmController.do?findttAddMonthlyPlanHeadquartersDetailList";
        sharingSearch("ttMonthlyPlanHeadDetailList",url,searchData)
    }
	//共用查询
	function sharingSearch(grid,url,searchData) {
        var grObj = $('#' + grid );
        var queryParams = grObj.datagrid('options').queryParams;
        encapsulationSearchParameters(queryParams,searchData);
        grObj.datagrid({url:url });
    }
    function monthlyPlanGatheronLoadSuccessFun(rowData){
//        setTimeout("onLoadSuccessFun('ttMonthlyPlanGatherList')",0);
        syncTemp("onLoadSuccessFun('ttMonthlyPlanGatherList')");
    }
    function monthlyPlanProductonLoadSuccessFun(rowData){
//        setTimeout("onLoadSuccessFun('ttMonthlyPlanProductList')",20);
        syncTemp("onLoadSuccessFun('ttMonthlyPlanProductList')");
    }
    function monthlyPlanHeadDetailonLoadSuccessFun(rowData){
//        setTimeout("onLoadSuccessFun('ttMonthlyPlanHeadDetailList')",40);
        syncTemp("onLoadSuccessFun('ttMonthlyPlanHeadDetailList')");
    }

    //目标执行数
    var targ = 0;
    function syncTemp(fun){
        targ ++ ;
        setTimeout(fun,80);
    }
	//伪同步锁
	var lockStr = '';
	function checklock(name){
	    if(lockStr == ''){
            lockStr = name
            return true;
		}else{
			setTimeout("onLoadSuccessFun(" + name + ")",200);
		}
		return false;
	}
    var iTemp = 0;

   	function onLoadSuccessFun(name){
   	    if(checklock(name)){
            iTemp ++;
            if (iTemp == targ){
                if(chooseUseSearchType == 2){
                    searchSingle = 1;
                }
                $('.tabs-selected').click();
                iTemp = 0;
                targ = 0;
            }
            lockStr = '';
		}
	}
    function encapsulationSearchParameters(queryParams,searchData) {
        for(key in searchData){
            queryParams[key] = searchData[key];
        }
    }

	//重置
	function searchReset(){
        initInputValue();
        setTimeout("ttMonthlyPlanGatherListSearchFunction()",0);
        setAllParmsNull();
	}
	function setAllParmsNull(){
        $('div[class="qf panel datagrid"] form input[class!="Wdate"]').val('');
	}

	//导出
	function ownExcelExport(){
        var year = $('#year').val();
        if (year == '') {
            tip("填写年份");
            return ;
        }
        var month = $('#month').val();
        if (month == '') {
            tip("填写月份");
            return ;
        }
        //获取组织？？--待定

        var thisData = {
            year : year,
            month : month
		}

        var url = "ttMonthlyPlanHeadquartersConfirmController.do?exportXls" + changeDataToUrlData(thisData);
        window.open(url);
	}

	function ownImportDataByXml() {
        importDataByXml({impName:'ttMonthlyPlan', gridName:'ttMonthlyPlanGatherList'})
    }
    //导出数据--传入url
    function ownExcelExportByUrl(url){
        var year = $('#year').val();
        if (year == '') {
            tip("填写年份");
            return ;
        }
        var month = $('#month').val();
        if (month == '') {
            tip("填写月份");
            return ;
        }
        var thisData = {
            year : year,
            month : month
        }
        //批量封装数据
        var filds = "orgCode,customerCode,customerName,productCode,productName";
        enExportSearchData(filds,thisData)

		url = url + changeDataToUrlData(thisData);
        window.open(url);

    }

    function enExportSearchData(fildStr,obj){
        var fildarr = fildStr.split(",");
		for(var i in fildarr){
		    var fild = fildarr[i];
            obj[fild] = returnNotNullValueValue($('#' + fild).val())
		}
	}


    //处理键值对
    function HashKey(){
        var data = {};
        this.set = function(key,value){   //set方法
            data[key] = value;
        };
        this.unset = function(key){     //unset方法
            delete data[key];
        };
        this.get = function(key){     //get方法
            return data[key] || "";
        }
        this.returnKey = function(){
            var arrTemp = [];
            for(name in data){
                arrTemp.push(name);
            }
            return arrTemp;
        }
    }
    //返回不为‘null’的数据--避免传到后台为 undefined
    function returnNotNullValueValue(obj){
        if(typeof(obj) != 'undefined' && obj != '' && obj != null && obj != 'null' ){
            return obj;
        }
        return '';
    }
    //将obj转换为urlData
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }
</script>