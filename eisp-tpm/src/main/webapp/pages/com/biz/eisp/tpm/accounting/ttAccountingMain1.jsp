<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>上账</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
    <script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
    <style>
    </style>
</head>
<body>
<div class="easyui-layout" fit="true" id="layoutId">
    <div id="huobuhiddendiv" style="display: none">
        <label id="hbLabel" class="Validform_label">货补产品:</label>
        <input name="productName" id="productName" readonly="readonly" type="text" class="inputxt" value="" />
        <a href="#" id="hba" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="selectProduct(1,this);"></a>
    </div>
    <div id="acconthiddendiv" style="display: none">
        <a id="addpzBtn" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" onclick="addVoucher()">添加</a>
    </div>
    <%--待处理上账数据模块开始--%>
    <div id="westDiv" data-options="region:'center'" style='padding:1px;'>
        <div class="easyui-layout" fit="true">
            <%--列表数据--%>
            <div id="accountingDiv" data-options="region:'center'" style="padding:1px;">
                <div id="tb" style="height:auto">
                    <span style="color:red;" >未上账金额：<span id="notAccountMoney">0.00</span></span>
                    <span style="color:red;" >本次上账金额：<span id="accountMoney">0.00</span></span>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" onclick="addAccounting()">添加上账数据</a>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeAccounting()">移除</a>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-dataOut',plain:true" onclick="accountingExport()">导出</a>
                </div>
                <div class="datagrid-wrap panel-body">
                    <div class="datagrid-view" style="height: 100%; overflow: auto;">
                        <table class="actTable" id="pending_accounting_list">
                            <thead>
                            <tr>
                                <td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
                                <td style="display:none">ID</td>
                                <td>客户</td>
                                <td>产品名称</td>
                                <td>归属事业部名称</td>
                                <td>预算科目</td>
                                <td>结案金额</td>
                                <td>已上账金额</td>
                                <td>未上账金额</td>
                                <td style="display:none">支付方式编码</td>
                                <td>支付方式</td>
                                <td style="display:none">货补产品编码</td>
                                <td>货补产品</td>
                                <td>本次上账金额</td>
                                <td>财务凭证号</td>
                                <td>上账状态</td>
                                <td>结案明细编码</td>
                                <td>上账编码</td>
                                <td>备注</td>
                            </tr>
                            </thead>
                            <tbody id="accountingTbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%--待处理上账数据模块结束--%>

    <%--凭证数据模块开始--%>
    <div id="pingzhengdiv" data-options="region:'east'" title="发票登记" split="true" style="width:400px;padding:1px;" collapsed="true">
        <div class="easyui-layout" fit="true">
            <%--表头数据--%>
            <div id="pzDiv" data-options="region:'north'" style="height:100px;padding:10px;">
                <table>
                    <tr>
                        <td>抬头文本:</td>
                        <td><input class="easyui-textbox" maxlength="50" type="text" id="TTWB" name="TTWB" /></td>
                        <td>凭证附件张数:</td>
                        <td><input class="easyui-textbox" maxlength="3" type="text" onkeyup="checkNum(this)" id="YEMA" name="YEMA" /></td>
                    </tr>
                    <tr>
                        <td>编辑摘要:</td>
                        <td colspan="3"><textarea rows="2" cols="50" id="remarkInput" onchange="changeRemark()"></textarea></td>
                    </tr>
                </table>
            </div>

            <%--列表数据--%>
            <div id="tbDiv" data-options="region:'center'" style="padding:1px;">
                <div id="tb2" style="">
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-reload',plain:true" onclick="reloadVoucher()">刷新</a>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeVoucher()">移除</a>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-dataIn',plain:true" onclick="voucherImport()">导入</a>
                    <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-dataOut',plain:true" onclick="voucherExport()">导出</a>
                    <select style="width: 150px;"  onchange="changeAllFinancial()" onclick="getAllOpion()" id="finacialSelect">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="datagrid-wrap panel-body">
                    <div class="datagrid-view" style="overflow: auto;">
                        <table class="actTable" id="voucherList">
                            <thead>
                            <tr>
                                <td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
                                <td id="id"  style="display:none">ID</td>
                                <td>上账编码</td>
                                <td>发票号</td>
                                <td>结案金额上账会计科目名称</td>
                                <td>结案金额（元）（不含税）</td>
                                <td>税码</td>
                                <td>税金（元）</td>
                                <td>进项税转出金额</td>
                                <td>上账金额（元）</td>
                                <td>上账客户名称</td>
                                <td>SAP成本中心</td>
                                <td>SAP成本中心描述</td>
                                <td>摘要</td>
                            </tr>
                            </thead>
                            <tbody id="voucherTbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--凭证数据模块结束--%>
</div>
</body>
</html>

<script type="text/javascript">
    $(document).ready(function(){
        //获取税率
        initTaxList();
    });
    $(function () {
        setInterval(function () {
            $(window).resize();
        },2000);
    })
    $(window).resize(function () {
        $("#tb").siblings(".datagrid-wrap").height($("#tb").parent().height() - $("#tb").height()).children().css({height:'100%'});
        $("#tb2").siblings(".datagrid-wrap").height($("#tb2").parent().height() - $("#tb2").height()).children().css({height:'100%'});
    })
    //默认上账金额为正
    var accountingType = 1;
    //税率
    var _list =[];
    //获取税率
    function initTaxList(){
        <c:forEach items="${ttTaxConfigList}" var="tax">
        var obj = new Object();

        obj.id = '${tax.id}';//id
        obj.taxCode = '${tax.taxCode}';//税编码
        obj.taxName = '${tax.taxName}';//税名称
        obj.taxcodeCode = '${tax.taxcodeCode}';//税码编码
        obj.taxcodeName = '${tax.taxcodeName}';//税码名称
        obj.taxRate = '${tax.taxRate}';//税率
        _list.push(obj);
        </c:forEach>
    }
    //添加上账
    function addAccounting() {
        var paymentCode = "";//支付方式
        var accountMoneyType = "";
        var len = $("#accountingTbody tr").length;
        if(len > 0){
            $("#accountingTbody tr").each(function(i,o){
                var code = $(this).children("#paymentCode").html();//上账方式
                var auditAmount = $(this).children("#auditAmount").html();//结案金额
                if(auditAmount >= 0){
                    accountMoneyType = "1";
                }else{
                    accountMoneyType = "2";
                }
                paymentCode = code;
            });
        }
        toAddAccountingPage(paymentCode,accountMoneyType);
    }
    //展示添加上账数据页面
    function toAddAccountingPage(paymentCode,accountMoneyType){
        var url = "ttAccountingController.do?goTtAuditedDataMain&paymentCode=" + paymentCode + "&accountMoneyType=" + accountMoneyType;
        createwindowExt("选择结案数据",url,900,600, {
            button:[
                {
                    name : "全部添加",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var result = iframe.saveAll();
                        if(result){
                            var queryParams = iframe.getQueryParams();
                            //保存数据
                            $.ajax({
                                async: false,
                                url: "ttAccountingController.do?savePendingAccountingByQueryData",
                                data: queryParams,
                                type: "post",
                                dataType : "json",
                                success : function(d) {
                                    var msg = d.msg;
                                    if (d.success) {
                                        tip(msg,'info');
                                        //写入table表数据
                                        addToTable(d.obj);
                                        return true;
                                    }else{
                                        tip(msg,'error');
                                        return;
                                    }
                                }
                            });
                        }
                        return result;
                    }
                },
                {
                    name : "确定",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var result = iframe.checkData();
                        if(result){
                            var rows = iframe.$('#ttAuditedDataList').datagrid('getSelections');
                            if(rows.length > 0){
                                //保存数据
                                $.ajax({
                                    async: false,
                                    url: "ttAccountingController.do?savePendingAccounting",
                                    data: {'jsonData': JSON.stringify(rows)},
                                    type: "post",
                                    dataType : "json",
                                    success : function(d) {
                                        var msg = d.msg;
                                        if (d.success) {
                                            //tip(msg,'info');
                                            //写入table表数据
                                            addToTable(rows);
                                            return true;
                                        }else{
                                            tip(msg,'error');
                                            return;
                                        }
                                    }
                                });
                            }
                        }
                        return result;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }
    //添加到table
    function addToTable(rows){
        var trStrs = "";
        var auditCodeStrs = "";
        for (var i = 0; i < rows.length; i++) {
            var auditCode = rows[i].auditCode;
            if(auditCodeStrs == ""){
                auditCodeStrs = auditCode;
            }else{
                auditCodeStrs += "," + auditCode;
            }
        }
        //获取待上账数据
        $.ajax({
            async: false,
            url: "ttAccounting2Controller.do?getPendingAccountingInfo",
            data: {'auditCodeStrs': auditCodeStrs},
            type: "post",
            dataType : "json",
            success : function(d) {
                var msg = d.msg;
                if (d.success) {
                    //写入table表数据
                    var list = d.obj;
                    writeToTable(list);
                }
            }
        });
    }
    //返回不为undefined的数据
    function returnNotUndefinedData(value){
        return (typeof(value) != 'undefined' && value != '' && value !=null) ? value : '';
    }
    //检查table是否有数据
    function isTableHaveData(rows){
        var len = $("#accountingTbody tr").length;
        if(len == 0){
            var data = rows[0];
            var auditAmount = returnNotUndefinedData(data.auditAmount); //结案金额
            var paymentCode = returnNotUndefinedData(data.paymentCode); //支付方式编码
            //货补方式
            if (paymentCode == 20) {
                var hbHtml = $("#huobuhiddendiv").html();
                $("#tb").append(hbHtml);
                $("#productName").val("");
            } else {
                $("#tb").children("#hbLabel").remove();
                $("#tb").children("#productName").remove();
                $("#tb").children("#hba").remove();
            }
            //转预付款
            if ((paymentCode == 30 && auditAmount >= 0) || paymentCode == 40) {
                $('#layoutId').layout('expand', 'east');
                $("#tb").children("#addpzBtn").remove();
                var btnHtml = $("#acconthiddendiv").html();
                $("#tb").append(btnHtml);
                if(paymentCode == 40){
                    $("#TTWB").attr("disabled",true);
                    $("#TTWB").css("background","#CCCCCC");
                    $("#YEMA").attr("disabled",true);
                    $("#YEMA").css("background","#CCCCCC");
                }else{
                    $("#TTWB").attr("disabled",false);
                    $("#TTWB").css("background","");
                    $("#YEMA").attr("disabled",false);
                    $("#YEMA").css("background","");
                }
            } else {
                $('#layoutId').layout('collapse', 'east');
                $("#tb").children("#addpzBtn").remove();
            }
            if(auditAmount < 0){
                $('#layoutId').layout('expand', 'east');
                $("#TTWB").attr("disabled",true);
                $("#TTWB").css("background","#CCCCCC");
//                $("#YEMA").attr("disabled",true);
//                $("#YEMA").css("background","#CCCCCC");
            }
        }
    }
    //写入table数据
    function writeToTable(rows){
        isTableHaveData(rows);
        for (var i = 0; i < rows.length; i++) {
            var data = rows[i];
            if(checkWhileExist(data.auditCode)){
                var obj = new Object();
                var trStrs = "";
                var paymentCode = "";//支付方式编码
                var auditAmount;
                obj.id = data.id; //id
                obj.customerName = returnNotUndefinedData(data.customerName);  //客户
                obj.productName = returnNotUndefinedData(data.productName);  //产品名称
                obj.businessUnitName = returnNotUndefinedData(data.sybOrgName);  //归属事业部名称
                obj.financialAccountCode = returnNotUndefinedData(data.financialAccountCode); //预算科目编码
                obj.financialAccountName = returnNotUndefinedData(data.financialAccountName); //预算科目名称
                obj.auditAmount = returnNotUndefinedData(data.auditAmount); //结案金额
                obj.accountingedAmount = returnNotUndefinedData(data.accountingedAmount); //已上账金额
                obj.paymentCode = returnNotUndefinedData(data.paymentCode); //支付方式编码
                obj.paymentName = returnNotUndefinedData(data.paymentName); //支付方式名称
                obj.supplyProductCode = returnNotUndefinedData(data.supplyProductCode); //货补产品
                obj.supplyProductName = returnNotUndefinedData(data.supplyProductName);//货补产品名称
                obj.accountingAmount = returnNotUndefinedData(data.accountingAmount); //本次上账金额
                obj.accountingStatus = data.accountingStatus; //上账状态
                obj.costAccountCode = returnNotUndefinedData(data.costAccountCode); //费用科目编码
                obj.costAccountName= returnNotUndefinedData(data.costAccountName); //费用科目名称
                obj.erpCode = returnNotUndefinedData(data.erpCode); //费用科目名称
                obj.financeVoucherCode = returnNotUndefinedData(data.financeVoucherCode); //财务凭证号
                obj.accountingCode = returnNotUndefinedData(data.accountingCode);//上账编码
                obj.customerCode = returnNotUndefinedData(data.customerCode);//客户编码
                obj.ttVoucherVoList = returnNotUndefinedData(data.ttVoucherVoList);//凭证数据
                obj.sapCostCenter = returnNotUndefinedData(data.sapCostCenter);//sap成本中心
                obj.auditCode = returnNotUndefinedData(data.auditCode);//核销编码
                obj.financialCode = returnNotUndefinedData(data.financialCode);//财务科目名编码
                obj.sapCostCenterDesc = returnNotUndefinedData(data.sapCostCenterDesc);//sap成本中心描述
                obj.unAccountingAmount = Number(Number(obj.auditAmount)-Number(obj.accountingedAmount)).toFixed(2);
                //构造组成行数据
                trStrs += structureRowData(obj);
                paymentCode = obj.paymentCode;
                auditAmount = obj.auditAmount;
                if (trStrs != '') {
                    $("#accountingTbody").append(trStrs);
                    if(obj.ttVoucherVoList != null && obj.ttVoucherVoList != ""){
                        voucherData(obj.ttVoucherVoList);
                    }else{
                        //结案金额为负 默认添加一条凭证数据
                        if(auditAmount < 0){
                            addDefaultVoucher(obj);
                        }
                    }
                    if((paymentCode == 30 && auditAmount > 0)|| paymentCode == 40 || auditAmount < 0){
                        sumAccounting();
                    }
                }
            }
        }
        sumAmount();
    }
    //计算未上账 已上账金额
    function sumAmount(){
        var notAccountMoney = "0.00";
        var accountMoney = "0.00";
        $("#accountingTbody tr").each(function(i,o){
            var auditAmount = $(this).children("#auditAmount").html();//结案金额
            var unAccountingAmount = $(this).children("#unAccountingAmount").html();//未上账金额
            var paymentCode = $(this).children("#paymentCode").html();//支付方式
            var accountingAmount = "";
            if(paymentCode == 30 || paymentCode == 40 || auditAmount <0){
                accountingAmount = $(this).children("#accountingAmount").html(); //本次上账金额
            }else{
                accountingAmount = $(this).children("#accountingAmount").children("input").val(); //本次上账金额
            }
            notAccountMoney = Number(Number(notAccountMoney) + Number(unAccountingAmount)).toFixed(2);
            accountMoney = Number(Number(accountMoney) + Number(accountingAmount)).toFixed(2);
        });
        $("#notAccountMoney").html(notAccountMoney);
        $("#accountMoney").html(accountMoney);
    }
    //刷新凭证数据
    function reloadVoucher(){
        $("#voucherTbody").html("");
        var trStrs = "";
        var auditCodeStrs = "";
        $("#accountingTbody tr").each(function(i,o){
            var auditCode = $(this).children("#auditCode").html();
            if(auditCodeStrs == ""){
                auditCodeStrs = auditCode;
            }else{
                auditCodeStrs += "," + auditCode;
            }
        });
        //获取待上账数据
        $.ajax({
            async: false,
            url: "ttAccounting2Controller.do?getPendingAccountingInfo",
            data: {'auditCodeStrs': auditCodeStrs},
            type: "post",
            dataType : "json",
            success : function(d) {
                var msg = d.msg;
                if (d.success) {
                    //写入table表数据
                    var list = d.obj;
                    for (var i = 0; i < list.length; i++) {
                        var data = list[i];
                        voucherData(data.ttVoucherVoList);
                    }
                }
            }
        });
    }
    //导入凭证
    function voucherImport(){
        //importDataByXml({impName:'ttAccountingVoucher'});
        createwindowExt("导入","importController.do?importData&impName=ttAccountingVoucher",660,400, {
            button: [
                {
                    name: "上传",
                    callback: function(){
                        iframe = this.iframe.contentWindow;
                        iframe.upload();
                        return false;
                    }
                },
                {
                    name: "取消",
                    callback: function(){
                        this.close();
                        //reloadTable();
                        reloadVoucher();
                    }
                }
            ]
        });
    }
    //导出凭证
    function voucherExport(){
        var codes = "";
        $("#accountingTbody tr").each(function(i,o){
            var accountingCode = $(this).children("#accountingCode").html();//上账编码
            if(codes == ""){
                codes += accountingCode;
            }else{
                codes += "," +accountingCode;
            }
        });
        if(codes != ""){
        window.open("ttAccounting2Controller.do?exportVoucherXls&codes="+codes);
        }else{
            tip("请添加要导出的数据",'error');
    }
    }
    //导出上账数据
    function accountingExport(){
        var codes = "";
        $("#accountingTbody tr").each(function(i,o){
            var accountingCode = $(this).children("#accountingCode").html();//上账编码
            if(codes == ""){
                codes += accountingCode;
            }else{
                codes += "," +accountingCode;
            }
        });
        if(codes != ""){
            window.open("ttAccounting2Controller.do?exportAccountingXls&codes="+codes);
        }else{
            tip("请添加要导出的数据",'error');
        }

    }
//结案金额为负数时添加默认凭证

    function addDefaultVoucher(accObj) {

        var str = "";
        var obj = new Object();
        obj.id = "";
        obj.invoiceNum= "";//发票号
        obj.taxNum = "";//税码
        obj.auditAmount = accObj.auditAmount - accObj.accountingedAmount;//结案金额（元）（不含税）
        obj.auditAccountCode = accObj.financialCode;//结案金额上账会计科目编码
        obj.auditAccountName = "";//结案金额上账会计科目名称
        obj.taxAmount = "";//税金（元）
        obj.inputTaxAmount = "";//进项税转出金额
        obj.accountingAmount = accObj.auditAmount - accObj.accountingedAmount;//上账金额（元）
        obj.accountingCustomerCode = accObj.customerCode;//上账客户编码
        obj.accountingCustomerName = accObj.customerName;//上账客户名称
        obj.costCenter = accObj.sapCostCenter;//SAP成本中心
        obj.remark = "";//备注
        obj.accountingCode = accObj.accountingCode;
        obj.taxAccountName = "";//税金科目名称
        obj.taxAccountCode = "";//税金科目编码
        obj.auditCode = accObj.auditCode;//核销编码
        obj.financialAccountCode = accObj.financialAccountCode;//预算科目编码
        obj.sapCostCenterDesc = accObj.sapCostCenterDesc;//SAP成本中心描述
        str = addVoucherRowData(obj);
        $("#voucherTbody").append(str);
    }
    //检查是否存在
    function checkWhileExist(auditCode){
        var len = $("#accountingTbody tr").length;
        if(len > 0){
            var result = true;
            $("#accountingTbody tr").each(function(i,o) {
                var code = $(this).children("#auditCode").html(); //核销编码
                if(code == auditCode){
                    result = false;
                }
            });
            return result;
        }else{
            return true;
        }
    }
    //构造组成行数据
    function structureRowData(o){
        var str = '<tr> ';
        str += '<td><input type="checkbox" id="actTableId_input_" name="actTableId" value="' + o.id + '"/></td>';
        str += '<td id="id"  style="display:none">' + o.id + '</td>';//id
        str += '<td id="customerName">' + o.customerName + '</td>';//客户
        str += '<td id="productName">' + o.productName + '</td>';//产品名称
        str += '<td id="businessUnitName">' + o.businessUnitName + '</td>';//归属事业部名称
        str += '<td id="financialAccountName">' + o.financialAccountName + '</td>';//预算科目
        str += '<td id="auditAmount">' + o.auditAmount + '</td>';//结案金额
        str += '<td id="accountingedAmount">' +o.accountingedAmount + '</td>';//已上账金额
        str += '<td id="unAccountingAmount">' +o.unAccountingAmount + '</td>';//未上账金额
        str += '<td id="paymentCode" style="display:none">' +o.paymentCode + '</td>';//支付方式编码
        str += '<td id="paymentName">' +o.paymentName + '</td>';//支付方式名称
        str += '<td id="supplyProductCode" style="display:none">' +o.supplyProductCode + '</td>';//货补产品
        if(o.paymentCode == 20){
            str += '<td id="supplyProductName" style="width: 50px;"><input readonly="readonly" value="' + o.supplyProductName + '" onfocus="selectProduct(2,this)" /></td>';
        }else{
            str += '<td id="supplyProductName">' + o.supplyProductName + '</td>';
        }
        //本次上账金额
        if((o.paymentCode == 30 && o.auditAmount > 0) || o.paymentCode == 40 || o.auditAmount < 0){
            str += '<td id="accountingAmount">'+o.auditAmount+'</td>';
        }else if(o.accountingAmount != ""){
            str += '<td id="accountingAmount"><input name="accountingAmount_input" value="' + o.accountingAmount + '" onblur="lostFocusEvent(this,true)" /></td>';
        }else{
            str += '<td id="accountingAmount"><input name="accountingAmount_input" value="' + Number(Number(o.auditAmount).toFixed(2)-Number(o.accountingedAmount).toFixed(2)).toFixed(2) + '" onblur="lostFocusEvent(this,true)" /></td>';
            obj.accountingStatus = 2;
        }
        if(o.financeVoucherCode != "" || o.paymentCode == 50 || o.paymentCode == 40 || o.paymentCode == 70){
            str += '<td id="financeVoucherCode"><input  name="financeVoucherCode_input" value="'+o.financeVoucherCode+'" onkeyup="checkNum(this)" maxlength="10" /></td>';//财务凭证号
        }else{
            str += '<td id="financeVoucherCode"></td>';//财务凭证号
        }
        str += '<td id="accountingStatus" style="display:none">'+o.accountingStatus+'</td>';//上账状态
        if(obj.accountingStatus == 2){
            str += '<td id="accountingStatusName">完全上账</td>';//上账状态
        }else if(obj.accountingStatus == 1){
            str += '<td id="accountingStatusName">部分上账</td>';//上账状态
        }else{
            str += '<td id="accountingStatusName">未上账</td>';//上账状态
        }
        str += '<td id="costAccountCode" style="display:none">'+o.costAccountCode+'</td>';//费用科目编码
        str += '<td id="costAccountName" style="display:none">'+o.costAccountName+'</td>';//费用科目名称
        str += '<td id="erpCode" style="display:none">'+o.erpCode+'</td>';//费用科目名称
        str += '<td id="customerCode" style="display:none">' + o.customerCode + '</td>';//客户编码
        str += '<td id="sapCostCenter" style="display:none">' + o.sapCostCenter + '</td>';//sap成本中心
        str += '<td id="auditCode">' + o.auditCode + '</td>';//核销编码
        str += '<td id="accountingCode">' + o.accountingCode + '</td>';//上账编码
        if(o.paymentCode == 30 && o.auditAmount < 0){
            str += '<td id="remark"><input  name="remark_input" value="" /></td>';//备注
        }else{
            str += '<td id="remark"></td>';//备注
        }
        str += '<td id="financialCode" style="display:none">' + o.financialCode + '</td>';//财务科目编码
        str += '<td id="financialAccountCode" style="display:none">' + o.financialAccountCode + '</td>';//预算科目编码
        str += '<td id="sapCostCenterDesc" style="display:none">' + o.sapCostCenterDesc + '</td>';//sap成本中心描述
        str += '</tr> ';
        return str;
    }
    //选择货补产品
    function selectProduct(type,obj) {
        var customerCodes = "";
        if(type == 1){
            $("#accountingTbody tr").each(function(i,o){
                if(customerCodes == ""){
                    customerCodes +=  $(this).children("#customerCode").html();
                }else{
                    customerCodes += "," + $(this).children("#customerCode").html();
                }
            });
        }else{
            var tr=$(obj).parents("tr");
            customerCodes = $(tr).children("#customerCode").html();
        }
        if(customerCodes != ""){
            showSelectProductPage(type,obj,customerCodes);
        }
    }
    //展示选择货补产品页面
    function showSelectProductPage(type,obj,customerCodes){
        safeShowDialog({
            content : "url:tdProductApiController.do?goProPremiumProductMain&customerCodes="+customerCodes,
            lock : true,
            title : "选择货补产品",
            width : 400,
            height : 500,
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rows = iframe.$('#tmProductTreeList').datagrid('getSelections');
                if(rows.length > 0){
                    setProductInfo(rows,type,obj);
                }
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    //设置货补产品
    function setProductInfo(rows,type,obj){
        var productCode = rows[0].productCode;
        var productName = rows[0].productName;
        var customerCodes = rows[0].customerCode;
        if(type == 1){
            $('input[name="productName"]').val(productName);
            $("#accountingTbody tr").each(function(i,o){
                var customerCode = $(this).children("#customerCode").html();//客户编码
                if(checkCustomerProduct(customerCodes,customerCode)){
                    $(this).children("#supplyProductCode").html(productCode);
                    $(this).children("#supplyProductName").children("input").val(productName);
                }
            });
        }else{
            var tr = $(obj).parents("tr");
            $(tr).children("#supplyProductCode").html(productCode);
            $(tr).children("#supplyProductName").children("input").val(productName);
        }
    }
    //检查客户是否可用此补货产品
    function checkCustomerProduct(customerCodes,customerCode){
        var customerList = new Array(); //定义一数组
        var isContain = false;
        customerList = customerCodes.split(","); //字符分割
        for (i = 0;i < customerList.length ;i++ ) {
            if(customerCode == customerList[i]){
                isContain = true;
                break;
            }
        }
        return isContain;
    }
    //检查纯数字
    function checkNum(obj){
        obj.value = obj.value.replace(/[^\d]/g,"");
    }
    //失去焦点事件
    function lostFocusEvent(obj,isDouble){
        var tr = $(obj).parents("tr");
        var accountingedAmount = $(tr).children("#accountingedAmount").html();//已上账金额
        var auditAmount = $(tr).children("#auditAmount").html();//结案金额
        var type = 1;//默认结案金额大于0
        if(auditAmount < 0){
            type = 2;
        }
        var value = "";
        if(isDouble){
            verifyDouble(obj,type);
            value=$(obj).val();
        }else{
            value=$(obj).html();
        }

        if(value==''){
            if(Number(accountingedAmount) == 0){
                $(tr).children("#accountingStatus").html("0");
                $(tr).children("#accountingStatusName").html("未上账");
            }else{
                $(tr).children("#accountingStatus").html("1");
                $(tr).children("#accountingStatusName").html("部分上账");
            }
        }else{
            var max = ((Number(auditAmount)).toFixed(2)-(Number(accountingedAmount)).toFixed(2)).toFixed(2);
            if(auditAmount >= 0){
                if(value - max > 0){
                    $(obj).val(max);
                    $(tr).children("#accountingStatus").html("2");
                    $(tr).children("#accountingStatusName").html("完全上账");
                }else if(value - max == 0){
                    $(tr).children("#accountingStatus").html("2");
                    $(tr).children("#accountingStatusName").html("完全上账");
                }else{
                    $(tr).children("#accountingStatus").html("1");
                    $(tr).children("#accountingStatusName").html("部分上账");
                }
            }else{
                if(value - max < 0){
                    $(obj).val(max);
                    $(tr).children("#accountingStatus").html("2");
                    $(tr).children("#accountingStatusName").html("完全上账");
                }else if(value - max == 0){
                    $(tr).children("#accountingStatus").html("2");
                    $(tr).children("#accountingStatusName").html("完全上账");
                }else{
                    $(tr).children("#accountingStatus").html("1");
                    $(tr).children("#accountingStatusName").html("部分上账");
                }
            }
        }
        sumAmount();
    }
    //删除上账
    function removeAccounting() {
        var flag = false;
        var trDatas = $("#pending_accounting_list  tbody :checked[id ^= 'actTableId_'] ");
        var ids = [];
        trDatas.each(function(){
            var obj = $(this);
            if (obj.attr("checked") == "checked") {
                var id = obj.val();
                if (typeof(id) != 'undefined' && id != '' ) {
                    ids.push(id);
                }
                flag = true;
            }
        });
        if (!flag) {
            tip("请选择要移除的数据","error");
            return ;
        }
        trDatas.each(function(){
            var obj = $(this);
            if (obj.attr("checked") == "checked") {
                var tr=$(this).parents("tr");
                var paymentCode = $(tr).children("#paymentCode").html();//上账方式编码
                var auditAmount = $(tr).children("#auditAmount").html();//结案金额
                if(paymentCode == 30 || paymentCode == 40 || auditAmount < 0){
                    var accountingCode = $(tr).children("#accountingCode").html();
                    $("#voucherTbody tr").each(function(i,o){
                        var accCode = $(this).children("#accountingCode").html();
                        if( accountingCode == accCode){
                            $(this).remove();
                        }
                    });
                }
                $(tr).remove();
            }
        });
        sumAmount();
    }
    //批量修改摘要
    function changeRemark(){
        var remark = $("#remarkInput").val();
        var trDatas = $("#voucherList  tbody :checked[id ^= 'actTableId_'] ");
        trDatas.each(function(){
            if ($(this).attr("checked") == "checked") {
                var tr = $(this).parents("tr");
                $(tr).children("#remark").children("input").val(remark);
            }
        });
    }
    //获取全部下拉选项
    function getAllOpion(){
        $("#voucherTbody tr").each(function(i,o){
            var select = $(this).children("#auditAccountCode").children("select");
            var count = $(this).children("#auditAccountCode").children("select").children("option").length;
            for(var j =0;j < count;j++){
                var contain = false;
                var val = $(select).get(0).options[j].value;
                var text = $(select).get(0).options[j].text;
                var len = $("#finacialSelect option").length;
                for ( var n = 0; n < len; n++) {
                    var v = $("#finacialSelect").get(0).options[n].value;
                    if(val == v){
                        contain = true;
                    }
                }
                if(!contain){
                    $("#finacialSelect").append("<option value='"+val+"'>"+text+"</option>");
                }
            }
        });
    }
    //批量修改会计科目
    function changeAllFinancial(){
        var v = $("#finacialSelect").val();
        var trDatas = $("#voucherList  tbody :checked[id ^= 'actTableId_'] ");
        trDatas.each(function(){
            if ($(this).attr("checked") == "checked") {
                var tr = $(this).parents("tr");
                $(tr).children("#auditAccountCode").children("select").val(v);
            }
        });
    }
    //清除table数据
    function clearActTableTbody(){
        $("#accountingTbody").html("");
        $("#voucherTbody").html("");
        $("#YEMA").val("");
        $("#TTWB").val("");
    }
    //计算上账金额
    function countAmount(obj,isDouble){
        //是否允许为负数
        if(isDouble){
            verifyDouble(obj,3);
        }else{
            verifyDouble(obj,1);
        }
        var tr=$(obj).parents("tr");
        var v1=$(tr).children("#auditAmount").children("input").val();
        var v2=$(tr).children("#taxAmount").children("input").val();
        var v3=$(tr).children("#inputTaxAmount").children("input").val();
        var value=(Number(v1)+Number(v2)-Number(v3)).toFixed(2);
        $(tr).children("#accountingAmount").html(value);
        setAccount(obj);
    }
    //检查数字+小数
    function verifyDouble(obj,type){
        if(type == 1){
            obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字
        }else{
            obj.value = obj.value.replace(/[^\-\d.]/g,""); //清除"数字"和"."以外的字
        }
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
        obj.value=(Number(obj.value)).toFixed(2);
        if(obj.value=="NaN"){
            obj.value="";
        }
        if(type == 2 && obj.value > 0){
            obj.value = "";
        }
    }
    //添加凭证
    function addVoucher() {
        var flag = false;
        var trDatas = $("#pending_accounting_list  tbody :checked[id ^= 'actTableId_'] ");
        var ids = [];
        var costAccountCode = "";
        var costAccountName = "";
        var erpCode = "";
        var customerCode="";
        var customerName="";
        var accountingCode = "";
        var sapCostCenter = "";
        var auditCode = "";
        var financialCode = "";
        var financialAccountCode = "";
        var sapCostCenterDesc = "";
        trDatas.each(function(){
            var obj = $(this);
            if (obj.attr("checked") == "checked") {
                var id = obj.val();
                if (typeof(id) != 'undefined' && id != '' ) {
                    ids.push(id);
                    var tr=$(this).parents("tr");
                    costAccountCode = $(tr).children("#costAccountCode").html();
                    costAccountName = $(tr).children("#costAccountName").html();
                    erpCode = $(tr).children("#erpCode").html();
                    customerCode = $(tr).children("#customerCode").html();
                    customerName = $(tr).children("#customerName").html();
                    accountingCode = $(tr).children("#accountingCode").html();
                    sapCostCenter = $(tr).children("#sapCostCenter").html();
                    auditCode = $(tr).children("#auditCode").html();
                    financialCode = $(tr).children("#financialCode").html();
                    financialAccountCode = $(tr).children("#financialAccountCode").html();
                    sapCostCenterDesc = $(tr).children("#sapCostCenterDesc").html();
                }
                flag = true;
            }
        });
        if (!flag) {
            tip("请选择要添加凭证的数据","error");
            return ;
        }
        if(ids.length != 1){
            tip("只能选择一条数据","error");
            return ;
        }
        var str = "";
        var obj = new Object();
        obj.id = "";
        obj.invoiceNum= "";//发票号
        obj.taxNum = "";//税码
        obj.auditAmount = "";//结案金额（元）（不含税）
        obj.auditAccountCode = financialCode;//结案金额上账会计科目编码
        obj.auditAccountName = "";//结案金额上账会计科目名称
        obj.taxAmount = "";//税金（元）
        obj.inputTaxAmount = "";//进项税转出金额
        obj.accountingAmount = "";//上账金额（元）
        obj.accountingCustomerCode = customerCode;//上账客户编码
        obj.accountingCustomerName = customerName;//上账客户名称
        obj.costCenter = sapCostCenter;//SAP成本中心
        obj.remark = "";//备注
        obj.accountingCode = accountingCode;
        obj.taxAccountName = "";//税金科目名称
        obj.taxAccountCode = "";//税金科目编码
        obj.auditCode = auditCode;//核销编码
        obj.financialAccountCode = financialAccountCode;//预算科目编码
        obj.sapCostCenterDesc = sapCostCenterDesc;//SAP成本中心描述
        str = addVoucherRowData(obj);
        $("#voucherTbody").append(str);
    }
    //写入table数据
    function voucherData(rows){
        var trStrs = "" ;
        for (var i = 0; i < rows.length; i++) {
            var data = rows[i];
            var obj = new Object();
            obj.id = data.id;
            obj.invoiceNum= returnNotUndefinedData(data.invoiceNum);//发票号
            obj.taxNum = returnNotUndefinedData(data.taxNum);//税码
            obj.auditAmount = returnNotUndefinedData(data.auditAmount);//结案金额（元）（不含税）
            obj.auditAccountCode = returnNotUndefinedData(data.auditAccountCode);//结案金额上账会计科目编码
            obj.auditAccountName = returnNotUndefinedData(data.auditAccountName);//结案金额上账会计科目名称
            obj.taxAmount = returnNotUndefinedData(data.taxAmount);//税金（元）
            obj.inputTaxAmount = returnNotUndefinedData(data.inputTaxAmount);//进项税转出金额
            obj.accountingAmount = returnNotUndefinedData(data.accountingAmount);//上账金额（元）
            obj.accountingCustomerCode = returnNotUndefinedData(data.accountingCustomerCode);//上账客户编码
            obj.accountingCustomerName = returnNotUndefinedData(data.accountingCustomerName);//上账客户名称
            obj.costCenter = returnNotUndefinedData(data.costCenter);//SAP成本中心
            obj.sapCostCenterDesc = returnNotUndefinedData(data.sapCostCenterDesc);//SAP成本中心
            obj.remark = returnNotUndefinedData(data.remark);//备注
            obj.accountingCode = returnNotUndefinedData(data.accountingCode);//上账编码
            obj.auditCode = returnNotUndefinedData(data.auditCode);//核销编码
            obj.financialAccountCode = returnNotUndefinedData(data.financialAccountCode);//预算科目编码
            //构造组成行数据
            trStrs += addVoucherRowData(obj);
        }
        if (trStrs != '') {
            $("#voucherTbody").append(trStrs);
            sumAccounting();
        }
    }
    //添加凭证行
    function addVoucherRowData(o){
        var str = '<tr> ';
        str += '<td><input type="checkbox" id="actTableId_input_" name="actTableId" value="' + o.id + '"/></td>';
        str += '<td id="id"  style="display:none">' + o.id + '</td>';//id
        str += '<td id="auditCode" style="display:none">' + o.auditCode + '</td>';//核销编码
        str += '<td id="accountingCode">' + o.accountingCode + '</td>';//上账编码
        str += '<td id="invoiceNum"><input maxlength="8" onkeyup="checkNum(this)" name="invoiceNum_input" value="' + o.invoiceNum + '"/></td>';//发票号
        str += '<td id="auditAccountName" style="display:none">' + getDefaultAccountName(o.auditAccountCode,o.accountingCode,o.financialAccountCode) + '</td>';//结案金额上账会计科目名称
        str += '<td id="auditAccountCode">' + getAuditAccountName(o.auditAccountCode,o.accountingCode,o.financialAccountCode) + '</td>';//结案金额上账会计科目编码
        str += '<td id="auditAmount"><input name="auditAmount_input" onblur="countAmount(this,true)" value="' + o.auditAmount + '"/></td>';//结案金额（元）（不含税）
        str += '<td id="taxNum">'+gettaxNumMode(o.taxNum)+ '</td>';//税码
        str += '<td id="taxAmount"><input  name="taxAmount_input" onblur="countAmount(this,false)" value="' + o.taxAmount + '"/></td>';//税金（元）
        str += '<td id="inputTaxAmount"><input onblur="countAmount(this,false)" name="inputTaxAmount_input" value="' + o.inputTaxAmount + '"/></td>';//进项税转出金额
        str += '<td id="accountingAmount">' + o.accountingAmount + '</td>';//上账金额（元）
        str += '<td id="accountingCustomerCode" style="display:none">' + o.accountingCustomerCode + '</td>';//上账客户编码
        str += '<td id="accountingCustomerName"><input readonly="readonly" onfocus="getCustomer('+o.accountingCustomerCode+',this);" name="accountingCustomerName_input" value="' + o.accountingCustomerName + '"/>';
        str += '</td>';//上账客户名称
        str += '<td id="costCenter">' + o.costCenter + '</td>';//SAP成本中心
        str += '<td id="sapCostCenterDesc">' + o.sapCostCenterDesc + '</td>';//SAP成本中心描述
        str += '<td id="remark"><input  name="remark_input" value="' + o.remark + '"/></td>';//备注
        str += '<td id="financialAccountCode" style="display:none">' + o.financialAccountCode + '</td>';//预算科目编码
        str += '</tr> ';
        return str;
    }
    //获取税码选择信息
    function gettaxNumMode(v){
        var optionStr='<select style="width: 150px;" value="'+v+'" name="taxNum_select_"><option value="">请选择</option>';
        for (var i = 0; i < _list.length; i++) {
            optionStr+="<option value='"+_list[i].taxcodeCode+"'";
            if(v==_list[i].taxcodeCode){
                optionStr+=" selected='selected' ";
            }
            optionStr+=">"+_list[i].taxcodeName+"</option>";
        }
        optionStr += '</select>';
        return optionStr;
    }
    //选择客户
    function getCustomer(customerCode,obj){
        safeShowDialog({
            content : "url:ttAccounting2Controller.do?goSelectCustomerMain&customerCode="+customerCode,
            lock : true,
            title : "选择客户",
            width : 400,
            height : 500,
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rows = iframe.$('#ttCustomerList').datagrid('getSelections');
                if(rows.length>0){
                    changeCustomer(obj,rows);
                }
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });

    }
    //改变客户
    function changeCustomer(obj,rows){
        $($(obj).parents("tr").children("#accountingCustomerCode")).html(rows[0].customerCode);
        $(obj).val(rows[0].customerName);
        var accountingCode = $(obj).parents("tr").children("#accountingCode").html();
        checkSapCenter(obj,rows[0].customerCode,accountingCode);
    }
    //检查sap成本中心是否需要改变
    function checkSapCenter(obj,customerCode,accountingCode){
        $.ajax({
            async : false,
            cache : false,
            type : 'POST',
            data:{customerCode:customerCode,accountingCode:accountingCode},
            url : "ttAccounting2Controller.do?checkSapCenter",// 请求的action路径
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    var curSapCenter = $(obj).parents("tr").children("#costCenter").html();
                    if(d.obj.sapCostCenter != curSapCenter){
                        $(obj).parents("tr").children("#costCenter").html(d.obj.sapCostCenter);
                        $(obj).parents("tr").children("#sapCostCenterDesc").html(d.obj.sapCostCenterDesc);
                    }
                }
            }
        });
    }
    //选择上账会计科目
    function getAuditAccountName(v,accountCode,financialAccountCode){
        var financialCode = financialAccountCode;
        if(financialCode == ""){
            $("#accountingTbody tr").each(function(i,o){
                var accCode = $(this).children("#accountingCode").html();//上账编号
                if(accCode == accountCode){
                    financialCode = $(this).children("#financialAccountCode").html();
                }
            });
        }
        var optionStr='<select style="width: 150px;" value="'+v+'" onchange="changeAccountName(this)" name="auditAccountName_select_"><option value="">请选择 </option>';
        $.ajax({
            async : false,
            cache : false,
            type : 'POST',
            data:{financialCode:financialCode},
            url : "ttAccounting2Controller.do?getFinancialList",// 请求的action路径
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    for (var i = 0; i < d.obj.length; i++) {
                        optionStr+="<option value='"+d.obj[i].financialCode+"'";
                        if(v==d.obj[i].financialCode){
                            optionStr+=" selected='selected' ";
                        }
                        optionStr+=">"+d.obj[i].remark+"</option>";
                    }
                    optionStr += '</select>';
                }
            }
        });
        return optionStr;
    }
    //获取默认会计科目名称
    function getDefaultAccountName(v,accountCode,financialAccountCode){
        var financialCode = financialAccountCode;
        if(financialCode == ""){
            $("#accountingTbody tr").each(function(i,o){
                var accCode = $(this).children("#accountingCode").html();//上账编号
                if(accCode == accountCode){
                    financialCode = $(this).children("#financialAccountCode").html();
                }
            });
        }
        var name = "";
        $.ajax({
            async : false,
            cache : false,
            type : 'POST',
            data:{financialCode:financialCode},
            url : "ttAccounting2Controller.do?getFinancialList",// 请求的action路径
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    for (var i = 0; i < d.obj.length; i++) {
                        if(v == d.obj[i].financialCode){
                            name = d.obj[i].remark;
                        }
                    }
                }
            }
        });
        return name;
    }
    //改变上账会计科目名称
    function changeAccountName(obj) {
        var checkText=$(obj).find("option:selected").text();
        var tr = $(obj).parents("tr");
        $(tr).children("#auditAccountName").html(checkText);
    }
    //暂存上账凭证
    function saveVoucher() {
        if(checkVoucher()){
            $.ajax({
                async: false,
                url: "ttVoucherController.do?saveVoucher",
                data: {'jsonData': JSON.stringify(saveObjList)},
                type: "post",
                dataType : "json",
                success : function(d) {
                    var msg = d.msg;
                    if (d.success) {
                        $("#voucherTbody").html("");
                        voucherData(d.obj);
                        tip(msg,'info');
                        //刷新  待写
                    }else{
                        tip(msg,'error');
                    }
                },
                error:function(){
                }
            });
        }
    }
    //要保存数据
    var saveObjList = [];
    //验证凭证
    function checkVoucher(){
        saveObjList = [];
        var flag = true;
        var info = "";
        var len = $("#voucherTbody tr").length;
        if(len == 0){
            tip("请添加凭证数据","error");
            return false;
        }
        var paymentCode = "";//上账方式

        $("#accountingTbody tr").each(function(i,o){
            var code = $(this).children("#paymentCode").html();
            paymentCode = code;
            return false;
        });
        if(len > 0){
            $("#voucherTbody tr").each(function(i,o){
                var obj = new Object();
                obj.id = $(this).children("#id").html();
                obj.invoiceNum = $(this).children("#invoiceNum").children("input").val();//发票号
                var taxNum = $(this).children("#taxNum").children("select").find("option:selected").val();//税码
                obj.taxNum = taxNum;
                obj.auditAmount = $(this).children("#auditAmount").children("input").val();//结案金额不含税
                obj.auditAccountCode = $(this).children("#auditAccountCode").children("select").find("option:selected").val();//会计科目编码
                obj.auditAccountName = $(this).children("#auditAccountName").html();//会计科目名称
                obj.taxAmount = $(this).children("#taxAmount").children("input").val();//税金
                obj.inputTaxAmount = $(this).children("#inputTaxAmount").children("input").val();//进项税转出金额
                obj.accountingAmount = $(this).children("#accountingAmount").html();//上账金额
                obj.accountingCustomerCode = $(this).children("#accountingCustomerCode").html();//客户编码
                obj.accountingCustomerName = $(this).children("#accountingCustomerName").children("input").val();//客户名称
                obj.costCenter = $(this).children("#costCenter").html();//sap编码
                obj.remark = $(this).children("#remark").children("input").val();//备注
                obj.accountingCode = $(this).children("#accountingCode").html();//上账编号
                obj.lineNum = i+1;//行号
                obj.inputTaxAccountName = "1000 应交税费-应交增值税-进项税额转出-非应税项目用、集体福利、个人消费";//进项税转出金额科目名称
                obj.inputTaxAccountCode = "**********";//进项税转出金额科目编码 **********
                obj.taxAccountName = getTaxAccountName(taxNum);//税金科目名称
                obj.taxAccountCode = getTaxAccountCode(taxNum);//税金科目编码
                obj.auditCode = $(this).children("#auditCode").html();//核销编码
                obj.ttwb = $("#TTWB").val();
                obj.yema = $("#YEMA").val();

                //验证数据是否为空
                if(obj.invoiceNum != null && obj.invoiceNum != ""){
                    if(obj.invoiceNum.length != 8){
                        info = "发票号必须是8位纯数字";
                    }
                    flag = false;
                }
                if(obj.auditAmount == null || obj.auditAmount == ""){
                    if(info == ""){
                        info = "结案金额（不含税）不允许为空";
                    }
                    flag = false;
                }
                if(obj.auditAccountCode == null || obj.auditAccountCode == ""){
                    if(info == ""){
                        info = "上账会计科目不允许为空";
                    }
                    flag = false;
                }
                if(obj.accountingAmount == null || obj.accountingAmount == ""){
                    if(info == ""){
                        info = "上账金额不允许为空";
                    }
                    flag = false;
                }

                if(paymentCode == 30 && (obj.accountingCustomerName == null || obj.accountingCustomerName == "")){
                    if(info == ""){
                        info = "上账客户不允许为空";
                    }
                    flag = false;
                }
                if(flag){
                    saveObjList.push(obj);
                }
            });
        }
        if(!flag){
            tip(info,"error");
            return flag;
        }else{
            return flag;
        }
    }
    //获取税金科目名称
    function getTaxAccountName(taxNum){
        var taxAccountName = "";
        for (var i = 0; i < _list.length; i++) {
            if(_list[i].taxcodeCode == taxNum){
                taxAccountName = _list[i].taxName;
                break;
            }
        }
        return taxAccountName;
    }
    //获取税金科目编码
    function getTaxAccountCode(taxNum){
        var taxAccountCode = "";
        for (var i = 0; i < _list.length; i++) {
            if(_list[i].taxcodeCode == taxNum){
                taxAccountCode = _list[i].taxCode;
                break;
            }
        }
        return taxAccountCode;
    }
    //获取上账凭证
    function getVoucher(type){
        saveObjList = [];
        var flag = true;
        var info = "";
        var paymentCode = "";//上账方式

        $("#accountingTbody tr").each(function(i,o){
            var code = $(this).children("#paymentCode").html();
            paymentCode = code;
            return false;
        });
        $("#voucherTbody tr").each(function(i,o){
            var obj = new Object();
            obj.id = $(this).children("#id").html();
            obj.invoiceNum = $(this).children("#invoiceNum").children("input").val();//发票号
            var taxNum = $(this).children("#taxNum").children("select").find("option:selected").val();//税码
            obj.taxNum = taxNum;
            obj.auditAmount = $(this).children("#auditAmount").children("input").val();//结案金额不含税
            obj.auditAccountCode = $(this).children("#auditAccountCode").children("select").find("option:selected").val();//会计科目编码
            obj.auditAccountName = $(this).children("#auditAccountName").html();//会计科目名称
            obj.taxAmount = $(this).children("#taxAmount").children("input").val();//税金
            obj.inputTaxAmount = $(this).children("#inputTaxAmount").children("input").val();//进项税转出金额
            obj.accountingAmount = $(this).children("#accountingAmount").html();//上账金额
            obj.accountingCustomerCode = $(this).children("#accountingCustomerCode").html();//客户编码
            obj.accountingCustomerName = $(this).children("#accountingCustomerName").children("input").val();//客户名称
            obj.costCenter = $(this).children("#costCenter").html();//sap编码
            obj.remark = $(this).children("#remark").children("input").val();//备注
            obj.accountingCode = $(this).children("#accountingCode").html();
            obj.lineNum = i+1;//行号
            obj.inputTaxAccountName = "1000 应交税费-应交增值税-进项税额转出-非应税项目用、集体福利、个人消费";//进项税转出金额科目名称
            obj.inputTaxAccountCode = "**********";//进项税转出金额科目编码
            obj.taxAccountName = getTaxAccountName(taxNum);//税金科目名称
            obj.taxAccountCode = getTaxAccountCode(taxNum);//税金科目编码
            obj.auditCode = $(this).children("#auditCode").html();//核销编码
            obj.ttwb = $("#TTWB").val();
            obj.yema = $("#YEMA").val();
            //验证数据是否为空
            if(obj.invoiceNum != null && obj.invoiceNum != ""){
                if(obj.invoiceNum.length != 8){
                    if(info == ""){
                        info = "发票号必须为8位纯数字";
                    }
                    flag = false;
                }
            }
            if(obj.auditAmount == null || obj.auditAmount == ""){
                if(info == ""){
                    info = "结案金额（不含税）不允许为空";
                }
                flag = false;
            }
            if(obj.auditAccountCode == null || obj.auditAccountCode == ""){
                if(info == ""){
                    info = "上账会计科目不允许为空";
                }
                flag = false;
            }
            if(obj.accountingAmount == null || obj.accountingAmount == ""){
                if(info == ""){
                    info = "上账金额不允许为空";
                }
                flag = false;
            }
            if(paymentCode == 30 && (obj.accountingCustomerName == null || obj.accountingCustomerName == "")){
                if(info == ""){
                    info = "上账客户不允许为空";
                }
                flag = false;
            }
            if(flag){
                saveObjList.push(obj);
            }else if(type == 2){
                saveObjList.push(obj);
            }
        });
        if(!flag && type == 1){
            tip(info,"error");
            return flag;
        }else{
            return true;
        }
    }
    //移除上账凭证
    function removeVoucher() {
        var flag = false;
        var trDatas = $("#voucherList  tbody :checked[id ^= 'actTableId_'] ");
        trDatas.each(function(){
            if ($(this).attr("checked") == "checked") {
                flag = true;
                var tr = $(this).parents("tr");
                var accountingId = $(tr).children("#accountingCode").html();//上账编码
                var accountingAmount = $(tr).children("#accountingAmount").html();//上账金额

                $(tr).remove();
            }
        });
        if (!flag) {
            tip("请选择要移除的数据","error");
            return ;
        }
        sumAccounting();
    }
    //凭证移除后重新计算上账金额回显
    function sumAccounting(){
        $("#accountingTbody tr").each(function(i,o){
            var accCode = $(this).children("#accountingCode").html();//上账编号
            var auditAmount = $(this).children("#auditAmount").html();//结案金额
            var accountingedAmount = $(this).children("#accountingedAmount").html();//已上账金额
            var newAmount = 0.00;
            $("#voucherTbody tr").each(function(i,o){
                var accountCode = $(this).children("#accountingCode").html();
                if(accCode == accountCode){
                    var accountAmount = $(this).children("#accountingAmount").html();
                    newAmount = Number(newAmount) + Number(accountAmount);
                }
            });
            var amountObj = $(this).children("#accountingAmount");
            if(newAmount != 0.00){
                $(amountObj).html(newAmount.toFixed(2));
                lostFocusEvent(amountObj,false);
            }else{
                $(amountObj).html("");
                lostFocusEvent(amountObj,false);
            }
        });
    }
    //监听凭证上账金额改变事件
    function setAccount(obj){
        var tr = $(obj).parents("tr");
        var accountingId = $(tr).children("#accountingCode").html();//上账id
        var accountingAmount = $(tr).children("#accountingAmount").html();//上账金额
        sumAccounting();
    }
    //暂存数据
    function save() {
        var flag = true;
        var paymentCode = "";//上账方式
        var bigZero = true;//默认结案金额大于0
        var accType = 1;

        $("#accountingTbody tr").each(function(i,o){
            var code = $(this).children("#paymentCode").html();
            if(paymentCode != null && paymentCode != "" && code != paymentCode){
                flag = false;
            }else{
                paymentCode = code;
            }
            var auditAmount = $(this).children("#auditAmount").html();
            if(auditAmount < 0){
                accType = 2;
            }
            if(auditAmount >= 0 && accType == 2){
                bigZero = false;
            }
            if(accType == 1 && auditAmount < 0){
                bigZero = false;
            }
        });

        if(!flag && accType == 1){
            tip("上账方式不统一","error");
            return false;
        }
        if(paymentCode == 30 || paymentCode == 40 || accType == 2){
            if(!getVoucher(2)){
                return false;
            }
        }
        var header = {paymentCode:paymentCode};
        if(checkAccountingData(2)){
            $.ajax({
                async: false,
                url: "ttAccountingController.do?saveAccountingData",
                data: {'headerJson':JSON.stringify(header), 'accountingJson':JSON.stringify(accountingObjList), 'voucherJson':JSON.stringify(saveObjList)},
                type: "post",
                dataType : "json",
                success : function(d) {
                    if (d.success) {
                        refreshVoucher();
                        tip("操作成功","info");

                    }else{
                        tip(d.msg,'error');
                    }
                }
            });
        }
    }
    //刷新凭证信息
    function refreshVoucher(){
        $("#voucherTbody").html("");
        $("#accountingTbody tr").each(function(i,o){
            var accountingCode = $(this).children("#accountingCode").html();
            $.ajax({
                async: false,
                url: "ttAccounting2Controller.do?getVoucherByAccountCode",
                data: {'accountingCode':accountingCode},
                type: "post",
                dataType : "json",
                success : function(d) {
                    if (d.success) {
                        var list = d.obj;
                        voucherData(list);
                    }
                }
            });
        });
    }
    var accountingObjList = [];
    var fcInfo = "";//凭证号
    //验证上账数据
    function checkAccountingData(type){
        accountingObjList = [];
        var flag = true;
        var info = "";
        fcInfo = "";
        var len = $("#accountingTbody tr").length;
        if(len == 0){
            tip("请添加上账数据","error");
            return false;
        }
        if(len > 0){
            $("#accountingTbody tr").each(function(i,o){
                var obj = new Object();
                obj.id = $(this).children("#id").html(); //id
                obj.paymentCode = $(this).children("#paymentCode").html(); //支付方式编码
                obj.paymentName = $(this).children("#paymentName").html(); //支付方式名称
                obj.auditAmount = $(this).children("#auditAmount").html();//上账金额
                obj.accountingedAmount = $(this).children("#accountingedAmount").html(); //已上账金额
                obj.supplyProductCode = $(this).children("#supplyProductCode").html(); //货补产品编码
                obj.supplyProductName = $(this).children("#supplyProductName").children("input").val(); //货补产品名称
                obj.accountingCode = $(this).children("#accountingCode").html();//上账编码
                obj.customerCode = $(this).children("#customerCode").html();//客户编码
                obj.customerName = $(this).children("#customerName").html();//客户名称
                if((obj.paymentCode == 30 && obj.auditAmount > 0) || obj.paymentCode == 40 || obj.auditAmount <0){
                    obj.accountingAmount = $(this).children("#accountingAmount").html(); //本次上账金额
                }else{
                    obj.accountingAmount = $(this).children("#accountingAmount").children("input").val(); //本次上账金额
                }
                obj.accountingStatus = $(this).children("#accountingStatus").html(); //上账状态
                if(obj.accountingAmount == "" || obj.accountingAmount == null){
                    if(info == ""){
                        info = "上账金额不能为空";
                    }
                    flag = false;
                }else{
                    if(obj.auditAmount > 0){
                        if((Number(obj.accountingAmount) + Number(obj.accountingedAmount) - Number(obj.auditAmount)) > 0){
                            if(info == ""){
                                info = "上账金额超出";
                            }
                            flag = false;
                        }
                    }else{
                        if((Number(obj.accountingAmount) + Number(obj.accountingedAmount) - Number(obj.auditAmount)) < 0){
                            if(info == ""){
                                info = "上账金额超出";
                            }
                            flag = false;
                        }
                    }
                }
                if(obj.paymentCode == 40 || obj.paymentCode == 50 || obj.paymentCode == 70){
                    obj.financeVoucherCode = $(this).children("#financeVoucherCode").children("input").val(); //财务凭证号
                    //验证数据是否为空
                    if(obj.financeVoucherCode == null || obj.financeVoucherCode == ""){
                        if(info == ""){
                            info = "财务凭证号不允许为空";
                        }
                        flag = false;
                    }else if(obj.financeVoucherCode.length != 10){
                        if(info == ""){
                            info = "财务凭证号必须为10位数字";
                        }
                        flag = false;
                    }else{
                        var f = checkFinanceVoucherCode(obj.financeVoucherCode);
                        if(!f){
                            fcInfo += obj.financeVoucherCode + ",";
                        }
                    }
                }else{
                    obj.financeVoucherCode = "";
                }
                if(flag){
                    accountingObjList.push(obj);
                }else if(type == 2){
                    accountingObjList.push(obj);
                }
            });
        }
        if(!flag && type == 1){
            tip(info,"error");
            return flag;
        }else{
            return true;
        }
    }
    function checkFinanceVoucherCode(financeVoucherCode){
        var flag = true;
        //验证凭证号是否重复
        $.ajax({
            async: false,
            url: "ttAccounting2Controller.do?checkFinanceVoucherCode",
            data: {id:obj.id,'financeVoucherCode': financeVoucherCode },
            type: "post",
            dataType : "json",
            success : function(d) {
                if (!d.success) {
                    flag = false;
                }
            }
        });

        return flag;
    }
    //上账
    function saveAccounting() {
        var flag = true;
        var paymentCode = "";//上账方式

        $("#accountingTbody tr").each(function(i,o){
            var code = $(this).children("#paymentCode").html();
            if(paymentCode != null && paymentCode != "" && code != paymentCode){
                flag =false;
            }else{
                paymentCode = code;
                return false;
            }
        });

        var bigZero = true;//默认结案金额大于0
        var accType = 1;
        $("#accountingTbody tr").each(function(i,o){
            var auditAmount = $(this).children("#auditAmount").html();
            if(auditAmount < 0){
                accType = 2;
            }
            if(auditAmount >= 0 && accType == 2){
                bigZero = false;
            }
            if(accType == 1 && auditAmount < 0){
                bigZero = false;
            }
        });
        if(!bigZero){
            tip("上账数据的结案金额必须全部为正或者全部为负，不能同时即有正又有负","error");
            return false;
        }
        if(!flag && accType == 1){
            tip("上账方式不统一","error");
            return false;
        }
        var YEMA = $("#YEMA").val();
        var TTWB = $("#TTWB").val();
        //转预付款
        if(paymentCode == 30 || paymentCode == 40 || accType == 2){
            if(getVoucher(1)){
                if(paymentCode == 30 && accType == 1){
                    if(YEMA == null || YEMA == ""){
                        tip("请填写凭证附件张数","error");
                        return false;
                    }
                }
            }else{
                return false;
            }
        }
        var FJZ = "";
        if(accType == 2){
            FJZ = "X";
        }
        var header = {paymentCode:paymentCode};
        var voucherHeaderJson = {YEMA:YEMA,TTWB:TTWB,FJZ:FJZ};
        if(checkAccountingData(1)){
            if(fcInfo != ""){
                $.messager.confirm('操作提示',fcInfo+"编码重复,是否继续上账",function(r){
                    if(r){
                        submitAccounting(header,voucherHeaderJson);
                    }
                });
            }else{
                submitAccounting(header,voucherHeaderJson);
            }
        }
    }
    //上账提交
    function submitAccounting(header,voucherHeaderJson){
        $.ajax({
            async: false,
            url: "ttAccountingController.do?saveAndSendAccountingData",
            data: {'headerJson':JSON.stringify(header), 'accountingJson':JSON.stringify(accountingObjList), 'voucherJson':JSON.stringify
            (saveObjList),'voucherHeaderJson':JSON.stringify(voucherHeaderJson)},
            type: "post",
            dataType : "json",
            success : function(d) {
                if (d.success) {
                    W.tip("操作成功","info");
                    windowapi.close();
                    W.reload();
                }else{
                    tip(d.msg,'error');
                }
            }
        });
    }
</script>