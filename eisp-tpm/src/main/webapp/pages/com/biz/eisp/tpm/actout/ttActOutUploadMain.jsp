<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="top_panel" fit="true" style="height: 70px;">
    <span style="font-size: 16px;color: mediumvioletred;font-family: 微软雅黑;font-weight: bold">
        &nbsp;1、遇到红脸(<img src='resources/img/unUploaded.gif' title='有照片未上传，快到手机APP离线队列看看'/>)不要慌，先"查看照片小视频"(按钮)，都上传了就不用管，确实没上传就找到上传照片的手机，打开APP的离线队列，会自动上传。
    <br />
        &nbsp;2、门头广告编号跳号没事，系统给你什么编号就用什么，做设计稿时要确保一一对应。
        <br />
        &nbsp;3、门头申请从”选址申请提交成功 到 提交完施工后照片“，有效期为2个月，逾期将被系统自动作废，请您及时制作并跟踪到位！
    </span>

</div>
<div id="buttom_pannel" style="clear:both; width: 100%;height: 635px;">
        <t:datagrid name="tsActApplyVoList"
                    fitColumns="false" checkbox="true"
                    title="设计稿上传(以及材料、尺寸等信息)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注意：一个店铺做2~3块门头的，要求经销商提前向公司报备！" actionUrl="ttActOutUploadController.do?findActOutUploadMain"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="false" replace="待上传设计稿_10,驳回_4,设计稿驳回_12" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="照片" field="isUpldMark" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="驳回过?" field="reject" hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="重复制作" field="isRepeat" replace="是_1,-_0" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="dadcodes"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="adCodes"  hidden="true" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="活动类型" field="actType" hidden="true" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="advType" hidden="false" dictionary="act_type" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="图片上传状态" field="isUploaded" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="4-店老板手机(招牌上)" field="rLinkPhone" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternatives" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="经销商" field="createName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司账号" field="mobilephone" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司手机" field="advCode" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" query="false" sortable="false" ></t:dgCol>

            <t:dgToolBar title="上传设计稿" icon="icon-upload" onclick="uploadLayout(0)"></t:dgToolBar>
            <t:dgToolBar title="上传设计稿（形象店）" icon="icon-upload" onclick="uploadLayout(1)"></t:dgToolBar>
            <t:dgToolBar title="查看材料尺寸" icon="icon-log" onclick="findDetailLayout()"></t:dgToolBar>
            <t:dgToolBar title="查看驳回原因" icon="icon-bcbhelp" onclick="findRejectReason()"></t:dgToolBar>
<%--            <t:dgToolBar title="修改店铺信息（含姓名）" icon="icon-log" url="ttActOutUploadController.do?goTmTerminal" funname="goPostTerminal"></t:dgToolBar>--%>
            <t:dgToolBar title="修改广告发布地址2" icon="icon-log" url="ttActOutUploadController.do?goEditAddr2" funname="goEditAddr2"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频" icon="icon-bcbgrid" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
            <t:dgToolBar title="门头选址作废"  icon="icon-bcbdel" onclick="changeDelMark(1)"></t:dgToolBar>
            <t:dgToolBar title="设计稿范例" icon="icon-log" url="ttActOutUploadController.do?showDemo" funname="showDemo"></t:dgToolBar>
        </t:datagrid>

</div>
<script>
    $(function () {
        $("a").each(function () {
            if($(this).hasClass("easyui-linkbutton l-btn l-btn-plain")){
                $(this).removeClass();
                $(this).addClass("easyui-linkbutton l-btn");
            }
        });
    });
    //$(window).load(function(){
    //$(document).ready(function () {
//    $(function() {
//        var dgrids = $('#tsActApplyVoList').datagrid('getSelections');
//        alert(dgrids.length);
//        for(var index = 0 ; index < dgrids.length ; index++){
//            alert(dgrids[i].isFileUpload());
//            if(dgrids[i].isFileUpload() == 0){
//                $(".datagrid-row-r1-2-" + index ).removeAttr("style");
//                $(".datagrid-row-r1-2-" + index ).attr("style","height:25px;background-color:#F08080;");
//            }
//        }
//    });
    function showDemo(title, url, id, width, height) {
        $.dialog({
            title: "设计稿范例",
            content: "url:" + url,
            lock: true,
            width: "750",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function goPostTerminal(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 400, 500);
    }

    function goPostPicture(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 1250, 650);
    }


    function goEditAddr2(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        url = url + "&adCodes=" + rowsData[0].adCodes.trim();
        update(title, url, id, 800, 350);
    }

    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 1000, 530);
    }
//    function checkFileIsUp(){
//        $.dialog.confirm('系统：您确定要检查吗? 该操作将持续5秒左右', function(r) {
//            $(".checkUp").attr('disabled',"true");;
//            var thisData = {
//                status: 0
//            }
//
//            var url = "ttActOutUploadController.do?checkFileIsUp";
//
//            var d = ajaxPost(thisData, url);
//
//            if (d.success) {
//                $("#tsActApplyVoList").datagrid("reload");
//            }
//            tip(d.msg);
//        });
//    }
    function changeDelMark(status){

        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        $.dialog.confirm('系统：您确定要删除吗?', function(r) {
            var ids = []
            for (var i = 0; i < row.length; i++) {
                ids.push(row[i].id);
            }

            var thisData = {
                ids: ids.join(','),
                status: status
            }

            var url = "ttActOutUploadController.do?changeDelMark";

            var d = ajaxPost(thisData, url);

            if (d.success) {
                $("#tsActApplyVoList").datagrid("reload");
            }
            tip(d.msg);
        });
    }

    function changeStoreName(){

        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        $.dialog.confirm('系统：您确定要修改店铺名称吗?', function(r) {
            var ids = row[0].id;
            var terCode = row[0].terminalCode;
            var isRepeat = row[0].isRepeat;//白池标，不允许重复选址的提交  2018.09.21
            if(isRepeat=='1'){
                tip("属于重复选址，不能修改店铺名称。小技巧：删除其他重复的选址，本条选址的重复标记可智能转移。");
                return ;
            }
            var thisData = {
                ids: ids,
                terCode: terCode
            }

            var url = "ttActOutUploadController.do?goTmTerminal";

            var d = ajaxPost(thisData, url);

//            if (d.success) {
//                $("#tsActApplyVoList").datagrid("reload");
//            }
//            tip(d.msg);
        });
    }

//    function handle(a,b,c,d) {
//        alert(123);
//        //var url = "taTaskController.do?goTaskAbstractForm&isView=false&taskId=1084214&processInstanceId=1084201";
//        var url ="taTaskController.do?goInstanceHandleTabForm&isReadFlag=false&isView=false&processInstanceId=1083801&taskId=1083814&isCommunicate=N&id=1083814";
//        $.dialog({
//            title: "",
//            content: "url:"+url,
//            lock: true,
//            width: "1200",
//            height: "700",
//            zIndex: 10000,
//            parent: windowapi,
//            cancelVal: '关闭',
//        });
//
//    }
    function uploadLayout(status) {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var bpmStatus=row[0].bpmStatus;
        if(!(bpmStatus=='10'||bpmStatus=='4'||bpmStatus=='5'||bpmStatus=='6'||bpmStatus=='12')){
            tip("该状态不能上传设计稿!");
            return ;
        }
        var isUploaded = row[0].isUploaded;   //白池标  2018.09.18  下一步添加
//  /*      if(isUploaded == 0){
//            tip("尚有照片未上传，不能上传设计稿，请到做门头选址手机，打开APP，打开离线队列确认一下");
//            return ;
//        }*/

        var actType = row[0].actType;
        if(actType == undefined || actType == "" || actType == null){
            tip("流程状态异常,不能操作!");
            return ;
        }

        var isRepeat = row[0].isRepeat;//白池标，不允许重复选址的提交  2018.08.29
        if(isRepeat == '1'){//bcb  获取优先提交权  2019.02.18
            var ids = row[0].id;
            var thisData = {
                ids: ids,
                status: status
            }
            var url = "ttActOutUploadController.do?getComPower";
            var d = ajaxPost(thisData, url);
            if (d.success) {//如果获取优先权成功
                isRepeat = '0';
            }
        }
        if(isRepeat=='1'){
            tip("该申请属于重复选址，不允许上传该门头的设计稿!");
            return ;
        }

        gridname="tsActApplyVoList";
        var url = "ttActOutUploadController.do?goActOutUploadForm&id="+row[0].id+"&symbol="+status;
        if(actType=="2"){
            url = "ttActOutUploadController.do?goActOutDoorUploadForm&id="+row[0].id+"&symbol="+status;
        }
        var _title = "";
        if(status == 1){
            _title = "上传设计稿（形象店），并填写材料及尺寸信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;------>>>>>> 提交方式：点击右下方的“确定”按钮。";
        } else {
            _title = "上传设计稿，并填写材料及尺寸信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;------>>>>>> 提交方式：点击右下方的“确定”按钮。";
        }
        $.dialog.confirm('广告费用结算方式：公司与经销商结算，经销商与广告公司结算。是否已知悉？',function(r) {
            if (r) {
                $.dialog({
                    title: _title,
                    content: "url:"+url,
                    lock: true,
                    width: "1250",
                    height: "620",
                    zIndex: 10000,
                    parent: windowapi,
                    ok: function () {
                        iframe = this.iframe.contentWindow;
                        var confirmBtn = $('#btn_sub', iframe.document);
                        if(actType=="2"){
                            $.dialog.confirm('公司规定：1、单门头超20平方，按20平方核销。2、单铝塑背景墙超7平方，按7平方核销。如您确定已知悉，请点击确认！',function(r) {
                                if (r) {
                                    confirmBtn.click();
                                }
                            });
                        }else{
                            confirmBtn.click();
                        }
                        return false;
                    },
                    cancelVal: '关闭',
                    cancel: function () {
                        $("#tsActApplyVoList").datagrid("reload");
                    }
                });
            }
        });
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }
    function findRejectReason() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goRejectReason&id="+row[0].id+"&actCode="+row[0].actCode;
        $.dialog({
            title: "驳回原因(历史记录) —— 如有异议，请联系QQ审核。",
            content: "url:"+url,
            lock: true,
            width: "700",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
</script>
