<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report019List" fitColumns="true" title="直营促销活动对比"
                    pagination="true" autoLoadData="true" actionUrl="report019Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="开票记账日期" field="billingDateStr" sortable="false" ></t:dgCol>
            <t:dgCol title="开票记账日期" field="billingDate" hidden="true" frozenColumn="true" sortable="false" query="true" queryMode="group" formatter="yyyyMMdd"></t:dgCol>
            <t:dgCol title="销售片区" field="salesDistrict"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="销售片区描述" field="salesDistrictsDr"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="售达方" field="soldToPartyCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="售达方名称" field="soldToPartyName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="送达方" field="shipToPartyCode"  sortable="false"></t:dgCol>
            <t:dgCol title="送达方名称" field="shipToPartyName"  sortable="false"></t:dgCol>
            <t:dgCol title="物料号" field="materialNum"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="物料号描述" field="materialDr"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="开票凭证类型" field="invoiceVoucherType"  sortable="false"></t:dgCol>
            <t:dgCol title="行项目类型" field="itemType"  sortable="false"></t:dgCol>
            <t:dgCol title="开票数量" field="invoiceNum"  sortable="false"></t:dgCol>
            <t:dgCol title="开票数量单位" field="invoiceNumUnit"  sortable="false"></t:dgCol>
            <t:dgCol title="开票基本数量" field="invoiceBasicNum"  sortable="false"></t:dgCol>
            <t:dgCol title="开票基本单位" field="invoiceBasicUnit"  sortable="false"></t:dgCol>
            <t:dgCol title="净重" field="suttle"  sortable="false"></t:dgCol>
            <t:dgCol title="行项目销售成本" field="itemSaleCost"  sortable="false"></t:dgCol>
            <t:dgCol title="行项目总金额" field="itemAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="开票凭证号" field="invoiceVoucherNum"  sortable="false"></t:dgCol>
            <t:dgCol title="库存地" field="storageLocation"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="物料组" field="materialGrop"  sortable="false"></t:dgCol>
            <t:dgCol title="单据类型" field="invoicesType"  sortable="false"></t:dgCol>
            <t:dgCol title="原供价" field="originalPrice"  sortable="false"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" query="true"  sortable="false"></t:dgCol>
            <t:dgCol title="申请价" field="applyPrice"  sortable="false"></t:dgCol>
            <t:dgCol title="可用折扣" field="yesDiscount"  sortable="false"></t:dgCol>
            <t:dgCol title="实际折扣额" field="practicalDiscount"  sortable="false"></t:dgCol>
            <t:dgCol title="超审批" field="examineAndApprove"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report019Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
//        $("#report019Listtb_r").find("input[name='billingDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report019Listsearch() {
        var orgCode = $("#report019Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report019Listtb_r").find("input[name='yearMonth']").val();



        var queryParams = $("#report019List").datagrid('options').queryParams;
        $("#report019Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report019List").datagrid({url:'report019Controller.do?findReportList'});
    }

</script>
