<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<script type="text/javascript">
	$(function(){
		$("#extChar3").attr("readonly","readonly"); 
		$("#extChar4").attr("readonly","readonly"); 
		$("#extChar8").attr("readonly","readonly"); 
		var str = '<a href="#" class="easyui-linkbutton l-btn l-btn-plain" plain="true" icon="icon-search" onclick="openSapBankInfo();"><span class="l-btn-left"><span class="l-btn-text"><span class="l-btn-empty icon-search">&nbsp;</span></span></span></a>';
		$("input[name='extChar3']").parent().append(str);
	});
	
	function openSapBankInfo() {
		createwindowExt("银行信息",
				"tsapBankInfoController.do?goTsapBankInfoSelectMain",
				400,300,{
			            ok : function() {
			               iframe = this.iframe.contentWindow;
			               var r = iframe.$("#tsapBankInfoList").datagrid("getSelected");
			               if(r == null) {
			            	   tip("请选择数据");
			            	   return false;
			               }
			               $("#extChar3").val(r.banka);
			               $("#extChar4").val(r.bankl);
			               $("#extChar8").val(r.banks);
			            },
			            cancelVal : '关闭',
			            cancel : true
			     });
	}
</script>