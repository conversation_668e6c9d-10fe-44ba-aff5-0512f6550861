<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="dealerActApplyList" title="授信积分管理【市场部】"  actionUrl="ttAreaActApplyController.do?findAreaActApplyList&sRegionTp=10"
	  		 checkbox="true" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<%--<t:dgCol title="审批类型" field="bpmStatus" dictionary="bpm_status" query="true" width="100" ></t:dgCol>--%>
			<%--<t:dgCol title="活动类型" field="actType" query="true	" dictionary="act_type" width="200"></t:dgCol>--%>
			<t:dgCol title="活动类型" field="actTypeName"   width="100"></t:dgCol>
			<t:dgCol title="活动细类编码" field="actType"   width="100"></t:dgCol>
			<t:dgCol title="经销商编码" field="dealerCode" query="true" width="100"></t:dgCol>

			<t:dgCol title="经销商名称" field="dealerName" query="true" width="300"></t:dgCol>
			<%--<t:dgCol title="材料" field="material"   width="200" ></t:dgCol>--%>
			<t:dgCol title="门头数量" field="doorNum"  hidden="true" width="100"></t:dgCol>
			<t:dgCol title="授信积分"  field="applyAmount" width="100" ></t:dgCol>
			<%--<t:dgCol title="基金余额"  field="fundBalance" width="100" ></t:dgCol>
			<t:dgCol title="未报销金额" field="otherBalance" width="80" sortable="false" ></t:dgCol>
			<t:dgCol title="CRMS待报销积分" field="crmsBlance" width="80" sortable="false" ></t:dgCol>
			<t:dgCol title="本系统未报销金额" field="eblance" width="80" sortable="false" ></t:dgCol>--%>
			<%--<t:dgCol title="申请金额" field="applyAmount"  width="200"></t:dgCol>--%>
			<t:dgCol title="开始时间" field="startDate" formatter="yyyy-MM-dd" query="true" queryMode="group" width="100"></t:dgCol>
			<t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd" width="100"></t:dgCol>

			<%--<t:dgCol title="活动状态" field="useState" dictionary="act_state" width="100"></t:dgCol>--%>
			<t:dgCol title="活动状态" field="useState" formatterjs="statevalue" width="100"></t:dgCol>

			<t:dgCol title="活动编号" field="actCode" query="true" width="150" ></t:dgCol>
			<t:dgCol title="活动名称" field="actName" query="true"  width="200"></t:dgCol>

			<t:dgToolBar title="创建" icon="icon-add" operationCode="add" url="ttAreaActApplyController.do?goAreaActForm&optType=0" width="1000" height="500" funname="addAreaActApply"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit"  operationCode="update" url="ttDealerActApplyController.do?goDealerActApplyForm&optType=1&isAreaAct=1"  funname="ownUpdate"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url="ttAreaActApplyController.do?deleteAreaActApply"  funname="deleteALLSelect"></t:dgToolBar>
			<%--<t:dgToolBar title="提交审批" operationCode="dataIn" icon="icon-ok" onclick="starSubmitForm()" ></t:dgToolBar>--%>
			<t:dgToolBar title="导入" operationCode="dataIn" icon="icon-dataIn" onclick="importDataByXml({impName:'ttDealerActApply10', gridName:'dealerActApplyList'})"></t:dgToolBar>

			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
			<%--<t:dgToolBar title="查看日志" icon="icon-log" onclick="showLog('dealerActApplyList')" ></t:dgToolBar>--%>
			<t:dgToolBar title="停用" icon="icon-edit"  operationCode="update" url=""  funname="stopAct"></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

	function statevalue(value) {
	    if(value==1){
	        return '生效';
		}
		if(value==0){
	        return '停用';
		}

    }
    //新增
    function addAreaActApply(title, url, grid, width, height){
        gridname = grid;
        openWindOwn(title, url,width, height);
    }

    //修改
    function ownUpdate(title, url, id, width, height) {
        gridname = id;
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }

        var id = rowsData[0].id;
        //检查是否可继续
        if(!checkTheDataIsCouldToContinue(id) ){
			return ;
		}

        url += '&id=' + id;
        if (!width || width=="" || width=='null' || !height || height=="" || height=='null')
            createwindowSmall(title, url, width, height);
        else
            createwindow(title, url, width, height);
    }
    //检查是否可继续
    function checkTheDataIsCouldToContinue(id) {
        var thisData = {
            id : id
		}
		var url = "ttAreaActApplyController.do?checkTheDataIsCouldToContinue";
        var d = ajaxPost(thisData,url);
        if(!d.success){
			tip(d.msg);
		}
		return d.success;

    }

    function stopAct() {
        var rows = $("#dealerActApplyList").datagrid('getSelections');
        if(rows.length <= 0){
            tip("请至少选择一条数据")
        }else {
            getSafeJq().dialog.confirm("你确定停用所选数据吗?", function(r) {
                if (r) {
                    var ids = [] ;
                    for ( var i = 0; i < rows.length; i++) {
                        var subid = rows[i].id;
                        ids.push(subid);
                    }
                    var url="ttDealerActApplyController.do?stopActApply";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
//                                 $("#dealerActApplyList").datagrid('reload');
                                // $("#ttAccruedFormulaList").datagrid('unselectAll');
                                window.location.reload();
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        }

    }

    //启用停用
    function startOrStop(flag){
        var rows = $("#ttAccruedFormulaList").datagrid('getSelections');
        var title="启用";
        if(flag!=0){
            title="停用";
		}
		var url="ttAccruedFormulaController.do?updateTtAccruedFormula";
        var ids=[];
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定"+title+"所选数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(','),
							flag:flag
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                $("#ttAccruedFormulaList").datagrid('reload');
                                $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要"+title+"的数据");
        }
    }
    //导出
    function toExcel(){
            excelExport("ttAreaActApplyController.do?exportXls&sRegionTp=10","dealerActApplyList");
    }

    function showLog(datagridName) {
        var rowData = $('#'+datagridName).datagrid("getSelections");
        if (rowData.length==null||rowData.length<1||rowData.length>1) {
            tip("请选择一行数据进行操作");
            return;
        }
        var id = rowData[0].id;
        var url = "url:logController.do?goLogMain&id="+id;
        safeShowDialog({
            content : url,
            lock : true,
            title : "日志",
            width : 1000,
            height : 500,
            cache : false,
            cancelVal : '关闭',
            cancel : true
        });
    }


    //----------------------提交流程star--------------------------//
    //提交流程
    function starSubmitForm() {
        var rowDatas = $("#dealerActApplyList").datagrid("getSelections");
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
		var name = '';
        var ids = [];
        for ( var i = 0; i < rowDatas.length; i++) {
            var rowData = rowDatas[i];
            if (rowData.bpmStatus == 2){
                tip("处理中的数据不可再次发起");
                return ;
            }else if (rowData.bpmStatus == 3){
                tip("审批通过的数据不可再次发起");
                return ;
            }
            ids.push(rowData.id);
            name = rowData.actName;
        }

        top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });

        var processKeyType='direct_travel_expenses_type';//---占位使用
        var params = {
            processKeyType:processKeyType
        };

        var json  = {
            businessKey : ids.join(","),
            fullPathName : 'com.biz.eisp.tpm.doorActApply.areaWorkFlow.controller.TtAreaActApplyWorkFlowController',
            params : params
		}
		var url = "customTaProcessThemeController.do?getIntoTaProcessThemesParams";
        var d = ajaxPost(json,url)
		if(d.success){
            var obj = d.obj;
            params.processKey = 'BPM003';//--大区
            json  = {
                name : name ,
                detail : '',
                businessKey : obj.businessKey ,
                businessKeyMain : obj.businessKeyMain ,
                fullPathName : obj.fullPathName ,
				params : JSON.stringify(params)
            }
            url = "taProcessThemeController.do?doSubmit";
            d = ajaxPost(json,url)
			if(d.success){
                reloadTable();
			}
        }
        top.$.messager.progress("close");
		tip(d.msg);
        /*
        customSubmitDialog(ids.join(","),"","","com.biz.eisp.tpm.travelexpenses.controller.TsTravelExpensesWebWorkFlowController",JSON.stringify(params))
    	*/
    }

    //----------------------提交流程end----------------------//

    /**
     * 通用ajax post 方法
     * @param json 传入参数json
     * @param url  调用url
     */
    function ajaxPost(json,url){
        var json;
        $.ajax({
            url:url,
            data:json,
            dataType:'json',
            async:false,
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }
    function openWindOwn(title, url,width, height){
        width = width != '' ? width : "1000";
        height = height != '' ? height : "500";
        createwindowExt(title,url,width,height,{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }
</script>
