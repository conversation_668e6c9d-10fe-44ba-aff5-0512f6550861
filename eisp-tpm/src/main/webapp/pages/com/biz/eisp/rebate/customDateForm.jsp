<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>返利公式</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <script src="resources/laytpl/laytpl.js"></script>
    <script src="resources/laytpl/formulacheck.js"></script>
</head>
<body>
<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" action="" beforeSubmit="checkSubmit">
    <div class="form">
        <label class="Validform_label">开始时间:</label>
        <input id="beginDate" name="beginDate"
               class="inputxt Wdate" dataType="*"
               style="width: 120px;"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\')}'})" readonly />
    </div>
    <div class="form">
        <label class="Validform_label">结束时间:</label>
        <input id="endDate" name="endDate"
               class="inputxt Wdate" dataType="*"
               style="width: 120px;"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}'})" readonly />
    </div>
</t:formvalid>
</body>
<script type="text/javascript">
    function checkSubmit() {
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        if(beginDate == null || beginDate == "") {
            tip("请选择开始时间");
            return false;
        }
        if(endDate == null || endDate == "") {
            tip("请选择结束时间");
            return false;
        }
    }
</script>
</html>
