<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>新增定额费用结案总单</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttAuditLongtermController.do?saveTtAuditLongTotal" refresh="true">
    <input type="hidden" id="id" name="id" value="${vo.id }">

    <input type="hidden" id="bpmStatus" name="bpmStatus" value="1">

    <div class="form">
        <label class="Validform_label">结案类型:</label>
        <t:dictSelect field="actTypeCode"  extendJson="{style:'width:150px'}"
                      divClass="STYLE_LEG" type="select" typeGroupCode="process_audit_cqdt_type"
                      defaultVal="${vo.actTypeCode}" dataType="*"
                      hasLabel="true" title="结案类型"></t:dictSelect>
        <span style="color: red">*</span>
    </div>

    <c:if test="${vo.billCode!= null}">
        <div class="form">
            <label class="Validform_label">结案申请编码: </label>
            <input name="billCode"  class="inputxt" value="${vo.billCode}" readonly = "readonly"/>
        </div>
    </c:if>

    <div class="form">
        <label class="Validform_label">结案申请名称: </label>
        <input ajaxUrl="ttAuditLongtermController.do?validateLongTermName&id=${vo.id}"
               name="billName" id="billName" datatype="*" class="inputxt" value="${vo.billName}" />
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">结案申请备注: </label>
        <textarea rows="10" cols="30" id="remark" name="remark" style="resize:none">${vo.remark }</textarea>
    </div>

</t:formvalid>
</body>
</html>