<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id = "remakDiv" >
    <div id="remarkValueDiv" style="float: left; width: 419px; margin-left: 5px; size: 18px;" >
        <label style="font-size: 40px; text-align: center; line-height: 150px; " >备注:&nbsp;</label>
        <textarea title="remark" id="remark" name="remark" id="" cols="60" rows="13" style="position: absolute;top: 5px; font-size: 16px; width: 360px;height: 200px; " ></textarea>
    </div>
    <div id = "remarkLengthShow" style="background-color: #f4f4f4; width: 65px;height: 20px; position: absolute; left: 480px; top: 5px;">
        <span id="lengthNum">0</span>/<span id="maxLengthNum">0</span>
    </div>

</div>
</html>

<script type="text/javascript">

    var dataobj = new ThisDataObj();
    var backFun = function () {}
    var remarkFild;

    function setParams(dataobjTemp,remarkTemp,remarkFildTemp,backFunTemp){
        setDataobj(dataobjTemp);
        setRemark(remarkTemp);
        remarkFild = remarkFildTemp;
        backFun = backFunTemp;
        initRemarkEvent();

    }

    function setDataobj(dataobjTemp){
        dataobj.setDataObj(dataobjTemp);
    }

    function getDataObj(){
        return dataobj.getDataObj();
    }

    function initRemarkEvent(){
        $('#remarkLengthShow').hide();
        var obj = $('#remark');
        obj.focus(function(){
        $('#maxLengthNum').html(returnMaxLengthNum());
            $('#remarkLengthShow').show();
        });
        obj.blur(function(){
            checkRemarkIsOk(this);
            $('#remarkLengthShow').hide();
        });
        obj.bind('input propertychange',function(){
            checkRemarkIsOk(this)
        });
    }
    function returnMaxLengthNum(){
        return '${maxLength}';
    }

    function checkRemarkIsOk(obj){
        var thisObj = $(obj);
        var length = thisObj.val().length;
        $('#lengthNum').html(length);
        var maxLength = returnMaxLengthNum();
        if(length > maxLength){
            $('#remarkLengthShow').css("color","red");
            thisObj.css("background-color","#ffe4e1");
        }else{
            $('#remarkLengthShow').css("color","");
            thisObj.css("background-color","");
        }
    }

    function saveRemark(){
        var remark = getRemark();
        if(remark == ''){
            tip('请填写备注');
            return ;
        }
        var maxLength = returnMaxLengthNum();
        if (remark.length > maxLength) {
            tip("备注字数(包含标点、空格、换行等所有字符)不能超过" + maxLength );
            return ;
        }
        starSaveRemark(remark);
    }

    //保存备注
    function starSaveRemark(remark) {
        if(remarkFild == ''){
            tip('传入备注对应字段为空!');
            return ;
        }
        var upPageData = getDataObj();
        if(upPageData == null){
            tip('关键头表数据为空');
            return ;
        }
        var thisDatas = {
            yearMonth : upPageData.yearMonth,
            orgCode : upPageData.orgCode,
        }
        thisDatas[remarkFild] = remark;
        var url = "ttMonthlyPlanConfirmController.do?saveWriteRemark";
        var d = ajaxPost(thisDatas,url);
        if (d.success){
            W.tip(d.msg);
            backFun(upPageData,thisDatas,remarkFild);
            close();
        }
        tip(d.msg);
    }

    //数据对象
    function ThisDataObj(){
        var dataObj;
        this.setDataObj = function(dataObjTemp){
            dataObj = dataObjTemp;
        };
        this.getDataObj = function(){
            return dataObj
        };
    }

    //写入备注
    function setRemark(remark){
        $('#remark').val(remark);
    }

    function getRemark(){
        return $('#remark').val();
    }
    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    function close(){
        frameElement.api.close();
    }
</script>