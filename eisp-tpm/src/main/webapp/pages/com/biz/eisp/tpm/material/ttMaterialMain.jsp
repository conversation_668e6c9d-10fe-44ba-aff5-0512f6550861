<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<%--<%
    String imagePath="http://"+request.getServerName()+":"+request.getServerPort()+"/image/sci/";
%>--%>
<style>
    img{ width:80px; height:80px}
</style>
<div id="system_ttHqMaterialList" class="easyui-layout" fit="true">
    <input type="hidden" id="imagePath" value="${imagePath}">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="ttHqMaterialList" title="物料指导"  actionUrl="ttHqMaterialController.do?findHqMaterialList"
                    idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group" singleSelect="false">
            <t:dgCol title="id"  field="id"  hidden="true"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="事业部"  field="orgName"  hidden="false" query="false"  queryMode="single"></t:dgCol>
            <t:dgCol title="物料编号"  field="materialCode"  hidden="false" query="true"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="物料名称"  field="materialName"  hidden="false"  query="true" queryMode="single"  ></t:dgCol>
            <t:dgCol title="使用说明"  field="description"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="申报年月"  field="yearMonth" hidden="true" query="false" formatter="yyyy-MM" ></t:dgCol>
            <t:dgCol title="开始申报时间"  field="beginDate" hidden="false" query="true" formatter="yyyy-MM-dd" ></t:dgCol>
            <t:dgCol title="结束申报时间"  field="endDate" hidden="false" query="true" formatter="yyyy-MM-dd" ></t:dgCol>
            <t:dgCol title="成本价（元）"  field="costPrice"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="最小包装量（EA）"  field="minPack"   hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="客户承担费用（元）"  field="bearCost"   hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="客户承担比例（%）"  field="bearScale"   hidden="false"  queryMode="single"  ></t:dgCol>

            <t:dgCol title="照片"  field="picUrl"  hidden="false" formatterjs="picture" queryMode="single"  ></t:dgCol>
            <t:dgCol title="创建人"  field="createName"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="创建时间"  field="createDate"  hidden="false" formatter="yyyy-MM-dd hh:mm:ss"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="最近更新人"  field="updateName" dictionary="purchase_type"   hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="最近更新时间"  field="updateDate"  hidden="false"  queryMode="single" formatter="yyyy-MM-dd hh:mm:ss" ></t:dgCol>

            <t:dgToolBar title="新增物料" operationCode="add" icon="icon-add" url="ttHqMaterialController.do?goTtHqMaterialForm" funname="add" width="800" height="650"></t:dgToolBar>
            <t:dgToolBar title="编辑" operationCode="update" icon="icon-edit" url="ttHqMaterialController.do?goTtHqMaterialForm&type=edit" funname="update" width="1000" height="650"></t:dgToolBar>
            <t:dgToolBar title="查看" operationCode="info"  icon="icon-look" url="ttHqMaterialController.do?goTtHqMaterialForm&type=edit" funname="detail" width="1000" height="650"></t:dgToolBar>
            <t:dgToolBar title="删除" operationCode="delete"  icon="icon-delete" url="ttHqMaterialController.do?doBatchDel" funname="deleteALLSelect"></t:dgToolBar>
            <t:dgToolBar title="上传照片" operationCode="file" icon="icon-upload" url="tbAttachmentController.do?goTbAttachmentMain&extendService=ttHqMaterialPicService&attachmentType=40" onclick="uploadImg()"></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="exout" icon="icon-dataOut" url="" funname="toExcel"></t:dgToolBar>
            <t:dgToolBar title="日志" operationCode="log" icon="icon-log" url="ttHqMaterialController.do?goLogMain" funname="detailLog" width="1000" height="650"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 年月
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
            $("input[name='beginDate']").attr("class","Wdate").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
            $("input[name='endDate']").attr("class","Wdate").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });

    function picture(value) {
        var img = value;
        var imagePath=$("#imagePath").val();
        if(value != ""&&value!=null){
            var str = "<img class='imgW' style='width: 80px;height:80px;cursor: pointer;' src="+imagePath+value + "  onclick='picBig(this)'>";
            return str;
        }
        return "";
    }

    //物料详情
    function materialDetail(){
        var rowDatas = $("#ttHqMaterialList").datagrid('getSelections');
        if(rowDatas.length != 1){
            tip("请选择一条要查看的数据");
            return;
        }
        var id = rowDatas[0].id;
        safeShowDialog({
            content : "url:ttHqMaterialController.do?materialInfo&id="+id,
            lock : true,
            title : "物料详情",
            width : 800,
            height : 650,
            left :'50%',
            cache : true,
            cancel:true,
            cancelVal:'关闭'
        });
    }

    //上传照片
    function uploadImg(){
        var rowDatas = $("#ttHqMaterialList").datagrid('getSelections');
        if(rowDatas.length != 1){
            tip("请选择一条数据");
            return;
        }
        var id = rowDatas[0].id;
        safeShowDialog({
            content : "url:tbAttachmentController.do?goTbAttachmentMain&extendService=ttHqMaterialPicService&attachmentType=40&extend=pic&id="+id,
            lock : true,
            title : "物料照片上传",
            width : 800,
            height : 650,
            left :'50%',
            cache : true,
            cancel:true,
            cancelVal:'关闭'
        });
    }


    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性

        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }

    //导出
    function toExcel(){
        var flag = 0 ;

        safeShowDialog({
            content: "url:ttHqMaterialController.do?checkExportOutInfo",
            lock: true,
            title: "可选待导出内容",
            width: 400,
            height: 200,
            cache: false,
            ok: function() {
                iframe = this.iframe.contentWindow;
                flag = iframe.getCheckedInfo();
                excelExport("ttHqMaterialController.do?exportXls&flag="+flag,"ttHqMaterialList");
                return true;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>