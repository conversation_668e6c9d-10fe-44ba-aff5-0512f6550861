<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="costUseForRebate"  fitColumns="false" title="费用使用明细"
                    actionUrl="tdCostUseController.do?findCostUseList&kunnr=${selectdealerId}" idField="id" fit="true" queryMode="group" pageSize="30">
			<t:dgCol title="" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="经销商编号" field="kunnr" ></t:dgCol>
			<t:dgCol title="经销商名称" field="kunnrName" ></t:dgCol>
			<t:dgCol title="活动编号" field="discountSum" ></t:dgCol>
			<t:dgCol title="活动名称" field="supplementSum"  ></t:dgCol>
			<t:dgCol title="核销单号" field="verifCode"  ></t:dgCol>
			<t:dgCol title="费用类型" field="costType" replace="核销_1,返利_2,手工入账_3" ></t:dgCol>
			<t:dgCol title="核销金额" field="verifMoney"  ></t:dgCol>
			<t:dgCol title="订单编号" field="orderId"  ></t:dgCol>
			<t:dgCol title="使用金额" field="money"  ></t:dgCol>
			<t:dgCol title="支付方式" field="payType" query="true" replace="货补_1,折扣_2"></t:dgCol>
			<t:dgCol title="物料编号" field="matnr" ></t:dgCol>
            <t:dgCol title="物料名称 " field="maktx" ></t:dgCol>
			<t:dgToolBar title="导出费用使用明细" icon="icon-dataOut" url="rebatePoolController.do?exportCostUseDetailXlsForBack&kunnr=${selectdealerId}" funname="excelExport" ></t:dgToolBar>
		</t:datagrid>
    </div>
</div>

<script>
//var selectdealerId = "${selectdealerId}";
$(function () {
	
//    $("input[name='kunnr']").val(selectdealerId);
//    costUseForRebatesearch();
})
</script>
