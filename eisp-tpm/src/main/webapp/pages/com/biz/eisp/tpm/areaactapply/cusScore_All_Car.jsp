<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="top_panel" fit="true" style="height: 45px;">
    <span style="font-size: 16px;color: mediumvioletred;font-family: 微软雅黑;font-weight: bold">
          &nbsp;1、可用积分 =  (2)SAP基金余额 - (3)SAP未报销 - (4)CRMS待报销 - (5)本系统未报销。
    <br />
        &nbsp;2、@可用积分(减掉经销商未审批) = 可用积分 - @经销商未审批
    </span>

</div>
<div id="buttom_pannel" style="clear:both; width: 100%;height: 655px;">
        <t:datagrid name="dealerScoreList" title="经销商积分详情列表（汽车门头）"  actionUrl="ttAreaActApplyController.do?findCusScoreList_All&adsType=2"
                    checkbox="false"  fit="true" idField="id"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
            <t:dgCol title="经销商名称" field="dealerName" query="true"  width="200"></t:dgCol>
            <t:dgCol title="经销商编码" field="dealerCode" query="true"   width="80" ></t:dgCol>
            <%--t:dgCol title="本批次未报销(SAP)" field="wbxWrz"   width="120" ></t:dgCol--%>
            <t:dgCol title="@经销商未审批" field="wEblanStr" width="130" sortable="false" ></t:dgCol>
            <t:dgCol title="@可用积分(减掉经销商未审批)" field="avaIncWAuit" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="可用积分" field="available"   width="90" ></t:dgCol>
            <%--t:dgCol title="(1) 授信积分" field="applyAmount"   width="85" ></t:dgCol--%>
            <t:dgCol title="(1) 更新日期" field="crDate"   width="85" ></t:dgCol>
            <t:dgCol title="(1) 截止日期" field="enDate"   width="85" ></t:dgCol>
            <t:dgCol title="(2) SAP基金余额" field="fundBalance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(3) SAP未报销" field="otherBalance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(4) CRMS待报销" field="crmsBlance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(5) 本系统未报销" field="eblanceStr" width="130" sortable="false" ></t:dgCol>
            <t:dgCol title="可做门头数(最少)" field="doorNumMin2" width="115" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="可做门头数(最大)" field="doorNumMax2" width="115" sortable="false" align="center" ></t:dgCol>

            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
        </t:datagrid>

</div>
<script type="text/javascript">
    //导出
    function toExcel(){
        excelExport("ttAreaActApplyController.do?exportXlsQ","dealerScoreList");
    }

    function showAdvDetail1(dealerCode,adsType) {
        var url = "ttActOutUploadController.do?goAdvByCusWAudit&customerCode="+dealerCode+"&adsType="+adsType;
        $.dialog({
            title: "经销商未审批门头列表",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function showAdvDetail2(dealerCode,adsType) {
        var url = "ttActOutUploadController.do?goAdvByCusAudited&customerCode="+dealerCode+"&adsType="+adsType;
        $.dialog({
            title: "经销商已审批（未完结）门头列表",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

</script>
