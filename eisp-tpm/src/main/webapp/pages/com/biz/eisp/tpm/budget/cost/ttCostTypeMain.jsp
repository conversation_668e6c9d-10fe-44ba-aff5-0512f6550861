<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<link rel="stylesheet" href="resources/uploadifive/uploadfive.css" type="text/css"></link>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttCostTypeList" title="活动大类管理"  actionUrl="ttCostTypeController.do?findTtCostTypeList" 
	  		idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		
  		    <t:dgCol field="id" title="id" hidden="true"></t:dgCol>
  		    
  		    <t:dgCol field="costTypeCode" title="活动大类编号" query="true" width="150"></t:dgCol>
		    <t:dgCol field="costTypeName" title="活动大类名称" query="true" width="150"></t:dgCol>
		    <t:dgCol field="costClassify" title="财务费用归类" query="true" width="150" dictionary="costType"></t:dgCol>

		   <%-- <t:dgCol field="businessCostType" title="业务费用归类" query="true" width="150" dictionary="business_cost_type"></t:dgCol>--%>

		    <t:dgCol field="financialAccountName" title="关联预算科目" query="true" width="150"></t:dgCol>
		    
	        <t:dgCol field="costAccountName" title="关联活动细类" width="200"></t:dgCol>  
	        
	        <t:dgCol field="remark" title="备注" width="200"></t:dgCol>  
	        
  		    <t:dgCol field="enableStatus" title="生效状态" query="true" width="120" dictionary="enable_status"></t:dgCol> 
  		    <t:dgCol field="actRoleCode" title="角色编码" hidden="true"></t:dgCol>
  		    <t:dgCol field="actPositionCode" title="职位编码" hidden="true"></t:dgCol>

  		    <t:dgCol field="createName" title="创建人" width="150" query="true"></t:dgCol>
  		    <t:dgCol field="createDate" title="创建时间" formatter="yyyy-MM-dd HH:mm:ss" width="150"></t:dgCol>
  		    <t:dgCol field="cd" title="创建时间" hidden="true" query="true"></t:dgCol>

  		    <t:dgCol field="updateName" title="最近更新人" width="150" query="true"></t:dgCol>
 		    <t:dgCol field="updateDate" title="最近更新时间" width="120"  formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
 		    
			<t:dgToolBar operationCode="add" title="录入活动大类" icon="icon-add" url="ttCostTypeController.do?goTtCostTypeForm" funname="add" width="450" height="500"></t:dgToolBar>
			<t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttCostTypeController.do?goTtCostTypeForm" funname="update" width="400" height="500"></t:dgToolBar>
			<t:dgToolBar operationCode="look" title="查看" icon="icon-look" url="ttCostTypeController.do?goTtCostTypeForm" funname="detail" width="400" height="500"></t:dgToolBar>

			<t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="" funname="del"></t:dgToolBar>


			<t:dgToolBar operationCode="start" title="启用" icon="icon-start"  onclick="startOrStop(0)"></t:dgToolBar>
			<t:dgToolBar operationCode="stop" title="停用" icon="icon-stop"  onclick="startOrStop(1)"></t:dgToolBar>
			<t:dgToolBar operationCode="out" title="导出" icon="icon-dataOut" url="ttCostTypeController.do?exportXls"  funname="excelExport"></t:dgToolBar>
			<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttCostTypeController.do?goTtCostTypeLogMain" funname="detailLog" width="1200" height="500"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">
	$(function () {
        $("#ttCostTypeListtb_r").find("input[name='cd']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });
    function del(){
        var seletctTarget =  $("#ttCostTypeList").datagrid("getSelections");
        var title = "";
        if(seletctTarget==null || seletctTarget==""){
            tip("必须选择一条数据");
            return;
        }
        $.messager.confirm('操作提示','确定删除?',function(r){
            if (r){
                $.ajax({
                    type : "POST",
                    url : "ttCostTypeController.do?delTtCostTypeById&id="+seletctTarget[0].id,
                    async:false,
                    success : function(data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        $("#ttCostTypeList").datagrid("reload");
                    }
                });
            }
        });
    }
	//启用停用
	function startOrStop(flag){
		var seleted = $("#ttCostTypeList").datagrid('getSelected');
		if(seleted == null){
			tip("请选择一条要操作的数据");
			return;
		}
		var tipmMsg = "";
		if(flag == 0){
			tipmMsg = "确定要启用该数据吗?"
			if(seleted.enableStatus == flag){
				tip("已经处于启用状态,无需再次启用");
				return false;
			}
		}else{
			if(seleted.enableStatus == flag){
				tip("已经处于停用状态,无需再次停用");
				return false;
			}
			tipmMsg = "确定要停用该数据吗?"
		}
		$.messager.confirm('操作提示',tipmMsg,function(r){ 
		    if (r){
		    	$.ajax({
		        	type : "POST",
		        	url : "ttCostTypeController.do?isEnableStatus",
		        	data : {
		            	"id" : seleted.id,
		            	"enableStatus": flag
		        	},
		        	dataType : "json",
		        	success : function(data) {
		        		tip(data.msg);
		        		$("#ttCostTypeList").datagrid('reload');
		        	},
		        	error:function(){
		        		tip("服务器异常，请稍后再试");
		        	}
			   });
		    }
		});
	}

</script>
