<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
</head>
<body>
<div id="system_org_orgList" class="easyui-layout" fit="true">
	<div region="center">
		<div class="panel datagrid">
			<input id="allAmount" name="allAmount" type="hidden" value="${totalAmount}"/>
			<input id="temporaryKey" name="temporaryKey" type="hidden" value="${temporaryKey}"/>
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
						</span>
						<span style="float:right">
							<span style="color:red;line-height:30px;margin-right:50px;" >费用金额合计：<span id="amountAvailable">${amount}</span></span>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">
					</div>
				</div>
				<div class="datagrid-view">
					<table class="actTable"  id="ttSplitCostTerminalList">
						<thead>
							<tr>
								<td>序号</td>
								<td>客户编号</td>
								<td>客户名称</td>
								<td>年月</td>
								<td>门店名称</td>
								<td>门店编号</td>
								<td>重点门店</td>
								<td>陈列类型</td>
								<td>数量</td>
								<td>公司承担金额</td>
								<td>活动总金额</td>
								<td>标准</td>
								<td>门店销售额</td>
								<td>费率</td>
								<td>总费率</td>
								<td style="display:none"></td>
								<td style="display:none"></td>
								<td style="display:none"></td>
								<td>备注</td>
							</tr>
						</thead>
						<tbody>
							 <c:if test="${(hisList)!= null && fn:length(hisList) > 0}">
								 <c:forEach items="${hisList}" var="actVo" varStatus="idxStatus">
								 <tr>
								 	<td>${idxStatus.index +1}</td>
									<td>${actVo.customerCode}</td>
									<td>${actVo.customerName}</td>
									<td>${actVo.yearMonth}</td>
									<td >${actVo.terminalName}</td>
									<td >${actVo.terminalCode}</td>
									 <td >${actVo.terminalPoint}</td>
									 <td>${actVo.displayTypeName}</td>
									 <td>${actVo.quantity} </td>
									 <td>${actVo.companyAmount} </td>
									 <td>${actVo.amount}</td>
									 <td>${actVo.standard}</td>
									 <td>${actVo.salesVolume}</td>
									 <td>${actVo.rateStr}</td>
									 <td>${actVo.totalRateStr}</td>
									<td style="display:none">${actVo.id}</td>
									<td style="display:none">${actVo.orgCode}</td>
								    <td style="display:none">${actVo.orgName}</td>
								    <td>${actVo.remark}</td>
								</tr>
								 </c:forEach>
							 </c:if>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">

</script>
</body>
