<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>广告材料与设计稿上传</title>
    <style>
        #sureFilesDetail{display:table;}
        .sureFilesDetail_a{float:left;padding-right:20px;margin-right:10px;position:relative;}
        .sureFilesDetail_a img{position:absolute;top:7px;right:0;}
        /*#steps form div.form {*/
        /*position:relative;*/
        /*padding-left:30px;*/
        /*min-height:35px;*/
        /*!* width:450px;*/
        /*margin:0 auto; *!*/
        /*}*/
        #steps form div.form {
            position:relative !important;
            padding-left:30px !important;
            min-height:35px !important;
        }
        #steps form select {
            border: 1px solid darkmagenta !important;
        }
        .htSelectEditor_bcb {

            position: absolute !important;
        }
    </style>
</head>
<t:base type="jquery,easyui,tools,DatePicker,handsontable"></t:base>
<body style="overflow-y: hidden" scroll="no">
  <div region="center" fit="true">

    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="saveAllDataInfo"
                >
        <input id="id" name="id" type="hidden"
               value="${actApplyVo.id}" title="双击我">
        <input id="actCode" name="actCode" type="hidden"
               value="${actApplyVo.actCode}">
        <input id="actType" name="actType" type="hidden"
               value="${actApplyVo.actType}">
        <input id="terminalCode" name="terminalCode" type="hidden"
               value="${actApplyVo.terminalCode}">
        <input id="terminalName" name="terminalName" type="hidden"
               value="${actApplyVo.terminalName}">
        <input id="tmpbusinessId" name="tmpbusinessId" type="hidden"
               value="${tmpbusinessId}">
        <input id="customerCode" name="customerCode" type="hidden"
               value="${actApplyVo.customerCode}">
        <input id="accountCode" name="accountCode" type="hidden"
               value="${actApplyVo.accountCode}">
        <input id="detailJson" name="detailJson" type="hidden"
               value="">
        <div class="form">
            <label><span style="padding-left:5px; color: red">*</span>门头广告材料明细（填写方式：鼠标双击空白单元格）：</label><a href="#" class="easyui-linkbutton"  plain="true" icon="icon-add" onclick="addOneRow()">添加一行广告材料</a>
            <div class="datagrid-view" style="width: 100% ;height: 160px; overflow: auto;">
                  <div id="example"></div>
            </div>
        </div>
        <label style="color: green">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src='resources/img/sap_bcb_index_r.gif' />1、可上传设计稿文件类型:" jpg 、 jpeg、 png "，单个文件建议2M以内。
            &nbsp;&nbsp;&nbsp;&nbsp;
            <a href="ttActOutUploadController.do?showDemo" target="_blank" style="font-size:25px;color: red;font-weight: bold">设计稿范例(点我查看)</a>
        </label>
        <div class="form">
            <span>
                <span style='padding-left:5px; color: red'>*</span>
				<t:uploadH5 name="file_upload" buttonText="选择设计稿" multi="true"
                             dialog="false" onUploadSuccess="uploadForSet" callback=""
                            uploader="ttActOutUploadController.do?saveOutfile&businessId=${actApplyVo.businessId}&custom=${actApplyVo.customerCode}"
                            extend="*.jpg;*.jpeg;*.png;"
                            id="file_upload" formData=""></t:uploadH5>
                </span>
	         	<div id="filediv" style="width: 700px;">
                    <c:if test="${not empty actApplyVo.picVoList}">
                        <table border="0" style="">
                            <c:forEach items="${actApplyVo.picVoList}" var="bean" varStatus="queue">
                                <tr id="${bean.id}" style="margin-bottom: 10px;">
                                    <td style="word-wrap: break-word "><span style="margin-right: 5px;">${bean.imgTypeRemark}</span></td>
                                    <td>
                                        <a  onclick="deleteActOutActApply('${bean.id}')" class="easyui-linkbutton" iconCls="icon-cancel">删除</a>
                                    </td>
                                </tr>
                            </c:forEach>
                        </table>
                    </c:if>
                </div>
	         </div>
			</span>
        </div>
        <br>
        <div class="form">
		<span style="font-size:20px;font-weight:bold;font-family: 微软雅黑, 宋体, Arial, sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注意事项：</span>
	
		<br /><span style="font-size:16px;color:red;font-family: 微软雅黑, 宋体, Arial, sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1、铝塑延伸是指门头封边用料,上封边不报销！</span>
	
        <br /><span style="font-size:16px;color:red;font-family: 微软雅黑, 宋体, Arial, sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、按核销规则，如单个门头的面积超过20平方，并且未曾报备过，公司将按20平方报销！</span>
            <br /><span style="font-size:16px;color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3、报备方法：经销商找区域经理向公司报备。</span>

            <br /><span style="font-size:16px;color:blue;font-family: 微软雅黑, 宋体, Arial, sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4、材料名带"(冻结)"字样代表该经销商暂时无法使用该材料做广告。</span>
            <br /><span style="font-size:16px;color:red;font-family: 微软雅黑, 宋体, Arial, sans-serif">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5、门头申请从”选址申请提交成功 到 提交完施工后照片“，有效期为2个月，逾期将被系统自动作废，请您及时制作并跟踪到位！</span>
            <br /><br /><br />
            <span style="font-size:18px;color:darkred;font-weight: bold;font-family: 微软雅黑, 宋体, Arial, sans-serif;float: right">
            提交方式：点击下方的“确定”按钮。
            </span>
		</div>

    </t:formvalid>
      <div id="model" hidden="hidden" >
          <table>
              <td title="id" file="detailId" hiddenColumn="true" type="text"></td>
              <td title="门头广告编号" file="adCode" type="text"></td>
              <td title="网点编号(自动)" file="terminalCode" type="text" readOnly="true"></td>
              <td title="网点名称(自动)" file="terminalName" type="text" readOnly="true"></td>
              <td title="材料编码" file="materialCode" type="text"  hiddenColumn="true"></td>
              <td title="材料名" file="materialName" type="text" ></td>
              <td title="长(m) <=36" file="mlength" type="numeric" formatter="0,0.00"></td>
              <td title="高(m) <=36" file="mwidth" type="numeric" formatter="0,0.00" ></td>
              <td title="数量" file="nums" type="numeric" formatter="0,0"></td>
              <td title="面积(㎡)(自动)" file="mspace" type="numeric" formatter="0,0.00" readOnly="true"  ></td>
              <td title="金额" file="money" type="numeric" formatter="0,0.00"  hiddenColumn="true" ></td>
              <td title="备注(选址填写)" file="remark" type="text" readonly="true"  ></td>
          </table>
      </div>
</div>
</body>
</html>
<script type="text/javascript">
    $(function(){
        setInterval(function(){
            //$(".htSelectEditor").removeAttr("style");
            if($("select").hasClass("htSelectEditor")){
                $("select").removeClass("htSelectEditor");
                $("select").addClass("htSelectEditor_bcb")
            }
        }, 1000)
    });

    var container = document.getElementById('example');
    var hot;
    var globMap = new HashKey();
    var headerArrKey = "headerArr";
    var columnsKey = "columns";
    var hiddenColumnsKey = "hiddenColumns";
    var switchMapKey = "switchMapKey";
    var columnsArrKey = "columnsArr";
    var fileKeyAndTitleMapKey = "fileKeyAndTitleMap";
    var reg_ymd = /^([0-9]{4})(-)([0-9]{1,2})(-)([0-9]{1,2})$/;//验证yyyy-MM-dd格式
    var selectNums=[1,2,3,4,5];
    var datasTemp = [];
    var selectData=[];
    var matnrData=new Object();
    var remarkData = new Object();
    var matnrSelectData=[];
    $(document).ready(function(){
        getAdvSelect();//加载广告单号
        getMatnrSelect();//加载广告物料
        //初始化Handsontable所需的表头和对应列组合和其他后续需要字段集合
        initHeaderArrAndColumnsAndOtherMap();
        initHandsontable();
        initHandsontableRightMenu();
        if(${not empty actApplyVo.advDetailVos}){
            sureLoadData();
        }else{
            initAddOneRow();
        }
       //showOrHideAddToUploadDiv(false);
    });


    function initAddOneRow(){
        setTimeout(addOneRow,500);
    }

    function sureLoadData() {
        $.ajax({
            url:"ttActOutUploadController.do?findAdvDetail",
            data:{
                "id" : '${actApplyVo.id}',
            },
            method:"post",
            success:function(data){

                var d = $.parseJSON(data);
                if (d.success) {
                    var result = d.obj;
                    hot.loadData(result);
                }
            }
        });
    }

    function initHandsontable(){
        var headerArr = globMap.get(headerArrKey);
        var columns = globMap.get(columnsKey);
        var hiddenColumns = globMap.get(hiddenColumnsKey);
        hot = new Handsontable(container, {
            data : datasTemp,
            rowHeaders : true,
            colHeaders : headerArr,
            columns : columns,
            colWidths: 110,
            hiddenColumns : {
                columns: hiddenColumns,
                indicators: true
            },
            cells: function (row, col, prop) {
                var cellProperties = {};
                cellProperties.renderer = "negativeValueRenderer";
                return cellProperties;
            },
            afterChange : function(changes, source){
                if(changes!=null){
                    for(var i=0;i<changes.length;i++){
                        var rownum=changes[i][0];
                        var file=changes[i][1];
                        var vl=changes[i][3];

                        if(file!=null&&file=="adCode"){
                            hot.setDataAtRowProp(rownum,"terminalCode",$("#terminalCode").val());
                        }
                        if(file!=null&&file=="adCode"){
                            hot.setDataAtRowProp(rownum,"terminalName",$("#terminalName").val());
                        }
                        if(file!=null&&file=="adCode"){
                            hot.setDataAtRowProp(rownum,"remark",remarkData[vl]);
                        }
                        if(file!=null&&file=="materialName"){
                            hot.setDataAtRowProp(rownum,"materialCode",matnrData[vl]);
                        }
                        if(file!=null&&file=="mlength"){ //mspace
                            var mlength=vl;
                            var mwidth=hot.getDataAtRowProp(rownum,"mwidth");
                            var nums=hot.getDataAtRowProp(rownum,"nums");
                            if (!checkNumber(vl)){
                                mlength=0;
                                hot.setDataAtRowProp(rownum,"mlength",0);
                            }
                            if(mwidth==null||mwidth==undefined){
                                hot.setDataAtRowProp(rownum,"mwidth",0);
                                mwidth=0;
                            }
                            if(nums==null||nums==undefined){
                                hot.setDataAtRowProp(rownum,"nums",selectNums[0]);
                                nums=1;
                            }
                            setMspace(rownum,mlength,mwidth,nums);
                        }
                        if(file!=null&&file=="mwidth"){
                            var mwidth=vl;
                            var mlength=hot.getDataAtRowProp(rownum,"mlength");
                            var nums=hot.getDataAtRowProp(rownum,"nums");
                            if (!checkNumber(vl)){
                                mwidth=0;
                                hot.setDataAtRowProp(rownum,"mwidth",0);
                            }
                            if(mlength==null||mlength==undefined){
                                hot.setDataAtRowProp(rownum,"mlength",0);
                                mlength=0;
                            }
                            if(nums==null||nums==undefined){
                                hot.setDataAtRowProp(rownum,"nums",selectNums[0]);
                                nums=1;
                            }
                            setMspace(rownum,mlength,mwidth,nums);
                        }
                        if(file!=null&&file=="nums") {
                            var nums = vl;
                            var mlength = hot.getDataAtRowProp(rownum, "mlength");
                            var mwidth = hot.getDataAtRowProp(rownum, "mwidth");
                            if (!checkNumber(vl)) {
                                nums = 1;
                                hot.setDataAtRowProp(rownum, "nums", selectNums[0]);
                            }
                            if (mlength == null || mlength == undefined) {
                                hot.setDataAtRowProp(rownum, "mlength", 0);
                                mlength = 0;
                            }
                            if (mlength == null || mlength == undefined) {
                                hot.setDataAtRowProp(rownum, "mwidth", 0);
                                mwidth = 0;
                            }
                            setMspace(rownum,mlength,mwidth,nums);
                        }
                    }
                }
            }
        });
    }

    function setMspace(rownum,mlength,mwidth,nums) {
        hot.setDataAtRowProp(rownum,"mspace",Number((mlength*mwidth*nums).toFixed(3)));
        //alert(Number((mlength*mwidth*nums).toFixed(3)));
        if(mlength > 36){//白池标  2018年09月07日
            tip("[长] 异常，不能大于 36米！");
            hot.setDataAtRowProp(rownum,"mlength",0);
            return false;
        }
        if(mwidth > 36){//白池标  2018年09月07日
            tip("[高] 异常，不能大于 36米！");
            hot.setDataAtRowProp(rownum,"mwidth",0);
            return false;
        }
        if(nums > 5){//白池标  2018年09月07日
            tip("[数量] 异常，不能大于 5个！");
            hot.setDataAtRowProp(rownum,"nums",1);
            return false;
        }
        var mj = Number((mlength*mwidth*nums).toFixed(3));
        if(mj > 110){
            tip("[面积] 异常，不能大于 110平方米！");
            hot.setDataAtRowProp(rownum,"mwidth",0);
            return false;
        }
//        if(mlength != 0 && mwidth != 0){//白池标  2018年09月07日      0910取消
//            if(mlength < mwidth){
//                tip("[长度] 不能小于 [宽/高]，请确认是否填反！");
//                hot.setDataAtRowProp(rownum,"mwidth",0);
//                return false;
//            }
//        }
    }
    
    //验证字符串是否是数字
    function checkNumber(theObj) {
        var reg = /^[0-9]+.?[0-9]*$/;
        if (reg.test(theObj)) {
            return true;
        }
        return false;
    }
    //添加右键菜单
    function initHandsontableRightMenu(){
        hot.updateSettings({
            contextMenu: {
                callback: function (key, options) {
                    if (key === 'remove_row') {
                        setTimeout(function () {starSumAllExpensesAmount("expensesAmount");},100);
                    }
                },
                items: {
                    "row_above": {
                        name: '向上添加一行',
//                    disabled: function () {
//                        //如果选中的为第一行则失效
//                        return hot.getSelected()[0] === 0;
//                    }
                    },
                    "row_below": { name: '向下添加一行' },
                    "hsep1": "---------",
                    "remove_row": {
                        name: '移除选中行？',
                    	disabled: function () {
                            //获取改行id
                            /*var row=hot.getSelected()[0];
                            var id=hot.getDataAtRowProp(row,'id');
                            $.ajax({
                                url:"ttActOutUploadController.do?deleteAdvDetail",
                                data:{
                                    "id" : id,
                                },
                                method:"post",
                                success:function(data){

                                    var d = $.parseJSON(data);
                                    if (d.success) {
                                        tip("删除成功");
                                    }
                                }
                            });*/
                            return false;
						}
                    }
//                    "hsep2": "---------",
//                    "about": {name: '关于菜单'}
                }
            }
        });
    }
    //初始化Handsontable所需的表头和对应列组合
    function initHeaderArrAndColumnsAndOtherMap(){
        var headerArr = [];
        var columns = [];
        var hiddenColumns = [];
        var columnsArr = [];
        var fileKeyAndTitleMap = new HashKey();
        var i = 0;
        $('#model td').each(function(){
            var thisObj = $(this);

            var title = getPropertyValueByAttr(thisObj,'title');
            var file = getPropertyValueByAttr(thisObj,'file');
            var type = getPropertyValueByAttr(thisObj,'type');
            var formatter = getPropertyValueByAttr(thisObj,'formatter');
            var dateFormatter = getPropertyValueByAttr(thisObj,'dateFormatter');
            var readOnly = getPropertyValueByAttr(thisObj,'readOnly');
            var hiddenColumn = getPropertyValueByAttr(thisObj,'hiddenColumn');
            var isNull = getPropertyValueByAttr(thisObj,'isNull');

            headerArr.push(title);

            var columnObj = new Object();
            columnObj.data = file;

            if(!checkIsNotUndefinedAndNullAndNullValue(type)){
                type = 'text';
            }
            columnObj.type = type;

            if(checkIsNotUndefinedAndNullAndNullValue(formatter)){
                var obj = new Object();
                obj.pattern = formatter;
                columnObj.numericFormat = obj;
//                columnObj.numericFormat.pattern = obj;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(dateFormatter)){
                columnObj.dateFormat = dateFormatter;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(readOnly)){
                columnObj.readOnly = readOnly;
            }
            if(file=="adCode"){
                columnObj.editor="select";
                columnObj.selectOptions=selectData;
            }
            if(file=="materialName"){
                columnObj.editor="select";
                columnObj.selectOptions=matnrSelectData;
            }
            if(file=="nums"){
                columnObj.editor="select";
                columnObj.selectOptions=selectNums;
                columnObj
            }
            if(checkIsNotUndefinedAndNullAndNullValue(hiddenColumn)){
                hiddenColumns.push(i);
            }else{
                if(isNull != 'true'){
                    columnsArr.push(file);
                    fileKeyAndTitleMap.set(file,title);
                }
            }
            columns.push(columnObj);
            i ++ ;
        });
        globMap.set(headerArrKey,headerArr);
        globMap.set(columnsKey,columns);
        globMap.set(hiddenColumnsKey,hiddenColumns);
        globMap.set(columnsArrKey,columnsArr);
        globMap.set(fileKeyAndTitleMapKey,fileKeyAndTitleMap);
    }

    //添加一行
    function addOneRow(){
        hot.alter('insert_row', 0);
    }
    //获取属性值
    function getPropertyValueByAttr(obj,property){
        return obj.attr(property);
    }

    //处理键值对
    function HashKey(){
        var data = {};
        this.set = function(key,value){   //set方法
            data[key] = value;
        };
        this.unset = function(key){     //unset方法
            delete data[key];
        };
        this.get = function(key){     //get方法
            return data[key] || "";
        }
        this.returnKey = function(){
            //获得对象所有属性的数组
            return Object.getOwnPropertyNames(data);
        }
    }

    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }

    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }

   function getAdvSelect(){
       $.ajax({
           url: "ttActOutUploadController.do?findActDetail",
           data:{"headId":'${actApplyVo.id}'},
           method: "post",
           success: function (data) {
               var data = $.parseJSON(data);
               for(var i=0;i<data.length;i++){
                   selectData.push(data[i].adCode);
                   remarkData[data[i].adCode] = data[i].remarks;
               }
           }
       })
   }
    function getMatnrSelect(){
        $.ajax({
            url: "tmAdMatnrController.do?findMatnrsExt&cusCode="+ ${actApplyVo.customerCode},
            method: "post",
            success: function (data) {
                var data = $.parseJSON(data);
                for(var i=0;i<data.length;i++){
                    matnrSelectData.push(data[i].mname);
                    matnrData[data[i].mname]=data[i].mtype;
                }
            }
        })
    }
    //保存数据进校验
    function saveAllDataInfo(){
        //检查并得到明细数据
        var info = checkAndGetTheDataDetailData();
        if(!checkIsNotUndefinedAndNullAndNullValue(info)){
            return false;
        }
        var strInfo=JSON.stringify(info);
        $("#detailJson").val(strInfo);
        if ($(".uploadifive-queue-item").length > 0) {

            var obj = $("div.uploadifive-queue-item span.filename");//白池标  2018.08.31增加文件类型判断
            var filename = "";
            if(obj.length != 0){
                if(obj.length < selectData.length){
                   tip("设计稿的数量 不能比 门头少！！！");
                   return false;
                }
            }
            for(var i = 0; i < obj.length; i++){
                //alert(obj[i].innerText.toUpperCase().lastIndexOf(".JPG"));
                //alert(obj[i].innerText.toUpperCase().lastIndexOf(".PNG"));
                //alert(obj[i].innerText);
                if(    obj[i].innerText.toUpperCase().lastIndexOf(".JPG") == -1
                    && obj[i].innerText.toUpperCase().lastIndexOf(".PNG") == -1
                    && obj[i].innerText.toUpperCase().lastIndexOf(".JPEG") == -1 ) {
                    tip("上传的附件要求 JPG 或 PNG 或 JPEG 格式，请重新选择！！！");// 增加JPEG
                    return false;
                }
            }
            //return false;
            upload();
        } else {
            tip("请选择设计稿!");
            return false;
        }
        return false;
    }


    //检查并得到明细数据
    function checkAndGetTheDataDetailData(){
        //读取明细数据--所有
        var detailDataObjArr = hot.getSourceData();
        //读取列file数组集合
        var columnsArr = globMap.get(columnsArrKey);
        //读取file与title对应的集合map
        var fileKeyAndTitleMap = globMap.get(fileKeyAndTitleMapKey);
        var actCodeArr = [];

        //循环遍历
        for(var i = 0 ; i < detailDataObjArr.length ; i ++ ){
            //数据对象
            var detailDataObj = detailDataObjArr[i];
            //遍历file字段
            for(var j in columnsArr){
                var file = columnsArr[j];
                //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                //检查为空
                var seeRowNum = hot.getRowHeader(i);
                if(checkIsNotUndefinedAndNullAndNullValue(objValue) ){
                    detailDataObj.pnum = hot.getRowHeader(i);
                }else{

                    var message = '第' + seeRowNum + '行,' + fileKeyAndTitleMap.get(file) + "不能为空!";
                    tip(message);
                    return '';
                }
                if(file == 'materialName'){
                    if(objValue != null &&  objValue.indexOf('(冻结)') == 0){
                        tip('第' + seeRowNum + '行,(' + objValue + ") 材料价格没维护，请联系QQ审核，联系方式详见登陆后的系统首页!");
                        return '';
                    }
                    if(objValue == '铝塑正面'){
                        var nums = detailDataObj['nums'];
                        if(nums != null && nums == 2 ){
                            tip("[材料：铝塑正面] 的 数量 只能填写 1");
                            return '';
                        }
                    }
                    if(objValue == '铝塑延伸(左右封边)'){
                        var nums = detailDataObj['nums'];
                        if(nums != null && nums > 2 ){
                            tip("[材料：铝塑延伸(左右封边)] 的 数量 最大只能填写 2，即左封边和右封边。");
                            return '';
                        }
                    }
                    if(objValue == '铝塑延伸(下底封边)'){
                        var nums = detailDataObj['nums'];
                        if(nums != null && nums > 1 ){
                            tip("[材料：铝塑延伸(下底封边)] 的 数量 最大只能填写 1，即下封边。");
                            return '';
                        }
                    }
                    if(objValue == '彩钢'){
                        var nums = detailDataObj['nums'];
                        if(nums != null && nums == 2 ){
                            tip("[材料：彩钢] 的 数量 只能填写 1");
                            return '';
                        }
                    }

                }
                if(file == 'mspace'){//白池标  2018年09月07日   增加判断面积是否为0
                    if(objValue == 0){
                        tip("面积不允许为 0");
                        return '';
                    }
                }
                if(file =='adCode'){
                    actCodeArr.push(objValue);
                }
                if(!checkValid(hot.getRowHeader(i)-1,file,fileKeyAndTitleMap)){
                    return '';
                }
            }
        }

        //白池标   2018.09.18
        if(detailDataObjArr.length == 1) {
            var detailDataObj = detailDataObjArr[0];
            for (var j in columnsArr) {
                var file = columnsArr[j];
                //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                if(file == 'materialName'){
                    if(objValue != null &&  (objValue  == '铝塑延伸(左右封边)' || objValue  =='铝塑延伸(下底封边)')){
                        tip("（铝塑延伸）是封边材料，不能单独提交，请确认是您选错还是漏上报（铝塑正面）！");
                        return '';
                    }
                }
            }
        }
        //白池标   2018.10.22
        if(detailDataObjArr.length == 2) {
            var counter = 0;
            var detailDataObj = detailDataObjArr[0];
            for (var j in columnsArr) {
                var file = columnsArr[j];
                //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                if(file == 'materialName'){
                    if(objValue != null &&  (objValue  == '铝塑延伸(左右封边)' || objValue  =='铝塑延伸(下底封边)')){
                        counter = counter +1;
                    }
                }
            }
            detailDataObj = detailDataObjArr[1];
            for (var j in columnsArr) {
                var file = columnsArr[j];
                //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                if(file == 'materialName'){
                    if(objValue != null &&  (objValue  == '铝塑延伸(左右封边)' || objValue  =='铝塑延伸(下底封边)')){
                        counter = counter + 1;
                    }
                }
            }

            if(counter == 2){
                tip("（铝塑延伸）是封边材料，不能单独提交，请确认是您选错还是漏了（铝塑正面）！");
                return '';
            }
        }

        //验证广告编号是否都存在
        for(var i=0;i<selectData.length;i++){
            if(actCodeArr.indexOf(selectData[i])==-1){
                tip('还有门头未上报材料,请核查填写后再提交');
                return '';
            }
        }
        return detailDataObjArr;
    }
    //获取列号
    function checkValid(rowNum,file,fileKeyAndTitleMap){
        //读取meta对象
        var metaCells = hot.getCellMetaAtRow(rowNum);
        for(var i = 0 ; i < metaCells.length ; i ++ ){
            var metaCell = metaCells[i];
            if(metaCell.valid!=undefined&&!metaCell.valid&&metaCell.prop == file){
                var message = '第' + rowNum+1 + '行,' + fileKeyAndTitleMap.get(file) + "类型错误!";
                tip(message);
                return false;
            }

        }
        return true;
    }
    function uploadForSet(d,file,response) {
        var falg = false;
        if (d.success) {
            $.ajax({
                url: "ttActOutUploadController.do?saveWorkFlow",
                data:$('#formobj').serialize(),
                method: "post",
                success: function (data) {
                    var data = $.parseJSON(data);
                    tip(data.msg);
                    if (data.success) {
                        falg = true;
                        frameElement.api.opener.reloadTable();
                        frameElement.api.close();
                    }
                }
            })
      }else{
            W.tip(d.msg);
      }
      return falg;
    }
    /*删除设计稿*/
    function deleteActOutActApply(id,imgPath) {
        if(id!=null){
            $.ajax({
                url: "ttActOutUploadController.do?deleteActOutActApply",
                data:{"id":id,"status":'0',"imgPath":imgPath},
                method: "post",
                success: function (data) {
                    var data = $.parseJSON(data);
                    tip(data.msg);
                    if(data.success){
                        document.getElementById(id).remove();
                    }
                }
            })
        }
    }
    function negativeValueRenderer(instance, td, row, col, prop, value, cellProperties) {
        Handsontable.renderers.TextRenderer.apply(this, arguments);
        if (prop == 'mspace') {//修改字体颜色
            if(value!=undefined&&Number(value)>20){
                td.style.color = '#FF0000';
            }
        }
        td.style.textAlign = 'center';
    }
    Handsontable.renderers.registerRenderer('negativeValueRenderer', negativeValueRenderer);
</script>
