<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>产品预提计算</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="beforeSubmitFn" action="ttProductPolicyAccruedController.do?doTtProductAccrued" callback="callbackFn@Override">
    <div class="form">
        <label class="Validform_label">事业部:</label>
        <select id="orgCode" name="orgCode">
            <c:forEach items="${orgList}" var="i">
                <option value="${i.orgCode}">${i.orgName}</option>
            </c:forEach>
        </select>
        <span style="color: red;">*</span>
    </div>
    <div class="form">
        <label class="Validform_label">年月:</label>
        <label class="Validform_label">预提年月: </label>
        <input name="yearMonth" id="yearMonth" datatype="*" style="width: 150px;"
                <c:if test="${!isProcess}">
                    class="Wdate"  onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})"
                </c:if>
               readonly="readonly"/>
        <span style="color: red;">*</span>
    </div>
</div>
</t:formvalid>
</div>

</body>
</html>

<script type="text/javascript">
    function beforeSubmitFn() {
        window.top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });

        var orgCode = $("select[name='orgCode']").val();
        var yearMonth = $("#yearMonth").val();

        $.ajax({
            type: "POST",
            url: "ttProductPolicyAccruedController.do?checkPreAccrued",
            data:{orgCode:orgCode,yearMonth:yearMonth},
            async: false,
            success: function (data) {
                var d = $.parseJSON(data);
                if(d.success) {
                    $.ajax({
                        url:"ttProductPolicyAccruedController.do?doTtProductAccrued",
                        type:'post',
                        data:{orgCode:orgCode,yearMonth:yearMonth,showPreData:false},
                        cache:false,
                        success:function(data) {
                            window.top.$.messager.progress("close");
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            //删除提示信息
                            createwindowExt("", "", "", "", {
                                id:'',
                                content : "<div style='text-align:center'>"+msg+"</div><style>.ui_content{height:100%;display:block;overflow:auto;}</style>",
                                lock : true,
                                width : 550,
                                height : 400,
                                title : "提示",
                                opacity : 0.3,
                                cache : true,
                                async: false,
                                okVal: '确定',
                                ok : true,
                                cancelVal : '关闭',
                                cancel : true
                            });
                            window.top.$.messager.progress("close");
                            frameElement.api.close();
                        }
                    });
                } else {
                    window.top.$.messager.progress("close");
                    if(d.obj == null) {
                        tip("失败:" + d.msg);
                        return;
                    }

                    getSafeJq().dialog.confirm(d.msg + ",是否重新计算？", function() {
                        window.top.$.messager.progress({
                            text : '操作执行中....',
                            interval : 300
                        });

                        $.ajax({
                            url:"ttProductPolicyAccruedController.do?doTtProductAccrued",
                            type:'post',
                            data:{orgCode:orgCode,yearMonth:yearMonth,showPreData:true},
                            cache:false,
                            success:function(data) {
                                window.top.$.messager.progress("close");
                                var d = $.parseJSON(data);
                                var msg = d.msg;
                                //删除提示信息
                                createwindowExt("", "", "", "", {
                                    id:'',
                                    content : "<div style='text-align:center'>"+msg+"</div><style>.ui_content{height:100%;display:block;overflow:auto;}</style>",
                                    lock : true,
                                    width : 550,
                                    height : 400,
                                    title : "提示",
                                    opacity : 0.3,
                                    cache : true,
                                    async: false,
                                    okVal: '确定',
                                    ok : true,
                                    cancelVal : '关闭',
                                    cancel : true
                                });
                                frameElement.api.close();
                            }
                        });
                    });
                   submitFlag = false;
                }
            },
            error:function () {
                window.top.$.messager.progress("close");
            }
        });
        return false;
    }

    function callbackFn(data) {
        var win = frameElement.api.config.parent;
        if(win == null || win == "" || win=="undefined"){
            win = frameElement.api.opener;
        }else{
            win = frameElement.api.config.parent.content;
        }

        win.reloadTable();
    }
</script>