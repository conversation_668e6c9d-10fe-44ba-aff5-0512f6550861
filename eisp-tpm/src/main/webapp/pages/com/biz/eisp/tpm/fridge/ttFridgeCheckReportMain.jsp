<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">

        <t:datagrid name="ttFridgeList" fitColumns="false" title="冰柜检查报表" actionUrl="ttFridgeMainController.do?findFridgeMainOutList"
                    idField="id" fit="true" queryMode="group" >


            <t:dgToolBar title="导出"   icon="icon-dataOut"  operationCode="export"  url="ttFridgeMainController.do?exportCheckXls"  funname="toExcel"></t:dgToolBar>

            <t:dgCol title="上传人"  field="psName"  hidden="false" queryMode="single"  width="120"></t:dgCol>
            <t:dgCol title="上传时间"  field="psTm" formatter="yyyy-MM-dd" hidden="true" queryMode="group" query="true"  width="120"></t:dgCol>
            <t:dgCol title="上传时间"  field="psTime" formatter="yyyy-MM-dd HH:mm:ss"   width="120"></t:dgCol>

            <t:dgCol title="终端编码"  field="terminalCode"  query="true"  width="120"></t:dgCol>
            <t:dgCol title="终端名称"  field="terminalName"   hidden="false" query="true"   width="120"></t:dgCol>
            <t:dgCol title="主键"  field="id"  hidden="true"   width="120"></t:dgCol>
            <t:dgCol title="冰柜编号"  field="fridgeCode" query="true" hidden="false"  width="120"></t:dgCol>
            <t:dgCol title="冰柜型号"  field="fridgeModel"  width="120"></t:dgCol>
            <t:dgCol title="冰柜规格" field="fridgeStandard"    width="120"></t:dgCol>

            <t:dgCol title="冰柜品牌"  field="fridgeSupplier"  dictionary="fridge_brand" hidden="false"  queryMode="single"  width="120"></t:dgCol>
            <t:dgCol title="业务归属"  field="type"  dictionary="fridge_ascription" query="true" width="120" ></t:dgCol>
            <t:dgCol title="所属客户"  field="customerName"  hidden="false"  queryMode="single" query="true"  width="120"></t:dgCol>



            <t:dgCol title="照片1"  field="imagePath1"   hidden="false"  formatterjs="picture" queryMode="single"  width="120"></t:dgCol>
            <t:dgCol title="照片2"  field="imagePath2"   hidden="false"  formatterjs="picture" queryMode="single"  width="120"></t:dgCol>
            <t:dgCol title="照片3"  field="imagePath3"   hidden="false"  formatterjs="picture" queryMode="single" width="120"></t:dgCol>
            <t:dgCol title="执行地点"  field="executionPlace"   hidden="false"   queryMode="single" width="200"></t:dgCol>

            <t:dgCol title="有无照片"  field="tpCount"   hidden="true" query="${isShow}"   dictionary="picExist" width="200"></t:dgCol>


        </t:datagrid>
    </div>
</div>
<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
<div id="innerdiv" style="position:absolute;">
<img id="bigimg" style="border:5px solid #fff;" src="" />
<script type="text/javascript">
    $(document).ready(function () {
        //日期格式查询条件
        $("input[name='psTime']")
            .attr("class", "Wdate")
            .attr("style", "height:20px;width:90px;")
            .click(function () {
                WdatePicker({dateFmt: 'yyyy-MM-dd'});
            });
    });
    function picture(value) {
        var img = value;
        var str="";
        if(null !=img && "null" != img&&img.indexOf("null")==-1){
            str = "<img style='width: 120px;height:80px;cursor: pointer;' src="+img + "  onclick='picBig(this)'>";
        }
        return str;
    }

    //function picBig(obj){
    //	 var src=obj.src;
    // var url = "WorkPlanInOutWebController.do?goWorkPlanInOutPic&src="+src;
    //	$.dialog({
    //     content : 'url:' + url,
    //     title : "放大图片",
    //     cache : false,
    //     lock : true,
    //     width : 600,
    //     height : 500,
    //      zIndex : 999
    //  });
    //}
    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性

        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }

    //导出
    function toExcel(){
        var flag = null ;
        var title = '是否导出图片?';

        var accessEntry = $("#accessEntry").val();
        $.dialog.confirm(title, function(){
            excelExport("ttFridgeMainController.do?exportCheckXls&flag=1","ttFridgeList");
        }, function(){
            excelExport("ttFridgeMainController.do?exportCheckXls&flag=0","ttFridgeList");
        });
    }

</script>