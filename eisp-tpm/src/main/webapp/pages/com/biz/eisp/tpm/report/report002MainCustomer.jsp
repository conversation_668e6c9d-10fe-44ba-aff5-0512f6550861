<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="ttReportCusList" actionUrl="${CusList}" idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
            <%--客户别--%>
            <t:dgCol title="基本信息"  field=""  colspan="29"></t:dgCol>
            <t:dgCol title="申请信息"  field=""  colspan="4"></t:dgCol>
            <t:dgCol title="结案信息"  field=""   colspan="2"></t:dgCol>
            <t:dgCol title="上账信息"  field=""   colspan="2"></t:dgCol>
            <t:dgCol title="反转信息"  field=""   colspan="2"></t:dgCol>
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="审批状态" field="approvalStatus" sortable="false" replace="审批通过_3,关闭_6" dictionary="bpm_status" query="true"></t:dgCol>
            <t:dgCol title="活动类型" field="actType"  sortable="false" query="true" replace="定额活动_quotaacttype,长期待摊费用_longtermacttype,广告费申请_adacttype"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程提交日期" field="flowDate"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程编号" field="processCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="提交主题" field="submitTheme"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="业务费用归类" field="expenseClassification" sortable="false" dictionary="business_cost_type" query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="费用归属事业部" field="costSyb"  sortable="false"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"  sortable="false" query="true"></t:dgCol>


            <t:dgCol title="活动总单编号" field="billCode" query="true"  sortable="false"></t:dgCol>
            <t:dgCol title="活动总单名称" field="billName"  sortable="false"></t:dgCol>


            <t:dgCol title="活动编号" field="actCode" query="true"  sortable="false"></t:dgCol>
            <t:dgCol title="活动名称" field="actName"  sortable="false"></t:dgCol>


            <t:dgCol title="预算科目编码" field="financialCode"  sortable="false"></t:dgCol>
            <t:dgCol title="预算科目名称" field="financialName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode"  sortable="false"></t:dgCol>
            <t:dgCol title="活动大类名称" field="costTypeName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类编码" field="costAccountCode"  sortable="false"></t:dgCol>
            <t:dgCol title="活动细类名称" field="costAccountName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="开始时间" field="beginTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品层级编码" field="productHierarchyCode"  sortable="false" ></t:dgCol>
            <t:dgCol title="产品层级名称" field="productHierarchyName"  sortable="false" query="true"></t:dgCol>
            <%--申请信息--%>
            <t:dgCol title="数量" field="num"  sortable="false" ></t:dgCol>
            <t:dgCol title="费用申请额" field="costApply"  sortable="false" ></t:dgCol>
            <t:dgCol title="申请支付方式" field="applyPayment"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="申请货补品项" field="applyNewArrival"  sortable="false"></t:dgCol>
            <%--结案信息--%>
            <t:dgCol title="结案数量" field="actualSalesNum"  sortable="false"></t:dgCol>
            <t:dgCol title="结案费用额" field="actualSalesCost"  sortable="false"></t:dgCol>


            <%--<t:dgCol title="客户实际销售额（含税）" field="cusEffectiveSaleY"  sortable="false"></t:dgCol>--%>
            <%--<t:dgCol title="费率（含税）" field="rateYStr"  sortable="false"></t:dgCol>--%>
            <%--<t:dgCol title="客户实际销售额（不含税）" field="cusEffectiveSaleN"  sortable="false"></t:dgCol>--%>
            <%--<t:dgCol title="费率（不含税）" field="rateNStr"  sortable="false"></t:dgCol>--%>

            <%--上账信息--%>
            <t:dgCol title="上账金额" field="yesSzAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="未上账金额" field="noSzAmount"  sortable="false"></t:dgCol>
            <%--反转信息--%>
            <t:dgCol title="反转年月" field="reversingTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="反转额" field="reversingAmount"  sortable="false"></t:dgCol>

            <t:dgToolBar title="导出" icon="icon-dataOut" url="report002Controller.do?exportXlsCus"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="查看销售额(含/不含)+费率(含/不含)" icon="icon-look" url="report002Controller.do?goCustSale"  funname="viewData"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        $("#ttReportCusListtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("#ttReportCusListtb_r").find("input[name='flowDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#ttReportCusListtb_r").find("input[name='beginTime']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#ttReportCusListtb_r").find("input[name='endTime']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#ttReportCusListtb_r").find("input[name='reversingTime']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
    })
    function viewData(){
        var st =  $("#ttReportCusList").datagrid("getSelections");
        var title = "";
        if(st==null || st==""){
            tip("必须选择一条数据");
            return;
        }
        var actualSalesCost = st[0].actualSalesCost;
        if(st[0].actualSalesCost == null){
            actualSalesCost = 0;
        }
        var url = "report002Controller.do?goCustSale&customerCode="+st[0].customerCode+
                  "&yearMonth="+st[0].yearMonth+"&actualSalesCost="+actualSalesCost+
                  "&costAccountCode="+st[0].costAccountCode;
        createwindow('查看',url,400,200);
    }
</script>
