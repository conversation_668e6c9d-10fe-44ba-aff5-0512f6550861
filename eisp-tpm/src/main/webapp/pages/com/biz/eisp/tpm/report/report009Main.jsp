<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report009List" fitColumns="false" title="同比环比"
                    pagination="false" autoLoadData="false" actionUrl="report009Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" ></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" width="400"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName" width="150" sortable="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="客户" field="customerName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="年" field="year" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="月" field="month" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="品项" field="prdItems" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="销量" field="salesVolume" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="赠品量" field="complimentaryNum" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="销售额" field="sale" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="含赠吨位（净重）" field="tonnage" width="150"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report009Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report009Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report009Listsearch() {
        var orgCode = $("#report009Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report009Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report009List").datagrid('options').queryParams;
        $("#report009Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report009List").datagrid({url:'report009Controller.do?findReportList'});
    }

</script>
