<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tdOrgTemplatePartList" fitColumns="false" title="模块组织维护"
                    actionUrl="tdOrgTemplatePartController.do?findTdOrgTemplatePartList" idField="id" fit="true"
                    queryMode="group" pageSize="20">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName" query="true" width="150"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="模块名称" field="tplPartName" query="true" width="120"></t:dgCol>
            <t:dgCol title="模板标签" field="tplPartTag" query="true" width="60"></t:dgCol>
            <t:dgCol title="创建人" field="createName"  width="150"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"  width="150"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss" width="150"></t:dgCol>

            <t:dgToolBar  title="新增" height="500" width="800" icon="icon-add" url="tdOrgTemplatePartController.do?goTdOrgTemplatePartForm" funname="add"></t:dgToolBar>
            <t:dgToolBar  title="编辑" height="500" width="800" icon="icon-edit" url="tdOrgTemplatePartController.do?goTdOrgTemplatePartForm" funname="update"></t:dgToolBar>
            <t:dgToolBar  title="刪除"  height="500" width="800" icon="icon-remove" onclick="deleteALLSelect()"></t:dgToolBar>
            <t:dgToolBar  title="导出" icon="icon-dataOut" url="tdOrgTemplatePartController.do?exportXls"  funname="excelExport"></t:dgToolBar>

        </t:datagrid>

    </div>
</div>
<script type="text/javascript">
    //批量删除
    function deleteALLSelect(){
        var rowsData = $('#tdOrgTemplatePartList').datagrid('getSelections');

        if( rowsData.length > 0){
            var ids = "";
            for(var i = 0;i<rowsData.length;i++){

                if(i==0){
                    ids = rowsData[i].id;
                }else{
                    ids +=","+rowsData[i].id;
                }
            }
            getSafeJq().dialog.confirm("确定要删除吗?", function(r) {
                $.ajax({
                    url : 'tdOrgTemplatePartController.do?deleteAllList',
                    type : 'post',
                    dataType:"json",
                    async: false,
                    data : {
                        "ids" :ids
                    },
                    cache : false,
                    success : function(data) {
                        tip(data.msg);
                        tdOrgTemplatePartListsearch();
                    },
                    error : function(){
                        tip('服务器繁忙，请稍后再试');
                    }
                });
            });
        }else{
            $.messager.alert('提示','至少选择一条');
            return;
        }
    }

</script>