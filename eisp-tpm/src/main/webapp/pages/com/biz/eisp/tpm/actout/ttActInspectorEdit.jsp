<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>稽查任务指派</title>
    <t:base type="jquery,easyui,tools"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" dialog="true" layout="div" action="ttAdvOutDoorReportController.do?saveAssign">
    <input id="taskId" name="taskId" type="hidden" value="" />
    <div class="form">
        <label class="Validform_label" name="actCode">活动编号: </label>
        <input id="actCode" name="actCode"  readonly="readonly" class="inputxt"  value="${actCode}" />
    </div>

    <div class="form">
        <label class="Validform_label" name="adsCode">门头广告编号: </label>
        <input id="adCode" name="adsCode"  readonly="readonly" class="inputxt"  value="${adsCode}" />
    </div>
    <div class="form">
        <td align="right"><label class="Validform_label"> 指派给: </label></td>
        <input name="userName" type="hidden" value="" id="userName">
        <input name="fullName" class="inputxt" value="" id="fullName" readonly="readonly" datatype="*" />
        <t:choose hiddenName="userName" hiddenid="userName" url="ttAdvOutDoorReportController.do?goInspectorList" name="inspectorList"
                  icon="icon-search" title="稽查人员列表" textname="fullName" isclear="true"></t:choose>
    </div>
    <div class="form">
        <td align="right"><label class="Validform_label"> 指派说明: </label></td>
        <textarea id="assignComment" rows="4" cols="22" name="assignComment" class="dtci_show_advice" datatype="/^[0-9a-zA-Z_\x21-\x7e\u4e00-\u9fa5\s*]{0,128}$/" errormsg="只能填写长度为2~128，字符类型（英文、数字、中文、标点符号、特殊字符）"></textarea>
    </div>
</t:formvalid>
</body>