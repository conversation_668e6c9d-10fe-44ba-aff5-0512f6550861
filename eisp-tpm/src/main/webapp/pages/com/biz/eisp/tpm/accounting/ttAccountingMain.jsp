<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttAuditedDataList" checkbox="false" fitColumns="true"
                    singleSelect="true" actionUrl="ttAccountingController.do?findTtAuditedDataList" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="结案申请单号" field="billCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案单名称" field="billName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案明细编号" field="auditCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动名称" field="actName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="upupOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="upOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" sortable="false" query="false" hidden="true"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品名称" field="productName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="归属事业部" field="businessUnitName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="活动预算科目" field="financialAccountName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动开始时间" field="beginDate" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="结案日期" field="auditedDate" sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="支付方式" field="paymentCode" sortable="false" dictionary="accounting_pay_type" query="true"></t:dgCol>
            <t:dgCol title="货补产品编码" field="premiumProductCode" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="货补产品" field="premiumProductName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="结案金额（元）" field="auditAmount" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="已上账金额（元）" field="accountingAmount" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="可上账金额（元）" field="unAccountingAmount" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="上账状态" field="accountingStatus" sortable="false" query="true" dictionary="account_status"></t:dgCol>
            <t:dgCol title="最新上账人" field="accountingName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="最新上账时间" field="accountDate" sortable="false" hidden="true" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="最新上账时间" field="accountingDate" sortable="false" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
            <t:dgCol title="组织" field="orgNames"  query="true" hidden="true"></t:dgCol>
            <t:dgToolBar title="上账" operationCode="add" icon="icon-add" url="" funname="addAccounting" ></t:dgToolBar>
            <t:dgToolBar title="查看财务凭证" operationCode="check" icon="icon-look" url="" funname="accountingInfo" ></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="exout" icon="icon-dataOut" url="ttAccounting2Controller.do?exportXls" funname="excelExport" ></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='beginDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 结束日期
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 上账时间开始
        $("input[name='accountDate_begin']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 上账时间开始
        $("input[name='accountDate_end']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        $("input[name='orgNames']").attr("readonly",true).attr("id","orgNames").attr("style","width:180px").click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='orgCodes' id='orgCodes'/>");
    });
    function openOrgSelect(){
        var orgCode ='';
        var currentOrgCode = '';
        var paraArr = {
            textName: 'orgCode,orgName',
            inputTextName: 'orgCodes,orgNames',
            searchType: '1',//查询类型？？
            encapsulationType: 'input',//封装类型--不传或默认
            isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            currentOrgCode:currentOrgCode,
            couldNull : true,
            pageData: {
                orgCode: orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    //上账
    function addAccounting(){
        var url = "ttAccounting2Controller.do?goAddAccount";
        createwindowExt("上账",url,1200,800, {
            button:[
                {
                    name : "保存",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var result = iframe.save();
                        return false;
                    }
                },
                {
                    name : "上账",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var result = iframe.saveAccounting();
                        return false;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }
    //刷新
    function reload(){
        $("#ttAuditedDataList").datagrid("reload");
    }

    //查看财务凭证
    function accountingInfo(){
        var rows = $("#ttAuditedDataList").datagrid("getSelected");
        if(rows == null || rows.length == 0){
            tip("请选择一条数据","error");
            return false;
        }
        if(rows.accountingStatus == 0){
            tip("该数据尚未上账","error");
            return false;
        }
        var auditCode = rows.auditCode;
        safeShowDialog({
            content : "url:ttAccounting2Controller.do?goAccountingInfoPage&auditCode="+auditCode,
            lock : true,
            title : "凭证详情",
            width : 1000,
            height : 800,
            cache : false,
            cancelVal : '关闭',
            cancel : true
        });
    }
    //上账日志
    function logInfo(){
    }
</script>