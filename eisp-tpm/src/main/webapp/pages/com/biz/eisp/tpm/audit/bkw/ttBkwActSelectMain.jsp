<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttActBkwMainSelectList" fitColumns="true" title="宝库旺申请" queryMode = "group" idField="id" pagination="true"
                    autoLoadData="true" actionUrl="ttBkwAuditController.do?findTtActBkwMainList&billMainId=${billMainId}" singleSelect="false">
            <t:dgCol title="主键" hidden="true" field="id"  ></t:dgCol>

            <t:dgCol title="活动编码" field="actCode"  query="true"   ></t:dgCol>
            <t:dgCol title="活动名称" field="actName" query="true"   ></t:dgCol>
            <t:dgCol title="流程类型" hidden="true" field="actTypeCode" dictionary="bkw_act_type" query="true"  ></t:dgCol>
            <t:dgCol title="流程类型" field="actTypeName"    ></t:dgCol>

            <t:dgCol title="客户编码" field="customerCode" hidden="true" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true"  ></t:dgCol>
            <t:dgCol title="活动大类编码" hidden="true" field="costTypeCode"  ></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeName" query="true"   ></t:dgCol>

            <t:dgCol title="开始时间" field="beginDate" formatter="yyyy-MM-dd"  ></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd" ></t:dgCol>


            <t:dgCol title="费用金额" field="amount"    ></t:dgCol>

            <t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" query="true" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus"  dictionary="bpm_status" ></t:dgCol>
            <t:dgCol title="创建人" field="createName" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss"  ></t:dgCol>
            <t:dgToolBar title="添加" icon="icon-add"  onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add"  onclick="addAllItem()"></t:dgToolBar>

        </t:datagrid>
    </div>
    <div region="east" style="width:550px;padding:1px;">
        <t:datagrid name="ttActBkwMainSelectedList" fitColumns="false"
                    title="已经选择宝库旺申请" queryMode = "group" idField="id" pagination="false"
                    autoLoadData="false"  actionUrl="" singleSelect="false" >
            <t:dgCol title="主键" hidden="true" field="id"  ></t:dgCol>

            <t:dgCol title="活动编码" field="actCode" width = "100" ></t:dgCol>
            <t:dgCol title="活动名称" field="actName" width = "150" ></t:dgCol>
            <t:dgCol title="流程类型" hidden="true" field="actTypeCode" dictionary="bkw_act_type" ></t:dgCol>
            <t:dgCol title="流程类型" field="actTypeName"  width = "190"  ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" hidden="true"  ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" width = "150"   ></t:dgCol>
            <t:dgCol title="活动大类编码" hidden="true" field="costTypeCode" width = "150"  ></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeName" width = "150" ></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate" formatter="yyyy-MM-dd" width = "150" ></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd" width = "150" ></t:dgCol>
            <t:dgCol title="费用金额" field="amount"  width = "150"  ></t:dgCol>
            <t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" width = "150" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus"      dictionary="bpm_status" width = "150" ></t:dgCol>
            <t:dgCol title="创建人" field="createName"  width = "200" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" width = "150" ></t:dgCol>
            <t:dgToolBar title="移除" icon="icon-remove"  onclick="removeItemBkw()"></t:dgToolBar>
            <t:dgToolBar title="全部移除" icon="icon-remove"  onclick="removeAllItemBkw()"></t:dgToolBar>

        </t:datagrid>

    <div id="btn_sub" onclick="submitForm()"></div>
</div>
</div>
<script type="text/javascript">
    $(function () {
        var str='<label title="申请金额汇总" style="margin-left: 239px">申请金额汇总:</label> <span style="color: red" id="allApplyAmount">0</span>';

        $("#ttActBkwMainSelectedList_toolbar_div").parent().append(str);
        $("#ttActBkwMainSelectedList_toolbar_div").remove();

        //双击添加
        $('#ttActBkwMainSelectList').datagrid({
            //双击事件
            onDblClickRow :function(rowIndex,rowData){
                $("#ttActBkwMainSelectedList").datagrid("insertRow",{row:rowData});
                loadElecteGrid();
                setAllApplyAmount();
            }
        });
        //双击移除
        $('#ttActBkwMainSelectedList').datagrid({
            //双击事件
            onDblClickRow :function(rowIndex,rowData){
                $('#ttActBkwMainSelectedList').datagrid('deleteRow',rowIndex);
                loadElecteGrid();
                setAllApplyAmount();
            }
        });
    })
    //添加
    function addItem(){
        var seletctTarget =  $("#ttActBkwMainSelectList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttActBkwMainSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
        setAllApplyAmount();
    }

    //添加全部
    function addAllItem(){
        var name = "ttActBkwMainSelectList";
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.ajax({
            url:"ttBkwAuditController.do?findTtActBkwMainList&billMainId=${billMainId}",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttActBkwMainSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
        setAllApplyAmount();
    }

    function removeItemBkw() {
        var checkListTarget =  $("#ttActBkwMainSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        var copyRows = [];
        for ( var j= 0; j < checkListTarget.length; j++) {
            copyRows.push(checkListTarget[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttActBkwMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttActBkwMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
        setAllApplyAmount();
    }

    function removeAllItemBkw() {
        var rowsData = $("#ttActBkwMainSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttActBkwMainSelectedList").datagrid("getRows");

        var copyRows = [];
        for ( var j= 0; j < rows.length; j++) {
            copyRows.push(rows[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttActBkwMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttActBkwMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
        $("#allApplyAmount").html("0");
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttActBkwMainSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].id+"'";
            }
        }
        var actType=$("select[name='actType']").val();
        $('#ttActBkwMainSelectList').datagrid("reload", {"exclusiveCodes":exclusiveCodes,"actType":actType});
    }

    function submitForm() {
        var rows = $("#ttActBkwMainSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }
        var codes = [];
        var actCodes = [];
        for ( var i = 0; i < rows.length; i++) {
            codes.push(rows[i].id);
            actCodes.push(rows[i].actCode);
        }

        var result=false;
        $.ajax({
            async: false,
            url: "ttBkwAuditController.do?saveTtBkwAudit",
            data: {
                ids:codes.join(","),
                actCodes:actCodes.join(","),
                billMainId : '${billMainId}'
            },
            type: "post",
            dataType : "json",
            success : function(data) {
                if(data.success){
                    result=data.success;
                } else {
                    tip(data.msg);
                }
            },
            error:function(){
                tip("服务器异常，请稍后再试");
            }
        });

        return result;
    }
    function setAllApplyAmount() {
        var rows = $("#ttActBkwMainSelectedList").datagrid("getRows");
        var allApplyAmount = 0;
        for ( var i = 0; i < rows.length; i++) {
            allApplyAmount =allApplyAmount+parseInt(rows[i].amount);
        }
        $("#allApplyAmount").html(allApplyAmount);
    }
</script>

