<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>户外广告基础信息维护</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="tsActBaseController.do?saveTsActBase" refresh="true">
    <input type="hidden" id="id" name="id" value="${vo.id}"/>

    <div class="form">
        <label class="Validform_label">合同编码：</label>
        <input name="contractCode" id="contractCode"
               datatype="*" class="inputxt" value="${vo.contractCode}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">合同名称：</label>
        <input name="contractName" id="contractName"
               datatype="*" class="inputxt" value="${vo.contractName}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">合作公司：</label>
        <input name="partnerCompany" id="partnerCompany"
               datatype="*" readonly="readonly" class="inputxt" value="${vo.partnerCompany}"/>
        <span style="color: red;">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search"
           onclick="addPartnerCompany()"></a>
    </div>

    <div class="form" hidden="hidden">
        <label class="Validform_label">活动类型：</label>
            <%--<input  name="advType" id="advType"--%>
            <%--datatype="*"  class="inputxt"  value="${vo.advType}" />--%>
        <input name="advType" id="advType"
               datatype="*" class="inputxt" readonly="readonly" value="户外广告"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">活动细类：</label>
        <input name="account" id="account"
               class="inputxt"  />
        <input name="accountCode" id="accountCode" hidden="hidden"
               datatype="*" class="inputxt" readonly="readonly"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form" hidden="hidden">
        <label class="Validform_label">活动细类名称：</label>
        <input name="accountName" id="accountName"
               datatype="*" class="inputxt" readonly="readonly"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">合同开始日期：</label>
        <input name="startDate" id="startDate" readonly="readonly"
               datatype="*" class="Wdate" value="${vo.startDate}"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: red;">*</span>
    </div>



    <div class="form">
        <label class="Validform_label">广告有效月数: </label>
        <input name="advEffectPeriod" id="advEffectPeriod"
               datatype="/^[0-9]*[1-9][0-9]*$/" class="inputxt" value="${vo.advEffectPeriod}" errormsg="只能输入大于等于0的数字"/>
        <span style="color: red;">*</span>
    </div>
    <div class="form">
        <label class="Validform_label">地址：</label>
        <input name="address" id="address" datatype="*" class="inputxt" value="${vo.address}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">备注：</label>
        <textarea name="remark" id="remark" cols="15" rows="5" class="inputxt" >${vo.remark} </textarea>
    </div>

</t:formvalid>
</body>
</html>
<script type="text/javascript">
    $("#account").combobox({
        url: 'tsActBaseController.do?findComboBoxList&id=${vo.id}',
        valueField: 'id',
        textField: 'text',
        multiple: true,
        editable:false,
        panelHeight: 'auto',
        required:true,
        editable: false,
        onChange: function () {
            $("#accountCode").val($('#account').combobox('getValues'));
             $("#accountName").val($('#account').combobox('getText'));
            setTimeout("overChangeAccountCode()", 100);
        }
    })

    function overChangeAccountCode() {
        $("#accountName").val($('#account').combobox('getText'));
    }

    function addPartnerCompany() {
        var targetUrl = 'tsActBaseController.do?goTsActBaseCompanyForm';
        //弹出窗口
        $.dialog({
            content: 'url:' + targetUrl,
            zIndex: 500000,
            title: '合作公司列表',
            lock: true,
            parent: windowapi,
            width: 600,
            height: 600,
            left: '65%',
            top: '65%',
            opacity: 0.4,
            button: [{
                name: '确定',
                callback: function () {
                    //回调事件
                    editDateRowStatus = true;
                    iframe = this.iframe.contentWindow;
                    var rowsData = iframe.$('#ttActBaseCompanyList').datagrid('getSelections');
                    if (rowsData.length == 0) {
                        return;
                    }
                    $("#partnerCompany").val(rowsData[0].fullName);
                },
                focus: true
            }, {
                name: '取消',
                callback: function () {
                }
            }]
        });
    }

</script>