<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tsActApplyVoList"
                    fitColumns="true" checkbox="true"
                    title="" actionUrl="ttActOutUploadController.do?findAdvertisementJXS"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="true" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="重复制作" field="isRepeat" replace="是_1,-_0" hidden="false" query="true" sortable="false" align="center"></t:dgCol>

            <t:dgCol title="门头广告编号" field="dadcodes"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="操作" field="auditButton" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="广告公司手机" field="advCode" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司" field="advName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="活动类型" field="actType" hidden="true" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="advType" hidden="false" dictionary="act_type" query="false" sortable="false" align="center"></t:dgCol>


            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternatives" hidden="false"  sortable="false" ></t:dgCol>

            <t:dgCol title="经销商" field="createName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>

            <t:dgToolBar title="查看材料尺寸" icon="icon-log" onclick="findDetailLayout()"></t:dgToolBar>
            <t:dgToolBar title="查看驳回原因" icon="icon-log" onclick="findRejectReason()"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    function jxsAudit(a,b,c,d) {
        var url ="tnTaTaskController.do?goInstanceHandleTabForm&isReadFlag=false&isView=false&processInstanceId="+b+"&taskId="+a+"&isCommunicate=N&id="+a;
        safeShowDialog({
            title: "3-经销商选址和价格审批",
            content: "url:"+url,
            lock: true,
            width: "1300",
            height: "1300",
            zIndex: 10000,
            parent: windowapi,
            close: function(event, ui) {
                //window.location.reload(); //整个页面刷新
                $("#tsActApplyVoList").datagrid("reload");//单个列表刷新
            }
        });

    }

    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 930, 500);
    }


    function findDetailLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function findRejectReason() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goRejectReason&id="+row[0].id+"&actCode="+row[0].actCode;
        $.dialog({
            title: "驳回原因(历史记录)",
            content: "url:"+url,
            lock: true,
            width: "700",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
</script>
