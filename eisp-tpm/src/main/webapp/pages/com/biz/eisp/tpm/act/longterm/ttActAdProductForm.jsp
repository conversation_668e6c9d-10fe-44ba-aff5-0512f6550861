<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<style type="text/css">
	.actTablePro{
		border-collapse:collapse;
		width:100%;
	}
	.actTablePro thead tr td:nth-child(1){
		width:25px;
	}
	.actTablePro td{
		height: 25px;
		border: 1px solid #ccc;
		text-align: left;
		white-space: nowrap;
		padding: 0 3px;
	}
</style>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<div class="easyui-layout" fit="true">
	<div region="west" style="width:380px;">
	<t:formvalid formid="formobj" layout="div" dialog="true" action="ttActAdController.do?saveTtActAd" refresh="true"
		beforeSubmit="handlerSubmit">
		<div>
			<!-- id -->
			<input name="id" type="hidden" value="${vo.id}">
			<!-- 头表code -->
			<input name="billCode" id="billCode" type="hidden" value="${vo.billCode}">
			<!-- 流程状态 -->
			<input name="bpmStatus" type="hidden" value="1">
			<!-- 拆分明细 -->
			<input name="splitCostJson" id="splitCostJson" type="hidden" value='${vo.splitCostJson}' />
			<!-- 拆分到产品明细 -->
			<input name="splitCostToTermJson" id="splitCostToTermJson" type="hidden" value='${vo.splitCostToTermJson}' />
			<!-- 是否改变了值 -->
			<input id="isViewCostTermForJson" type="hidden" value='true'/>

			<!--  金额口否为负数-->
			<input id="isNagative" type="hidden" value='${isNagative}'/>
			
			<input id="monthData" type="hidden">
			
			<div class="form">
				<label class="Validform_label">活动名称: </label>
				<input name="billName" id="billName" datatype="/^[0-9a-zA-Z\u4e00-\u9fa5,`~·~\<\>,，。？：；《》【】‘’!！\-（）“”—……、\?\|\{\}\[\]\.\(\)]{1,30}$/" 
				       errormsg="只能填写汉字，数字，大小写英文，标点符号" class="inputxt" value="${vo.billName}"
					   ajaxUrl="ttActAdController.do?validateActAdNameOnly&id=${vo.id}"/>
				<span style="color: red;">*</span>
			</div>
			
			<div class="form">
				<label class="Validform_label">活动开始时间: </label>
				<input name="beginDate" id="beginDate" datatype="*" class="Wdate" style="width: 150px;" onchange="initDate()"
				   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\')}',onpicked:function(){$('.Wdate').blur();}})"
				   readonly="readonly"  value="${vo.beginDate}" />
				<span style="color: red;">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">活动结束时间: </label>
				<input name="endDate" id="endDate" datatype="*" class="Wdate" style="width: 150px;" onchange="initDate()"
				   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
				   readonly="readonly"  value="${vo.endDate}" />
				<span style="color: red;">*</span>
			</div>
			
			<div class="form">
				<label class="Validform_label">流程类型: </label>
				<select id="actTypeCode" name="actTypeCode" datatype="*">
					<option value=''>--请选择--</option>
					<c:forEach items="${actTypeList}" var="pay">
						<option value='${pay.value }' <c:if test="${pay.value eq vo.actTypeCode}">selected</c:if> > ${pay.text}</option>
					</c:forEach>
				</select>
				<span style="color: red;">*</span>
			</div>
			
			<div class="form">
				<label class="Validform_label">活动细类: </label>
				<input name="costAccountName" id="costAccountName" class="inputxt"  datatype="*" readonly="readonly"  readonly="readonly" value="${vo.costAccountName}" />
				<input type="hidden" id="costAccountCode" datatype="*" name="costAccountCode" value='${vo.costAccountCode}' >
				<input type="hidden" id="costTypeCode" name="costTypeCode" value='${vo.costTypeCode}' >
				<input type="hidden" id="costTypeName" name="costTypeName" value='${vo.costTypeName}' >
				<span style="color: red">*</span>
				<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openCostAccountSelect();"></a>
			</div>
			
			
			<div class="form">
				<label class="Validform_label">活动总金额: </label>
				<input name="amount" id="amount" datatype="*" class="inputxt" value="${vo.amount}" onchange="writeAmount()"/>
				<span style="color: red;">*</span>
			</div>
			
			<div class="form">
				<label class="Validform_label">支付方式: </label>
				<t:comboBox name="paymentCode" id="paymentCode"  url="ttCostAccountController.do?findPaymentComboxByAccountCode&costAccountCode=${vo.costAccountCode}"
					defaultVal="${vo.paymentCode}" required="true"></t:comboBox>
				<span style="color: red;">*</span>
			</div>
			
			<div class="form">
				<label class="Validform_label">文本描述: </label>
				<textarea id="remark" name="remark" style="resize:none;width:150px;height:100px;">${vo.remark }</textarea>
			</div>
		</div>
	</t:formvalid>
	</div>
	<div region="center">

		<div region="center" style="height:70%;">
			<div class="panel datagrid">
				<div class="datagrid-wrap panel-body">
					<div class="datagrid-toolbar">
						<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-add" onclick="addOrg();">添加组织</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="removeOrg();">移除组织</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-add" onclick="actTableAdd();">添加产品</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="clearProduct();">清空产品</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost" onclick="costShare();">费用均摊</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-reset" onclick="resetCost();">重置</a>
						</span>
							<span style="float:right;margin-right:30px;">
							待分摊金额:<span id="waiteAmount"></span>
						</span>
							<div style="clear:both;float: none;height:0;display: block;"></div>
						</div>
						<div class="datagrid-toolbar-search">
						</div>
					</div>
					<div class="datagrid-view">
						<table class="actTable" id="avgTable">
							<thead>
							<tr>
								<th><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></th>
								<th>序号</th>
								<th>年月</th>
								<th>组织</th>
								<th>产品</th>
								<th>费用金额(元)</th>
							</tr>
							</thead>
							<tbody id="costContent">
								<c:forEach items="${vo.actVos}" var="ac" varStatus="idxStatus">
									<tr name="contents">
										<td>
											<input type="checkbox" name="actTableId"/>
										</td>

										<td name="num">${idxStatus.index+1}</td>

										<td name="yearMonth">${ac.yearMonth}</td>

										<td>${ac.orgName}<input type="hidden" value="${ac.orgCode}" name="orgCode" /></td>

										<td name="proName">
											${ac.productName}
											<input type="hidden" value="${ac.productCode}" name="productCode" />
										</td>

										<td>
											<input type="text" name="avgAmount" id="${ac.yearMonth}"  onkeydown="keyCode(event)"
												   onkeyup="verifyInput(this)" onblur="deductionAmount(this)" value="${ac.amount}"/>
										</td>
									</tr>
								</c:forEach>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
//选择流程类型，加载活动细类
$(function(){
	//总金额
	$('#amount').numberbox({
	    min:0,
	    precision:2
	});
});
</script>
<script type="text/javascript">
    //写金额
    function writeAmount(){
        var amount = $("#amount").val();
        $("#waiteAmount").text("");
        $("#waiteAmount").text(amount);
    }
	//当选择日期的时候，加载日期
	function initDate(){
		var beginDate = $("#beginDate").val();
	    var endDate = $("#endDate").val();
		if(beginDate == null || beginDate == '' || endDate == null || endDate == '' ){
			 return;
		}
	    $(".actTable tbody").html("");
	    var yearMonths=getMonthBetween(beginDate,endDate);
	    $("#monthData").val(yearMonths.join(','));
		changeViewCostTermForJson();
	}
	//是否显示产品分摊信息
	function changeViewCostTermForJson(){
		$("#isViewCostTermForJson").val("false");
		$("#splitCostToTermJson").val("");
        $(".actTablePro tbody").text("");
	}

	//弹出选择费用科目
	function openCostAccountSelect(){
		safeShowDialog({
			content : "url:ttCostAccountController.do?goSelectTtCostAccount&actType=ad_act_type",
			lock : true,
			title : "选择活动细类",
			width : 500,
			height : 450,
			left :'85%',
			cache : false,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rowsData = iframe.$('#tmCostAccountList').datagrid('getSelected');
				if (rowsData == '' || rowsData == null) {
					iframe.tip("请选择一条数据");
					return false;
				} else {
					$("#costAccountCode" ).val(rowsData.accountCode);
					$("#costAccountName").val(rowsData.accountName);
					$("#costTypeCode" ).val(rowsData.costTypeCode);
					$("#costTypeName").val(rowsData.costTypeName);
					initPaymentComboBox(rowsData.accountCode);//加载支付方式

					//根据选择的活动细类控制金额是否可以为负数
					validateAmountIsNagative(rowsData.accountCode);

					return true;
				}
			},
			cancelVal : '关闭',
			cancel : true
		});
	}

	//根据活动细类加载金额可否为负
	function validateAmountIsNagative(costAccountCode){
		var url = "ttBudgetApiController.do?validateAmountIsNagative&costAccountCode="+costAccountCode;
		$.ajax({
			url:url,
			type:"post",
			success:function(data){
				//1是,0否
				var d = $.parseJSON(data);
				if(d.success){
					var nagative = d.obj;
					if(nagative == 0){
						//总金额
						$('#amount').numberbox({
						    min:0,
						    precision:2
						});
					}else{
						//总金额
						$('#amount').numberbox({
						    precision:2
						});
					}
				}else{
					tip(d.msg,"error")
				}
			}
		});
	}
	//根据所选科目查询支付方式
	function initPaymentComboBox(accountCode){
        $("#paymentCode").val("");
        $("#cbpaymentCode").combobox('clear');
		$("#cbpaymentCode").combobox("reload","ttCostAccountController.do?findPaymentComboxByAccountCode&costAccountCode="+accountCode)
	}
	//重置
	function resetCost(){
        var rows = $(".actTable tbody tr");
        $.each(rows,function(i,row){
            $(this).find("input[name='avgAmount']").val("");
        });
	}
	//删除选中
	function removeOrg(){
		var trTarget = $("#costContent tr");
		if(trTarget.length == 0){
			tip("请至少选中一条数据");
			return false;
		}
		var orgCode = [];
		$(trTarget).each(function(){
			if ($(this).find("[name='actTableId']")[0].checked){
                $(this).remove();
                var oc = $(this).find("input[name='orgCode']").val();
				if($.inArray(oc,orgCode) == -1){
					orgCode.push(oc);
				}
			}
		});
        $(trTarget).each(function(){
			var oc = $(this).find("input[name='orgCode']").val();
			if($.inArray(oc,orgCode) != -1){
                $(this).remove();
			}
        });

		refreshAmountDisplay();
	}
    function deductionAmount(obj){
        var sumAmount = $("#amount").val();//填写总金额
        var writeAmount = $(obj).val();//每行填写的金额
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            $(obj).val("0");
            return;
        }
        if(Number(writeAmount) > Number(sumAmount)){
            tip("输入的金额不能大于总分配金额","error");
            $(obj).val("0");
            return;
        }
        var rows = $("#avgTable tbody tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {return;}
        var waitingAssgin = sumAmount;
        var totalAmount=0;
        $.each(rows, function(i, row) {
            var avgAmount = $(row).find("input[name='avgAmount']").val();
            totalAmount=addNum(totalAmount,Number(avgAmount));
        });
        if(Number(totalAmount) > Number(sumAmount)){tip("所有列的总金额不能大于待分配金额","error");$(obj).val("0");return;}
        $("#waiteAmount").text("0");
        $("#waiteAmount").text(subtract(sumAmount,totalAmount));
    }
	function addOrg(){
		var amount = $("#amount").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		if(amount == null || amount == '' ||beginDate == null || beginDate == '' || endDate == null || endDate == ''){
			tip("金额，开始日期,结束日期不能为空");
			return false;
		}
		var currOrgCode = '01';
        var paraArr = {
            searchType:'1',//查询类型
            encapsulationType:'returnHasIframe',//封装类型--不传或默认
            currentOrgCode : currOrgCode,
            isCouldRemove:true,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            callBackFun : addOrgChooseOrgBackFun,
        }
        openChooseOrgSelect(paraArr);
	}
	function addOrgChooseOrgBackFun(iframe,datas){
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        var yearMonths = getMonthBetween(beginDate,endDate);
        var flag = true;
        if(yearMonths!=null&&yearMonths.length>0){
            //选中的值
            var sels = datas;
            var index = 0;
            var orgArray = new Array();
            $.each(yearMonths,function(k,v){
                $.each(sels,function(i,row){
                    var extChar1 = row.extChar5;
                    if(extChar1 == "" || extChar1 == null){
                        flag = false;
					}
                    var yearMonthOrgCode = v+row.orgCode;
                    orgArray[index] = yearMonthOrgCode;
                    index++;
                });
            });
            if(!flag){
                iframe.tip("产品成本中心不能为空,请重选","error");
                return false;
			}
            var tr= $("#costContent tr");
            tr.each(function(tri,trc){
                var orgCode = $(trc).find("input[name='orgCode']").val();
                var yearMonth = $(trc).find("td[name='yearMonth']").html();
                var yearMonthOrgCode = yearMonth+orgCode;
                var index = $.inArray(yearMonthOrgCode,orgArray);
                if(index >= 0){
                    $(this).remove();
                }
            });
            index = 0;
            $.each(yearMonths,function(k,v){//年月循环
                $.each(sels,function(a,b){
                    var i=Number(index)+1;
                    var str='<tr name="contents">'
                        +'<td><input type="checkbox" name="actTableId" /></td>'
                        +'<td name="num">'+ i +'</td>'
                        +'<td name="yearMonth">'+v+'</td>'
                        +'<td>'+b.orgName+'<input type="hidden" value="'+b.orgCode+'" name="orgCode"/></td>'
						+'<td name="proName"><input type="hidden" name="productCode" readonly="readonly"></td>'
                        +'<td><input type="text" name="avgAmount"  id="'+v+'"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0" onblur="deductionAmount(this)" placeholder="请填写金额"/></td>'
                        +'</tr>';
                    $(".actTable tbody").append(str);
                    index++;
                });
            });
            changeViewCostTermForJson();
            refreshAmountDisplay();
        }
	}
    //添加产品
    function actTableAdd(){
        popOptClickNow("tmCommonMdmController.do?goTmProductTreeMainNoJur",addContent ,800, 500,"选择产品");
    }
    //添加选择的产品
    function addContent(rowData){
        var content = '';
        if(rowData != null){
            var rows = $("#avgTable tbody tr");
            $.each(rows,function(i,k){
                var text = rowData.productName+'<input type="hidden" name="productCode" value="'+rowData.productCode+'">';
				$(this).find("td[name='proName']").html("");
                $(this).find("td[name='proName']").html(text);
			});
        }
        return true;
    }
    //清除产品选项
    function clearProduct(){
		var rows = $("#avgTable tbody tr");
		$.each(rows,function(i,k){
		    $(this).find("td[name='proName']").html("");
		});
	}

	/**
	 * 刷新
	 */
	function refreshAmountDisplay() {
		var sumAmountAll = 0;
		$("table input[name='avgAmount']").each(function(i, o) {
			var num = Number($(o).val());
			sumAmountAll = addNum(sumAmountAll,  num);
		});
		var allAmount = Number($("#amount").val());
		if (sumAmountAll > allAmount) {
			$("table input[name='avgAmount']").each(function(i, o) {
				$(o).val("");
			});
			refreshAmountDisplay();
			return;
		}
		// 排序
		$("#costContent tr").each(function(i, o) {
			$(o).find("td[name='num']").html(i+1);
		});
		$("#waiteAmount").text(subtract(allAmount,sumAmountAll));
	}

	//获取两月份之间的所有年月
    function getMonthBetween(start,end){
        var result = [];
        var url = "ttActLongTermController.do?getBetweenYearMonth&minDate="+start+"&maxDate="+end;
        $.ajax({url:url,type:"post",async:false,success:function(data){
            var d = $.parseJSON(data);
            result = d;
        }});
        return result;
    }

	//费用分摊
	function costShare(){
        $("#waiteAmount").text("0");
        var sumAmount = $("#amount").val();
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            return;
        }
        var rows = $("#avgTable tbody tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {
            return;
        }
        var waitingAssgin = sumAmount;
        var avgAmount = (waitingAssgin / amountSize).toFixed(2);
        var totalAmount=0;
        $.each(rows, function(i, row) {
            totalAmount=addNum(totalAmount,avgAmount);
            $(this).find("input[name='avgAmount']").val(avgAmount);
        });
        if(Number(totalAmount)!=Number(sumAmount)){
            var num=subtract(sumAmount,totalAmount);
            $(rows[rows.length -1]).find("input[name='avgAmount']").val(addNum(avgAmount,num));
        }
	}


  	//统一选择支付方式为货补时 弹出选择货补产品
    function openSelectProduct(){
    	var payment=$("#paymentCode").val();
    	if(70!=Number(payment)){
    		tip("请选择支付方式为货补");
    		return;
    	}
    	safeShowDialog({
    		content : "url:tsapMaterialController.do?goMaterialMain",
    		lock : true,
    		title : "选择货补产品",
    		width : 500,
    		height : 450,
    		left :'85%',
    		cache : false,
    		ok : function() {
            	iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tsapMaterialList').datagrid('getSelections');
                if (!rowsData || rowsData.length == 0) {
                    newTip('请选择货补产品!');
    		        return false;
    		    }
    			if (rowsData.length > 1) {
                    newTip('只能选择一条数据');
    		        return false;
    		    }
    			$("#premiumProductName").val(rowsData[0].maktx);
    			$("#premiumProductCode").val(rowsData[0].matnr);

    			return true;
    		},
    		cancelVal : '关闭',
    		cancel : true
    	});
    }

    function p(s){
        return s < 10 ? '0' + s : s;
	}
	function getNowDate(){
        var myDate = new Date();
        var fullYear = myDate.getFullYear();
        var month = myDate.getMonth() + 1;
        var date = myDate.getDate();
        return fullYear + '-' + p(month) + "-" + p(date);
	}
	//提交
	function handlerSubmit(){
		//验证
		var bd = $("#beginDate").val();
		var ed = $("#endDate").val();
		if(bd > ed){
			tip("活动开始时间大于活动结束时间");
			return false;
		}
        //提交前校验如果选择的活动细类是xx，那么时间可以随便选择，不用校验，否则校验开始时间不能小于今天
		var costAccountCode = $("#costAccountCode").val();
		if(costAccountCode != "XL400089"){
		    var substring = (getNowDate()).substring('0','7');
			if(bd < (substring+'-01')){
			    tip("非活动细类:奶粉预提差异,活动开始时间必须从本月月初开始,请重新选择!");
				return false;
			}
		}

		//组装月份分摊json
		var splitJson = [];
		var splitProJson = [];
		$(".actTable").find("tr[name='contents']").each(function(){
			var splitData = {};
			var splitPro = {};

			var orgCode = $(this).find("input[name='orgCode']").val();
			var amount = $(this).find("input[name='avgAmount']").val();
			var yearMonth = $(this).find("input[name='avgAmount']").attr("id");
			var proCode = $(this).find("input[name='productCode']").val();


			splitData.amount = amount;
			splitData.yearMonth = yearMonth;
			splitData.orgCode = orgCode;

			if(proCode != null){
                splitPro.amount = amount;
                splitPro.yearMonth = yearMonth;
                splitPro.productCode = proCode;
                splitPro.orgCode = orgCode;
                splitProJson.push(splitPro)
            }

            splitJson.push(splitData);
		});
		if(splitJson.length == 0){
			tip("未分摊金额到月份");
			return false;
		}
		$("#splitCostJson").val(JSON.stringify(splitJson));
		$("#splitCostToTermJson").val(JSON.stringify(splitProJson));
		//检查分摊金额是否等于总金额
		return isEqualSplitCostAmount();
	}

	//检查总金额是否等于分摊金额总和
	function isEqualSplitCostAmount(){
		var amount = $("#amount").val();
		var splitCostAmount = 0;
		//计算分摊金额总和
		$(".actTable").find("input[type='text']").each(function(){
			var splitCostValue = $(this).val();
			if(splitCostValue != ""){
				splitCostAmount = addNum(splitCostAmount,splitCostValue);
			}
		});
		if(amount != splitCostAmount){
			tip("活动总金额与分摊总金额不相等,请检查");
			return false;
		}

		//校验产品金额是否相等
        splitCostAmount = 0;
		var tJson = $("#splitCostToTermJson").val();
		if(tJson == ""){
		    tJson = '${vo.splitCostToTermJson}';
		}
		if(tJson != "" && tJson != '[]'){
            var parse = JSON.parse(tJson);
            $.each(parse,function(){
                var amount1 = $(this)[0].amount;
                splitCostAmount = addNum(splitCostAmount,Number(amount1));
			});
            if(amount != splitCostAmount){
                tip("活动总金额与分摊到产品总金额不相等,请重新填写产品");
                return false;
            }
		}

		return true;
	}
	/************************************************************************************************************/

	//获取客户
	function popCustomer(){
		popMyClick("customerName,customerCode","customerName,customerCode","tmCommonMdmController.do?goCustomerSearch",400,400);
	}
	/************************************************************************************************************/
	    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }
    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }
	/*
	 * 高精减法函数
	 */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精度加法函数
     */
    function addNum(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();
        try {
            m += s1.split(".")[1].length;
        } catch (e) { }
        try {
            m += s2.split(".")[1].length;
        } catch (e) {}
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch(e) {
            r2 = 0;
        }
        return Math.pow(10, Math.max(r1, r2));
    }

	function popMyClick(obj, name, url, width, height) {
	    var names = name.split(",");
	    var objs = obj.split(",");
	    safeShowDialog({
	            content : "url:" + url,
	            lock : true,
	            title : "选择",
	            width : width==null?700:width,
	            height : height==null?400:height,
	            left :'85%',
	            cache : false,
	            ok : function() {
	                iframe = this.iframe.contentWindow;
	                var selected = iframe.getSelectRows();
	                if (selected == '' || selected == null) {
	                	iframe.$.messager.alert('错误',"请至少选择一条数据","error");
	                    return false;
	                } else {
	                    for ( var i1 = 0; i1 < names.length; i1++) {
	                        var str = "";
	                        $.each(selected, function(i, n) {
	                            if (i == 0){
	                                str += n[names[i1]];
	                            }else {
	                                str += ",";
	                                str += n[names[i1]];
	                            }
	                        });
	                        if ($("#" + objs[i1]).length >= 1) {
	                            $("#" + objs[i1]).val("");
	                            $("#" + objs[i1]).val(str);
	                        } else {
	                            $("input[name='" + objs[i1] + "']").val("");
	                            $("input[name='" + objs[i1] + "']").val(str);
	                        }
	                    }
	                    $("#orgCode").val("");
	                    $("#orgName").val("");
	                    $("#orgCode").val(selected[0].orgCode);
	                    $("#orgName").val(selected[0].orgName);
	                    return true;
	                }
	            },
	            cancelVal : '关闭',
	            cancel : true
	        });
	}
    function popOptClickNow(url,optFunction ,width, height,title) {
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : (title==null || title=="" || title==undefined)?"选择":title,
            width : width==null?700:width,
            height : height==null?400:height,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.$("#tmProductTreeList").datagrid("getSelected");
                if (selected == '' || selected == null) {
                    iframe.$.messager.alert('错误','请至少选择一条数据','error');
                    return false;
                } else {
                    //是否回调方法
                    if(optFunction != null && optFunction != undefined){
                        return optFunction(selected);
                    }
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>