<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttProductActWorkFlowList" checkbox="false" fitColumns="true" title="" onLoadSuccess="loadTotal"
              actionUrl="ttQuotaActWorkFlowController.do?findTtHiActListQ01&phoneSend=1&flagKey=${flagKey}" idField="id" fit="true" queryMode="group" >
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol field="costAccountCode" title="活动细类编码" hidden="true" ></t:dgCol>
            <t:dgCol field="actCode" title="活动编号" query="false" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" sortable="false" query="true"></t:dgCol>

            <t:dgCol field="businessUnitType" title="事业部经销商分类" dictionary="customer_sale"></t:dgCol>
            <t:dgCol field="customerTypeName" title="客户级别"></t:dgCol>

            <t:dgCol field="yearMonth" title="年月" query="true" ></t:dgCol>
            <t:dgCol field="custCount" title="客户商超数量"></t:dgCol>
            <t:dgCol field="costTypeName" title="活动大类"  query="true" ></t:dgCol>
            <t:dgCol field="costAccountName" title="活动细类" query="true" ></t:dgCol>

            <t:dgCol field="paymentCode" title="支付方式" dictionary="payment_type"></t:dgCol>
            <t:dgCol field="premiumProductName" title="货补产品" ></t:dgCol>


            <t:dgCol field="amount" title="费用金额"></t:dgCol>
            <t:dgCol field="quantity" title="数量"></t:dgCol>
            <t:dgCol field="productName" title="产品"></t:dgCol>
            <t:dgCol field="salesVolume" title="门店销售额汇总"></t:dgCol>
            <t:dgCol field="rateStr" title="费率"></t:dgCol>

            <t:dgCol title="操作" field="opt"></t:dgCol>
            <t:dgFunOpt title="门店详情" funname="detailShow"></t:dgFunOpt>
            <t:dgToolBar title="导出门店详情" icon="icon-dataOut" url="ttQuotaActWorkFlowController.do?exportXlsTerminalDetail&phoneSend=1&flagKey=${flagKey}" funname="excelExport"></t:dgToolBar>
            </t:datagrid>
        </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });

        $("input[name='costAccountName']").parent().append("&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
    });

    function loadTotal(){
        var queryParams = $('#ttProductActWorkFlowList').datagrid('options').queryParams;
        $('#ttProductActWorkFlowListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttQuotaActWorkFlowController.do?findTtHiActTotalAmount&phoneSend=1&flagKey=${flagKey}";
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("费用总金额："+d.obj);
            }
        }
        });
    }

    function detailShow(index) {
        $("#ttProductActWorkFlowList").datagrid("unselectAll");
        $("#ttProductActWorkFlowList").datagrid("selectRow",index);
        var select= $("#ttProductActWorkFlowList").datagrid("getSelected");
        var id=select.id;
        var costAccountCode=select.costAccountCode;
        createwindowExt(
            '门店详情',
            'ttQuotaActWorkFlowController.do?goTtActWorkFlowTerminalMain&phoneSend=1&flagKey=${flagKey}&id='+id+'&costAccountCode='+costAccountCode,
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex:12000,
                width : 900,
                height : 400,
                button : [ {
                    name : '取消',
                    callback : function() {
                    }
                } ]
            });
    }
</script>

