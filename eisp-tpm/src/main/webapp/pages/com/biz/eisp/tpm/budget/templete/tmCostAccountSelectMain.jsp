<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div id="tmCostAccountMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
	    <t:datagrid name="tmCostAccountList" fitColumns="true" title="未关联科目" queryMode = "group" singleSelect="false"
	     idField="id" actionUrl="${datagridUrl}">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="费用类型名称" field="costTypeName" query="true" width="120"></t:dgCol>
	        <t:dgCol title="费用类型编码" field="costTypeCode" hidden="true"></t:dgCol>
	        <t:dgCol title="费用科目名称" field="accountName" query="true" width="120"></t:dgCol>
	        <t:dgCol title="费用科目编码" field="accountCode" hidden="true"></t:dgCol>
	    	<t:dgToolBar title="添加" icon="icon-add" onclick="addCostAccount()"></t:dgToolBar>
	    </t:datagrid>
	</div>
	<div data-options="region:'east',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
		style="width: 500px; overflow: hidden;">
		<div class="easyui-panel" style="padding: 1px;" fit="true" border="false" id="checkedGrid"></div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		$("#tmCostAccountMain").layout("expand","east");
		checkedGrid();
	});
	var excludeId="";
	function checkedGrid(){
		$("#checkedGrid").panel("refresh","${selectedMainUrl}");
	}
	//静态添加
	function addCostAccount(){
		var seletctTarget =  $("#tmCostAccountList").datagrid("getSelections");
		if(seletctTarget==null || seletctTarget==""){
			tip("请至少选择一条数据");
			return false;
		}
		//添加
		for (var i = 0; i < seletctTarget.length; i++) {
			var rows = seletctTarget[i];
			$("#ttCostAccountSelectedList").datagrid("insertRow",{row:rows});
		}
		loadElecteGrid();
	}
</script> 