<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report021List" fitColumns="true" title="产品费用台账明细"
                    pagination="true" autoLoadData="true" actionUrl="report021Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="基本信息"  field=""  colspan="31"></t:dgCol>
            <t:dgCol title="申请信息"  field=""  colspan="16"></t:dgCol>
            <t:dgCol title="结案信息" field=""   colspan="7"></t:dgCol>
            <t:dgCol title="上帐信息" field=""   colspan="4"></t:dgCol>
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth"  sortable="false" query="true" formatter="yyyy-MM"></t:dgCol>
            <t:dgCol title="活动是否开启" field="isOpen"  sortable="false" query="true" dictionary="market_yn"></t:dgCol>
            <t:dgCol title="是否计算后返" field="dmsRebateFlag" replace="是_Y,否_N"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="申请日期" field="applyDate"  sortable="false" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="批复日期" field="expenseClassification"  sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"   sortable="false" ></t:dgCol>
            <t:dgCol title="流程编码" field="processCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="提交主题" field="submitTheme"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程名称" field="processName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动编号" field="actCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策编码" field="policyCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策名称" field="policyName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="随车政策内容" field="carPolicyContent"   sortable="false"></t:dgCol>
            <t:dgCol title="后返政策内容" field="returnPolicyContent"   sortable="false"></t:dgCol>
            <t:dgCol title="随车预算科目编码" field="carFinancialCode"   sortable="false"></t:dgCol>
            <t:dgCol title="随车预算科目" field="carFinancialName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返预算科目编码" field="returnFinancialCode"   sortable="false"></t:dgCol>
            <t:dgCol title="后返预算科目" field="returnFinancialName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动大类编码" field="returnCostTypeCode"   sortable="false"></t:dgCol>
            <t:dgCol title="后返活动大类名称" field="returnCostTypeName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动细类编码" field="returnCostAccountCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动细类名称" field="returnCostAccountName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="开始时间" field="beginTime"   sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"   sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户级别" field="customerlevel"   sortable="false" dictionary="cust_type"></t:dgCol>
            <!-- 申请信息 -->
            <t:dgCol title="活动品项编码" field="actConditionCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动品项" field="actConditionName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="原供价(元)" field="originalPrice"   sortable="false"></t:dgCol>
            <t:dgCol title="目标销量1(EA)" field="targetSales1"   sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额1(元)" field="targetSalesAmount1"   sortable="false"></t:dgCol>
            <t:dgCol title="目标销量2(EA)" field="targetSales2"   sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额2(元)" field="targetSalesAmount2"   sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品" field="associatedProduct"   sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品销量（EA）" field="associatedNum"   sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品销售额（元）" field="associatedAmount"   sortable="false"></t:dgCol>
            <t:dgCol title="当期费用(元)" field="periodCharges1"   sortable="false"></t:dgCol>
            <t:dgCol title="当期费率" field="currentRate1Str"   sortable="false"></t:dgCol>
            <t:dgCol title="后返费用(元)" field="afterReturnCharges1"   sortable="false"></t:dgCol>
            <t:dgCol title="后返费率" field="afterReturnRate1Str"   sortable="false"></t:dgCol>
            <t:dgCol title="货补产品" field="supplementProducts1"   sortable="false"></t:dgCol>
            <t:dgCol title="备注" field="remark"   sortable="false"></t:dgCol>
            <%--结案信息--%>
            <t:dgCol title="结案时间" field="closingTime"  sortable="false"></t:dgCol>
            <t:dgCol title="结案流程号" field="closingProcessCode"  sortable="false"></t:dgCol>
            <t:dgCol title="结案总单号" field="closingTotalNum"  sortable="false"></t:dgCol>
            <t:dgCol title="结案子单号" field="closingSonNumber"  sortable="false"></t:dgCol>
            <t:dgCol title="结案金额" field="closingAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="结案状态" field="closingStatus"  sortable="false" dictionary="audit_bkw_status"></t:dgCol>
            <t:dgCol title="支付方式" field="paymentMode"  sortable="false" dictionary="payment_type"></t:dgCol>
            <%--上账信息--%>
            <t:dgCol title="上账月份" field="upAccountMonth"  sortable="false"></t:dgCol>
            <t:dgCol title="上账编码" field="upAccountCode"  sortable="false"></t:dgCol>
            <t:dgCol title="上账金额" field="upAccount"  sortable="false"></t:dgCol>
            <t:dgCol title="上账凭证号" field="upAccountVoucher"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report021Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report021Listtb_r").find("input[name='billingDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
        $("#report021Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report021Listsearch() {
        var orgCode = $("#report021Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report021Listtb_r").find("input[name='yearMonth']").val();



        var queryParams = $("#report021List").datagrid('options').queryParams;
        $("#report021Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report021List").datagrid({url:'report021Controller.do?findReportList'});
    }

</script>
