<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>SAP预算科目管理</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttManagementCostAccountController.do?saveManagementCostAccount"
             refresh="true">
    <!-- id -->
    <input name="id" type="hidden" value="${accountVo.id}"/>
    <input name="enableStatus" type="hidden" value="${accountVo.enableStatus}"/>

    <div class="form">
        <label class="Validform_label">SAP预算科目编码:</label>
        <input class="inputxt" name="accountCode" id="accountCode" datatype="*" value="${accountVo.accountCode}"
               <%--ajaxUrl="ttManagementCostAccountController.do?validateAccountCode&id=${accountVo.id}"--%>
        >
        <span style="color: red">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">SAP预算科目名称:</label>
        <input class="inputxt" name="accountName" id="accountName" datatype="*" value="${accountVo.accountName}">
        <span style="color: red">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">费用科目ERP编码:</label>
        <input class="inputxt" name="financialCode" id="financialCode" datatype="*" value="${accountVo.financialCode}"
               <%--ajaxUrl="ttManagementCostAccountController.do?validateRepeat&id=${accountVo.id}"--%>
        >
        <span style="color: red">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">SAP预算科目类型:</label>
        <select name="accountType" dataType="*" value="${accountVo.accountType}">
            <option value="">--请选择--</option>
            <c:forEach items="${cost_type}" var="c">
                <option value="${c.dictCode}"
                        <c:if test="${accountVo.accountType == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">备注:</label>
        <textarea rows="3" name="remark" id="remark" style="width:150px;resize: none;">${accountVo.remark}</textarea>
    </div>

</t:formvalid>
</body>
</html>


<script type="text/javascript">

    //只能输入数字，或者保留两位小数
    function clearNoNum(obj){
        obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }

    $(document).ready(function () {
        $("#cbproductCode").combobox({
            onChange:function(newValue, oldValue){
                load();
            }
        });
    });
</script>