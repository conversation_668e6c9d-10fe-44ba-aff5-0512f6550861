<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>照片删除</title>
    <link rel="stylesheet" href="resources/photo/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="resources/photo/dist/viewer.css">
    <link rel="stylesheet" href="resources/photo/assets/css/main.css">

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <style type="text/css">

    </style>
    <![endif]-->
</head>
<body>

<!-- Content -->
<div class="container">
    <input type="hidden" id="type" value="${type}" name="type">
    <div class="row">
        <div class="col-sm-8 col-md-6">
            <h3 class="page-header"></h3>
            <div class="docs-galley">
                <c:forEach items="${timeList}" var="time">
                    <span>${time.time}</span>
                    <c:forEach items="${photoList}" var="photo">
                        <c:if test="${photo.note eq time.time}">
                            <c:if test="${firstShow==true}">
                                <span>${photo.photoType}</span>
                            </c:if>
                            <c:if test="${secondShow==true}">
                                <span>${photo.content}</span>
                            </c:if>
                        </c:if>
                    </c:forEach>
                    <ul class="docs-pictures clearfix">
                        <c:forEach items="${photoList}" var="photo">
                            <c:if test="${photo.note eq time.time}">
                                <li>
                                    <img data-original="${photo.realPath}" src="${photo.realPath}">
                                    <div style="z-index:100" ><input name="photo" type="checkbox" value="${photo.id}" /></div>
                                </li>
                            </c:if>
                        </c:forEach>

                    </ul>
                </c:forEach>
                <ul class="docs-pictures clearfix">
                <li>
                    <img data-original="resources/photo/assets/img/tibet-1.jpg" src="resources/photo/assets/img/tibet-1.jpg">
                    <input name="photo" type="checkbox" value="" />
                </li>
                    <li>
                        <img data-original="resources/photo/assets/img/tibet-2.jpg" src="resources/photo/assets/img/tibet-2.jpg">
                        <input name="photo" type="checkbox" value="" />
                    </li>
                    <li>
                        <img data-original="resources/photo/assets/img/tibet-3.jpg" src="resources/photo/assets/img/tibet-3.jpg">
                        <input name="photo" type="checkbox" value="" />
                    </li>
                    <li>
                        <img data-original="resources/photo/assets/img/tibet-4.jpg" src="resources/photo/assets/img/tibet-4.jpg">
                        <input name="photo" type="checkbox" value="" />
                    </li>
                    <li>
                        <img data-original="resources/photo/assets/img/tibet-5.jpg" src="resources/photo/assets/img/tibet-5.jpg">
                        <input name="photo" type="checkbox" value="" />
                    </li>
                    <li>
                        <img data-original="resources/photo/assets/img/tibet-6.jpg" src="resources/photo/assets/img/tibet-6.jpg">
                        <input name="photo" type="checkbox" value="" />
                    </li>
                    <li>
                        <img data-original="resources/photo/assets/img/tibet-7.jpg" src="resources/photo/assets/img/tibet-7.jpg">
                        <input name="photo" type="checkbox" value="" />
                    </li>


                </ul>
                <%--<c:if test="${empty timeList}">
                    <span>暂无照片信息</span>
                </c:if>--%>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="resources/photo/assets/js/jquery.min.js"></script>
<script src="resources/photo/assets/js/bootstrap.min.js"></script>
<script src="resources/photo/dist/viewer.js"></script>
<script src="resources/photo/assets/js/main.js"></script>
<script type="text/javascript">
    //
    function submit(){
        var pIds = "";
        $("input:checkbox[name='photo']:checked").each(function(i){
            if(0==i){
                pIds = $(this).val();
            }else{
                pIds += (","+$(this).val());
            }
        });
        var flag=false;
        if(pIds!=""){
            $.ajax({
                async : false,
                cache : false,
                data:{ids:pIds,type:$("#type").val()},
                type : 'POST',
                url : 'photoWallController.do?updatePhotoStatus',// 请求的action路径
                success : function(data) {
                    var d = $.parseJSON(data);
                    if (d.success) {
                        flag=true;
                    }else{
                        tip(d.msg);
                    }
                }
            });
        }
        return flag;
    }
</script>
</body>
</html>
