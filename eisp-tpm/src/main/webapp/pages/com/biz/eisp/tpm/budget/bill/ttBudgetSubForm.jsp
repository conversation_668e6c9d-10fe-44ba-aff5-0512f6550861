<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>预算削减</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<input type="hidden" value="${budgetVo.id}" id="id">
<t:formvalid formid="budgetIncrease" layout="div" dialog="true" action="ttBudgetBillSubController.do?saveBudgetSubAmount" refresh="true" beforeSubmit="checkAmount()">
		<input type="hidden" value="${billVo.adjustFlag}" id="addOrSub" name="adjustFlag">
		<div class="form">
			<label class="Validform_label">年度:</label>
			<input name="year" class="inputxt" style="width: 150px" value="${billVo.year}" readonly="readonly">
		</div>
		<div class="form">
			<label class="Validform_label">月:</label>
			<input name="month" class="inputxt" style="width: 150px" value="${billVo.month}" readonly="readonly">
		</div>		
		<div class="form">
			<label class="Validform_label" name="custName">部门: </label> 
			<input class="inputxt" value="${billVo.orgName}" name="orgName" readonly="readonly"/>
			<input type="hidden" name="orgCode" value="${billVo.orgCode}">
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">预算科目: </label> 
			<input class="inputxt" value="${billVo.accountName}" name="accountName" readonly="readonly"/>
			<input type="hidden" name="accountCode" value="${billVo.accountCode}">
		</div>

		<div class="form">
			<label class="Validform_label" name="areaName">期初金额: </label> 
			<input name="periodAmount" class="inputxt" value="${billVo.periodAmount}" readonly="readonly"/>
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">可用余额: </label> 
			<input id="accountName" class="inputxt" value="${billVo.beforeAdjustBalance}" readonly="readonly"/>
		</div>
		
		<div class="form">
			<label class="Validform_label">削减金额</label> 
			<input type="text" id="adjustAmount" name="adjustAmount" class="inputxt" onblur="checkAmount()" 
				   datatype="/^([1-9][\d]{0,11}|0)(\.[\d]{1,2})?$/" 
				   errormsg="只能是带两位小数或者整数或者位数不能超过12位"/>
			<span style="color: red">*</span>
			<span class="Validform_checktip" id="error">
				可削减最大金额:${subAmount},最小金额:0
			</span>
			<input type="hidden" id="usableNum" value="${subAmount}">
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">备注: </label> 
			<textarea style="width:150px;height:100px;resize:none;" name="remark"></textarea>
		</div>		
		
</t:formvalid>
</body>
</html>
<script type="text/javascript">
function checkAmount(){
	var usableNum = $("#usableNum").val();
	var subNum = $("#adjustAmount").val();
	if(parseFloat(subNum) > parseFloat(usableNum)){
		$("#error").addClass("Validform_wrong");
		$("#error").attr("title","削减金额不能大于可用金额");
		tip("削减金额不能大于可用金额");
		return false;
	}
}
</script>