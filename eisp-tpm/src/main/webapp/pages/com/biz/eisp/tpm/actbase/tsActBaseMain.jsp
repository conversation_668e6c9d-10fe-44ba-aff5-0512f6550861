<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="tsActBaseList" title="户外广告基础信息"  actionUrl="tsActBaseController.do?findtsActBaseList"
	  		 checkbox="true" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="合作公司" field="partnerCompany" query="true" width="200" ></t:dgCol>
			<t:dgCol title="活动类型" field="advType" dictionary="act_type" query="true"  width="200"></t:dgCol>
			<t:dgCol title="合同广告数量" field="contractNum"   width="200"></t:dgCol>
			<t:dgCol title="合同金额(元)" field="contractAmount"   width="200" ></t:dgCol>
			<t:dgCol title="开始日期" field="startDate" query="true" queryMode="group"  formatter="yyyy-MM-dd" ></t:dgCol>
			<t:dgCol title="结束日期"  field="endDate" width="200" ></t:dgCol>
			<t:dgCol title="状态" field="enableStatus" dictionary="train_base_status" width="200"></t:dgCol>

			<t:dgToolBar title="创建" icon="icon-add" operationCode="add" url="tsActBaseController.do?tsActBaseForm&optype=0" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit"   url=""  onclick="updateInfo"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteTrainData"></t:dgToolBar>
			<t:dgToolBar title="导入"  icon="icon-dataIn" onclick="importDataByXml({impName:'tsActBase', gridName:'tsActBaseList'})"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

	function updateInfo() {
        var rows = $("#tsActBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请选择一条数据");
            return false;
        }

        if(rows.length>1){
            tip("只能选择一条数据");
            return false;
		}
		var row=rows[0];
        var url="";
        var id=row.id;
            $.dialog({
                title: "编辑",
                content: "url:tsActBaseController.do?tsActBaseForm&id="+id,
                lock: true,
                width: "500",
                height: "400",
                zIndex: 10000,
                parent: windowapi,
                ok: function () {
                    iframe = this.iframe.contentWindow;
                    var updateform=iframe.$("#formobj");
                    //updateform.submit();

                    $('#updateform', iframe.document).form('submit', {
                        onSubmit : function() {
                        },
                        success : function(r) {
                            var data =$.parseJSON(r);
                            // alert(data);
                            tip(data.msg);
                            //reloadTable();

                        }
                    });
                   // window.location.reload();
                },
                cancelVal: '关闭',
                cancel: true
            });

    }

    function deleteTrainData() {
        var rows = $("#tsActBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请至少选择一条数据")
		}else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="tsActBaseController.do?deleteTsActBase";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
		}

    }
    //启用停用
    function startOrStop(flag){
        var rows = $("#ttAccruedFormulaList").datagrid('getSelections');
        var title="启用";
        if(flag!=0){
            title="停用";
		}
		var url="ttAccruedFormulaController.do?updateTtAccruedFormula";
        var ids=[];
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定"+title+"所选数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(','),
							flag:flag
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                $("#ttAccruedFormulaList").datagrid('reload');
                                $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要"+title+"的数据");
        }
    }
    //导出
    function toExcel(){
        excelExport("tsActBaseController.do?exportXls","tsActBaseList");
    }
</script>
