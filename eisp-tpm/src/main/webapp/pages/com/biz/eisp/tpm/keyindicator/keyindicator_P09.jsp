<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div region="north"  style="padding: 1px;height: 60px;">
    <fieldset style="height: 60px;padding: 10px;border: 2px solid #b9d5ee;" >
        <legend style="color: red">核销信息</legend>

        <t:formvalid formid="submitForm" layout="" dialog=""  action="ttTrainAdBaseAuditController.do?saveTrainAdBaseAudit" beforeSubmit="getData" >
            <input  name="checkIds" id="checkIds"datatype="*"   hidden="true" class="inputxt"  />
            <input  name="unitName" id="unitName"datatype="*"   hidden="true" class="inputxt"  />
            <input  name="id" id="id"datatype="*"  value="${vo.id}"  hidden="true" class="inputxt"  />
            标题：<input  name="title" id="title"datatype="*"  value="${vo.title}" class="inputxt"  style="margin-right: 2%;" />
            瑕疵率：<input  name="flawChance" id="flawChance"datatype="*" value="${vo.flawChance}"  class="inputxt" readonly="readonly" style="margin-right: 2%;" />
            核销申请金额：<input  name="auditAmount" id="auditAmount"datatype="*" value="${vo.auditAmount}"  class="inputxt"  />
        </t:formvalid>



    </fieldset>
</div>

<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="tainAdCheckList" title="高铁普列活动检查信息"  actionUrl="ttTrainAdBaseAuditController.do?findcheckInfoByAuditId&id=${vo.id}"
                    checkbox="true" idField="id" fit="true"  fitColumns="false" pagination="false" queryMode="group" singleSelect="false">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="bussinessId" field="bussinessId" hidden="true"></t:dgCol>
            <t:dgCol title="检查日期" field="createDate" query="true" queryMode="group" formatter="yyyy-MM-dd"  ></t:dgCol>
            <t:dgCol title="所属公司" field="partnerCompany" query="true" ></t:dgCol>

            <t:dgCol title="车次号" field="trainNumber"  ></t:dgCol>
            <t:dgCol title="车底号" field="trainBtNumber"   ></t:dgCol>
            <t:dgCol title="GPS信息" field="gpsAddress"   ></t:dgCol>
            <t:dgCol title="检查人"  field="createName"  ></t:dgCol>
            <t:dgCol title="是否有瑕疵" field="isOutadvFlaw"  formatterjs="xiaci"  ></t:dgCol>
            <t:dgCol title="瑕疵率" field="xcl"  hidden="true" ></t:dgCol>

            <t:dgToolBar title="图片视频" icon="icon-search"   url=""  funname="showPic"></t:dgToolBar>


        </t:datagrid>
    </div>
    <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
        <div id="innerdiv" style="position:absolute;">
            <img id="bigimg" style="border:5px solid #fff;" src="" />
        </div>
    </div>

</div>

<script type="text/javascript">

    function xiaci(value,row) {
        //$("#flawChance").val(row.xcl);
        if(value==0){
            return '无';
        }else {
            return '有';
        }


    }
    function showPic() {
        var rows= $("#tainAdCheckList").datagrid('getSelections');
        var bussinessId=rows[0].bussinessId;
        //alert(bussinessId);

        var url="tsPictureController.do?goTrainPictureMain&bussinessId="+bussinessId;
        createwindowExt("创建",url,800,800, {
            button:[
                {

                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });

    }

    function picture(value) {
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='picBig(this)'>";
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性

        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }


    function getData() {
        debugger;
        var ids="";
        var rows = $("#tainAdCheckList").datagrid('getRows');
        if(rows!=null&&rows.length!=0){
            for(var i=0;i<rows.length;i++){
                ids+=rows[i].id+',';
            }
            $("#checkIds").val(ids);
        }else{
            ;
        }

    }


    function updateInfo() {
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请选择一条数据");
            return;
        }

        if(rows.length>1){
            tip("只能选择一条数据");
            return;
        }
        var row=rows[0];
        var url="";
        var id=row.id;
        $.dialog({
            title: "编辑",
            content: "url:tsActBaseController.do?tsActBaseForm&id="+id,
            lock: true,
            width: "500",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var updateform=iframe.$("#formobj");
                updateform.submit();
                window.location.reload();
            },
            cancelVal: '关闭',
            cancel: true
        });

    }

    function deleteTrainData() {
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请至少选择一条数据")
        }else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="tsActBaseController.do?deleteTsActBase";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        }

    }
    //启用停用
    function startOrStop(flag){
        var rows = $("#ttAccruedFormulaList").datagrid('getSelections');
        var title="启用";
        if(flag!=0){
            title="停用";
        }
        var url="ttAccruedFormulaController.do?updateTtAccruedFormula";
        var ids=[];
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定"+title+"所选数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(','),
                            flag:flag
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                $("#ttAccruedFormulaList").datagrid('reload');
                                $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要"+title+"的数据");
        }
    }
    //导出
    function toExcel(){
        excelExport("trainAdBaseAuditController.do?exportXls","trainADBaseList");
    }


    //上账
    function createAudit(){
        var url = "trainADBaseController.do?goTrainActCheck";
        createwindowExt("创建",url,1200,800, {
            button:[
                {
                    name : "保存",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var result = iframe.save();
                        return false;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }
</script>
