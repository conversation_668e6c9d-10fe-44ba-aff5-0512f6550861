<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<style>
    .actTable tr td:nth-child(1){display:none;}
    .g-img-popup{
        width:300px;
        height:450px;
        padding: 10px;
        border-radius: 5px;
        background-color: #ccc;
        position: fixed;
        top:10px;
        display: none;
        transition: all 1s;
    }
    ul#ulTemp{
        padding: 20px;
        width:420px;
    }
    ul#ulTemp li{
        width: 100px;
        height: 100px;
        float: left;
        margin: 1px;
    }
</style>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="tmCostAccountMain" class="easyui-layout" fit="true">
    <div region="center" style="width:500px;">
        <input name="auditId" id="auditId" value="${auditId}" type="hidden">
        <t:datagrid name="ttAuditQuotaMainList" fit="true" fitColumns="true" singleSelect="false"
                    title="门店"
                    queryMode="group"
                    actionUrl="ttAuditTerminalController.do?findAuditTerminalList&auditId=${auditId}"
                    idField="id"
                    autoLoadData="true">

            <t:dgCol field="id" title="主键" hidden="true"></t:dgCol>

            <t:dgCol field="bpmStatus" title="审批状态" hidden="true"></t:dgCol>

            <t:dgCol field="terminalName" title="门店名称" query="true" frozenColumn = "true"></t:dgCol>
            <t:dgCol field="terminalCode" title="门店编码"></t:dgCol>
            <t:dgCol title="活动金额" field="amount" width="100"></t:dgCol>
            <t:dgCol field="planQuantity" title="门店申请数量" width="120"></t:dgCol>
            <t:dgCol field="companyAmount" title="门店申请金额" width="120"></t:dgCol>
            <t:dgCol field="applyAuditQuantity" title="门店申请结案数量"  width="150"></t:dgCol>
            <t:dgCol field="applyAuditAmount" title="门店申请结案金额"  width="150"></t:dgCol>

            <t:dgCol field="auditQuantity" title="门店审核结案数量" editor="{type:'numberbox',options:{precision:2,min:0}}" width="150"></t:dgCol>
            <t:dgCol field="auditAmount" title="门店审核结案金额" editor="{type:'numberbox',options:{precision:2}}" width="150"></t:dgCol>

            <c:if test="${ft == '1'}">
                <t:dgCol field="displayTypeName" title="陈列类型"></t:dgCol>
                <t:dgCol field="standard" title="标准"></t:dgCol>
            </c:if>
            <t:dgCol field="applyRemark" title="申请备注"></t:dgCol>
            <t:dgCol field="auditRemark" title="结案备注"  editor="{type:'text'}"></t:dgCol>
            <t:dgToolBar title="保存" icon="icon-ok" onclick="saveData()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div data-options="region:'east',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
         style="width: 500px; overflow: hidden;">
        <div class="easyui-panel" style="padding: 1px;" fit="true" border="false" id="showPictrue"></div>
    </div>

</div>
<script type="text/javascript">

    var li_east = 0;
    $(function(){
        $("#tmCostAccountMain").layout("expand","east");
        li_east = 1;
        $("#showPictrue").panel("refresh","ttAuditQuotaPictureController.do?goTerminalPhotos&auditId=${auditId}&ft=${ft}");

        //绑定当行点击事件
        $('#ttAuditQuotaMainList').datagrid({
            onClickRow: function(index,row){
                editRow(index,row);
            },onEndEdit:function(){
               // alert(1);
            }
        });
    });

    //保存
    function saveData(){
        var rows=$("#ttAuditQuotaMainList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditQuotaMainList").datagrid("getRowIndex",row);
            $("#ttAuditQuotaMainList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttAuditQuotaMainList").datagrid("getChanges","updated");
        for(var i = 0;i<updated.length;i++){
            var cc = $("#ttAuditQuotaMainList").datagrid("getRowIndex",updated[i].id);
            updated[i].index = cc;
        }
        $.ajax({
            url : "ttAuditTerminalController.do?saveAuditWorkFlowTerminal",
            type : 'post',
            data : {terminalJson : JSON.stringify(updated),auditId:'${auditId}'},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"info");
                    $("#ttAuditQuotaMainList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditQuotaMainList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditQuotaMainList").datagrid('getColumnFields',true).concat($("#ttAuditQuotaMainList").datagrid('getColumnFields'));
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaMainList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var planAmount = row.planAmount;
            if ((fields[i] == "applyAuditAmount"||fields[i] == "applyAuditQuantity"||fields[i] == "auditRemark")){
                //暂时没有确认那些不启动
            }
        }
        $("#ttAuditQuotaMainList").datagrid('beginEdit',index);
        var editors=$("#ttAuditQuotaMainList").datagrid('getEditors',index);
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaMainList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }
</script>