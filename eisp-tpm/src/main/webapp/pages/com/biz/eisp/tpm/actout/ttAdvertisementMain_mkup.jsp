<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tsActApplyVoList"
                    fitColumns="true" checkbox="false"
                    title="" actionUrl="ttActOutUploadController.do?findAdvLastAuditMkup&auxType=Bike"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="flagKey" field="flagKey" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessKey" field="businessKey" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="susId" field="susId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="false" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="驳回过?" field="reject" hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="调过尺寸?" field="isModify" hidden="false" sortable="false" align="center"></t:dgCol>

            <%--t:dgCol title="照片" field="isUpldMark" hidden="false" sortable="false" align="center"></t:dgCol--%>
            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="重复制作" field="isRepeat" replace="是_1,-_0" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="dadcodes"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <%--t:dgCol title="操作" field="auditButton" hidden="false" sortable="false" align="center"></t:dgCol--%>

            <t:dgCol title="活动类型" field="actType" hidden="true" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="advType" hidden="false" dictionary="act_type" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="(0-疑似造假" field="isSusFraud" dictionary="dict_is_suspicious" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="0-疑似造假原因" field="susFraudReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="0-填写人)" field="innerUser2ed" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="经销商(*)" field="createName" hidden="false" query="true" sortable="true" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="图片上传状态" field="isUploaded" hidden="true" sortable="false" ></t:dgCol>



            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="审批状态" field="bpmStatusStr" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="当前审批人" field="activityName" hidden="true" sortable="false" ></t:dgCol>

            <t:dgCol title="广告公司手机" field="advCode" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司(*)" field="advName" hidden="false" query="true" sortable="true" ></t:dgCol>

            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternatives" hidden="false"  sortable="false" ></t:dgCol>


            <t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="更新日期(*)" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="true" ></t:dgCol>
            <t:dgCol title="删除" field="delMark" hidden="true" query="false" replace="-_0,是_1" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="删除" field="delMarkStr" hidden="true" query="false"  sortable="false" align="center"></t:dgCol>

            <t:dgCol title="当前任务处理人" field="activityUser" hidden="true" sortable="false" ></t:dgCol>
            <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut" url="ttActOutUploadController.do?exportExcelLast"  funname="excelExport"></t:dgToolBar>


            <t:dgToolBar title="查看材料尺寸" icon="icon-log" onclick="findDetailLayout()"></t:dgToolBar>
            <t:dgToolBar title="查看驳回原因" icon="icon-log" onclick="findRejectReason()"></t:dgToolBar>

            <t:dgToolBar title="修改店铺名称" icon="icon-log" url="ttActOutUploadController.do?goTmTerminal" funname="goTmTerminal"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
            <t:dgToolBar title="填写可疑意见" icon="icon-edit" url="ttActApplyExcuteWorkFlowController.do?saveOrUpdateTheSuspOpin" funname="editTheSuspOpin" ></t:dgToolBar>
            <t:dgToolBar title="补充见证性材料" icon="icon-log" url="tsPictureController.do?findPictureListByHW4" funname="queryPic"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script>

    function getTitle(){
        return '填写意见';
    }

    //填写意见
    function editTheSuspOpin(title, url, gname, callback){
        //获取数据
        var rowsData = $('#' + gname).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        var reasons = rowsData[0].susFraudReson==null?'':rowsData[0].susFraudReson;
        var html = returnShowHtml(reasons);
        title = getTitle();
        $.dialog({
            title: title,
            content: html,
            lock: true,
            width: "380",
            height: "200",
            zIndex: 100000,
            parent: windowapi,
            init : function(){

            },
            ok : function() {
                var isSuspicious = parent.$('#tempIsSuspicious').val();

                var opinion = parent.$('#tempMsg').val();
                var tempData = {
                    title : title,
                    opinion : opinion,
                    isSuspicious : isSuspicious
                }
                //开始保存
                return starEditTheSuspOpin(gname,rowsData[0],url,tempData);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    function structureRadio(){
        var selectHtml = '<select id="tempIsSuspicious">';
        selectHtml += '<option value="">--请选择--</option>';

        selectHtml += '<option value="-1">重复</option>';
        selectHtml += '<option value="0">可疑</option>';
        selectHtml += '<option value="1">正常</option>';
        selectHtml += '</select>';
        return selectHtml;
    }

    function returnShowHtml(reason) {
        var html = "";
        html += '<div class="form"><label class="Validform_label" name="tempIsSuspicious"><span style="color: red;">*</span>是否可疑：</label> ';
        html += structureRadio();
        html += ' </div>';
        html += '<div class="form" style="margin-top: 10px;"><label class="Validform_label" style="position: relative;left: 0;top: 0;font-size: 15px; float: left; ">意见：</label><textarea id="tempMsg" rows="10" cols="40">';
        html +=   reason ;
        html +=    '</textarea><div>';
        return html;
    }

    //开始
    function starEditTheSuspOpin(gname,rowsData,url,tempData){

        var flg = false;
        var thisData = {
            id : rowsData.susId,
            flagKey : rowsData.flagKey,
            nodeCode : 'task1514890994703',//生产环境  后审节点
            businessKey : rowsData.businessKey,
            srcType : 'BPM006',
            title : '是否疑似造假',
            opinion : tempData.opinion,
            isSuspicious : tempData.isSuspicious
        }

        var d = ajaxPost(thisData, url);

        if(d.success){
            $("#" + gname).datagrid('reload');
            flg = true;
        }
        newTip(d.msg);
        return flg;
    }

    function preAudit(a,b,c,d) {
        var url ="tnTaTaskController.do?goInstanceHandleTabForm&isReadFlag=false&isView=false&processInstanceId="+b+"&taskId="+a+"&isCommunicate=N&id="+a;
        safeShowDialog({
            title: "7-市场部后审",
            content: "url:"+url,
            lock: true,
            width: "1300",
            height: "1200",
            zIndex: 10000,
            parent: windowapi,
            close: function(event, ui) {
                //window.location.reload(); //整个页面刷新
                $("#tsActApplyVoList").datagrid("reload");//单个列表刷新
            }
        });

    }
    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 930, 500);
    }
    function goTmTerminal(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, width, height);
    }

    function changeDelMark(status){

        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var ids = []
        for(var i = 0;i<row.length;i++){
            ids.push(row[i].id);
            if(row[i].delMark == '0'){
                tip("该流程未打删除标记，没必要做恢复操作！");
                return;
            }
        }

        var thisData = {
            ids : ids.join(','),
            status : status
        }

        var url = "ttActOutUploadController.do?changeDelMark";

        var d = ajaxPost(thisData,url);

        if(d.success){
            $("#tsActApplyVoList").datagrid("reload");
        }
        tip(d.msg);
    }

    function uploadLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var bpmStatus=row[0].bpmStatus;
        if(!(bpmStatus=='10'||bpmStatus=='4'||bpmStatus=='5'||bpmStatus=='6'||bpmStatus=='12')){
            tip("该状态不能上传设计稿!");
            return ;
        }
        var actType=row[0].actType;
        if(actType==undefined||actType==""||actType==null){
            tip("流程状态异常,不能操作!");
            return ;
        }
        gridname="tsActApplyVoList";
        var url = "ttActOutUploadController.do?goActOutUploadForm&id="+row[0].id;
        if(actType=="2"){
            url = "ttActOutUploadController.do?goActOutDoorUploadForm&id="+row[0].id;
        }
        $.dialog({
            title: "设计稿上传",
            content: "url:"+url,
            lock: true,
            width: "1000",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var confirmBtn = $('#btn_sub', iframe.document);
                if(actType=="2"){
                    $.dialog.confirm('公司规定：超20平方/门头将按照20平方核销，是否已知悉？',function(r) {
                        if (r) {
                            confirmBtn.click();
                        }
                    });
                }else{
                    confirmBtn.click();
                }

                return false;
            },
            cancelVal: '关闭',
            cancel: function () {
                $("#tsActApplyVoList").datagrid("reload");
            }
        });
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function findRejectReason() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goRejectReason&id="+row[0].id+"&actCode="+row[0].actCode;
        $.dialog({
            title: "驳回原因(历史记录)",
            content: "url:"+url,
            lock: true,
            width: "700",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
</script>
