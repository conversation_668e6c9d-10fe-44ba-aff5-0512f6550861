<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="ttQuotaCostList" fitColumns="false" title="预提明细管理"
                    actionUrl="ttQuotaAccruedController.do?findTtQuotaAccruedList" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="预提年月" field="accruedYearMonth"  query="true" ></t:dgCol>
            <t:dgCol title="活动年月" field="actYearMonth" query="true" ></t:dgCol>
            <t:dgCol title="预提类型" field="accruedType" dictionary="accrued_type"  query="true" ></t:dgCol>
            <t:dgCol title="活动主单编号" field="billCode" query="true" ></t:dgCol>
            <t:dgCol title="活动编号" field="actCode" query="true" ></t:dgCol>
            <t:dgCol title="活动名称" field="actName" query="true" ></t:dgCol>
            <t:dgCol title="费用归属事业部" field="businessUnitName"  ></t:dgCol>
            <t:dgCol title="组织" field="orgName" query="true" ></t:dgCol>
            <t:dgCol title="SAP成本中心" field="costCenter"  query="true" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" query="true" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true" ></t:dgCol>
            <t:dgCol title="财务科目ERP编码" field="finacialAccountCode" query="true"></t:dgCol>
            <t:dgCol title="预算科目编码" field="finacialCode"  ></t:dgCol>
            <t:dgCol title="预算科目" field="finacialName" query="true" ></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode"  ></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeName" query="true" ></t:dgCol>
            <t:dgCol title="活动细类编码" field="costAccountCode" ></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountName" query="true" ></t:dgCol>
            <t:dgCol title="产品层级" field="productName"></t:dgCol>
            <t:dgCol title="活动开始时间" field="beginDate"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate"  ></t:dgCol>
            <t:dgCol title="支付方式" field="paymentCode" query="true"  dictionary="payment_type"></t:dgCol>
            <t:dgCol title="活动申请金额(元)" field="applyAmount" formatterjs="numExtend" ></t:dgCol>
            <t:dgCol title="原始金额(元)" field="oriAccruedAmount" formatterjs="numExtend" ></t:dgCol>
            <t:dgCol title="货补系数" field="accruedRate"  ></t:dgCol>
            <t:dgCol title="预提金额(元)" field="accruedAmount" formatterjs="numExtend" ></t:dgCol>
            <t:dgCol title="创建人" field="createName" hidden="true"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" ></t:dgCol>

            <t:dgToolBar operationCode="add" title="新增" height="630" width="400" icon="icon-add" url="ttQuotaAccruedController.do?goTtQuotaAccruedForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="importQuota" title="定额预提计算" height="300" width="400" icon="icon-add" url="" funname="importQuota"></t:dgToolBar>
            <t:dgToolBar operationCode="importProduct" title="产品预提计算" height="300" width="400" icon="icon-add" url="ttProductPolicyAccruedController.do?goDoAccruedForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="importMilk" title="奶粉产品预提计算" height="300" width="400" icon="icon-add" url="ttProductPolicyAccruedController.do?goDoAccruedForm" funname="addMilk"></t:dgToolBar>
            <%--<t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'orgPositionImport', gridName:'OrgPositionList'})"></t:dgToolBar>--%>
            <t:dgToolBar  title="导出" operationCode="export" icon="icon-dataOut" url="ttQuotaAccruedController.do?exportXls" funname="excelExport"></t:dgToolBar>
            <%--<t:dgToolBar title="日志" operationCode="log" icon="icon-log" url="ttQuotaAccruedController.do?goTtDirectIncomeLogMain" funname="detail" width="1200"></t:dgToolBar>--%>
        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $(function () {
        $("#ttQuotaCostListtb_r").find("input[name='accruedYearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("#ttQuotaCostListtb_r").find("input[name='actYearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("#ttQuotaCostListtb_r").find("input[name='beginDate']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

        $("#ttQuotaCostListtb_r").find("input[name='endDate']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });
    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#TtQuotaCostList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "orgPositionController.do?deleteOrgPosition&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#TtQuotaCostList").datagrid("reload");
                    }
                });
            }
        });
    }

    function addMilk(title, addurl, gname, width, height, params) {
        $.dialog.confirm("导入奶粉预提数据需要几分钟时间，是否要进行导入操作？", function() {
            doAddMilkHandler(title, addurl, gname, width, height, params)
        });
    }

    function doAddMilkHandler(title, addurl, gname, width, height, params) {
        window.top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });

        $.ajax({
            url:"ttMilkAccruedController.do?doTtMilkAccrued",
            type:'post',
            data:{},
            cache:false,
            success:function(data) {
                window.top.$.messager.progress("close");
                var d = $.parseJSON(data);
                var msg = d.msg;
                tip(msg);
                window.top.$.messager.progress("close");
                frameElement.api.close();
            }
        });
    }

    function importQuota() {
        $.dialog.confirm("确定进行上月定额预提计算，已计算的数据将被清除？", function() {

            window.top.$.messager.progress({
                text : '操作执行中....',
                interval : 300
            });

            $.ajax({
                url:"ttQuotaAccruedController.do?insertTtQuotaAccruedNotPool",
                type:'post',
                data:{},
                cache:false,
                success:function(data) {
                    window.top.$.messager.progress("close");
                    var d = $.parseJSON(data);
                    var msg = d.msg;
                    tip(msg);
                    window.top.$.messager.progress("close");
                    frameElement.api.close();
                    $("#TtQuotaCostList").datagrid("reload");
                }
            });
        });
    }
</script>
