<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="fyearList" title="预算财年设置"  actionUrl="ttFinancialYearController.do?findFinancialYearList" 
	  		 idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		<t:dgCol title="编码" field="id" hidden="true"></t:dgCol>
	  		<t:dgCol title="预算财年" field="year" query="true"></t:dgCol>
	  		<t:dgCol title="季度" field="quarter" query="true" replace="1_1,2_2,3_3,4_4"></t:dgCol>
	  		<t:dgCol title="月度" field="month" query="true" replace="01_01,02_02,03_03,04_04,05_05,06_06,07_07,08_08,09_09,10_10,11_11,12_12"></t:dgCol>
	  		<t:dgCol title="开始日期" field="beginDate"></t:dgCol>
	  		<t:dgCol title="结束日期" field="endDate"></t:dgCol>
	  		<t:dgCol title="创建人" field="createName"></t:dgCol>
	  	 	<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		 
			<t:dgToolBar operationCode="new" title="创建财年" icon="icon-add" url="ttFinancialYearController.do?goFinancialYearForm" funname="add"></t:dgToolBar>
			<t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="" funname="updateYear()"></t:dgToolBar>
			<t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="" funname="delYear()"></t:dgToolBar>
			<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detailLog" width="1200" height="460"></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
$(function(){
	$("#fyearListtb_r").find("input[name='year']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy'});});
});
//删除
function delYear(){
	var seletctTarget =  $("#fyearList").datagrid("getSelections");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("必须选择一条数据");
		return;
	}
	$.messager.confirm('操作提示','确定删除?',function(r){ 
	    if (r){
	    	$.ajax({
	        	type : "POST",
	        	url : "ttFinancialYearController.do?deleteYearById&id="+seletctTarget[0].id,
	        	async:false,
	        	success : function(data) {
	        		var d = $.parseJSON(data);
	        		tip(d.msg);
	        		$("#fyearList").datagrid("reload");
	        	}
		   });
	    }
	});
}
function updateYear(gridname){
	var seletctTarget =  $("#fyearList").datagrid("getSelections");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("必须选择一条数据");
		return;
	}
 	var url = "ttFinancialYearController.do?validateFinanicalYearIsUse&year="+seletctTarget[0].year + "&month="+seletctTarget[0].month;
	var flag = true;
	$.ajax({url:url,type:"post",async:false,success:function(data){
		var d = $.parseJSON(data);
		if(d.success == false){
			flag = false;
			tip(d.msg);
		}
	}});
	if(flag){ 
		var openUrl = "ttFinancialYearController.do?goFinancialYearForm&id="+seletctTarget[0].id;
		createwindow('编辑财年',openUrl,400,400);
	 } 
}
</script>
