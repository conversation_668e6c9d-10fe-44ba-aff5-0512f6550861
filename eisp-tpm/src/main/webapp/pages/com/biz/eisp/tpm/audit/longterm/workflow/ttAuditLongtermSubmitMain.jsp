<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<style type="text/css">
    .datagrid-toolbar-search form div label{
        width:100px;
    }
</style>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;width:500px;">
        <t:datagrid name="ttActLongtermList" title="长期待摊费用"  actionUrl="ttAuditLongtermWorkflowController.do?findAuditLongtermSelectList"
                    idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group" singleSelect="false">

            <t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
            <t:dgCol field="actTypeCode" title="结案类型" query="true" dictionary="audit_longterm_type"></t:dgCol>
            <t:dgCol field="billCode" title="结案申请单号" query="true" ></t:dgCol>
            <t:dgCol field="billName" title="结案申请名称" query="true" ></t:dgCol>
            <t:dgCol field="bpmStatus" title="审批状态" query="true" dictionary="bpm_status"></t:dgCol>
            <t:dgCol field="createName" title="结案申请人"  ></t:dgCol>
            <t:dgCol field="updateDateStr" title="结案申请时间"></t:dgCol>


            <t:dgToolBar title="添加" icon="icon-add" onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add" onclick="addAllItem()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div region="east" style="width:450px;padding:1px;">
        <table id="ttActLongtermSelectedList" class="easyui-datagrid" style="height:auto" fit="true"
               data-options="singleSelect: false,toolbar: '#tb',method: 'get',url:''">
            <thead>
            <tr>
                <th data-options="field:'id',width:80,hidden:'true'">id</th>
                <th data-options="field:'actTypeCode',width:100,hidden:'true'">结案类型</th>
                <th data-options="field:'billCode',width:100,align:'center'">结案申请单号</th>
                <th data-options="field:'billName',width:150,align:'center'">结案申请名称</th>
                <th data-options="field:'bpmStatus',width:80,align:'center',hidden:'true'">审批状态</th>
                <th data-options="field:'bpm',width:80,align:'center'">审批状态</th>
                <th data-options="field:'createName',width:200,align:'center'">结案申请人</th>
                <th data-options="field:'updateDateStr',width:150,align:'center'">结案申请时间</th>
            </tr>
            </thead>
        </table>

        <div id="tb" style="height:auto">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeItem()">删除</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeAllItem()">全部删除</a>
        </div>
    </div>

    <div id="btn_sub" onclick="submitForm()"></div>
</div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        $("select[name='actType']").find("option[value='0']").attr("selected",true);
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });

        $("select[name='actType']").change(function(){
            var rowsData = $('#ttActLongtermSelectedList').datagrid('getRows');
            var actType=$("select[name='actType']").val();
            if(rowsData!=null&&rowsData.length>0){
                var actTypeSelected=rowsData[0].actType;
                if(actTypeSelected!=actType){
                    tip("所选类型数据请先提交");
                    $("select[name='actType'] option[value='"+actTypeSelected+"']").attr("selected", "selected");
                }
            }else{
                ttActLongtermListsearch();
            }
        });
    });
    //添加
    function addItem(){
        var seletctTarget =  $("#ttActLongtermList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            var bpm = "";
            var bpmStatus = rows.bpmStatus;
            if(bpmStatus == 1){
                bpm = "待提交";
            }else if(bpmStatus == 4){
                bpm = '驳回';
            }else{
                bpm = '追回';
            }
            rows.bpm = bpm;
            $("#ttActLongtermSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
    }

    //添加全部
    function addAllItem(){
        var name = "ttActLongtermList";
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }
        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        queryParams.rows = 99999999;
        $.ajax({
            url:"ttAuditLongtermWorkflowController.do?findAuditLongtermSelectList",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    //添加
                    var rows = d.rows;
                    var bpm = "";
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var bpmStatus = row.bpmStatus;
                        if(bpmStatus == 1){
                            bpm = "待提交";
                        }else if(bpmStatus == 4){
                            bpm = '驳回';
                        }else{
                            bpm = '追回';
                        }
                        row.bpm = bpm;
                        $("#ttActLongtermSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
    }

    function removeItem() {
        var checkListTarget =  $("#ttActLongtermSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        for (var i = 0; i < checkListTarget.length; i++) {
            var checkRowIndex = $("#ttActLongtermSelectedList").datagrid("getRowIndex", checkListTarget[i]);
            //移除该数据
            $("#ttActLongtermSelectedList").datagrid("deleteRow",checkRowIndex);
        }
        loadElecteGrid();
    }

    function removeAllItem() {
        var rowsData = $("#ttActLongtermSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttActLongtermSelectedList").datagrid("getRows");
        $.each(rows, function(i, obj) {
            var checkRowIndex = $("#ttActLongtermSelectedList").datagrid("getRowIndex",obj);
            //移除该数据
            $("#ttActLongtermSelectedList").datagrid("deleteRow",checkRowIndex);
        });
        loadElecteGrid();
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttActLongtermSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].billCode+"'";
            }
        }
        var actType=$("select[name='actType']").val();
        $('#ttActLongtermList').datagrid("reload", {"exclusiveCodes":exclusiveCodes,"actType":actType});
    }

    function submitForm() {
        var rows = $("#ttActLongtermSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }
        var codes = [];
        for ( var i = 0; i < rows.length; i++) {
            codes.push(rows[i].id);
        }
        var actType = rows[0].actType;
        processKey = "process_audit_cqdt_type";
        var params = {processKeyType:processKey};
        jblSubmitDialog(codes.join(","),"","","com.biz.eisp.tpm.audit.longterm.controller.TtAuditLongtermWorkflowController",JSON.stringify(params))
    }
</script>

