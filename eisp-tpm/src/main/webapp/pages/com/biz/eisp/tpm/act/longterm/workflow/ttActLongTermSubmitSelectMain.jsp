<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<style type="text/css">
    .datagrid-toolbar-search form div label{
        width:100px;
    }
</style>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;width:500px;">
            <t:datagrid name="ttActLongtermList" title="长期待摊费用"  actionUrl="ttActLongTermWorkFlowController.do?findTtActLongTermList"
	  		  idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group" singleSelect="false">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>

            <t:dgCol title="审批状态" field="bpmStatus" dictionary="bpm_status"></t:dgCol>

	  		<t:dgCol title="流程类型" field="actTypeCode" dictionary="ad_long_act_type"></t:dgCol>
	  		<t:dgCol title="流程类型" field="actType" hidden="true" query="true" replace="长期待摊_0,广告费用或营销费用(到部门)_1"></t:dgCol>
	  		
	  		<t:dgCol title="活动编号" field="billCode" align="center" query="true"></t:dgCol>
	  		
	  		<t:dgCol title="活动名称" field="billName" align="center" query="true"></t:dgCol>

	  		<t:dgCol title="活动大类" field="costTypeCode" hidden="true"></t:dgCol>
	  		<t:dgCol title="活动大类" field="costTypeName" query="true"></t:dgCol>
	  		<t:dgCol title="活动细类" field="costAccountCode" hidden="true"></t:dgCol>
	  		<t:dgCol title="活动细类" field="costAccountName" autocomplete="true" query="true"></t:dgCol>
	  		
	  		<t:dgCol title="客户名称" field="customerName"  query="true"></t:dgCol>
	  		<t:dgCol title="开始时间" field="beginDate" formatter="yyyy-MM-dd" query="true" queryMode="group"></t:dgCol>
	  		<t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>
	  		<t:dgCol title="组织名称" field="orgName" query="true"></t:dgCol>
	  		<t:dgCol title="组织编码" field="orgCode" hidden="true"></t:dgCol>
	  		<t:dgCol title="活动总金额" field="amount"></t:dgCol>
	  		<t:dgCol title="费用归属事业部" field="businessUnitName" hidden="true"></t:dgCol>
	  		<t:dgCol title="费用归属事业部编码" field="businessUnitCode" dictionary="SYBManager" query="true"></t:dgCol>

	  		<t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" query="true"></t:dgCol>

	  		<t:dgCol title="备注" field="remark"></t:dgCol>
	  		<t:dgCol title="发起人" field="createName" query="true"></t:dgCol>
	  		<t:dgCol title="发起时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>

            <t:dgToolBar title="添加" icon="icon-add" onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add" onclick="addAllItem()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div region="east" style="width:450px;padding:1px;">
        <table id="ttActLongtermSelectedList" class="easyui-datagrid" style="height:auto" fit="true"
               data-options="singleSelect: false,rownumbers:true,toolbar: '#tb',method: 'get',url:''">
            <thead>
                <tr>
                    <th data-options="field:'id',width:80,hidden:'true'">ID</th>
                    
                    <%--<th data-options="field:'actTypeCode',width:80,align:'center'">流程类型</th>--%>
                    <th data-options="field:'actType',width:80,hidden:'true'">ID</th>

                    <th data-options="field:'billCode',width:80,align:'center'">活动编号</th>
                    <th data-options="field:'billName',width:80,align:'center'">活动名称</th>

                    <th data-options="field:'costTypeName',width:80,align:'center'">活动大类</th>
                    <%--<th data-options="field:'costAccountCode',width:120,align:'center'">活动细类</th>--%>
                    <th data-options="field:'costAccountName',width:80,align:'center'">活动细类名称</th>
                    
                    <th data-options="field:'customerName',width:100,align:'center'">客户名称</th>
                    <th data-options="field:'beginDate',width:80,align:'center'">开始时间</th>
                    <th data-options="field:'endDate',width:80,align:'center'">结束时间</th>
                    
                    <th data-options="field:'orgName',width:100,align:'center'">部门</th>
                    <%--<th data-options="field:'orgCode',width:100,align:'center'">部门编码</th>--%>
                    <th data-options="field:'amount',width:80,align:'center'">活动总金额</th>
                    <th data-options="field:'businessUnitName',width:100,align:'center'">费用归属事业部</th>
                    <%--<th data-options="field:'businessUnitCode',width:100,align:'center'">费用归属事业部编码</th>--%>
                    
                    <%--<th data-options="field:'paymentCode',width:100,align:'center'">支付方式</th>--%>
                    <%--<th data-options="field:'bpmStatus',width:110,align:'center'">审批状态</th>--%>
                    <th data-options="field:'remark',width:100,align:'center'">备注</th>
                    
                    <%--<th data-options="field:'createName',width:110,align:'center'">发起人</th>--%>
                    <%--<th data-options="field:'createDate',width:100,align:'center'">发起时间</th>--%>
                    <%--<th data-options="field:'updateName',width:100,align:'center'">最近更新人</th>--%>
                    <%--<th data-options="field:'updateDate',width:100,align:'center'">最近更新时间</th>--%>
                    
                </tr>
            </thead>
        </table>

        <div id="tb" style="height:auto">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeItem()">移除</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeAllItem()">全部移除</a>
        </div>
    </div>

    <div id="btn_sub" onclick="submitForm()"></div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        $("#ttActLongtermListForm").find("label").eq(0).attr("style","color:red");
        $("select[name='actType']").find("option[value='0']").attr("selected",true);
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });

        $("select[name='actType']").change(function(){
            var rowsData = $('#ttActLongtermSelectedList').datagrid('getRows');
            var actType=$("select[name='actType']").val();
            if(rowsData!=null&&rowsData.length>0){
                var actTypeSelected=rowsData[0].actType;
                if(actTypeSelected!=actType){
                    tip("所选类型数据请先提交");
                    $("select[name='actType'] option[value='"+actTypeSelected+"']").attr("selected", "selected");
                }
            }else{
                ttActLongtermListsearch();
            }

        });
    });
    //添加
    function addItem(){
        var seletctTarget =  $("#ttActLongtermList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttActLongtermSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
    }

    //添加全部
    function addAllItem(){
        var name = "ttActLongtermList";	
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }
        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.ajax({
            url:"ttActLongTermWorkFlowController.do?findTtActLongTermList",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttActLongtermSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
    }

    function removeItem() {
        var checkListTarget =  $("#ttActLongtermSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        for (var i = 0; i < checkListTarget.length; i++) {
            var checkRowIndex = $("#ttActLongtermSelectedList").datagrid("getRowIndex", checkListTarget[i]);
            //移除该数据
            $("#ttActLongtermSelectedList").datagrid("deleteRow",checkRowIndex);
        }
        loadElecteGrid();
    }

    function removeAllItem() {
        var rowsData = $("#ttActLongtermSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttActLongtermSelectedList").datagrid("getRows");
        $.each(rows, function(i, obj) {
            var checkRowIndex = $("#ttActLongtermSelectedList").datagrid("getRowIndex",obj);
            //移除该数据
            $("#ttActLongtermSelectedList").datagrid("deleteRow",checkRowIndex);
        });
        loadElecteGrid();
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttActLongtermSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].billCode+"'";
            }
        }
        var actType=$("select[name='actType']").val();
        $('#ttActLongtermList').datagrid("reload", {"exclusiveCodes":exclusiveCodes,"actType":actType});
    }

    function submitForm() {
        var rows = $("#ttActLongtermSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }

        var codes = [];
        for ( var i = 0; i < rows.length; i++) {
            codes.push(rows[i].id);
        }
        var actType = rows[0].actType;
        if(actType == 0){//长期待摊
            processKey = "longterm_act_type";
        }else{
            processKey = "ad_act_type";
        }
        var params = {processKeyType:processKey};
        jblSubmitDialog(codes.join(","),"","","com.biz.eisp.tpm.act.longterm.workflow.controller.TtActLongTermWorkFlowController",JSON.stringify(params))
    }
</script>

