<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>核销资料管理</title>
<t:base type="jquery,easyui,tools"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
	<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
		action="ttAuditMaterialController.do?saveTtAuditMaterial">
		<input name="id" type="hidden" value="${vo.id}">
		<c:if test="${not empty vo.id }">
			<div class="form">
			<label class="Validform_label">核销资料编号: </label>
			<input name="materialCode" id="materialCode" readonly="readonly" datatype="*" class="inputxt" value="${vo.materialCode}" />
				<span style="color: red;">*</span>
		</div>
		</c:if>
		<div class="form">
			<label class="Validform_label">核销资料名称: </label>
			<input ajaxUrl="ttAuditMaterialController.do?validTtAuditMaterial&id=${vo.id}"
				   name="materialName" id="materialName" datatype="/^[0-9a-zA-Z\u4e00-\u9fa5]{1,30}$/"
				   errormsg="只能录入汉字，数字，大小写英文" class="inputxt" value="${vo.materialName}" />
				<span style="color: red;">*</span>
		</div>
	</t:formvalid>
	<script type="text/javascript">
</script>
</body>
</html>
