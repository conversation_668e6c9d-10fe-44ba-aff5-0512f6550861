<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
</style>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'north',title:'月度计划提报'" style="padding:1px;height:112px;" >
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-add" onClick="chooseOrg()">选择组织</a>
							<a href="#" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-edit" onClick="updateEditStatus(1)">可编辑</a>
							<%--<a href="#" class="easyui-linkbutton" plain="true" name="addMonthlyPlan" icon="icon-edit" onClick="updateEditStatus(0)">禁止编辑</a>--%>
						</span>
						<span style="float:right">
							<a href="#" class="easyui-linkbutton" iconcls="icon-search" onclick="ttMonthlyPlanEditListSearchFunction()">查询</a>
							<a href="#" class="easyui-linkbutton" iconcls="icon-reload" onclick="searchReset()">重置</a>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search" style=" height: 40px;">
		            	<form>
		            		<div>
			            		<label>计划年份</label>
		            			<input type="text" name="year" id="year" class="Wdate"
								onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){chooseYearAndMonthSearch();}})" readonly="readonly" />
		            			<span style="color: red;">*</span>
		            			<label>计划月份</label>
		            			<input type="text" name="month" id="month" class="Wdate"
								onclick="WdatePicker({dateFmt:'MM',onpicked:function(){chooseYearAndMonthSearch();}})" readonly="readonly" />
			            		<span style="color: red;">*</span>
		            			<input type="hidden" readonly="readonly" id="orgCode">
		            			<%-- <label>选择组织</label>
		            			<b:search hiddenFields="orgCode,orgName" inputTextName="orgCode,orgName" value="" 
	            				inputTextId="orgCode,orgName" name="customerList" type="5" title="选择组织" fun="callBack" ></b:search> --%>
		            		</div>
		            	</form>
	            		<div class="top_tip" >
		            	</div>
					</div>
				</div>
				<div class="datagrid-view">
				</div>
			</div>
		</div>
	</div>
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttMonthlyPlanEditList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
	       autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanEditList" onLoadSuccess="onLoadSuccessFun" onClick="clickMonthlyPlanFun">
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
	        <t:dgCol title="年" field="year" hidden="true" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="月" field="month" hidden="true" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
         	<t:dgCol title="区域编码" field="orgCode" hidden="true" frozenColumn="true" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="区域" field="orgName" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="是否启用" field="enableStatus" replace="是_1,否_0" sortable="false" width="100" ></t:dgCol>
	    </t:datagrid>
	</div>
	<div data-options="region:'east',
	title:'',
	collapsed:true,
	split:true,
	border:false,
	onExpand : function(){
		li_east = 1;
	},
	onCollapse : function() {
	    li_east = 0;
	}"
	 style="padding:1px;width:700px;">
		<t:datagrid name="ttMonthLyPlanByEditList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="false"
	       autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthLyPlanByEditList" >
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
			<t:dgCol title="年" field="year" hidden="true" sortable="false" width="100" ></t:dgCol>
			<t:dgCol title="月" field="month" hidden="true" sortable="false" width="100" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
         	<t:dgCol title="销售部" field="salesDepartName" sortable="false" width="100" ></t:dgCol>
         	<t:dgCol title="区域编码" field="orgCode" hidden="true" frozenColumn="true" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="区域" field="orgName" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="是否可编辑" field="editableStatus" replace="可编辑_1,不可编辑_0" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="状态" field="bpmStatus" dictionary="monthlyPlan_Bpm_status" sortable="false" width="100" ></t:dgCol>
			<t:dgCol title="创建人" field="createName" sortable="false" width="100" ></t:dgCol>
			<t:dgCol title="操作" field="opt" sortable="false" width="100" ></t:dgCol>
			<t:dgFunOpt title="详情"  funname="operationDetail(orgCode,year,month,yearMonth)" ></t:dgFunOpt>
		</t:datagrid>
	</div>
</div>

<script type="text/javascript">
    var li_east = 0;
	function clickMonthlyPlanFun(rowIndex,rowData) {
        if(li_east == 0){
            $('#ttReportMonthlyPlanMain').layout('expand','east');
        }
		var orgCode = rowData.orgCode;
		var year = rowData.year;
		var month = rowData.month;
		var queryParams = $('#ttMonthLyPlanByEditList').datagrid('options').queryParams;
        queryParams.orgCode = orgCode;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthLyPlanByEditList").datagrid({url:"ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthLyPlanByEditList"});
	}
    //打开明细--列级
    function operationDetail(orgCode,year,month,yearMonth,index){
        var thisData = {
            orgCode : orgCode,
            year : year,
            month : month,
        	yearMonth : yearMonth
		}
        var data = changeDataToUrlData(thisData);
        createwindowExt("查看明细","ttMonthlyPlanHeadquartersConfirmController.do?goTtMonthlyPlanDetailFromUpdateEditPage" + data,"1300","650",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }
	//选择组织
	function chooseOrg(){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var orgCode = $('#orgCode').val();
		orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
		var currOrgCode = '${currOrgCode}';
		var paraArr = {
			searchType:'1',//查询类型
			encapsulationType:'return',//封装类型--不传或默认
            currentOrgCode : currOrgCode,
			isCouldRemove:false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
			callBackFun : toSaveUpdateEditStatusLog,
			pageData:{
				orgCode : orgCode
			}
            /*searchData:{
			    orgType : 'XSB'
            }*/
		}
		openChooseOrgSelect(paraArr);
        /*var paraArr = {
            url : "",
            encapsulationType:'return',//封装类型--不传或默认
            currentOrgCode : currOrgCode,
            isCouldRemove:false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            callBackFun : toSaveUpdateEditStatusLog,
            pageData:{
                orgCode : orgCode
            }
        }
        openChooseSelect(paraArr);*/
	}
	//保存修改编辑状态的记录
	function toSaveUpdateEditStatusLog(datas){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var yearMonth = year + "-" + month;
		
		var dataList = [];
		for (var i = 0; i < datas.length; i++) {
			var dataObj = datas[i];
			//if ('1' == dataObj.extChar1) {
				var obj = new Object;
				obj.year = year;
				obj.month = month;
				obj.yearMonth = yearMonth;
				obj.orgCode = dataObj.orgCode;
				obj.orgName = dataObj.orgName;
				dataList.push(obj);
			//}
		}
		startSaveUpdateEditStatusLog(dataList,year,month);
	}
	function startSaveUpdateEditStatusLog(list,year,month){
		
	 	var thisdata = {
			info:JSON.stringify({ttMonthlyPlanEditList:list}),
			year:year,
			month:month
		}
		var url = "ttMonthlyPlanHeadquartersConfirmController.do?saveUpdateEditStatusLog";
		$.ajax({
			async : false,
			cache : false,
			data : thisdata,
			type : 'POST',
			url : url,// 请求的action路径
			error : function() {// 请求失败处理函数
			},
			success : function(data) {
				var d = $.parseJSON(data);
				if (d.success) {
					ttMonthlyPlanEditListSearchFunction();
				}
				tip(d.msg);
			}
		});  
	    /* ajaxRequest({tips:'确定保存吗 ?',url:url,params:thisdata,callbackfun:function(data){
	    	var d = $.parseJSON(data);
	        if(d.success == true) {
	        	ttMonthlyPlanEditListSearchFunction();
	        }
	        tip(d.msg);
	    }}); */
	}
	//选择年月后
	function chooseYearAndMonthSearch(){
		var year = $('#year').val();
		if (year == '') {
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			return ;
		}
		ttMonthlyPlanEditListSearchFunction();
	}
	//查询
	function ttMonthlyPlanEditListSearchFunction(){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var queryParams = $('#ttMonthlyPlanEditList').datagrid('options').queryParams;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthlyPlanEditList").datagrid({url:"ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanEditList" });
	}
	//加载成功后处理
	function onLoadSuccessFun(datas){
		var rowDatas =  datas.rows;
		var orgCodeTemp = '';
		if (typeof(rowDatas) != 'undefined' && rowDatas != null && rowDatas != '' && rowDatas.length > 0 ) {
			orgCodeTemp = rowDatas[0].orgCodes;
		}
		$("#orgCode").val(orgCodeTemp);
		/* if (num == 1) {
			$("#year").val('');
			$("#month").val('');
			$("#customerCode").val('');
			$("#customerName").val('');
			changeToDisable(false);
		}else{
			changeToDisable(true);
		} */
	}
	
	//重置
	function searchReset(){
		$("#year").val('');
		$("#month").val('');
		$("#customerCode").val('');
		$("#customerName").val('');
		ttMonthlyPlanEditListSearchFunction();
	}
	
	//修改编辑状态
	function updateEditStatus(editStatus){
		var rowDatas = $('#ttMonthlyPlanEditList').datagrid('getSelections');
		if (!checkObjListIsNotEmpty(rowDatas)) {
			tip('请选择项目');
			return ;
		}
		//循环遍历
		var ids = [];
		for (var i = 0; i < rowDatas.length; i++) {
			var rowData = rowDatas[i];
			ids.push(rowData.id);
		}
		//参数
		var dataTemp = {
			editStatus : editStatus,
			ids : ids.join(",")
		}
		var url = "ttMonthlyPlanHeadquartersConfirmController.do?updateEditStatus" ;
	    ajaxRequest({tips:'确定提交?',url:url,params:dataTemp,callbackfun:function(data){
	    	var d = $.parseJSON(data);
            if(d.success == true) {
            	ttMonthlyPlanEditListSearchFunction();
            	$('#ttMonthLyPlanByEditList').datagrid("reload");
            } 
            tip(d.msg);
	    }});
	}
	//检查单个对象
	function checkObjIsNotEmpty(obj){
		if (typeof(obj) != 'undefined' && obj != null && obj != '') {
			return true;
		}
		return false;
	}
	//检查集合
	function checkObjListIsNotEmpty(objList){
		if (checkObjIsNotEmpty(objList) && objList.length > 0 ) {
			return true;
		}
		return false;
	}
	//将obj转换为urlData
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
</script>