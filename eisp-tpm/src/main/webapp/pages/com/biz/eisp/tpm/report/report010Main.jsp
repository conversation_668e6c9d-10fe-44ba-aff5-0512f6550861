<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<%--<style>--%>
    <%--.datagrid-toolbar-search form div label{--%>
        <%--width:160px;--%>
    <%--}--%>
<%--</style>--%>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report010List" fitColumns="false" title="客户换户关系"
                    pagination="false" autoLoadData="true" actionUrl="report010Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="原户编码" field="origCusCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="原户名称" field="origCusName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="新户名称" field="newCusName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="新户编码" field="newCusCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="新户SAP第一次发票记账日期" field="newCusSAPDate"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="状态" field="status" dictionary="enable_status" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="是否承接原户任务" field="origCusTesk" dictionary="nooryes" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="是否承接原户去年销量" field="origCusSales" dictionary="nooryes" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="创建人" field="createName"  sortable="false" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" queryMode="group" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName"  sortable="false" ></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" queryMode="group" formatter="yyyy-MM-dd HH:mm:ss"  sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="add" title="新增" height="500" width="800" icon="icon-add" url="report010Controller.do?goReport010Form" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑" height="500" width="800" icon="icon-edit" url="report010Controller.do?goReport010Form" funname="update"></t:dgToolBar>
            <t:dgToolBar operationCode="del" title="刪除"  height="500" width="800" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>
            <t:dgToolBar operationCode="view" title="查看"  height="500" width="800" icon="icon-look" url="report010Controller.do?goReport010Form" funname="detail" ></t:dgToolBar>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report010Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="report010Controller.do?goReport010LogsMain" funname="detail" width="1200"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report010ListForm").find("label").eq(4).attr("style","width:190px");
        $("#report010ListForm").find("label").eq(6).attr("style","width:130px");
        $("#report010ListForm").find("label").eq(7).attr("style","width:150px");
//         $("#report010Listtb_r").find("input[name='newCusSAPDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");

        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });

        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });

        $("input[name='newCusSAPDate']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyyMMdd'});
        });
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report010Listsearch() {
        var orgCode = $("#report010Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report010Listtb_r").find("input[name='yearMonth']").val();

//        if(orgCode == null || orgCode == "") {
//            tip("请选择查询组织");
//            return;
//        }
//
//        if(yearMonth == null || yearMonth == "") {
//            tip("请选择查询年月");
//            return;
//        }

        var queryParams = $("#report010List").datagrid('options').queryParams;
        $("#report010Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report010List").datagrid({url:'report010Controller.do?findReportList'});
    }

    //年月格式化
    $("#TsInvoicingListtb_r").find("input[name='systemDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#report010List").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "report010Controller.do?deletefindReport010&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#report010List").datagrid("reload");
                    }
                });
            }
        });
    }

</script>
