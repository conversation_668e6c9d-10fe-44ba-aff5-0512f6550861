<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>财务科目新增</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttFinancialAccountController.do?saveFinancialAccount" refresh="true"
 >
	<input name="id" type="hidden" value="${account.id}">

	<div class="form">
		<label class="Validform_label">上级预算科目:</label>
		<input class="inputxt" name="parentName" id="parentName" readonly="readonly"  value="${account.parentName}">
		<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="poParent();"></a>
		<input type="hidden" id="parentId" name="parentId" value='${account.parentId}' >
		<span style="color: red"></span>
	</div>
	<div class="form">
	        <label class="Validform_label">预算科目编号:</label>
	        <input class="inputxt" name="accountCode" id="accountCode" datatype="/^[0-9a-zA-Z\,`~·~\<\>，。？\-：；《》【】‘’!！（）“”—……、\?\|\{\}\[\]\\\.\/\(\)]{1,30}$/" value="${account.accountCode}"
				   ajaxUrl="ttFinancialAccountController.do?validateCode&id=${account.id}"
				   errormsg="只能填写数字、字母、标点符号">
	        <span style="color: red">*</span>
	</div>

    <div class="form">
		<label class="Validform_label">财务科目ERP编码:</label>
		<input style="width: 150px" name="financialCode" id="financeCode" class="inputxt" value="${account.financialCode}"
			   <%--ajaxUrl="ttFinancialAccountController.do?validateRepeat&id=${account.id}"--%>
			   datatype='/^[0-9a-zA-Z]{1,30}$/' errormsg="只能填写数字、字母"/>
		<span style="color: red">*</span>
		<span class="Validform_checktip"></span>
	</div>

	<div class="form">
		<label class="Validform_label">预算科目名称:</label>
		<input style="width: 150px" 
		       name="accountName" 
			   ajaxUrl="ttFinancialAccountController.do?validFinancialAccountName&id=${account.id}"
			   class="inputxt" value="${account.accountName}" 
			   datatype='/^[0-9a-zA-Z\u4e00-\u9fa5,`~·~\<\>，。？\-：；《》【】‘’!！（）“”—……、\?\|\{\}\[\]\\\.\/\(\)]{1,30}$/'
			   errormsg="只能填写汉字、数字、字母、标点符号"/>
		<span style='color:red'>*</span>
		<span class="Validform_checktip"></span>
	</div>
	
<%--     <div class="form">
		<label class="Validform_label">科目类别:</label>
		<t:dictSelect id="accountType" field="accountType" type="select" defaultVal="${account.accountType}" typeGroupCode="account_category" 
		 			  hasLabel="true" title="科目类别" dataType="*">
		</t:dictSelect>
		<span style="color: red">*</span>
		<span class="Validform_checktip"></span> 
	</div> --%>

	<div class="form">
		<label class="Validform_label">预算科目类型： </label>
		<select name="accountType" dataType="*" value="${account.accountType}">
			<option value="">--请选择--</option>
			<c:forEach items="${cost_type}" var="c">
				<option value="${c.dictCode}"
						<c:if test="${account.accountType == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
			</c:forEach>
		</select>
		<span style="color: red">*</span>
	</div>


	<div class="form">
		<label class="Validform_label">备注:</label>
		<textarea rows="3" name="remark" id="remark" style="width:150px;resize: none;">${account.remark}</textarea>
	</div>
</t:formvalid>
</body>
<script>
    function poParent(){
        popClick("parentId,parentName", "id,accountName","ttFinancialAccountController.do?goFinancialAccountMainSelect&accessEntry="+accessEntry,1000,450);
//        popSelectClick("parentId", "parentName","accountName","ttFinancialAccountController.do?goFinancialAccountMainSelect&accessEntry="+accessEntry,"findFinancialSelectList",null,1000,450);
    }
</script>
</html>
