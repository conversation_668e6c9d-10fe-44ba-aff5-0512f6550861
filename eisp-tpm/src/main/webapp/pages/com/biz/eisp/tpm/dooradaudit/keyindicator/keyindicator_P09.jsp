<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="P09List" checkbox="false" fitColumns="true"
                    pagination="false"  actionUrl="ttTrainAuditWorkFlowController.do?findKeyindicatorBPM009&flagKey=${flagKey}" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="标题" field="title" sortable="false"></t:dgCol>
            <t:dgCol title="核销单号" field="auditCode" sortable="false"></t:dgCol>
            <t:dgCol title="经办人" field="operator" extendParams="hidden:true" sortable="false"></t:dgCol>
            <t:dgCol title="申请人" field="proposer" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="申请部门" field="orgName" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="所属单位" field="unitName" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="核报日期" field="auditDate" sortable="false"></t:dgCol>
            <t:dgCol title="费用开始日期" field="costStartDate" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="费用截止日期" field="costEndDate" hidden="true" sortable="false"></t:dgCol>
            <t:dgCol title="核销申请金额" field="auditAmount" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="瑕疵率" field="flawChance" sortable="false"></t:dgCol>
        </t:datagrid>
    </div>
</div>
