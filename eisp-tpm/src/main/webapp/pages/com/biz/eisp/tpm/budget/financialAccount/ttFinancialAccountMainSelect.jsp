<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
.datagrid-toolbar-search form div label{
	width:120px;
}
</style>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="findFinancialSelectList" title="预算科目选择"  actionUrl="ttFinancialAccountController.do?findFinancialAccountFiltrationEnableStateList"
	  		 idField="id" fit="true" singleSelect="true"  fitColumns="true" pagination="true" queryMode="group">
	  		<t:dgCol title="编码" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="预算科目层级" field="levelCode" dictionary="financial_level" query="true"></t:dgCol>
	  		<t:dgCol title="预算科目编号" field="accountCode" query="true"></t:dgCol>
	  		<t:dgCol title="预算科目名称" field="accountName" query="true"></t:dgCol>
			<t:dgCol title="上级预算科目编号" field="parentCode" query="true"></t:dgCol>
			<t:dgCol title="上级预算科目名称" field="parentName" query="true"></t:dgCol>
	  		<t:dgCol title="财务科目ERP编码" field="financialCode" query="true"></t:dgCol>
	  		<t:dgCol title="预算科目类型" field="accountType" dictionary="budget_type" query="true"></t:dgCol>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">

</script>
