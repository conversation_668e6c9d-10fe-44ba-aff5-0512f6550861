<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div data-options="region:'center',split:true" style="border:0px;">
		<t:datagrid name="ttBkwAuditMainList" title="宝旺库结案申请"
					actionUrl="ttBkwAuditMainController.do?findTtBkwAuditMainList"
					idField="id"
					fit="true"
					fitColumns="true"
					pagination="true"
					queryMode="group"
					singleSelect="true">
			<t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
			<t:dgCol field="bpmStatus" title="审批状态" dictionary="bpm_status" query="true" ></t:dgCol>
			<t:dgCol field="billCode" title="结案申请单号" query="true"  ></t:dgCol>
			<t:dgCol field="billName" title="结案申请名称" query="true"  ></t:dgCol>
			<t:dgCol field="auditTypeName" title="结案类型" query="true"></t:dgCol>

			<t:dgCol field="createName" title="结案申请人"   ></t:dgCol>
			<t:dgCol field="updateDate" title="结案申请时间" formatter="yyyy-MM-dd hh:mm:ss"  ></t:dgCol>
			<t:dgCol field="remark" title="备注"   ></t:dgCol>


			<t:dgToolBar title="创建结案申请" operationCode="add" icon="icon-add" url="ttBkwAuditMainController.do?goTtBkwAuditMainForm" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="ttBkwAuditMainController.do?goTtBkwAuditMainForm" funname="auditMainUpdate"></t:dgToolBar>
			<%--<t:dgToolBar title="附件" operationCode="upload" icon="icon-upload" url="" funname="fileUploadTotal"></t:dgToolBar>--%>
			<t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="ttBkwAuditMainController.do?deleteTtBkwAuditMain" funname="deleteALLAuditMainSelect"></t:dgToolBar>
			<t:dgToolBar title="流程日志" operationCode="log" icon="icon-log"  url="ttBkwAuditMainController.do?goTtBkwAuditMainLogMain"  funname="detail" width="1200"></t:dgToolBar>
			<t:dgToolBar title="提交审批" operationCode="conventional" icon="icon-ok" url="ttBkwAuditMainController.do?goTtBkwAuditWorkFlowSubmitMain" funname="submitLeave"></t:dgToolBar>
		</t:datagrid>

	</div>
	<div style="height:350px;border-left:0px" data-options="region:'south',split:true">
		<t:datagrid name="ttBkwAuditList" fit="true" fitColumns="true" singleSelect="true"
					title="宝库旺费用结案子单"
					queryMode = "group"
					actionUrl="ttBkwAuditController.do?findTtBkwAuditList"
					idField="id"
		            autoLoadData="true">

			<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="附件数量" field="fileNumber"></t:dgCol>
			<t:dgCol title="结案主单主键" field="businessKey" hidden="true"></t:dgCol>
			<t:dgCol title="审批状态"  field="bpmStatus" dictionary="bpm_status"></t:dgCol>
			<t:dgCol field="billCode" title="结案申请单号"></t:dgCol>
			<t:dgCol title="子单编码"  field="auditCode" query="true"></t:dgCol>
			<t:dgCol title="流程类型"  field="actTypeName"></t:dgCol>
			<t:dgCol title="活动编号"  field="actCode" query="true"></t:dgCol>

			<t:dgCol title="活动名称"  field="actName" query="true"></t:dgCol>
			<t:dgCol title="客户名称"  field="customerName" query="true"></t:dgCol>
			<t:dgCol title="产品名称"  field="productName" query="true"></t:dgCol>
			<t:dgCol title="活动细类"  field="costAccountName" query="true"></t:dgCol>
			<t:dgCol title="超额核销比"  field="overAuditScale" ></t:dgCol>

			<t:dgCol title="活动开始时间" field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
			<t:dgCol title="活动结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>

			<t:dgCol title="活动申请金额(元)"  field="planAmount"></t:dgCol>
			<t:dgCol title="本次结案金额(元)(含税)"  field="applyAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
			<t:dgCol title="本次实际结案金额(元)"  field="auditAmount"></t:dgCol>


			<t:dgCol title="支付方式"  field="paymentName"></t:dgCol>
			<t:dgCol title="结案状态"  field="auditStatus" editor="{type:'combobox'}" dictionary="audit_bkw_status"></t:dgCol>

			<t:dgToolBar title="添加费用明细"  operationCode="chadd" url="ttBkwAuditController.do?goTtBkwActSelectMain"  funname="goSelectBkwActAdd"  icon="icon-add"   ></t:dgToolBar>
			<t:dgToolBar title="附件" operationCode="chupload" icon="icon-upload" url="" funname="fileUpload"></t:dgToolBar>
			<t:dgToolBar title="保存" operationCode="chsave" icon="icon-save" onclick="saveBkwAuditForm()" ></t:dgToolBar>
			<t:dgToolBar title="移除" operationCode="chremove" icon="icon-remove" url="ttBkwAuditController.do?deleteTtBkwAudit" funname="deleteALLAuditSelect"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="chdataout" icon="icon-dataOut" url="ttBkwAuditController.do?exportCustomerXls" funname="excelCustomerExport"></t:dgToolBar>
		</t:datagrid>
	</div>

</div>
<script type="text/javascript">
	$(function () {
        $("input[name='auditTypeName']").combobox({
            url : 'ttBkwAuditMainController.do?getProcessComb&typeCode=process_audit_bkw_type',
            width:'280',
            panelHeight : '300'
        });
        $('#ttBkwAuditMainList').datagrid({
            onClickRow: function(index,row){
                var auditMainTarget = $("#ttBkwAuditMainList").datagrid("getSelected");
                $("#ttBkwAuditListtb_r").find(":input").val("");
                $('#ttBkwAuditList').datagrid('load',{
                    billMainId: auditMainTarget.id
                });

            }
        });
        //绑定当行点击事件
        $('#ttBkwAuditList').datagrid({
            onClickRow: function(index,row){
				if(row.bpmStatus == 1 || row.bpmStatus == 4 ||row.bpmStatus == 5 ){
                    editRow(index,row);
				}
            }
        });
    })

    //主单附件
    function fileUploadTotal() {
        var auditQuotaList = $("#ttBkwAuditMainList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttBkwAuditAttachemntController.do?goBkwAuditTotalFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttBkwAuditMainList", 600, 400);
    }
    //附件上传
    function fileUpload() {
        var auditQuotaList = $("#ttBkwAuditList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttBkwAuditAttachemntController.do?goAuditFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttBkwAuditList", 600, 400);
    }

	//保存行编辑数据
    function saveBkwAuditForm(){
        var ttBkwAuditMainTarget = $("#ttBkwAuditMainList").datagrid("getSelections");
        if (!ttBkwAuditMainTarget || ttBkwAuditMainTarget.length == 0) {
            tip('请先选择申请主单，再保存子单');
            return;
        }
        if (ttBkwAuditMainTarget.length > 1) {
            tip('请选择一条记录再保存');
            return;
        }

        if(ttBkwAuditMainTarget[0].bpmStatus == 2 || ttBkwAuditMainTarget[0].bpmStatus == 3){
            tip('该记录正在流程中或者已经审批通过,不能保存');
            return;
        }

        var rows=$("#ttBkwAuditList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttBkwAuditList").datagrid("getRowIndex",row);
            $("#ttBkwAuditList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttBkwAuditList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttBkwAuditController.do?saveBkwAuditByRows",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated)},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"info");
                    $("#ttBkwAuditList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttBkwAuditList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttBkwAuditList").datagrid('getColumnFields',true).concat($("#ttBkwAuditList").datagrid('getColumnFields'));
        for(var i=0; i<fields.length; i++){
            var col = $("#ttBkwAuditList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var planAmount = row.planAmount;
            if (planAmount<0&&fields[i] == "auditStatus"){
                col.editor = null;
            }
        }
        $("#ttBkwAuditList").datagrid('beginEdit', index);

        var editors=$("#ttBkwAuditList").datagrid('getEditors',index);
        $.each(editors,function (index1,editor){

            if(editor.type=="combobox"){
                if(editor.field=="auditStatus"){
                    $(editor.target).focus();
                    $(editor.target).combobox('reload',"tmTableConfigController.do?dictCombox&dictCode=audit_bkw_status");
                }
            }

        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttBkwAuditList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }
    //删除主单
    function deleteALLAuditMainSelect(title, url, gname, deleteCallback) {
        gridname = gname;
        var ids = [];
        var rows = $("#" + gname).datagrid('getSelections');
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定删除此费用申请单吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus == 2 ||rows[i].bpmStatus == 3){
                            tip("费用清单中存在产生了流程的数据,不能删除流程状态为审批中或者审批通过的数据,请检查");
                            return false;
                            break;
                        }
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                reloadTable();
                                $('#ttBkwAuditList').datagrid('load');
                                $("#" + gname).datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要删除的数据");
        }
    }
    //删除子单
    function deleteALLAuditSelect(title, url, gname, deleteCallback) {
        gridname = gname;
        var ids = [];
        var rows = $("#" + gname).datagrid('getSelections');
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定移除该费用明细数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus == 2 ||rows[i].bpmStatus == 3){
                            tip("费用清单中存在产生了流程的数据,不能删除流程状态为审批中或者审批通过的数据,请检查");
                            return false;
                            break;
                        }
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                reloadTable();
                                $("#" + gname).datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要删除的数据");
        }
    }
    //主单单编辑
    function auditMainUpdate(title, url, id, width, height){
        var ttBkwAuditMainTarget = $("#ttBkwAuditMainList").datagrid("getSelections");
        if (!ttBkwAuditMainTarget || ttBkwAuditMainTarget.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (ttBkwAuditMainTarget.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }

        if(ttBkwAuditMainTarget[0].bpmStatus == 2 || ttBkwAuditMainTarget[0].bpmStatus == 3){
            tip('该记录正在流程中或者已经审批通过,不能编辑');
            return;
        }
        update(title, url, id, width, height);
    }
    /**
     * 提交审批 XXX
     */
//    function submitAct(title, url, gname, width, height) {
//        $.dialog({
//            title: "提交流程",
//            content: "url:" + url,
//            lock: true,
//            width: "1200",
//            height: "600",
//            zIndex: 10000,
//            parent: windowapi,
//            ok: function () {
//                $('#btn_sub', this.iframe.contentWindow.document).click();
//                $("#ttBkwAuditMainList").datagrid("reload");
//                $("#ttBkwAuditList").datagrid("reload");
//                return false;
//            },
//            cancelVal: '关闭',
//            cancel: true
//        });
//    }
    //选择宝库旺审批通过的活动
    function goSelectBkwActAdd(){
        var bkwAuditMainTarget = $("#ttBkwAuditMainList").datagrid(
            "getSelected");
        if (bkwAuditMainTarget == null || bkwAuditMainTarget == "") {
            tip("请选择结案申请");
            return false;
        }

        if (bkwAuditMainTarget.bpmStatus == 2
            || bkwAuditMainTarget.bpmStatus == 3) {
            tip("该结案申请单正在审批中或者已经审批完成,不能新增费用结案子单");
            return false;
        }
        $.dialog({
            title: "添加宝库旺活动",
            content: "url:ttBkwAuditController.do?goTtBkwActSelectMain&billMainId="+bkwAuditMainTarget.id,
            lock: true,
            width: "1200",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var result = iframe.submitForm();
                $("#ttBkwAuditList").datagrid("reload");
                return result;
            },
            cancelVal: '关闭',
            cancel: function () {
                $("#ttBkwAuditList").datagrid("reload");
            }
        });
    }
    //客户导出
    function excelCustomerExport() {

        var auditMainTarget = $("#ttBkwAuditMainList").datagrid("getSelected");

        if (auditMainTarget == null ||  auditMainTarget== "") {
            tip("请选择宝库旺结案申请");
            return false;
        }

        var queryParams = $('#ttBkwAuditList').datagrid('options').queryParams;
        $('#' + 'ttBkwAuditList' + 'tb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';
        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#' + 'ttBkwAuditList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = "ttBkwAuditController.do?exportCustomerXls";
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.location.href = tagetUrl + encodeURI(fields + params);
    }
    //提交审批
    function submitLeave() {
        var rowsData = $('#ttBkwAuditMainList').datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择提交项目',"error");
            return;
        }
        if(rowsData.length > 1){
            tip("请选择一条数据进行提交","error");
            return;
        }
        if(!(rowsData[0].bpmStatus == "1" || rowsData[0].bpmStatus == "4"||rowsData[0].bpmStatus == "5")){
            tip("处于流程中或者已经审批完成的数据不能再次提交","error");
            return;
        }
        var param = {processKeyType:'process_audit_bkw_type'};

        jblSubmitDialog1(rowsData[0].id,rowsData[0].billName,rowsData[0].remark,"com.biz.eisp.tpm.audit.bkw.controller.workflow.TtBkwAuditWorkFlowController",JSON.stringify(param),rowsData[0].auditTypeCode);
    }
    function jblSubmitDialog1(bussinessKey,type,detail,path,params,auditTypeCode){
        detail = (detail==null || detail=="null")?"无":detail;
        var myOptions = {
            content : "url:jlbTaProcessThemeController.do?goNewTaProcessTheme&businessKey="+bussinessKey+
            "&type="+encodeURIComponent(type)+"&detail="+encodeURIComponent(detail)+
            "&fullPathName="+path+"&params="+encodeURIComponent(params)+"&processKey="+encodeURIComponent(auditTypeCode),
            lock : true,
            width : 600,
            height : 350,
            title : "提交",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                iframe = this.iframe.contentWindow;
                return false;
            } ,
            button: [
                {
                    name: "提交",
                    callback: function(){
                        var iframe = this.iframe.contentWindow;
                        var result= iframe.doSubmit();
                        if(result){
                            $("#ttBkwAuditMainList").datagrid("reload");
                            $("#ttBkwAuditList").datagrid("reload");
                        }
                        return result;
                    }
                }
            ]
        };
        safeShowDialog1(myOptions);
    }
    function safeShowDialog1(option) {
        var width = option.width;
        var height = option.height;
        var offsetW = window.top.document.body.offsetWidth;
        var offsetH = window.top.document.body.offsetHeight;
        width = (width&&width!='null') ? width : 700;
        height = (height&&height!='null') ? height : 400;
        if (width == "100%" || width > offsetW) {
            width = offsetW;
        }
        if (height == "100%" || height > (offsetH-100)) {
            height = offsetH - 100;
        }
        $.extend(option,{width:width,height:height});
        if (typeof(windowapi) === 'undefined') {
            return $.dialog(option).zindex();
        } else {
            return W.$.dialog($.extend(option, {parent: windowapi})).zindex();
        }
    }
</script>