<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="border-left:0px">
        <t:datagrid name="ttAuditQuotaList" fit="true" fitColumns="true" singleSelect="true"
                    title="城市结案明细"
                    queryMode = "group"
                    actionUrl="ttAuditQuotaCityController.do?findTtAuditQuotaCityList"
                    idField="id"
                    autoLoadData="true">

            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="核销总单ID" field="billMainId" hidden="true"></t:dgCol>


            <t:dgCol title="部门编码"  field="orgCode" hidden = "true"></t:dgCol>
            <t:dgCol title="活动发布要求" field="actDeployRequire" hidden="true"></t:dgCol>
            <t:dgCol title="结案主单主键" field="businessKey" hidden="true"></t:dgCol>

            <t:dgCol title="审批状态"  field="bpmStatus" query="true" dictionary="bpm_status" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="是否到门店"  field="containTerminal" dictionary="yesorno" query ="true" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="部门名称"  field="orgName" frozenColumn = "true" ></t:dgCol>
            <t:dgCol title="客户名称"  field="customerName" query="true" frozenColumn = "true"></t:dgCol>


            <t:dgCol title="活动类型"  field="actModeCode" query = "true" dictionary="audit_act_mode_type" frozenColumn = "true"></t:dgCol>
            <t:dgCol title="活动编号"  field="actCode" query = "true"></t:dgCol>
            <t:dgCol title="活动名称"  field="actName" query = "true" ></t:dgCol>
            <t:dgCol title="产品名称"  field="productName" query="true"></t:dgCol>
            <t:dgCol title="活动细类"  field="costAccountName" query = "true"></t:dgCol>
            <t:dgCol title="活动开始时间" field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="活动申请数量"  field="planQuantity"></t:dgCol>
            <t:dgCol title="活动申请金额"  field="planAmount"></t:dgCol>
            <t:dgCol title="超额核销比(%)"  field="overAuditScale" ></t:dgCol>
            <t:dgCol title="申请结案数量 "  field="applyAuditQuantity" editor="{type:'numberbox',options:{precision:1,min:0}}"></t:dgCol>
            <t:dgCol title="申请结案金额"  field="applyAuditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
            <t:dgCol title="归属事业部"  field="businessUnitName"></t:dgCol>
            <t:dgCol title="审核结案数量"  field="auditQuantity"></t:dgCol>
            <t:dgCol title="审核结案金额"  field="auditAmount"></t:dgCol>
            <t:dgCol title="最终结案金额"  field="realAuditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>


            <t:dgCol title="支付方式"  field="paymentCode" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="货补产品"  field="premiumProductName"></t:dgCol>
            <t:dgCol title="结案状态"  field="auditStatus" editor="{type:'combobox'}" dictionary="audit_bkw_status"></t:dgCol>
            <t:dgCol title="结案申请单号" field="billCode" query="true"></t:dgCol>
            <t:dgCol title="结案明细编码"  field="auditCode" ></t:dgCol>
            <t:dgCol title="附件数量" field="fileNumber" ></t:dgCol>
            <t:dgCol title="备注" field="workFlowRemark"  editor="{type:'text'}"></t:dgCol>
            <t:dgCol title="结案类型" field="auditTypeName" query="true"></t:dgCol>
            <t:dgCol title="结案申请人" field="billMaincreateName"  query="true"></t:dgCol>
            <t:dgToolBar title="门店结案金额录入" operationCode="chaddterminal" url=""  funname="goSelectQuotaTerminalAdd"  icon="icon-add"></t:dgToolBar>
            <t:dgToolBar title="查看门店结案金额" operationCode="chlookterminal" url="ttAuditQuotaController.do?goTtQuotaTerminalSelectedMain" funname="terminalDetail"  icon="icon-look"  width = "1500" height = "500"></t:dgToolBar>
            <t:dgToolBar title="保存" operationCode="chsave"  icon="icon-save" url="" funname="saveQuotaAuditForm"></t:dgToolBar>
            <t:dgToolBar title="附件" operationCode = "chupload" icon="icon-upload" url="" funname="fileUpload"></t:dgToolBar>
            <t:dgToolBar title="客户导出" operationCode = "chcustdataout" icon="icon-dataOut" url="ttAuditMainExportController.do?exportCustomerXls" funname="excelCustomerExport"></t:dgToolBar>
            <t:dgToolBar title="门店导出" operationCode = "chterminaldataout" icon="icon-dataOut" url="ttAuditMainExportController.do?exportTerminalIncludeImgXls" funname="excelTerminalExport"></t:dgToolBar>
            <t:dgToolBar title="查看客户图片" operationCode = "chlookimg" url=""  funname="detailPicture"  icon="icon-look"  width = "1000" height = "500"></t:dgToolBar>
			<t:dgToolBar title="流程日志" operationCode="log" icon="icon-log"  url="ttAuditMainLogController.do?goTtAuditMainLogMain"  onclick="showLogDetail()" width="1200"></t:dgToolBar>
        </t:datagrid>
    </div>

</div>
<script type="text/javascript">
    $(function () {
        $('#ttAuditQuotaMainList').datagrid({
            onClickRow: function(index,row){
                var auditMainTarget = $("#ttAuditQuotaMainList").datagrid("getSelected");
                $("#ttAuditQuotaListtb_r").find(":input").val("");
                $('#ttAuditQuotaList').datagrid('load',{
                    billMainId: auditMainTarget.id
                });
            }
        });
        //绑定当行点击事件
        $('#ttAuditQuotaList').datagrid({
            onClickRow: function(index,row){
                if(row.bpmStatus == 1 || row.bpmStatus == 4 ||row.bpmStatus == 5 ){
                    editRow(index,row);
                }
            }
        });
    })
    //子单图片
    function detailPicture(){
        var auditQuotaList = $("#ttAuditQuotaList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var id = auditQuotaList[0].id;
        var containTerminal = auditQuotaList[0].containTerminal;
        var actModeCode = auditQuotaList[0].actModeCode;
        if(containTerminal == "1"){
            tip("到门店活动，请点击查看门店结案金额查看照片","error");return false;
        }
        if(auditQuotaList[0].actModeCode == 'product_act_type'){
            tip("产品活动没有查看图片","error");return false;
        }

        if(auditQuotaList[0].actModeCode == 'ad_act_type'){
            tip("广告费活动没有查看图片","error");return false;
        }
        var url = "ttAuditQuotaPictureController.do?goActPhotoWall&auditId="+id;
        openwindow("查看图片", url,"ttAuditQuotaList", 1000, 600);
    }
    //主单附件
    function fileUploadTotal() {
        var auditQuotaList = $("#ttAuditQuotaMainList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditTotalFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttAuditQuotaMainList", 600, 400);
    }
    //附件上传
    function fileUpload() {
        var auditQuotaList = $("#ttAuditQuotaList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttAuditQuotaList", 600, 400);
    }
    //保存行编辑数据
    function saveQuotaAuditForm(){
        var rows=$("#ttAuditQuotaList").datagrid("getRows");
        for(var i = 0;i<rows.length;i++){
            var bpmStaus = rows[i].bpmStaus;
            if(bpmStaus == 2 || bpmStaus == 3){
                tip('该记录正在流程中或者已经审批通过,不能保存',"error");
                return false;
            }
        }
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditQuotaList").datagrid("getRowIndex",row);
            $("#ttAuditQuotaList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttAuditQuotaList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttAuditQuotaController.do?saveCityQuotaAuditByRows",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated)},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"error");
                    $("#ttAuditQuotaList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditQuotaList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditQuotaList").datagrid('getColumnFields',true).concat($("#ttAuditQuotaList").datagrid('getColumnFields'));
        var subStr  = "30";
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var containTerminal = row.containTerminal;
            var actModeCode = row.actModeCode;
            var planAmount = row.planAmount;
            if((fields[i] == "applyAuditQuantity")&&(actModeCode=="product_act_type"||actModeCode=="ad_act_type"||actModeCode=="longterm_act_type")){
                col.editor = null;
            }
            if ((containTerminal== "1")&&(fields[i] == "applyAuditAmount"||fields[i] == "applyAuditQuantity")){
                col.editor = null;
            }
            if (planAmount<0&&fields[i] == "auditStatus"){
                col.editor = null;
            }
        }
        $("#ttAuditQuotaList").datagrid('beginEdit', index);
        var editors=$("#ttAuditQuotaList").datagrid('getEditors',index);
        $.each(editors,function (index1,editor){
            if(editor.type=="combobox"){
                if(editor.field=="auditStatus"){
                    $(editor.target).focus();
                    $(editor.target).combobox('reload',"tmTableConfigController.do?dictCombox&dictCode=audit_bkw_status");
                }
            }
            if(editor.field=="applyAuditAmount"){
                editor.target.bind('change',function () {
                    var str = editor.target.val();
                    editors[index1+1].target.val(str);
                });
            }
        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }

    //选择定额审批通过的活动
    function goSelectQuotaTerminalAdd(){
        var ttAuditQuotaTarget = $("#ttAuditQuotaList").datagrid(
            "getSelected");
        if (ttAuditQuotaTarget == null ||  ttAuditQuotaTarget== "") {
            tip("请选择定额费用结案子单");
            return false;
        }
        if (ttAuditQuotaTarget.bpmStatus == 2
            || ttAuditQuotaTarget.bpmStatus == 3) {
            tip("该定额费用结案子单正在审批中或者已经审批完成,不能新增费用结案子单");
            return false;
        }

        var containTerminal = ttAuditQuotaTarget.containTerminal;
        if (containTerminal == "0"){
            tip("该定额费用结案子单不能到门店，不能选择门店");
            return false;
        }
        $.dialog({
            title: "添加活动门店",
            content: "url:ttAuditTerminalController.do?goAuditTerminalMain&auditId="+ttAuditQuotaTarget.id,
            lock: true,
            width: "1500",
            height: "500",
            zIndex: 1000,
            parent: windowapi,
            /*ok: function () {
                iframe = this.iframe.contentWindow;
                var result = iframe.submitForm(iframe);
                if(result){
                    tip("操作成功","info");
                }
                $("#ttAuditQuotaList").datagrid("reload");
                return result;
            },*/
            cancelVal: '关闭',
            cancel: function () {
                $("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }

    //门店详情
    function terminalDetail(title, url, id, width, height) {
        var ttAuditQuotaTarget = $("#ttAuditQuotaList").datagrid(
            "getSelected");
        if (ttAuditQuotaTarget == null ||  ttAuditQuotaTarget== "") {
            tip("请选择费用结案子单");
            return false;
        }

        var containTerminal = ttAuditQuotaTarget.containTerminal;
        if (containTerminal== "0"){
            tip("该费用结案子单不能到门店，不能查看门店");
            return false;
        }
        url += '&load=detail&auditId=' +ttAuditQuotaTarget.id;
        createwindow(title, url, width, height);
    }

    //客户导出
    function excelCustomerExport() {
        var queryParams = $('#ttAuditQuotaList').datagrid('options').queryParams;
        $('#' + 'ttAuditQuotaList' + 'tb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';
        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#' + 'ttAuditQuotaList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = "ttAuditQuotaCityController.do?exportCustomerXls";
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.location.href = tagetUrl + encodeURI(fields + params);
    }
    //门店导出
    function excelTerminalExport() {

        var auditMainTarget = $("#ttAuditQuotaList").datagrid("getSelected");

        if (auditMainTarget == null ||  auditMainTarget== "") {
            tip("请选择一条结案子单申请");
            return false;
        }
        var containTerminal = auditMainTarget.containTerminal;


        if (containTerminal== "0"){
            tip("该结案的活动不到门店，不能导出！");
            return false;
        }
        var tagetUrl = "ttAuditMainExportController.do?exportTerminalIncludeImgXls&auditId="+auditMainTarget.id;
        $.dialog.confirm('是否导出图片?', function(){
            var tagetUrl = "ttAuditMainExportController.do?exportTerminalIncludeImgXls&auditId="+auditMainTarget.id;
            //菜单id
            var accessEntry=$("#accessEntry").val();
            if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
                tagetUrl+="&accessEntry="+accessEntry;
            }
            window.location.href = tagetUrl;
        }, function(){
            var tagetUrl = "ttAuditMainExportController.do?exportTerminalXls&auditId="+auditMainTarget.id;
            //菜单id
            var accessEntry=$("#accessEntry").val();
            if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
                tagetUrl+="&accessEntry="+accessEntry;
            }
            window.location.href = tagetUrl;

        });
    }
    
    function showLogDetail() {
        var rowsData = $('#ttAuditQuotaList').datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择查看项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再查看');
            return;
        }
        var url = 'ttAuditMainLogController.do?goTtAuditMainLogMain&load=detail&id=' + rowsData[0].billMainId;
        openwindow('查看', url,'ttAuditQuotaListLog', 850, 500);
    }
</script>