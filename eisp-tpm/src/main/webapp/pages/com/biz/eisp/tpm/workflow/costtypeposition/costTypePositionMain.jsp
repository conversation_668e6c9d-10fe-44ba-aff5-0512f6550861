<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="CostTypePositionList" fitColumns="false" title="活动大类职位关系"
                    actionUrl="costTypePositionController.do?findCostTypePositionList" idField="id" fit="true">

            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动大类名称" field="costTypeName" query="true" width="150"></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="职位名称" field="positionName" query="true" width="150"></t:dgCol>
            <t:dgCol title="职位编码" field="positionCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="创建人" field="createName"  width="150"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss"  width="150"></t:dgCol>

            <t:dgToolBar operationCode="add" title="新增" height="500" width="800" icon="icon-add" url="costTypePositionController.do?goCostTypePositionForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑" height="500" width="800" icon="icon-edit" url="costTypePositionController.do?goCostTypePositionForm" funname="update"></t:dgToolBar>
            <t:dgToolBar operationCode="del" title="刪除"  height="500" width="800" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>
            <t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'costTypePositionImport', gridName:'CostTypePositionList'})"></t:dgToolBar>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="costTypePositionController.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $("#TsInvoicingListtb_r").find("input[name='systemDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    $(function () {
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='createDate']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='periodStart']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='periodEnd']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });
    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#CostTypePositionList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "costTypePositionController.do?deleteCostTypePosition&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#CostTypePositionList").datagrid("reload");
                    }
                });
            }
        });
    }
</script>
