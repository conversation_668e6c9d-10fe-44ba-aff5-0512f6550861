<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    iframe {
        height: 480px!important;
    }
    label {
        font-size: 15px;
    }
    .l-btn-left{
        font-weight: bold;
        color:red;
        font-family: 微软雅黑;
    }
</style>
<%@include file="showReferenceData.jsp" %>

<div style="clear:both; width: 1250px;height: 160px;">

        <t:datagrid name="tsActApplyVoList" fitColumns="false"  pagination="false" title=""
                    actionUrl="ttActOutUploadController.do?findActWorkFlowData&businessId=${businessId}" idField="id" fit="true" >
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true"></t:dgCol>
            <t:dgCol title="活动单号" field="actCode" hidden="false" align="center"></t:dgCol>
            <t:dgCol title="活动类型" field="actType" hidden="false" dictionary="act_type" align="center"></t:dgCol>
            <t:dgCol title="制作类型" field="adType" hidden="false" dictionary="ad_type" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="adCode" hidden="false" align="center"></t:dgCol>

            <t:dgCol title="广告公司手机" field="mobilephone" hidden="false" align="center"></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" align="center"></t:dgCol>
            <t:dgCol title="广告发布地址1（现场选址）" field="gpsAddress" width="250" ></t:dgCol>
            <t:dgCol title="广告发布地址2（非现场选址）" field="detailRemark" width="210" ></t:dgCol>

            <t:dgCol title="经销商" field="createName" hidden="false" width="190" align="center"></t:dgCol>
            <t:dgCol title="申请日期" field="createDate" hidden="false" formatter="yyyy-MM-dd" align="center"></t:dgCol>
            <%--t:dgCol title="更新人" field="updateName" hidden="false" width="100" align="center"></t:dgCol>
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyMMdd HH:mm" align="center"></t:dgCol>
            <t:dgCol title="店铺名称" field="terminalName"  width="90" align="center"></t:dgCol>
            <t:dgCol title="店铺编号" field="terminalCode" hidden="false" align="center"></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="false"  dictionary="bpm_status"></t:dgCol--%>
            <t:dgToolBar title="查看照片小视频" icon="icon-search" url="tsPictureController.do?findPictureListByHW" funname="queryPic" ></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频(新)" icon="icon-bcbgrid" url="tsPictureController.do?goPicList" funname="goPostPicture"></t:dgToolBar>
            <t:dgToolBar title="查看审批意见" icon="icon-bcbgrid" url="ttActOutUploadController.do?goAuditpage" funname="goAuditpage"></t:dgToolBar>

        </t:datagrid>
</div>

<%@include file="advMaterialInfo.jsp" %>

<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>


<div id="outerdiv2" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv2" style="position:absolute;">
        <

        <video src="" id="biVideo" style='width: 120px;height:80px;cursor: pointer;controls:controls;' ></video>
    </div>
</div>

<c:if test="${inclodeJspShow eq true}">
    <%@include file="showSuspiciousOpinionsJXS.jsp" %>
</c:if>

<%@include file="keyindicator_P06_init_js.jsp" %>

<%--</div>--%>
<script>
    function goAuditpage(title, url, id, width, height) {

        var processInstanceId =  window.parent.document.getElementById("processInstanceId").value;
        var rowsData = $('#' + id).datagrid('getRows');
        url += '&id=' + processInstanceId;
        $.dialog({
            title: "查看审批意见",
            content: "url:" + url,
            lock: true,
            width: "1250",
            height: "450",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function goPostPicture(title, url, id, width, height) {
        $('#picISReaded', window.parent.document).attr("value",1);
        var rowsData = $('#' + id).datagrid('getRows');
        url += '&id=' + rowsData[0].id;
        $.dialog({
            title: "照片小视频",
            content: "url:" + url,
            lock: true,
            width: "1250",
            height: "700",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function goPostPicture_BK(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 1250, 700);
    }

    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getRows');
        url += '&id=' + rowsData[0].id;
        $.dialog({
            title: "照片小视频",
            content: "url:" + url,
            lock: true,
            width: "800",
            height: "500",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    //@Override
    function returnShowReferenceDataHtml(objs) {
        var html = "";
    /*    for(var obj in objs){
            if(html != ''){
                html += "\t|\t"
            }
            html += '<label>' + obj + ':<span style="font-size: 15px;color: red;">' + objs[obj] + '</span></label>';
        }*/
        html = '<label>可用积分:<span style="font-size: 15px;color: red;">'+objs['可用积分']+'</span></label>';
        html += '\t<label>&nbsp;&nbsp;&nbsp;&nbsp;(授信积分:<span style="font-size: 15px;">' + objs['授信积分'] + '</span>&nbsp;&nbsp;&nbsp;&nbsp;\t|\t&nbsp;&nbsp;&nbsp;&nbsp;CRMS待审核:'+objs['CRMS待审核']+'&nbsp;&nbsp;&nbsp;&nbsp;\t|\t&nbsp;&nbsp;&nbsp;&nbsp;本系统待审核:'+objs['本系统待审核']+')</label><br>';
        html += '<label>本单提报:<span style="font-size: 15px;color: red;">'+objs['本单提报']+'</span>&nbsp;&nbsp;&nbsp;&nbsp;广告基金账户:'+objs['广告基金账户']+'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\t|\t&nbsp;&nbsp;&nbsp;&nbsp;广告门头未报销:'+objs['广告门头未报销']+'&nbsp;&nbsp;&nbsp;&nbsp;\t|\t&nbsp;&nbsp;&nbsp;&nbsp;本批次可报销:'+objs['本批次可报销']+'</label><br>';
        //html += '<label>本单实报:<span style="font-size: 15px;color: red;">'+objs['本单实报']+'</span>&nbsp;&nbsp;&nbsp;&nbsp;(本单提报:'+objs['本单提报']+'&nbsp;&nbsp;&nbsp;&nbsp;\t|\t&nbsp;&nbsp;&nbsp;&nbsp;报销比例:'+objs['报销比例']+'%)\t<span style="font-size: 15px;color: red;">&nbsp;&nbsp;&nbsp;&nbsp;注意:该行数据以审核后为准!</span></label>';
        //html += '<label>本单提报:<span style="font-size: 15px;color: red;">'+objs['本单提报']+'</span></label>';

        return html;
    }
    function showInfo(value,row) {
        // if(row.imgType==165){
        //     var url="tsPictureController.do?download&id="+row.id;
        //     return "<a href="+url+">点击下载</a>"
        // }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;

            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }



    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }


    function videoBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        $.dialog({
            title: "视频查看",
            content: "url:tsPictureController.do?showVideo&path="+src,
            lock: true,
            width: "680",
            height: "560",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }


    function showBigPic(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $.dialog({
            title: "图片查看",
            content: "url:tsPictureController.do?showBigPic&path="+src,
            lock: true,
            width: "800",
            height: "450",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            cancelVal: '关闭',
            cancel: true
        });
    }


    //填写意见--重写--Override
    function editTheSuspOpin(title, url, gname, callback){
        //获取数据
        var rowData = getTheRowData(gname,getTaskCode());

        var html = returnShowHtml();
        var title = "选址重复";
        $.dialog({
            title: title,
            content: html,
            lock: true,
            width: "380",
            height: "200",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            init : function(){
                var parentTemp = parent.parent.parent;
//                parentTemp.$('#tempIsSuspicious').val(rowData.isSuspicious);
                parentTemp.$('#tempMsg').val(rowData.opinion);
            },
            ok : function() {
                var parentTemp = parent.parent.parent;
                /*var isSuspicious = parentTemp.$('#tempIsSuspicious').val();
                 if(!checkIsNotUndefinedAndNullAndNullValue(isSuspicious)){
                 alert('是否可疑不能为空');
                 return false;
                 }*/
                var opinion = parentTemp.$('#tempMsg').val();
                if(!checkIsNotUndefinedAndNullAndNullValue(opinion)){
                    alert('意见不能为空');
                    return false;
                }
                var tempData = {
                    title : title,
                    opinion : opinion,
                    isSuspicious : -1
                }
                //开始保存
                return starEditTheSuspOpin(gname,rowData,url,tempData);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    //Override
    function returnShowHtml() {
        var html = "";
        /*html += '<div class="form"><label class="Validform_label" name="tempIsSuspicious"><span style="color: red;">*</span>是否可疑：</label> ';
         html += structureRadio();*/
        html += ' </div>';
        html += '<div class="form" style="margin-top: 10px;"><label class="Validform_label" style="position: relative;left: 0;top: 0;font-size: 15px; float: left; ">意见：</label><textarea id="tempMsg" rows="10" cols="40"></textarea><div>';
        return html;
    }


    function getBusinessKey(){
        return '${businessKey}';
    }

    function getFlagKey(){
        return '${flagKey}';
    }

    function getTaskCode(){
        return '${taskCode}';
    }

    function getSrcType(){
        return '${srcType}';
    }

    function ajaxPost(json,url){
        var json;
        $.ajax({
            url:url,
            data:json,
            dataType:'json',
            async:false,
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }



</script>
