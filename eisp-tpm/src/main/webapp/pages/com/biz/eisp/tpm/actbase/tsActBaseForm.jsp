<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>高铁普列广告基础信息维护</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="tsActBaseController.do?saveTsActBase" refresh="true">
	<input type="hidden" id="id" name="id" value="${vo.id}"/>
		<div class="form">
			<label class="Validform_label">合作公司: </label>
			<input  name="partnerCompany" id="partnerCompany"
					datatype="*"  class="inputxt" value="${vo.partnerCompany}" />
			<span style="color: red;">*</span>
		</div>

		<%--<div class="form">--%>
			<%--<label class="Validform_label">活动类型 : </label>--%>
			<%--<input  name="advType" id="advType"--%>
					<%--datatype="*"  class="inputxt" value="${vo.advType}" />--%>
			<%--<span style="color: red;">*</span>--%>
		<%--</div>--%>


	<div class="form">
		<label class="Validform_label" >活动类型:</label>
		<select name="advType" id="advType" datatype="*">
			<!-- <option value="">--请选择--</option> -->
			<c:forEach items="${actTypes}" var="type">
				<option value="${type.actType}" <c:if test="${vo.advType== type.actType}">selected="selected"</c:if>>${type.actTypeName}</option>
			</c:forEach>
		</select>
		<span style="color: red">*</span>
	</div>

		<div class="form">
			<label class="Validform_label">合同广告数量 : </label>
			<input  name="contractNum" id="contractNum"
					datatype="*"  class="inputxt" value="${vo.contractNum}" />
			<span style="color: red;">*</span>
		</div>

	<div class="form">
		<label class="Validform_label">合同金额(元) : </label>
		<input  name="contractAmount" id="contractAmount"
				datatype="*"  class="inputxt" value="${vo.contractAmount}" />
		<span style="color: red;">*</span>
	</div>

		<div class="form">
			<label class="Validform_label">合同开始日期 : </label>
			<input  name="startDate" id="startDate"
					datatype="*"  class="inputxt" value="${vo.startDate}"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
			<span style="color: red;">*</span>
		</div>


		<div class="form">
			<label class="Validform_label">合同结束日期 : </label>
			<input  name="endDate" id="endDate"
					datatype="*"  class="Wdate"  value="${vo.endDate}"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
			<span style="color: red;">*</span>
		</div>

</t:formvalid>
</body>
</html>
<script type="text/javascript">


</script>