<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="xx" title="关联活动大类列表"  actionUrl="ttFinancialAccountController.do?findFinancialAccountCostTypeList&accountCode=${accountCode}" 
	  		 idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		<t:dgCol title="编码" field="id" hidden="true"></t:dgCol>
	  		<t:dgCol title="活动大类编号" field="costTypeCode"></t:dgCol>
	  		<t:dgCol title="活动大类名称" field="costTypeName"></t:dgCol>
	  		<t:dgCol title="生效状态" field="enableStatus" dictionary="enable_status"></t:dgCol>
		</t:datagrid>
	</div>
</div>
