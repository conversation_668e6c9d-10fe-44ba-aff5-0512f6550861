<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
.datagrid-toolbar-search form div label{
	width:120px;
}
</style>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="useSendCopyList" title="预算科目管理"  actionUrl="ttFinancialAccountController.do?findFinancialAccountFiltrationEnableStateList"
	  		 idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		 
	  		<t:dgCol title="编码" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="预算科目层级" field="levelCode" dictionary="financial_level" query="true"></t:dgCol>
	  		<t:dgCol title="预算科目编号" field="accountCode" query="true"></t:dgCol>
	  		<t:dgCol title="预算科目名称" field="accountName" query="true"></t:dgCol>
			<t:dgCol title="上级预算科目编号" field="parentCode" query="true"></t:dgCol>
			<t:dgCol title="上级预算科目名称" field="parentName" query="true"></t:dgCol>
	  		<t:dgCol title="财务科目ERP编码" field="financialCode" query="true"></t:dgCol>
	  		<t:dgCol title="预算科目类型" field="accountType" dictionary="budget_type" query="true"></t:dgCol>
	  		<t:dgCol title="备注" field="remark" query="true"></t:dgCol>
	  		<t:dgCol title="生效状态" field="enableStatus" dictionary="enable_status" query="true"></t:dgCol>
	  		<t:dgCol title="创建人" field="createName"></t:dgCol>
	  		<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		 
			<t:dgToolBar operationCode="new" title="录入预算科目" icon="icon-add" url="ttFinancialAccountController.do?goFinancialAccountForm" funname="add"></t:dgToolBar>
			<t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttFinancialAccountController.do?goFinancialAccountForm&" funname="update"></t:dgToolBar>
			<t:dgToolBar operationCode="del" title="删除" icon="icon-edit" url="" funname="deleteTarget"></t:dgToolBar>
			<t:dgToolBar operationCode="start" title="启用" icon="icon-start" url="" funname="updateState(0)"></t:dgToolBar>
			<t:dgToolBar operationCode="stop" title="停用" icon="icon-stop" url="" funname="updateState(1)"></t:dgToolBar>
			<t:dgToolBar operationCode="view" title="查看关联活动大类" icon="icon-look" url="" funname="viewCostType()"></t:dgToolBar>
			<t:dgToolBar operationCode="out" title="导出"  icon="icon-dataOut" url="ttFinancialAccountController.do?exportXls"  funname="excelExport"></t:dgToolBar>
			<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttFinancialAccountController.do?goAccountCostTypeLogMain" funname="detailLog" width="1200" height="460"></t:dgToolBar>
			
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
function viewCostType(gridname){
	var seletctTarget =  $("#useSendCopyList").datagrid("getSelections");
	if(seletctTarget==null || seletctTarget==""){
		tip("请至少选择一条数据");
		return false;
	}
	var accountCode = seletctTarget[0].accountCode;
	var url = "ttFinancialAccountController.do?goFinancailAccountCostTypeMain&accountCode="+accountCode;
	openwindow('关联活动大类列表',url,gridname,650,400);
}

//删除
function deleteTarget(){
    var seletctTarget =  $("#useSendCopyList").datagrid("getSelections");
    var title = "";
    if(seletctTarget==null || seletctTarget==""){
        tip("必须选择一条数据");
        return;
    }
	$.messager.confirm('操作提示','确定删除?',function(r){
		if (r){
			$.ajax({
				type : "POST",
				url : "ttFinancialAccountController.do?deleteAccountById&id="+seletctTarget[0].id,
				async:false,
				success : function(data) {
					var d = $.parseJSON(data);
					tip(d.msg);
					$("#useSendCopyList").datagrid("reload");
				}
			});
		}
	});
}

//更新
function updateState(state){
	var seletctTarget =  $("#useSendCopyList").datagrid("getSelections");
	if(seletctTarget==null || seletctTarget==""){
		tip("请至少选择一条数据");
		return false;
	}
	var id = seletctTarget[0].id;
	var code = seletctTarget[0].accountCode;
	var url = "ttFinancialAccountController.do?updateEnableStatus&enableStatus="+state+"&id="+id;
	var tipMsg = null;
	if(state == 0){
		tipMsg = "确定要启用？";
		if(seletctTarget[0].enableStatus == state){
			tip("已经处于启用状态,无需再次启用");
			return false;
		}
	}else{
		//停用
		var flag = true;
		var stopUrl = "ttFinancialAccountController.do?validateAccountStop&accountCode="+code;
		$.ajax({url:stopUrl,type:"post",async:false,success:function(data){
			var d = $.parseJSON(data);
			if(d.success == false){
				tip(d.msg);
				flag = false;
			}
		}});
		if(!flag){
			return false;
		}
		if(seletctTarget[0].enableStatus == state){
			tip("已经处于停用状态,无需再次停用");
			return false;
		}
		tipMsg = "确定要停用？";
	}
	$.messager.confirm('操作提示',tipMsg,function(r){
		if(r){
			$.ajax({
				url:url,
				type:"post",
				success:function(data){
					 var d = $.parseJSON(data);
					 var msg = d.msg;
		             if(d.success == true){
			             tip(msg);
			             $("#useSendCopyList").datagrid("reload");
		             }
				}
			});
		}
	});
}
</script>
