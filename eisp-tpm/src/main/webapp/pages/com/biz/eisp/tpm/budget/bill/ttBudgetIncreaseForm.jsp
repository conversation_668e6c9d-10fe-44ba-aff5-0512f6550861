<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title><c:if test="${addOrSub == '1'}">预算追加</c:if><c:if test="${addOrSub == '-1'}">预算削减</c:if></title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<input type="hidden" value="${budgetVo.id}" id="id">
<t:formvalid formid="budgetIncrease" layout="div" dialog="true" action="ttBudgetBillController.do?saveBudgetAddOrSubAmount" refresh="true" >
		<input type="hidden" value="${billVo.adjustFlag}" id="addOrSub" name="adjustFlag">
		<div class="form">
			<label class="Validform_label">年度:</label>
			<input name="year" class="inputxt" style="width: 150px" value="${billVo.year}" readonly="readonly">
		</div>
		<div class="form">
			<label class="Validform_label">季度:</label>
			<input name="quarter" class="inputxt" style="width: 150px" value="${billVo.quarter}" readonly="readonly">
		</div>		
		<div class="form">
			<label class="Validform_label" name="custName">部门: </label> 
			<input class="inputxt" value="${billVo.orgName}" name="orgName" readonly="readonly"/>
			<input type="hidden" name="orgCode" value="${billVo.orgCode}">
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">费用类型: </label> 
			<input class="inputxt" value="${billVo.costTypeName}" name="costTypeName" readonly="readonly"/>
			<input type="hidden" name="costTypeCode" value="${billVo.costTypeCode}">
		</div>

		<div class="form">
			<label class="Validform_label" name="areaName">期初金额: </label> 
			<input name="periodAmount" class="inputxt" value="${billVo.periodAmount}" readonly="readonly"/>
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">可用余额: </label> 
			<input id="accountName" class="inputxt" value="" readonly="readonly"/>
		</div>
		
		<div class="form">
			<label class="Validform_label">
			<c:if test="${billVo.adjustFlag == 1}">追加金额</c:if><c:if test="${billVo.adjustFlag == -1}">削减金额</c:if>:</label> 
			<c:if test="${billVo.adjustFlag == 1}">
				<input id="adjustAmount" type="text" name="adjustAmount" class="inputxt" 
				datatype="/^([1-9]\d*(\.\d*[1-9])?)|(0\.\d*[1-9])?$/"
				errormsg="只能是带两位小数或者整数或者位数不能超过12位"/>
			</c:if>
			<c:if test="${billVo.adjustFlag == -1}">
				<input id="adjustAmount" name="adjustAmount" class="inputxt" onblur="checkAmount()" datatype="/^([1-9]\d*(\.\d*[1-9])?)|(0\.\d*[1-9])?$/" errormsg="只能是带两位小数或者整数或者位数不能超过12位"/>
			</c:if>
			<span style="color: red">*</span>
			<span class="Validform_checktip" id="error"></span>
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">备注: </label> 
			<textarea style="width:150px;height:100px;resize:none;" name="remark"></textarea>
		</div>		
</t:formvalid>
</body>
</html>
<script type="text/javascript">
function checkAdd(){
	var num = $("#adjustAmount").val();
	if(num == ""){
		$("#error").addClass("Validform_wrong");
		$("#error").attr("title","追加金额不能为空");
	}
}
function checkAmount(){
	var usableNum = $("#accountName").val();
	var subNum = $("#adjustAmount").val();
	if(subNum == ""){
		$("#error").addClass("Validform_wrong");
		$("#error").attr("title","削减金额不能为空");
	}
	if(parseFloat(subNum) > parseFloat(usableNum)){
		$("#error").addClass("Validform_wrong");
		$("#error").attr("title","削减金额不能大于可用金额");
		return false;
	}
}
</script>