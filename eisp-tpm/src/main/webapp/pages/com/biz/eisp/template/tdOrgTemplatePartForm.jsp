<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<link rel="stylesheet" href="resources/uploadify/css/uploadify.css" type="text/css">
<script type="text/javascript" src="resources/uploadify/jquery.uploadify-3.1.js"></script>


<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
                 action="tdOrgTemplatePartController.do?saveTdOrgTemplatePart">
        <input type="hidden" name="id" value="${orgTemplatePartVo.id}">

        <div>
            <div class="form">
                <label class="Validform_label">归属事业部: </label>
                <t:comboBox name="orgCode" url="tmCommonMdmController.do?findOrgCombox"
                            defaultVal="${orgTemplatePartVo.orgCode }" required="false" width="150"></t:comboBox>
            </div>
            <div class="form">
                <label class="Validform_label">所属模块:</label>
                <input class="inputxt" type="hidden"  name="tplPartTag" id="tplPartTag" value="${orgTemplatePartVo.tplPartTag}">
                <input class="inputxt"  name="tplPartName" id="tplName" value="${orgTemplatePartVo.tplPartName}">
                <input class="inputxt" type="hidden" name="tplPartId" id="tplPartId" value="${orgTemplatePartVo.tplPartId}">
                <t:choose hiddenName="tplPartId" hiddenid="tplId" url="tdOrgTemplatePartController.do?goTdTemplateOrgConfigMain"
                          name="tdOrgTemplatePartChoose" icon="icon-search"
                          title="模板列表" textname="tplPartName,tplPartTag" inputTextname="tplName,tplPartTag" isclear="true"
                          width="500" height="400"></t:choose>
                <span style="color: red">*</span>
            </div>


        </div>
    </t:formvalid>
</div>
<script type="text/javascript">

</script>