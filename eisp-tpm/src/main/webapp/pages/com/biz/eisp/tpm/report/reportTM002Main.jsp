<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div id="terminal" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="terminalList" title="终端管理" queryMode="group"  actionUrl="reportTM002Controller.do?findReportList"
                    extendTableName="${extendTableName}"   idField="id" fit="true"  fitColumns="false" pagination="true">

            <!--自定义非显示的查询条件 begin-->
            <t:dgCol title="对接人职位" field="dockPosition"  columnOrder="16" query="true"  hidden="false"  ></t:dgCol>
            <t:dgCol title="对接人姓名" field="dockUserName" columnOrder="17" query="true"  hidden="false"  ></t:dgCol>
            <t:dgCol title="业务组" field="businessGroup"   columnOrder="18"  dictionary="business_group"  hidden="true"  ></t:dgCol>
            <t:dgCol title="上级客户" field="customerName" columnOrder="19" query="true"  hidden="false"  ></t:dgCol>
            <!--自定义非显示的查询条件 end-->

            <!-- 工具栏操作  begin -->
            <t:dgToolBar title="查看" icon="icon-look" url="tmTerminalController.do?goTmTerminal&optype=2" funname="detail" width="1500" height="500"></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-dataOut" url="reportTM002Controller.do?exportXls" funname="excelExport" ></t:dgToolBar>
            <!-- 工具栏操作  begin -->

        </t:datagrid>
    </div>
</div>
<c:if test="${not empty includeJsp}">
    <jsp:include page="${includeJsp}" flush="true"></jsp:include>
</c:if>
<script type="text/javascript">
</script>