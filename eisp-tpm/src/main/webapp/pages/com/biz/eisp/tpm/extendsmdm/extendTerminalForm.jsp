<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<script type="text/javascript">
	$(function(){
		$("#extChar1").attr("readonly","readonly"); //零售大区
		$("#extChar2").attr("readonly","readonly"); //零售大区名称
		$("#extChar25").attr("readonly","readonly"); //零售大区编码
		
		var extChar1 = $("#extChar1").val();
		$.ajax({
			url:"tmCustomerExtendMdmController.do?getCustName&code="+extChar1,
			type:"post",
			success:function(data){
				var d = $.parseJSON(data);
				var input = "<input style='width:150px' id='extChar1Name' class='inputxt' value='"+d.obj+"' readonly='readonly'>";
				$("#extChar1").parent().append(input);
				$("#extChar1").hide();
				$("#extChar1Name").click(function(){
					openCustomerSelectMain();
				});

			}
		});
		
	});
	
	function openCustomerSelectMain() {
		safeShowDialog({
			content : "url:tmTerminalExtendMdmController.do?goTmCustOrgCustomerSelectMain",
			lock : true,
			showSpeed:10000,
			title : "信息",
			width : 550,
			height : 350,
			left :'85%',
			cache : false,
			cancelVal : '关闭',
			button:[{
				name:"确定",
				callback:function(){
					iframe = this.iframe.contentWindow;
		               var r = iframe.$("#custOrgList").datagrid("getSelected");
		               if(r == null) {
		            	   $("#extChar1").val("");//零售系统名称
			               $("#extChar1Name").val("");//零售系统id
			               $("#cb_extChar2").combobox('clear');
			               $("#extChar2").val("");
			               $("#cb_extChar2").combobox('reload',"tmTerminalExtendMdmController.do?findTmCustSubList&id=-11");
			               $("#extChar14").val("");
		               }else{
			               $("#extChar1Name").val(r.customerOrgName);//零售系统名称
			               $("#extChar1").val(r.custOrgCode);//零售系统id
			               $("#cb_extChar2").combobox('clear');
			               $("#extChar14").val("");
						   var dqUrl = "tmTerminalExtendMdmController.do?findTmCustSubList&id="+r.id;
						   $("#cb_extChar2").combobox("reload",dqUrl);
		               }
				}
			}],
			cancel : true
		});
	}
	$(function(){
        $("#cb_extChar15").combobox("reload","tmCustomerExtendMdmController.do?findOrgList");

        $("#cb_extChar15").combobox({
            onSelect:function(node){
                $("#extChar15").val(node.value);
                $("#ct_orgId").combotree('clear');
            }
        });

		$("#cb_extChar2").combobox({
			onSelect:function(node){
				$("#extChar2").val(node.text);
				$("#extChar14").val(node.value);
			}
		});
	});

</script>