<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>上传设计稿</title>
</head>
<t:base type="jquery,easyui,tools"></t:base>
<body style="overflow-y: hidden" scroll="no">
<div region="center" fit="true">

    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="upload"
                >
        <input id="id" name="id" type="hidden"
               value="${actApplyVo.id}">
        <input id="actCode" name="actCode" type="hidden"
               value="${actApplyVo.actCode}">
        <div class="form">
	         <div class="dtci_bottom">
				<t:uploadH5 name="file_upload" buttonText="选择文件"
                             dialog="false" onUploadSuccess="uploadForSet" callback=""
                            uploader="ttActOutUploadController.do?saveOutfile&businessId=${actApplyVo.businessId}"
                            extend="*.*"
                            id="file_upload" formData=""></t:uploadH5>
	         	<div id="filediv" style="width: 300px;"></div>
	         </div>
			</span>
                </div>
            </div>

        </div>

    </t:formvalid>



</div>
</body>
</html>
<script type="text/javascript">
    function uploadForSet(d,file,response) {
        if (d.success) {
            $.ajax({
                url: "ttActOutUploadController.do?saveWorkFlow&id=" + $("#id").val()+"&actCode="+ $("#actCode").val(),
                method: "post",
                success: function (data) {
                    var data = $.parseJSON(data);
                    debugger;
                    if (data.success) {
                        frameElement.api.opener.reloadTable();
                        frameElement.api.close();
                    }
                    tip(d.msg);
                }
            })
      }else{
            W.tip(d.msg);
        }
    }

</script>
