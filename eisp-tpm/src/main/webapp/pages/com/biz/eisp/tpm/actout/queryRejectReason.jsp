<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>


<div class="easyui-layout" fit="true">

    <div region="center" style="padding:1px;">

        <t:datagrid name="rejectReasonList"
                    fitColumns="true" checkbox="false"
                    title="" actionUrl="ttActOutUploadController.do?queryRejectReasonV2&id=${id}&actCode=${actCode}"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">

            <t:dgCol title="驳回时间" align="center" field="createDate" hidden="false" formatter="yy-MM-dd HH:mm:ss" query="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="驳回原因" align="center"  field="rejectReason" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="驳回人员" align="center"  field="resName" hidden="false" sortable="false" ></t:dgCol>

        </t:datagrid>
    </div>
</div>
