<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttMonthlyPlanGatherList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
	      autoLoadData="false" actionUrl="ttMonthlyPlanController.do?findTtMonthlyPlanGatherList" onClick="clickMonthlyPlanFun" onLoadSuccess="onLoadSuccessFun">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="组织编码" field="orgCode" hidden="true" sortable="false" ></t:dgCol>
	        <t:dgCol title="组织名称" field="orgName" hidden="true" sortable="false" ></t:dgCol>
	        <t:dgCol title="客户编码" field="customerCode"  query="true" sortable="false" ></t:dgCol>
	        <t:dgCol title="客户名称" field="customerName"  query="true" sortable="false" ></t:dgCol>
	        <t:dgCol title="计划年份" field="year" query="true" sortable="false" ></t:dgCol>
	        <t:dgCol title="计划月份" field="month"  query="true" sortable="false" ></t:dgCol>
	        <t:dgCol title="挑战版收入预算(元)" field="tiaoZhanRevenueBudget" sortable="false" formatterjs="numExtend" ></t:dgCol>
	        <t:dgCol title="计划总额（元）" field="totalPlanSales" sortable="false" formatterjs="numExtend" ></t:dgCol>
	        <t:dgCol title="是否可编辑" field="editableStatus" replace="可编辑_1,不可编辑_0" sortable="false" ></t:dgCol>
	        <t:dgCol title="状态" field="bpmStatus" query="true" dictionary="monthlyPlan_Bpm_status" sortable="false" ></t:dgCol>
			<t:dgCol title="备注" field="remark" sortable="false" ></t:dgCol>
			<t:dgToolBar title="录入" id="addMonthPlanId" operationCode="addMonthPlan" icon="icon-add" url="ttMonthlyPlanController.do?goMonthlyPlanAddOrUpdate" onclick="addMonthlyPlan(${heplatFormCSJL})" ></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="updateMonthPlan" icon="icon-edit" url="ttMonthlyPlanController.do?goMonthlyPlanAddOrUpdate" onclick="updateMonthlyPlan(${heplatFormCSJL})" ></t:dgToolBar>
			<t:dgToolBar title="部平台编辑" operationCode="thePlatformUpdate" icon="icon-edit" url="ttMonthlyPlanController.do?goMonthlyPlanAddOrUpdate" onclick="updateMonthlyPlan(${heplatFormBpt})" ></t:dgToolBar>
			<%--<t:dgToolBar title="查看" operationCode="detailMonthPlan" icon="icon-look" url="ttMonthlyPlanController.do?goMonthlyPlanAddOrUpdate" onclick="updateMonthlyPlan(1)" ></t:dgToolBar>--%>
			<t:dgToolBar title="删除" operationCode="deleteMonthPlan" icon="icon-remove" url="ttMonthlyPlanController.do?delMonthlyPlan" funname="ownDeleteALLSelect" ></t:dgToolBar>
			<t:dgToolBar title="提交" operationCode="submitMonthPlan" icon="icon-ok" onclick="submitMonthlyPlan(${heplatFormCSJL})"></t:dgToolBar>
			<t:dgToolBar title="部平台提交" operationCode="thePlatformSubmit" icon="icon-ok" onclick="submitMonthlyPlan(${heplatFormBpt})"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="exportMonthPlan" icon="icon-dataOut" url="ttMonthlyPlanController.do?exportXls" funname="excelExport" ></t:dgToolBar>
		</t:datagrid>
	</div>
	<div data-options="region:'east',
	title:'明细',
	collapsed:true,
	split:true,
	border:false"
	 style="padding:1px;width:850px;">
		<t:datagrid name="ttMonthlyPlanList" fitColumns="true" queryMode = "group" idField="id" singleSelect="false"
		pagination="true" autoLoadData="false" actionUrl="ttMonthlyPlanController.do?findTtMonthlyPlanList">
	     	<t:dgCol title="主键" field="id" hidden="true" sortable="false"  ></t:dgCol>
	      	<t:dgCol title="产品编号" field="productCode" query="true" sortable="false" ></t:dgCol>
	  		<t:dgCol title="产品名称" field="productName"  query="true" sortable="false" ></t:dgCol>
	  		<t:dgCol title="原供价（元）" field="originalPrice" sortable="false"  ></t:dgCol>
			<t:dgCol title="单价（元）" field="price" sortable="false"  ></t:dgCol>
			<t:dgCol title="价格差" field="priceDifference" sortable="false"  ></t:dgCol>
	  		<t:dgCol title="计划销量（EA）" field="planSales" sortable="false"  ></t:dgCol>
	  		<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="环比（元）" field="sequential" sortable="false" formatterjs="numExtend" ></t:dgCol>
	  		<t:dgCol title="计划销额（元）" field="totalPlanSales" sortable="false" formatterjs="numExtend" ></t:dgCol>
	  		<t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false"  ></t:dgCol>
	  		<t:dgCol title="吨位" field="tonnage" sortable="false" ></t:dgCol>
	  		<t:dgCol title="发起人" field="createName" sortable="false" ></t:dgCol>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
    var li_east = 0;
    $(document).ready(function(){
        //给时间控件加上样式
        //$("#ttMonthlyPlanGatherListForm").find("input[name='year']").attr("readonly",true).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:'yyyy',onpicked:function(){chooseYearAndMonthSearch(); }});});
        //$("#ttMonthlyPlanGatherListForm").find("input[name='month']").attr("readonly",true).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:'MM',onpicked:function(){chooseYearAndMonthSearch(); }});});
        addTimeCss("ttMonthlyPlanGatherList","year","yyyy");
        addTimeCss("ttMonthlyPlanGatherList","month","MM");
        initTotalMoneyShow('***');
        searchReset('ttMonthlyPlanGatherList');
    });
    function defaultYear(){
        return '${year}'
    }
    function defaultMonth(){
        return '${month}'
    }
    //区分城市经理
    function returnHeplatFormCSJL(){
        return '${heplatFormCSJL}';
	}
    function initInputValue(){
        $("#year").val(defaultYear());
        $("#month").val(defaultMonth());
    }
    function  addTimeCss(grid,inputName,dataFmt){
        var inputObj = returnInputObj(grid,inputName);
        inputObj.attr("readonly",true).attr("id",inputName).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:dataFmt,onpicked:function(){chooseYearOmonth(); }});});
        var preObj = inputObj.parent().find('label');
        preObj.html(preObj.html() + "<span style='color:red'>*</span>");
	}
    function initTotalMoneyShow(total){
        var str = '<div style="color:red;float: right;margin-top: 5px;margin-left: 50px;"><label><b>销售额汇总:</b>&nbsp;</label><span id="showTotalAmount" >' + total + '</span>&nbsp;&nbsp;元&nbsp;</div>';
        $("#addMonthPlanId").parent().find('a').last().after(str);
        //$("#addMonthPlanId").parent().find('a:last-child').after(str);
    }
	//返回datagrid的input的search框对象
	function returnInputObj(grid,inputName){
        return $("#" + grid + "Form").find("input[name='" + inputName + "']");
	}
	function clickMonthlyPlanFun(rowIndex,rowData) {
        if(li_east == 0){
            $('#ttReportMonthlyPlanMain').layout('expand','east');
            li_east ++;
        }
		var customerCode = rowData.customerCode;
		var year = rowData.year;
		var month = rowData.month;
		var queryParams = $('#ttMonthlyPlanList').datagrid('options').queryParams;
        queryParams.customerCode = customerCode;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthlyPlanList").datagrid({url:"ttMonthlyPlanController.do?findTtMonthlyPlanList"});
	}
	//录入月度计划提报
	function addMonthlyPlan(n){
		ownCreatewindowExt("月度计划提报录入","ttMonthlyPlanController.do?goMonthlyPlanAddOrUpdate&doingNum=" + n,'',"1100","650");
	}
	//编辑月度计划提报
	function updateMonthlyPlan(n){
		var rowsData = $('#ttMonthlyPlanGatherList').datagrid('getSelections');
		if (rowsData.length != 1) {
			tip("请选择一条项目");
			return ;
		}
		var data = rowsData[0];
		if (data.editableStatus != 1) {
			tip("该条项目不可编辑");
			return ;
		}
        var thisDatas = {
            customerCode : data.customerCode,
            customerName : data.customerName,
            orgCode : data.orgCode,
            orgName : data.orgName,
            year : data.year,
            month : data.month,
            doingNum : n
        }
		//检查是否可编辑
        if(checkIsCuldUpdate(thisDatas)){
            ownCreatewindowExt("月度计划提报编辑","ttMonthlyPlanController.do?goMonthlyPlanAddOrUpdate&isUpdate=1",thisDatas,"1100","650");
		}
	}
	//检查是否可编辑
	function checkIsCuldUpdate(thisDatas){
		var url = "ttMonthlyPlanController.do?checkIsCuldUpdate";
        var d = ajaxPost(thisDatas,url)
		if (d.success){
			return true;
		}
		tip(d.msg);
		return false;
    }

	function ownCreatewindowExt(title,url,data,width,height){
	    url += changeDataToUrlData(data);
		createwindowExt(title,url,width,height,{
			ok : function() {
				var iframeTemp = this.iframe.contentWindow;
				iframeTemp.saveMonthlyPlanData();
				return false;
			},
			cancelVal : '关闭',
			cancel : true
		});
	}
	//提交
	function submitMonthlyPlan(n){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var thisDatas = {
            year : year,
            month : month,
            doingNum : n
		}

		//检查是否可以提交
		if(checkTheDataIsCouldSubmit(thisDatas)){
			var tips = returnConfirmTips(thisDatas);
            url = "ttMonthlyPlanController.do?submitMonthlyPlan";
            ajaxRequest({tips:tips + '确定提交本部门当前所有数据吗 ?',url:url,params:thisDatas,callbackfun:function(data){
                var d = $.parseJSON(data);
                if(d.success == true) {
                    ttMonthlyPlanGatherListSearchFunction();
                }
                tip(d.msg);
            }});
		}
	}

	function returnConfirmTips(thisDatas){
	    var showMsg = thisDatas.showMsg;
		if(checkIsNotUndefinedAndNullAndNullValue(showMsg)){
			return showMsg + "，";
		}
		return '';
	}

	//检查是否可提交
	function checkTheDataIsCouldSubmit(thisDatas) {
	    var url = "ttMonthlyPlanController.do?checkTheDataIsCouldSubmit";
		var d = ajaxPost(thisDatas,url);
		if (d.success){
            thisDatas.showMsg = d.obj;
			return true;
		}
		tip(d.msg);
		return false;
    }
    //选择年月之后的处理
	function chooseYearOmonth(){
        var year = $('#year').val();
        if(year == ''){
            return ;
        }
        var month = $('#month').val();
        if(month == ''){
            return ;
        }
        ttMonthlyPlanGatherListSearchFunction();
	}

	//查询
	function ttMonthlyPlanGatherListSearchFunction(){
		var year = $('#year').val();
		if(year == ''){
			tip("年份不能为空");
			return ;
		}
		var month = $('#month').val();
		if(month == ''){
			tip("月份不能为空");
			return ;
		}
		var customerCode = $('#customerCode').val();
		var customerName = $('#customerName').val();
		
		var queryParams = $('#ttMonthlyPlanGatherList').datagrid('options').queryParams;
        queryParams.customerCode = customerCode;
        queryParams.customerName = customerName;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthlyPlanGatherList").datagrid({url:"ttMonthlyPlanController.do?findTtMonthlyPlanGatherList"});
	}
	
	var num = 0;
	//重置
	function searchReset(name){
	    if('ttMonthlyPlanGatherList' == name){
            initInputValue();
            returnInputObj('ttMonthlyPlanGatherList','customerCode').val(' ');
            returnInputObj('ttMonthlyPlanGatherList','customerName').val(' ');
            num = 1;
            setTimeout("ttMonthlyPlanGatherListSearchFunction()",0);
		}else{
            searchResetOwn(name);
		}
	}
    function searchResetOwn(name) {
        $("#"+name+"Form").find(":input").val("");
        eval(name+"search()");
    }
	//加载成功后处理
	function onLoadSuccessFun(data){
		if (num == 1) {
            returnInputObj('ttMonthlyPlanGatherList','customerCode').val('');
            returnInputObj('ttMonthlyPlanGatherList','customerName').val('');
			changeToDisable(false);
		}else{
			changeToDisable(true);
		}
        getAllTotalAmount();
	}
	//汇总销售额
	function getAllTotalAmount(){
	    debugger;
        var thisDatas = $('#ttMonthlyPlanGatherList').datagrid('options').queryParams
        var url = "ttMonthlyPlanController.do?getAllTotalAmount";
		var d = ajaxPost(thisDatas,url)
		if (d.success){
            $('#showTotalAmount').html(accounting.formatMoney(d.obj,""));
			return true;
		}
		tip(d.msg);
		return false;
	}
	//改变年月可重选状态
	function changeToDisable(trueOrFalse){
//		$("#year").attr("disabled",trueOrFalse);
//		$("#month").attr("disabled",trueOrFalse);
		num = 0;
	}
	//设置值
	function setVlaue(id,obj){
		$('#' + id).val(obj);
	}

    function ownDeleteALLSelect(title, url, gname, deleteCallback) {
        var year = $('#year').val();
        if(year == ''){
            tip("年份不能为空");
            return ;
        }
        var month = $('#month').val();
        if(month == ''){
            tip("月份不能为空");
            return ;
        }
        var rows = $("#" + gname).datagrid('getSelections');
		if(rows.length <= 0){
 			tip("请选择要删除的项目!");
 			return ;
		}
		var customerCodes = [];
		for( var i = 0; i < rows.length ; i ++ ){
		    var dataTemp = rows[i];
            customerCodes.push(dataTemp.customerCode);
		}

        var thisDatas = {
            year : year,
            month : month,
            customerCode: customerCodes.join(",")
		}

		//检查月度计划是否可操作
		if(checkMonthlyPlanIsCouldDo(thisDatas)){
            var d = ajaxPost(thisDatas,url)
            if (d.success){
                reloadPage();
                return true;
            }
            tip(d.msg);
            return false;
		}
    }

    function reloadPage(){
        $("#ttMonthlyPlanGatherList").datagrid("reload");
        $("#ttMonthlyPlanList").datagrid("reload");
	}
    //检查是否可操作
    function checkMonthlyPlanIsCouldDo(datas){
	    var url = "ttMonthlyPlanController.do?checkMonthlyPlanIsCouldDo";
        var d = ajaxPost(datas,url)
        if (d.success){
            return true;
        }
        tip(d.msg);
		return false;
	}
	//-------------------------共调fun

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }
</script>
