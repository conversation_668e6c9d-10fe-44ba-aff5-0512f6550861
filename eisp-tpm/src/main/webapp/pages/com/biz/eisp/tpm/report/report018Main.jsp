<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report018List" fitColumns="false" title="定额类结案统计"
                    pagination="false" autoLoadData="false" actionUrl="report018Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth" hidden="true" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="申请日期" field="xx" hidden="true" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="业务费用归类" field="xx" hidden="true" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" width="400"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName" width="150" sortable="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="活动名称" field="actName" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="政策编码" field="policyCode" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="政策名称" field="policyName" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="政策内容" field="policyContent" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="预算科目编码" field="financialCode" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="预算科目" field="financialName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="活动大类名称" field="costTypeName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="活动细类编码" field="costAccountCode" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="活动细类名称" field="costAccountName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="开始时间" field="beginTime" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="客户级别" field="customerLevel" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="产品层级编码" field="xx" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="产品层级名称" field="xx" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="产品编码" field="productCode"  width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="产品名称" field="productName"  width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="原供价(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销量1(EA)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额1(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销量2(EA)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额2(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="当期费用(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="当期费率" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="后返费用(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="后返费率" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="货补产品" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="终端执行方式" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="备注" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="实际达成销量" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="实际达成销售额" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="达成率" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="当期费用(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="后返费用(元)" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="后返费率" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="支付方式" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="货补产品" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="上帐金额" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="未上帐金额" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="反转时间" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="金额" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="支付方式" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="货补产品" field="xx" width="150"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report018Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report018Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report018Listsearch() {
        var orgCode = $("#report018Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report018Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report018List").datagrid('options').queryParams;
        $("#report018Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report018List").datagrid({url:'report018Controller.do?findReportList'});
    }

</script>
