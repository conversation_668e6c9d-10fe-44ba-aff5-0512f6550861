<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>产品政策计算</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="beforeSubmitFn" action="ttProductCalculateController.do?saveToAccounting" callback="callbackFn@Override">
    <div class="form">
        <label class="Validform_label">事业部:</label>
        <select name="orgCode">
            <c:forEach items="${orgList}" var="i">
                <option value="${i.orgCode}">${i.orgName}</option>
            </c:forEach>
        </select>
    </div>
</div>
</t:formvalid>
</div>

</body>
</html>

<script type="text/javascript">

    function beforeSubmitFn() {
        window.top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });
    }

    function callbackFn(data) {
        window.top.$.messager.progress("close");

        var win = frameElement.api.config.parent;
        if(win == null || win == "" || win=="undefined"){
            win = frameElement.api.opener;
        }else{
            win = frameElement.api.config.parent.content;
        }
        if (data.success == true) {
            createwindowExt("", "", "", "", {
                id:'',
                content : "<div style='text-align:center'>"+data.msg+"</div><style>.ui_content{height:100%;display:block;overflow:auto;}</style>",
                lock : true,
                width : 550,
                height : 400,
                title : "提示",
                opacity : 0.3,
                cache : true,
                async: false,
                okVal: '确定',
                ok : true,
                cancelVal : '关闭',
                cancel : true
            });
            frameElement.api.close();
        } else {
            if (data.responseText == '' || data.responseText == undefined) {
                $.messager.alert('错误', data.msg, 'error');
                $.Hidemsg();
            } else {
                try {
                    var emsg = data.responseText.substring(data.responseText.indexOf('错误描述'), data.responseText.indexOf('错误信息'));
                    $.messager.alert('错误', emsg, 'error');
                    $.Hidemsg();
                } catch(ex) {
                    $.messager.alert('错误', data.responseText + "", 'error');
                    $.Hidemsg();
                }
            }
            return false;
        }

        win.reloadTable();
    }
</script>