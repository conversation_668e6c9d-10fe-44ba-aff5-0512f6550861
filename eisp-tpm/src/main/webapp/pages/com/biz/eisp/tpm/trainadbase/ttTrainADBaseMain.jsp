<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="trainADBaseList" title="高铁普列广告基础信息"  actionUrl="trainADBaseController.do?findTrainADBaseList"
	  		 checkbox="true" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="合同编号" sortable="false" field="contractCode" query="true" width="200" ></t:dgCol>
			<t:dgCol title="合作公司" sortable="false" field="partnerCompany" query="true" width="200" ></t:dgCol>
			<t:dgCol title="签订车(底/次)号" field="trainBtNumber" sortable="false" query="true" width="200"></t:dgCol>
			<%--<t:dgCol title="车次号" field="trainNumber" query="true" sortable="false" width="200"></t:dgCol>--%>
			<t:dgCol title="合同签订日期" field="startDate" sortable="false"  width="200" ></t:dgCol>
			<t:dgCol title="广告有效月数" field="advEffectPeriod" sortable="false"  width="80" ></t:dgCol>

			<t:dgCol title="开始日期" field="pactStartDate" sortable="false" query="true" queryMode="group"  formatter="yyyy-MM-dd" hidden="true"></t:dgCol>
			<t:dgCol title="理论失效日期"  field="endDate" sortable="false" width="200" ></t:dgCol>
			<%--<t:dgCol title="车次开始时间"  field="trainStartDate" sortable="false" width="200" ></t:dgCol>
			<t:dgCol title="车次结束日期"  field="trainEndDate" sortable="false" width="200" ></t:dgCol>--%>
			<t:dgCol title="状态" field="enableStatus" sortable="false" dictionary="train_base_status" width="200"></t:dgCol>
			<t:dgCol title="上刊验收通过日期" field="publicationPassDate" sortable="false"  width="200"></t:dgCol>
			<t:dgOpenOpt width="800" height="400" title="上刊预览" url="trainADBaseController.do?goPhotoInfoUp&id={id}"/>
			<t:dgCol title="上刊备注" field="publicationRemark" sortable="false"  width="200"></t:dgCol>
			<t:dgCol title="实际失效日期" field="invalidRealDate" sortable="false"  width="200"></t:dgCol>

			<t:dgCol title="下刊验收通过日期" field="downPubPassDate" sortable="false" width="200"></t:dgCol>
			<t:dgOpenOpt width="800" height="400" title="下刊预览" url="trainADBaseController.do?goPhotoInfoDown&id={id}"/>
			<t:dgCol title="下刊备注" field="downPubRemark" sortable="false"  width="200"></t:dgCol>
			<t:dgCol title="验收材料" field="opt" width="120"></t:dgCol>

			<t:dgToolBar title="创建" icon="icon-add" operationCode="add" url="trainADBaseController.do?gottrainADBaseForm&optype=0" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit"  operationCode="update" url="trainADBaseController.do?gottrainADBaseForm"  funname="update"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteTrainData"></t:dgToolBar>
			<t:dgToolBar operationCode="publication" title="上刊处理"  icon="icon-edit" url="trainADBaseController.do?goTtTrainPublicationForm"  funname="publication"></t:dgToolBar>
			<t:dgToolBar operationCode="downPublication" title="下刊处理"  icon="icon-edit" url="trainADBaseController.do?goTtTrainDownPublicationForm"  funname="downPublication"></t:dgToolBar>
			<t:dgToolBar title="导入" operationCode="dataIn" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'ttTrainAdBase', gridName:'trainADBaseList'})"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
			<t:dgToolBar title="查看日志" icon="icon-log" onclick="showLog('trainADBaseList')" ></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

	function updateInfo() {
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请选择一条数据");
            return;
        }

        if(rows.length>1){
            tip("只能选择一条数据");
            return;
		}
		var row=rows[0];
        var url="";
        var id=row.id;
            $.dialog({
                title: "编辑",
                content: "url:trainADBaseController.do?gottrainADBaseForm&id="+id,
                lock: true,
                width: "500",
                height: "400",
                zIndex: 10000,
                parent: windowapi,
                ok: function () {
                    iframe = this.iframe.contentWindow;
                    var updateform=iframe.$("#formobj");
                    var startdate=updateform.find("input[name='startDate']").val()
                    var endDate=updateform.find("input[name='endDate']").val()
                    //alert(startdate+"lllllllllllllll"+endDate);
                    if(startdate>endDate){
                        tip("合同开始日期必须小于合同结束日期");
                        return false;
					}
                    //updateform.submit();
                    $('#formobj', iframe.document).form('submit', {
                        onSubmit : function() {
                        },
                        success : function(r) {
                            var data =$.parseJSON(r);
                            tip(data.msg);
                            reloadTable();

                        }
                    });
                    //reloadTable();
                    // window.location.reload();




                },
                cancelVal: '关闭',
                cancel: true
            });

    }

    function deleteTrainData() {
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请至少选择一条数据")
		}else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="trainADBaseController.do?deleteTrainADBase";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                // $("#ttAccruedFormulaList").datagrid('reload');
                                // $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                                window.location.reload();
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
		}

    }

    //导出
    function toExcel(){
            excelExport("trainADBaseController.do?exportXls","trainADBaseList");
    }

    function publication(){
        var rowsData = $("#trainADBaseList").datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        var myOptions = {
            content : "url:trainADBaseController.do?goTtTrainPublicationForm&id="+rowsData[0].id,
            lock : true,
            width : 600,
            height : 350,
            title : "上刊处理",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                iframe = this.iframe.contentWindow;
                return false;
            } ,
            button: [
                {
                    name: "提交",
                    callback: function(){
                        var iframe = this.iframe.contentWindow;
                        iframe.doSubmit();
                        return false;
                    }
                }
            ]
        };
        safeShowDialog(myOptions);
	}

	//下刊处理
    function downPublication(){
        var rowsData = $("#trainADBaseList").datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        var myOptions = {
            content : "url:trainADBaseController.do?goTtTrainDownPublicationForm&id="+rowsData[0].id,
            lock : true,
            width : 600,
            height : 350,
            title : "下刊处理",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                iframe = this.iframe.contentWindow;
                return false;
            } ,
            button: [
                {
                    name: "提交",
                    callback: function(){
                        var iframe = this.iframe.contentWindow;
                        iframe.doSubmit();
                        return false;
                    }
                }
            ]
        };
        safeShowDialog(myOptions);
	}

    function showLog(datagridName) {
        var rowData = $('#'+datagridName).datagrid("getSelections");
        if (rowData.length==null||rowData.length<1||rowData.length>1) {
			tip("请选择一行数据进行操作");
			return;
        }
		var id = rowData[0].id;
		var url = "url:logController.do?goLogMain&id="+id;
        safeShowDialog({
            content : url,
            lock : true,
            title : "日志",
            width : 1000,
            height : 500,
            cache : false,
            cancelVal : '关闭',
            cancel : true
        });
    }

</script>
