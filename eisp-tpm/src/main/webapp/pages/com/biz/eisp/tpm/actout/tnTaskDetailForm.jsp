<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<style>
	.classTable{border:1px solid #93b3d3;width:99%;height:auto;margin:0 auto;}
	.classTd{border-left:1px solid #93b3d3;border-bottom:1px solid #93b3d3;}
	.classTd_left{border-left:1px solid #93b3d3;}
	.classTd_right{border-right:1px solid #93b3d3;}
	.classTh{border-bottom:1px solid #93b3d3;word-break:break-all}
	td{padding-left: 5px;height:30px;}
	th{background-color: #ddd;width:143px;height:30px;}
	.classKeyIndicators{height:30px;line-height:30px;border-top:1px solid #ddd;border-bottom:1px solid #ddd;background-color:#ddebf7;text-align:center;font-weight:bold}
	._display{display:none;}
	.bolded{
		color: whitesmoke;
		font-size: 25px;
		font-weight: bold;
		padding-left: 3px;
		font-family: 微软雅黑;
	}
	.formtable {
		margin-bottom: 10px;width:100%;border-color: red;border: dashed;
	}
	.formtable td{
		height: 30px;
		border-collapse: collapse;
		background-color: darkorange;
	}
	.formtable td.section {
		text-align: center;
		color: #345;
		font-size: 16px;
		font-weight: bold;
		height: 33px;
	}
	.formtable td.value{
		background-color: whitesmoke;
	}
	.formtable td.approval {
		width: 10%;
		text-align: center;
	}
	.formtable td.approval div, .formtable td.approval hr {
		margin: 10px;
	}
	.data-table{
		line-height: 30px;
		text-align: center;
		width: 98%;
		margin: 10px 1%;
		background: #c8c8c8;
		border-collapse: collapse;
	}
	.data-table td {
		border: solid 1px #c8c8c8;
		border-collapse: collapse;
		border-spacing: 1px;
	}
	.data-table thead td{
		background: #ddd;
	}
	.data-table tbody td{
		background: #fff;
		word-break: break-all;
	}
	.centerdivBtn{
		text-align: center;
		line-height: 35px;
		margin: 10px 20px;
		background: #ccc;
		cursor: pointer;
	}
	.centerdivBtn:hover{
		background:#bbb;
	}
	.formtable .download td {
		padding: 0 10px;
	}
</style>
<link rel="stylesheet" href="pages/pop/css/bootstrap.min.css">
<script src="pages/pop/js/postbirdAlertBox.js"></script>
<link rel="stylesheet" href="pages/pop/css/postbirdAlertBox.css">
<script src="pages/button/js/main.js"></script>
<link rel="stylesheet" href="pages/button/css/main.css">
<!--div class="classKeyIndicators">业务关键指标</div><br/-->
<c:if test="${not empty nodeStart }">
	<iframe src="${nodeStart}" id="bcbIframe" width="100%" height="445px" FRAMEBORDER=0></iframe>
</c:if>
<c:if test="${empty  nodeStart }">
	无法显示业务指标,因为该流程没有配置业务指标地址
</c:if>
<br/>

<!--div class="classKeyIndicators">审批详情</div><br/-->
<table cellpadding="1" cellspacing="1" class="formtable" >
	<input id="picISReaded" name="picISReaded" type="hidden" value="9"/>
	<input id="haveSaved" name="haveSaved" type="hidden" value="9"/>
	<input id="mjhint" name="mjhint" type="hidden" value="9"/>
	<input id="processInstanceId" name="processInstanceId" type="hidden" value="${processInstanceId}"/>
	<tr>
		<c:if test="${isCommunicate eq 'N'}">
			<td rowspan="4" class="bolded approval">审批意见</td>
		</c:if>

		<td class="value"><span style="float: right">
				<c:if test="${!isView }">
					<c:if test="${nodeAuth.btn1 == 'Y'}">
						<div class="btn-border btn-round btn-green btn6">
							<button onclick="approval()" class="btn btn-green">审批通过</button>
						</div>
						<!--a iconcls="icon-bcbright" class="easyui-linkbutton l-btn" href="#"
						   id="approval" onclick="approval()" style="font-family:微软雅黑; color: green;font-weight: bold;">审批通过</a-->
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					</c:if>

				</c:if>
		</span> <span style="float: left">
		<!-- <a iconcls="icon-putout" class="easyui-linkbutton l-btn" href="#" id="back">驳回上一人</a> -->
<c:if test="${!isView }">
	<c:if test="${nodeAuth.btn8 == 'Y'}">
		&nbsp;&nbsp;
		<div class="btn-border btn-round btn-red btn6">
							<button onclick="rejectToApply()" class="btn btn-red">驳回广告公司</button>
						</div>
		<!--a iconcls="icon-put" class="easyui-linkbutton l-btn" href="javascript:rejectToApply()"
		id="stop" style="font-family:微软雅黑; color: red;font-weight: bold;">驳回广告公司</a-->
	</c:if>
</c:if>
				<c:if test="${hasRejectNodes eq true}">
					<c:if test="${nodeAuth.btn7 == 'Y'}">
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<div class="btn-border btn-round btn-red btn6">
							<button onclick="reject()" class="btn btn-red">指定节点驳回</button>
						</div>
						<!--a iconcls="icon-putout" class="easyui-linkbutton l-btn" href="javascript:reject()"
						   id="back" style="font-family:微软雅黑; color: red;font-weight: bold;">指定节点驳回</a-->&nbsp;&nbsp;
						<label id="couldJumpNodelb">
						<select id="couldJumpNode"
								name="couldJumpNode">
								<option value="">—— 指定驳回节点 ——</option>
								<c:forEach items="${processNode }" var="node">
									<option value="${node.id }">${node.name }</option>
								</c:forEach>
						</select></label>
						<!-- <label id="isBackThisNodelb"><input type="checkbox" id="isBackThisNode" name="isBackThisNode" value="4"> 驳回后返回本节点</label> -->
					</c:if>
				</c:if>
		</span></td>
	</tr>


	<tr>
		<td class="value" style="height: 70%; padding: 2px;"><textarea
				id="comment" name="comment"
				style="width: 100%; height: 70%; min-height: 50px;"></textarea></td>
	</tr>
		<!--tr>
			<td colspan="3" class="value" >
				<a iconcls="icon-see" class="easyui-linkbutton l-btn" href="#" onclick="openCheckTheBusinessFile()">查看附件数据</a>
			</td>
		</tr-->
	<div hidden = "hidden" id="attachmentHidenDiv">
		<div id="attachmentHidenDiv_showFile">
			<a targe="_blank" href="taAttachmentController.do?viewFile&fileid=paramsId "
			   class="easyui-linkbutton"  plain="true" iconCls="icon-download"  >paramsTitleAndExtend</a>
		</div>
		<div id="attachmentHidenDiv_imgShow">
			<img class='imgW' style='width: 120px;height:80px;cursor: pointer;border: black solid 1px;' src="paramsPath" onclick="showBigPic(this)" />
		</div>
		<div id="attachmentHidenDiv_imgDel">
			<img onclick="deleteFile('paramsId')" title="预览图片后，点删除才有效。" src="resources/Validform/images/error.png" />
		</div>
	</div>
		<tr>
			<td colspan="6" class="value" id="showAttachMentTd">
				<%--c:forEach items="${attachment}" var="a">
					<a targe="_blank" href="taAttachmentController.do?viewFile&fileid=${a.id } "
					   class="easyui-linkbutton"  plain="true" iconCls="icon-download"  >${a.attachmentTitle }.${a.extend }</a>
					<c:if test="${currPositionCode eq a.tmPositionEntity.positionCode && !isView}">
						<img onclick="deleteFile('${a.id }')" src="resources/Validform/images/error.png" />
					</c:if>
				</c:forEach--%>
			</td>
		</tr>
	<c:if test="${!isView }">
		<tr id="uploadAttmentsFile">
			<td colspan="6" class="value">
				<div class="dtci_bottom">
					<div id="sureFilesDetail" ></div>
					<t:uploadH5 name="file_upload" buttonText="选择文件" onUploadSuccess="sureUpload" dialog="false" 	 callback=""
								uploader="tbAttachmentController.do?saveFiles&businessKey=${businessKey }&attachmentType=theme" extend="*.*" id="file_upload" formData=""></t:uploadH5>
					<div class="dtci_bottom_a"><a href="javascript:;" class="easyui-linkbutton" plain="true" id="btn_sub" iconCls="icon-upload"
												  onclick="upload()" style="font-family:微软雅黑; color: orangered;font-weight: bold;">确定上传</a></div>
					<div></div>
					<div id="filediv" ></div>
				</div>
			</td>
		</tr>
	</c:if>
</table>
<script>
    var servicePath;
    $(function(){

        getTheSfaPath();
        //展示附件
        showTheAttach();

    });

    function getTheSfaPath(){
        var thisData = {};
        var url = "tmCommonMdmController.do?returnSfaPath";
        var d = ajaxPost(thisData,url);
        if(d.success){
            servicePath = d.obj;
        }
    }

    //去除字符串尾部空格或指定字符
    //去除字符串尾部空格或指定字符
    String.prototype.trimEnd = function(c)
    {
        if(c==null||c=="")
        {
            var str= this;
            var rg = new RegExp(c);
            var i = str.length;
            while (rg.test(str.charAt(--i)));
            return str.slice(0, i + 1);
        }
        else
        {
            var str= this;
            var rg = new RegExp(c);
            var i = str.length;
            while (rg.test(str.charAt(--i)));
            return str.slice(0, i + 1);
        }
    }
    $(function(){
        var list=document.getElementsByClassName("cx");

        for(var i=0;i<list.length;i++){
            var tmp=list[i].innerHTML;
            if(tmp!=null&& tmp!=undefined){
                tmp=tmp.toString().trimEnd(",");

                list[i].innerHTML=tmp;
            }
        }
    });
    function _displayShow() {
        $("#view").hide();
        $("._display").show();
    }

    function _displayHide() {
        $("#view").show();
        $("._display").hide();
    }

    /*===================查看业务数据star================================*/
    function openCheckTheBusinessData() {
        //openwindow("业务数据展示",'taTaskController.do?goTaskBusinessForm&taskId=${taskId }&processInstanceId=${processInstanceId }',null,1200,500);
        createwindowExt(
            '业务数据展示',
            'taTaskController.do?goTaskBusinessForm&taskId=${taskId }&processInstanceId=${processInstanceId }',
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex:11000,
                width : 1200,
                height : 500,
                button : [ {
                    name : '取消',
                    callback : function() {
                    }
                } ]
            });
    }
    /*===================查看业务数据end================================*/

    /*===================查看附件数据star================================*/
    function openCheckTheBusinessFile() {
        var extendServiceFlag = '${extendServiceFlag}';
        if(extendServiceFlag){
            var url = "tbAttachmentController.do?goTbAttachmentWorkFlowMain&taskId="+'${taskId}'
                +"&extendService=tbAttachmentExtendsActService&processInstanceId="+'${processInstanceId }';
            createwindowExt('查看附件数据', url, null, null, {
                ok: false,
                cancelVal: '关闭',
                cancel: true
            });
        }else {
            tip("ttActAndAuditBillMainExtendService方法未实现!");
        }
    }
    /*===================查看附件数据end================================*/


    function circulation() {
        var processInstanceId = '${processInstanceId }';
        var targetUrl = 'taCarbonCopyController.do?goAddCirculationMain&processInstanceId='
            + processInstanceId;
        //弹出窗口
        createwindowExt('传阅', targetUrl, "", "", {
            lock : true,
            parent : windowapi,
            zIndex : 5000,
            width : 1000,
            height : 600,
            button : [
                {
                    name : '确定',
                    callback : function() {
                        //回调事件
                        iframe = this.iframe.contentWindow;
                        var rowsData = iframe.$('#sendCopyList').datagrid(
                            'getRows');
                        var content = iframe.$('#content').val();
                        var codes = [];
                        var names = [];
                        var fullNames = [];
                        for (var i = 0; i < rowsData.length; i++) {
                            codes.push(rowsData[i].positionCode);
                            names.push(rowsData[i].positionName);
                            fullNames.push(rowsData[i].fullName);
                        }
                        var result = callAjax(codes, names, fullNames, content,iframe);
                        return result;
                    },
                    focus : true
                }, {
                    name : '取消',
                    callback : function() {
                    }
                } ]
        });
    }

    function communicate() {
        var taskId = '${taskId }';
        var targetUrl = 'taCommunicateController.do?goAddCommunicateMain&taskId='
            + taskId;
        //弹出窗口
        createwindowExt('沟通', targetUrl, "", "", {
            lock : true,
            parent : windowapi,
            zIndex : 5000,
            width : 1000,
            height : 600,
            button : [
                {
                    name : '确定',
                    callback : function() {
                        //回调事件
                        iframe = this.iframe.contentWindow;
                        var rowsData = iframe.$('#sendCommunicateList').datagrid(
                            'getRows');
                        var content = iframe.$('#content').val();
                        if (content == "" || content == null
                            || content == undefined) {
                            top.tip("请填写沟通内容");
                            return false;
                        }
                        var forceReplyFlag = iframe.$('#forceReplyFlag').val();
                        var codes = [];
                        var names = [];
                        var fullNames = [];
                        for (var i = 0; i < rowsData.length; i++) {
                            codes.push(rowsData[i].positionCode);
                            names.push(rowsData[i].positionName);
                            fullNames.push(rowsData[i].fullName);
                        }
                        var result = callCommunicateAjax(codes, names, fullNames, content,forceReplyFlag,iframe);
                        return result;
                    },
                    focus : true
                }, {
                    name : '取消',
                    callback : function() {
                    }
                } ]
        });
    }

    //转办
    function reassign() {
        var taskId = '${taskId }';
        var targetUrl = 'taTaskController.do?goReassignForm&taskId='
            + taskId;
        //弹出窗口
        createwindowExt('委派', targetUrl, "", "", {
            lock : true,
            width : 600,
            height : 400,
            button : [
                {
                    name : '确定',
                    callback : function() {
                        iframe = this.iframe.contentWindow;

                        var positionId = iframe.$('#id').val();
                        var reassignComment = iframe.$('#reassignComment').val();

                        if (positionId == "" || positionId == null
                            || positionId == undefined) {
                            iframe.tip("请选择委派人");
                            return false;
                        }

                        if (reassignComment == "" || reassignComment == null
                            || reassignComment == undefined) {
                            iframe.tip("请填写委派内容");
                            return false;
                        }

                        var flag = callReassignAjax(positionId, reassignComment);

                        return flag;
                    },
                    focus : true
                }, {
                    name : '取消',
                    callback : function() {
                    }
                } ]
        });
    }

    function callReassignAjax(positionId, reassignComment) {
        var taskId = '${taskId}';
        var flag = true;

        window.top.$.messager.progress({
            text : "处理中...",
            interval : 200
        });

        $.ajax({
            url : "taTaskController.do?reassign",
            type : "post",
            data : {
                taskId : taskId,
                positionId:positionId,
                reassignComment:reassignComment
            },
            success : function(data) {
                window.top.$.messager.progress('close');
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    if (typeof (parent.windowapi.opener.handleSearch) != "undefined")
                        parent.windowapi.opener.handleSearch();
                    if (typeof (parent.windowapi.opener.copySearch) != "undefined")
                        parent.windowapi.opener.copySearch();
                    if (typeof (parent.windowapi.opener.rejectSearch) != "undefined")
                        parent.windowapi.opener.rejectSearch();
                    parent.windowapi.opener.$("#myTaskList").datagrid("reload");
                    parent.windowapi.close();
                }else{
                    iframe.tip(msg);
                    flag = false;
                }
            }
        });

        return false;
    }

    function sureCommunicate() {
        var communicateId = $("#communicateId").val();
        var comment = $("#comment").val();
        if (comment == "" || comment == null || comment == undefined) {
            tip("请填写意见");
            return;
        }
        $.ajax({
            url : 'taCommunicateController.do?saveSureCommunicate&communicateId='+ communicateId,
            type : "post",
            data : {content : comment},
            async:false,
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    var msg = d.msg;
                    top.tip(msg);
                    if (typeof (parent.windowapi.opener.handleSearch) != "undefined")
                        parent.windowapi.opener.handleSearch();
                    if (typeof (parent.windowapi.opener.copySearch) != "undefined")
                        parent.windowapi.opener.copySearch();
                    if (typeof (parent.windowapi.opener.rejectSearch) != "undefined")
                        parent.windowapi.opener.rejectSearch();
                    parent.windowapi.opener.$("#myTaskList").datagrid(
                        "reload");
                    parent.windowapi.close();
                }
            }
        });
    }

    function callAjax(codes, names, fullNames, content,iframe) {
        var processInstanceId = '${processInstanceId}';
        var flag = true;
        $.ajax({
            url : "taCarbonCopyController.do?saveCirculation",
            type : "post",
            data : {
                codes : codes.join(','),
                names : names.join(','),
                fullNames : fullNames.join(','),
                content : content,
                processInstanceId:processInstanceId
            },async:false,
            success : function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    tip(msg);
                    if(typeof(parent.windowapi.opener.handleSearch)!="undefined") parent.windowapi.opener.handleSearch();
                    if(typeof(parent.windowapi.opener.copySearch)!="undefined") parent.windowapi.opener.copySearch();
                    if(typeof(parent.windowapi.opener.rejectSearch)!="undefined") parent.windowapi.opener.rejectSearch();
                    parent.windowapi.opener.$("#useSendCopyList").datagrid("reload");
//                    parent.windowapi.close();
                }else{
                    iframe.tip(msg);
                    flag = false;
                }
            }
        });
        return flag;
    }

    function callCommunicateAjax(codes, names, fullNames, content,forceReplyFlag,iframe) {
        var taskId = '${taskId}';
        var flag = true;
        $.ajax({
            url : "taCommunicateController.do?saveCommunicate",
            type : "post",
            data : {
                codes : codes.join(','),
                names : names.join(','),
                fullNames : fullNames.join(','),
                content : content,
                forceReplyFlag:forceReplyFlag,
                taskId : taskId
            },async:false,
            success : function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    tip(msg);
                    window.location.reload();
//                    parent.windowapi.close();
                }else{
                    iframe.tip(msg);
                    flag = false;
                }
            }
        });
        return flag;
    }

    function approval() {
        //alert($("#bcbIframe").contents().find("#mttype20200305").length);
        if(($("#bcbIframe").contents().find("#mttype20200305").length) > 0){
            tip('《门头类别》未设置！！！');
            return;
		}
		if(($("#bcbIframe").contents().find("#mtadtype20200630").length) > 0){
			tip('《门头位置》未设置！！！');
			return;
		}
        var isread = document.getElementById("picISReaded").value;

        if(isread != null && isread != "" && isread == 0){
            tip('您尚未查看过该门头的照片小视频');
            return;
		}

		//20200615 增加判断是否保存过折扣信息
        var haveSaved = document.getElementById("haveSaved").value;

        if(haveSaved != null && haveSaved != "" && haveSaved == 0){
            tip('折扣信息尚未保存！');
            return;
        }

        //20200617 增加材料超面积协议
        //var mjhint = document.getElementById("mjhint").value;
        var mjhint ="";
        $.ajax({
            async : false,
            cache : false,
            url: "ttActOutUploadController.do?getHint",
            data:{"taskId":${taskId}},
            method: "post",
            success: function (data) {
                var data = $.parseJSON(data);
                //tip(data.msg);
                if(data.success){
                    mjhint = data.msg;
                    //alert(mjhint);
                    //$('#mjhint').val(data.msg);
                }
            }
        })
        //var mjhint = document.getElementById("mjhint").value;
        if(mjhint != null && mjhint != "9"){
            //alert(mjhint);
            PostbirdAlertBox.confirm({
                'title': '系统温馨提示',
                'content': mjhint,
                'okBtn': '不同意',
                'cancelBtn': '同意',
                'contentColor': 'red',
                'onConfirm': function () {
                    //console.log("OK - 回调触发后隐藏提示框");
                    //alert("OK - 回调触发后隐藏提示框");

                    return false;

                },
                'onCancel': function () {
                    //console.log("Cancel-回调触发后隐藏提示框");
                    alert("您已同意超面积自费！");

                    var taskId = '${taskId}';

                    var comment = $("#comment").val();
                    if (comment == "" || comment == null || comment == undefined) {
                        //tip("请填写意见");
                        //return;
                        comment = "同意";
                    }
                    var processInstanceId = '${processInstanceId}';
                    var formData = {
                        taskId : taskId,
                        comment : comment,
                        processInstanceId : processInstanceId
                    };
                    $.ajax({
                        async : false,
                        cache : false,
                        type : 'POST',
                        data : formData,
                        url : "taTaskController.do?saveComplete",// 请求的action路径
                        error : function() {// 请求失败处理函数
                        },
                        success : function(data) {
                            var d = $.parseJSON(data);
                            if (d.success) {
                                var msg = d.msg;
                                top.tip(msg);
                                if (typeof (parent.windowapi.opener.handleSearch) != "undefined")
                                    parent.windowapi.opener.handleSearch();
                                if (typeof (parent.windowapi.opener.copySearch) != "undefined")
                                    parent.windowapi.opener.copySearch();
                                if (typeof (parent.windowapi.opener.rejectSearch) != "undefined")
                                    parent.windowapi.opener.rejectSearch();
                                parent.windowapi.opener.$("#myTaskList").datagrid(
                                    "reload");
                                parent.windowapi.close();
                            } else {
                                var msg = d.msg;
                                tip(msg);
                                return;
                            }
                        }
                    });

                }
            });
            //}
        } else {
            var taskId = '${taskId}';

            var comment = $("#comment").val();
            if (comment == "" || comment == null || comment == undefined) {
                //tip("请填写意见");
                //return;
                comment = "同意";
            }
            var processInstanceId = '${processInstanceId}';
            var formData = {
                taskId : taskId,
                comment : comment,
                processInstanceId : processInstanceId
            };
            $.ajax({
                async : false,
                cache : false,
                type : 'POST',
                data : formData,
                url : "taTaskController.do?saveComplete",// 请求的action路径
                error : function() {// 请求失败处理函数
                },
                success : function(data) {
                    var d = $.parseJSON(data);
                    if (d.success) {
                        var msg = d.msg;
                        top.tip(msg);
                        if (typeof (parent.windowapi.opener.handleSearch) != "undefined")
                            parent.windowapi.opener.handleSearch();
                        if (typeof (parent.windowapi.opener.copySearch) != "undefined")
                            parent.windowapi.opener.copySearch();
                        if (typeof (parent.windowapi.opener.rejectSearch) != "undefined")
                            parent.windowapi.opener.rejectSearch();
                        parent.windowapi.opener.$("#myTaskList").datagrid(
                            "reload");
                        parent.windowapi.close();
                    } else {
                        var msg = d.msg;
                        tip(msg);
                        return;
                    }
                }
            });
		}


    }

    //驳回任意节点
    function reject() {
        var taskId = '${taskId}';//任务id
        var comment = $("#comment").val();//审批意见
        var returnThisNode = $("input:checkbox:checked").val();
        var node = $("#couldJumpNode").val();
        if (comment == "" || comment == null || comment == undefined) {
            tip("请填写审批意见（驳回原因是什么？）");
            return;
        }
        if (node == "" || node == null || comment == node) {
            tip("请选择驳回节点");
            return;
        }

        var processInstanceId = '${processInstanceId}';
        var formData = {
            taskId : taskId,
            comment : comment,
            processInstanceId : processInstanceId,
            taskDefKey : node,
            returnThisNode:(returnThisNode == undefined || returnThisNode == "" || returnThisNode == null) ? "N" : returnThisNode
        };
        $.ajax({
            type : "post",
            data : formData,
            url : "taTaskController.do?saveReject",
            error : function() {
            },
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    var msg = d.msg;
                    top.tip(msg);
                    if (typeof (parent.windowapi.opener.handleSearch) != "undefined")
                        parent.windowapi.opener.handleSearch();
                    if (typeof (parent.windowapi.opener.copySearch) != "undefined")
                        parent.windowapi.opener.copySearch();
                    if (typeof (parent.windowapi.opener.rejectSearch) != "undefined")
                        parent.windowapi.opener.rejectSearch();
                    parent.windowapi.opener.$("#myTaskList").datagrid(
                        "reload");
                    parent.windowapi.close();
                }
            }
        });
    }

    function rejectToApply(state) {
        var taskId = '${taskId}';//任务id
        var comment = $("#comment").val();//审批意见
        if (comment == "" || comment == null || comment == undefined) {
            tip("请填写审批意见（驳回原因是什么？）");
            return;
        }
        var processInstanceId = '${processInstanceId}';
        var formData = {
            taskId : taskId,
            comment : comment,
            processInstanceId : processInstanceId
        };
        $.ajax({
            type : "post",
            data : formData,
            url : "taTaskController.do?saveRejectToApply",
            error : function() {
            },
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    var msg = d.msg;
                    top.tip(msg);
                    if (typeof (parent.windowapi.opener.handleSearch) != "undefined")
                        parent.windowapi.opener.handleSearch();
                    if (typeof (parent.windowapi.opener.copySearch) != "undefined")
                        parent.windowapi.opener.copySearch();
                    if (typeof (parent.windowapi.opener.rejectSearch) != "undefined")
                        parent.windowapi.opener.rejectSearch();
                    parent.windowapi.opener.$("#myTaskList").datagrid(
                        "reload");
                    parent.windowapi.close();
                }
            }
        });
    }


    function sureUpload(){
        window.location.reload();
    }

    function deleteFile(id,extendService){

        $.dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url:"tbAttachmentController.do?delObjFile",
                    data:{fileKey:id,extendService:extendService},
                    method:"post",
                    success:function(data){
                        sureUpload();
                    }
                });
            }
        });
    }
    //跳转隐藏页面配置项
    function hideNode(){
        var processInstanceId = '${processInstanceId}';
        var taskId = '${taskId}';
        createwindowExt(
            '配置',
            'taHideNodeController.do?goTaHidenNodeConfigMain&processInstanceId='+processInstanceId+"&taskId="+taskId,
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex : 9000,
                width : 1200,
                height : 500,
                button : [
                    {
                        name:'确定',
                        callback:function(){
                            iframe = this.iframe.contentWindow;
                            var trs = iframe.$("#ttSplitCostTerminalList").find("tr")
                            var hideNode = [];
                            var hideName = [];
                            $.each(trs,function(i,k){
                                var isCheck = $(this).find("input[type=checkbox]").is(':checked');
                                if(isCheck == true){
                                    var toHideNodes = $(this).find("td[name='pnc']").text();
                                    var toHideNames = $(this).find("td[name='pnn']").text();
                                    hideNode.push(toHideNodes);
                                    hideName.push(toHideNames);
                                }
                            });
                            var isCopyCir = iframe.$("#isCopyCir").is(":checked");
                            var isCommunicate = iframe.$("#isCommunicate").is(":checked");
                            var url = "taHideNodeController.do?saveHideNode";
                            $.ajax({
                                url:url,
                                type:"post",
                                data:{
                                    toHiddenNodeCodes:hideNode.join(","),
                                    toHiddenNodeNames:hideName.join(","),
                                    isCopyCir:isCopyCir == false ? "0":"1",
                                    isCommunicate:isCommunicate == false ? "0":"1",
                                    processInstanceId:processInstanceId,
                                    taskId:taskId
                                },success:function(data){
                                    var d = $.parseJSON(data);
                                    tip(d.msg);
                                }
                            });
                        }
                    },
                    {
                        name : '取消',
                        callback : function() {
                        }
                    } ]
            });
    }
    function showTheAttach() {
        var str = "";
        var extend = "";
        var src = "";
        var id = "";
        var attachmentHidenDivObj = $('#attachmentHidenDiv');

        <c:forEach items="${attachment}" var="a">
        id = '${a.id}';
        str += attachmentHidenDivObj.find("#attachmentHidenDiv_showFile").html().replace("paramsId",id).replace("paramsTitleAndExtend",'${a.attachmentTitle}' + '.' + '${a.extend}');
        extend = '${a.extend}';
        if( extend == 'jpg' || extend == 'png' || extend == 'jpeg' || extend == 'gif' || extend == 'bmp' ){
            src = servicePath + '${a.realPath}';
            str += attachmentHidenDivObj.find("#attachmentHidenDiv_imgShow").html().replace("paramsPath",src);
        }
        <c:if test="${currPositionCode eq a.tmPositionEntity.positionCode && !isView}">
        str += attachmentHidenDivObj.find("#attachmentHidenDiv_imgDel").html().replace("paramsId",id);
        </c:if>
        str += "<br />";
        </c:forEach>

        $('#showAttachMentTd').html(str);
    }

    function showBigPic(obj){

        var src = obj.src;
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $.dialog({
            title: "图片查看",
            content: "url:tsPictureController.do?showBigPic&needSfaPath=false&path="+src,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            cancelVal: '关闭',
            cancel: true
        });
    }

    //共------------------------------
    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
</script>

