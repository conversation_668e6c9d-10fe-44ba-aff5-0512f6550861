<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html style="overflow-x: hidden;">
<head>
    <title>上刊处理</title>
    <style>
        #sureFilesDetail{display:table;}
        .sureFilesDetail_a{float:left;padding-right:20px;margin-right:10px;position:relative;}
        .sureFilesDetail_a img{position:absolute;top:7px;right:0;}
    </style>
    <style type="text/css">
        #formobj>table tr td input{
            width:225px;
        }
    </style>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
<t:formvalid formid="formobj" dialog="true" usePlugin="password" layout="table"  callback="@Override uploadFile"  action="#"	tiptype="3">
    <input id="id" name="id" type="hidden"	value="${vo.id}">
    <table style="width:100%;height:100%" cellpadding="0" cellspacing="1" class="formtable">
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>合同编码:</label></td>
            <td class="value">
                <input id="contractCode" name="name" type="text" class="inputxt" readonly="true" value="${vo.contractCode}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>合作公司:</label></td>
            <td class="value">
                <input id="partnerCompany" name="partnerCompany" type="text" readonly="true" class="inputxt"  value="${vo.partnerCompany}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>车底号:</label></td>
            <td class="value">
                <input id="trainBtNumber" name="trainBtNumber" type="text" readonly="true"  class="inputxt"  value="${vo.trainBtNumber}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>合同签订日期:</label></td>
            <td class="value">
                <input id="startDate" name="startDate" type="text"  readonly="true" class="inputxt"  value="${vo.startDate}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>广告有效月数:</label></td>
            <td class="value">
                <input id="advEffectPeriod" name="advEffectPeriod" type="text" readonly="true" class="inputxt"  value="${vo.advEffectPeriod}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>下刊验收通过日期:</label></td>
            <td class="value">
                <input id="downPubPassDate" name="publicationPassDate" type="text"  class="Wdate"
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})" dataType="*" value="${vo.downPubPassDate}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>
        <tr >
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>下刊备注:</label></td>
            <td class="value">
                <input id="downPubRemark" name="publicationRemark" type="text"  class="inputxt" dataType="*" value="${vo.downPubRemark}">
                <span class="Validform_checktip"></span> <label
                    class="Validform_label" style="display: none;">主题</label></td>
        </tr>


        <tr id="uploadAttmentsFile">
            <td align="right" style="width: 20%;">
                <label class="Validform_label"><span style="color: red">*</span>下刊验收材料:</label></td>
            <td colspan="3" class="value">
                <div class="dtci_bottom">
                    <div id="sureFilesDetail" ></div>
                    <t:uploadH5 name="file_upload" buttonText="选择文件" onUploadSuccess="sureUpload" dialog="false" 	 callback=""
                                uploader="tbAttachmentController.do?saveFiles&businessKey=${vo.downBusinessId}&attachmentType=theme" extend="*.*" id="file_upload" formData=""></t:uploadH5>
                    <div hidden="true" class="dtci_bottom_a"><a href="javascript:;" class="easyui-linkbutton" plain="true" id="btn_sub" iconCls="icon-upload" onclick="upload()">确定上传</a></div>
                    <div></div>
                    <div id="filediv" ></div>
                </div>
            </td>
        </tr>
        </tr>
    </table>
</t:formvalid>
</body>
<style>
    #processKey{padding:0;}
</style>
<script type="text/javascript">
    //编写自定义JS代码
    $(document).ready(function(){
        sureUpload();
    }) ;
    var isCouldSubmit = 0;

    function sureUpload(){
        var isReadOnly = '${isReadOnly}';
        $.ajax({
            url:"tbAttachmentController.do?findAttachmentList&businessKey=${vo.downBusinessId}&attachmentType=${attachmentType }&extendService=${extendService }",
            data:{},
            method:"post",
            success:function(data){
                var str="";
                var d = $.parseJSON(data);
                if (d.success) {
                    var rows = d.rows;
                    for(var i =0;i<rows.length;i++){
                        str += '<div class="sureFilesDetail_a"><a targe="_blank" href="tbAttachmentController.do?viewFile&fileid='
                            +rows[i].id+'" class="easyui-linkbutton l-btn l-btn-plain" plain="true" iconcls="icon-download"><span class="l-btn-left"><span class="l-btn-text icon-download l-btn-icon-left">'
                            +rows[i].attachmentTitle+'.'+rows[i].extend+'</span></span></a>';
                        if(isReadOnly != 'false') {
                            str += '<img onclick="deleteFile(\''
                                + rows[i].id
                                + '\')" src="resources/Validform/images/error.png" />';
                        }
                        str += '</div>';
                    }
                }
                if(str == "") {
                    $("#sureFiles").hide();
                } else {
                    $("#sureFiles").show();
                }
                $("#sureFilesDetail").html(str);
            }
        });
    }
    function deleteFile(id, extendService){
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url:"tbAttachmentController.do?delObjFile",
                    data:{fileKey:id,extendService:extendService},
                    method:"post",
                    success:function(data){
                        sureUpload();
                    }
                });
            }
        });
    }
    //关闭弹出框页面
    function close(){
        windowapi.config.parent.close();
        windowapi.close();
    }
    //关闭遮罩层
    function closePro(){
        window.top.$.messager.progress('close');
    }
    //打开遮罩
    function openPro(){
        window.top.$.messager.progress({
            title:'提示',
            msg:'正在提交中......',
            text:'提交中'
        });
    }

    //提交操作
    function doSubmit(){


        var downPubPassDate = $("#downPubPassDate").val();
        if (downPubPassDate == '') {
            tip("没有选择下刊验收通过日期");
            return false;
        }
        var downPubRemark = $("#downPubRemark").val();
        var id = $("#id").val();


        //执行通用提交方法
        var param = {downPubPassDate:downPubPassDate,downPubRemark:downPubRemark,id:id};

        top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });

        $.ajax({
            type: "POST",
            url: "trainADBaseController.do?downPublication",
            data:param,
            //async: false,
            success: function (data) {
                top.$.messager.progress("close");
                var d = $.parseJSON(data);
                if(d.success) {
                    W.top.tip(d.msg);
                    upload();
                    W.reloadTable();
                    close();
                } else {
                    tip(d.msg);
                }
            },
            error:function () {
                top.$.messager.progress("close");
            }
        });
    }
</script>
