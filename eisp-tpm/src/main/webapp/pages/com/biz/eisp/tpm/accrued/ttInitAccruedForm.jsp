<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>预提往期</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttInitAccruedController.do?saveTtInitAccrued"
             refresh="true">
    <!-- id -->

    <div class="form">
        <label class="Validform_label">预提年月: </label>
        <input name="accruedYearMonth" id="accruedYearMonth" datatype="*" style="width: 150px;"
                <c:if test="${!isProcess}">
                    class="Wdate"  onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})"
                </c:if>
               readonly="readonly"  value="${vo.accruedYearMonth}" />
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">预提类型: </label>
        <t:dictSelect field="accruedType" typeGroupCode="accrued_type" dataType="*"
                      defaultVal="${vo.accruedType }"></t:dictSelect>
        <span style="color: red;">*</span>
    </div>
    <div class="form">
        <label class="Validform_label">往期预提类型: </label>
        <t:dictSelect field="accruedDataType" typeGroupCode="milk_init_accrued_type" dataType="*"
                      defaultVal="${vo.accruedDataType }"></t:dictSelect>
        <span style="color: red;">*</span>
    </div>
    <div class="form">
        <label class="Validform_label">产品名称: </label>
        <input type="hidden" id="productName" name="productName" />
        <select id="productCode" name="productCode" onchange="setProductName()" datatype="*" >
            <option value="">请选择</option>
            <c:forEach items="${productList}" var="pro">
                <option value="${pro.productCode}">${pro.productName}</option>
            </c:forEach>
        </select>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">活动年月: </label>
        <input name="actYearMonth" id="actYearMonth" datatype="*" style="width: 150px;"
                <c:if test="${!isProcess}">
                    class="Wdate"  onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})"
                </c:if>
               readonly="readonly"  value="${vo.actYearMonth}" />
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">组织:</label>
        <input type="hidden" readonly="readonly" id="orgCode" name="orgCode" value="${vo.orgCode}">
        <input name="orgName" id="orgName" class="inputxt" datatype="*"  readonly="readonly"  readonly="readonly" value="${vo.orgName}" />
        <span style="color: red;">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openOrgSelect();"></a>
    </div>

    <div class="form">
        <label class="Validform_label">SAP成本中心: </label>
        <input class="inputxt"  readonly="readonly" datatype="*" name="costCenter" id="costCenter"  value="${vo.costCenter}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">客户名称: </label>
        <input type="hidden" readonly="readonly" dataType="*"  name="customerCode" id="customerCode" value="${vo.customerCode}">

        <b:search hiddenFields="customerName,customerCode" inputTextName="customerName,customerCode" value="${vo.customerName}"
                  dialogUrl="tmCommonMdmController.do?findCustomerBySearchList"
                  url="tmCommonMdmController.do?goCustomerSearch" isClear="false" needQuery="false"
                  inputTextId="customerName"  name="ttCustomerList"  type="3" title="选择客户"  ></b:search>
    </div>

    <div class="form">
        <label class="Validform_label">活动细类: </label>
        <input name="costAccountName" id="costAccountName" class="inputxt"  datatype="*" readonly="readonly"  readonly="readonly" value="${vo.costAccountName}" />
        <span style="color: red">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openCostAccountSelect();"></a>
        <input type="hidden" id="costAccountCode" datatype="*" name="costAccountCode" value='${vo.costAccountCode}' >
    </div>
    <div class="form">
        <label class="Validform_label">支付方式: </label>
        <input type="hidden" name="paymentName" id="paymentName">
        <t:dictSelect id="paymentCode" field="paymentCode" typeGroupCode="payment_type" dataType="*"
                      defaultVal="${vo.paymentCode }"  ></t:dictSelect>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">活动大类: </label>
        <input class="inputxt" name="costTypeCode" id="costTypeCode"  hidden="true"  value="${vo.costTypeCode}"/>
        <input class="inputxt" name="costTypeName"  id="costTypeName" value="${vo.costTypeName}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">预算科目: </label>
        <input class="inputxt" name="finacialCode" hidden="true"  id="finacialCode" value="${vo.finacialCode}"/>
        <input class="inputxt" name="finacialName" id="finacialName" value="${vo.finacialName}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">财务科目ERP编码: </label>
        <input class="inputxt" name="finacialAccountCode" id="finacialAccountCode"  value="${vo.finacialAccountCode}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">活动开始时间: </label>
        <input name="beginDate" id="beginDate" style="width: 150px;"
                <c:if test="${!isProcess}">
                    class="Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\')}',onpicked:function(){$('.Wdate').blur();}})"
                </c:if>
               readonly="readonly"  value="${vo.beginDate}" />
    </div>

    <div class="form">
        <label class="Validform_label">活动结束时间: </label>
        <input name="endDate" id="endDate"  style="width: 150px;"
                <c:if test="${!isProcess}">
                    class="Wdate"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
                </c:if>
               readonly="readonly"  value="${vo.endDate}" />
    </div>

    <%--<div class="form">
        <label class="Validform_label">活动申请金额: </label>
        <input  name="applyAmount"  class="easyui-numberbox"  data-options="precision:2"  value="${vo.applyAmount}"/>
        <span style="color: red;">*</span>
    </div>--%>

    <div class="form">
        <label class="Validform_label">预提金额: </label>
        <input  name="accruedAmount" class="easyui-numberbox" datatype="*" data-options="precision:2" value="${vo.accruedAmount}"/>
        <span style="color: red;">*</span>
    </div>
    <div class="form">
        <label class="Validform_label">备注: </label>
        <textarea rows="3" cols="20" name="remark" maxlength="200">${vo.remark}</textarea>
    </div>

</t:formvalid>
</body>
</html>


<script type="text/javascript">

    //只能输入数字，或者保留两位小数
    function clearNoNum(obj){
        obj.value = obj.value.replace(/[^\-\d.]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\-\./g,""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }

    $(document).ready(function () {
        $("#paymentCode").change(function () {
            var checkText=$("#paymentCode").find("option:selected").text();
            $("#paymentName").val(checkText);
        });
    });
    function setProductName(){
        var checkText=$("#productCode").find("option:selected").text();
        $("#productName").val(checkText);
    }

    //弹出选择部门
    function openOrgSelect() {
        safeShowDialog({
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            title : "选择企业组织",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function()
            {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#orgList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#orgCode").val("");
                    $("#orgName").val("");
                    $("#costCenter").val("");

                    return true;
                }
                $("#orgCode").val(rowsData.orgCode);
                $("#orgName").val(rowsData.text);
                $("#costCenter").val(rowsData.extChar1);
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    //弹出选择费用科目
    function openCostAccountSelect(){
//	var actTypeCode=$("#actTypeCode").val();
//    if(actTypeCode==null||actTypeCode==''||actTypeCode==undefined){
//        newTip("请先选择流程类型");
//        return;
//    }
        safeShowDialog({
            content : "url:ttCostAccountController.do?goSelectTtCostAccount&actType=quota_act_type",
            lock : true,
            title : "选择费用科目",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tmCostAccountList').datagrid('getSelected');
                if (rowsData == '' || rowsData == null) {
                    iframe.tip("请选择一条数据");
                    return false;
                } else {
                    $("#costAccountCode" ).val(rowsData.accountCode);
                    $("#costAccountName").val(rowsData.accountName);
                    $("#costTypeCode" ).val(rowsData.costTypeCode);
                    $("#costTypeName").val(rowsData.costTypeName);
                    $("#finacialCode" ).val(rowsData.financialAccountCode);
                    $("#finacialName").val(rowsData.financialAccountName);
                    $("#finacialAccountCode").val(rowsData.financialCode);
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>