<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
</style>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'north',title:'月度计划提报'" style="padding:1px;height:112px;" >
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" name="appMonthlyPlan" icon="icon-add" onClick="approveMonthlyPlan(${pass},1)">通过</a>
							<c:if test="${isSaleDepart == 1}">
								<a href="#" class="easyui-linkbutton" plain="true" name="appMonthlyPlanBySale" icon="icon-add" onClick="approveMonthlyPlan(${pass},2)">销售部直接通过</a>
							</c:if>
							<a href="#" class="easyui-linkbutton" plain="true" name="updateMonthlyPlan" icon="icon-edit" onClick="approveMonthlyPlan(${rejecte},0)">驳回</a>
							<c:if test="${isSaleDepart == 1}">
								<a href="#" class="easyui-linkbutton" plain="true" name="updateMonthlyPlanBySale" icon="icon-edit" onClick="approveMonthlyPlan(${rejecte},4)">销售部直接驳回</a>
							</c:if>
						</span>
						<span style="float:right">
							<a href="#" class="easyui-linkbutton" iconcls="icon-search" onclick="ttMonthlyPlanGatherListSearchFunction()">查询</a>
							<a href="#" class="easyui-linkbutton" iconcls="icon-reload" onclick="searchReset()">重置</a>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search" style=" height: 40px;">
		            	<form>
		            		<div>
		            			<label>计划年份</label>
		            			<input type="text" name="year" id="year" class="Wdate"
								onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){chooseYearAndMonthSearch(); }})" readonly="readonly" />
		            			<span style="color:red;">*</span>
		            		</div>
		            		<div>
		            			<label>计划月份</label>
		            			<input type="text" name="month" id="month" class="Wdate"
								onclick="WdatePicker({dateFmt:'MM',onpicked:function(){ chooseYearAndMonthSearch(); }})" readonly="readonly" />
		            			<span style="color:red;">*</span>
		            		</div>
		            		<div>
		            			<label>客户编码</label>
		            			<input type="text" name="customerCode" id="customerCode" />
		            		</div>
		            		<div>
		            			<label>客户名称</label>
		            			<input type="text" name="customerName" id="customerName" />
		            		</div>
		            	</form>
						<div class="top_tip" style="margin-top: 10px;background-color: EEEEEE;" >
							<ul class="ownTabs" id="ulTemp">
								<input type="hidden" value="" id="tabs-sel" hidden="hidden" />
								<li  class="tabs-selected" data-id="ttMonthlyPlanGatherList"><a class="tabs-inner" href="javascript:void(0)"  ><span class="tabs-title">区域别汇总</span><span class="tabs-icon"></span></a></li>
								<li  data-id="ttMonthlyPlanProductList" id="priceGuide"><a class="tabs-inner" href="javascript:void(0)" ><span class="tabs-title">产品别汇总</span><span class="tabs-icon"></span></a></li>
							</ul>
						</div>
					</div>
				</div>
				<div class="datagrid-view">
				</div>
			</div>
		</div>
	</div>
	
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttMonthlyPlanGatherList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
	       autoLoadData="false" actionUrl="ttMonthlyPlanConfirmController.do?findTtMonthlyPlanGatherList&isSaleDepart=${isSaleDepart }" onClick="clickMonthlyPlanFun">
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
         	<t:dgCol title="销售部" field="salesDepartName" sortable="false" width="100" ></t:dgCol>
         	<t:dgCol title="区域编码" field="orgCode" hidden="true" frozenColumn="true" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="区域" field="orgName" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" width="100" ></t:dgCol>
          	<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="差异额" field="differenceAmount" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="状态" field="bpmStatus" dictionary="monthlyPlan_Bpm_status" sortable="false" width="100" ></t:dgCol>
	    </t:datagrid>
	</div>
	
	<div data-options="region:'east',
	title:'',
	collapsed:true,
	split:true,
	border:false,
	onExpand : function(){
		li_east = 1;
	},
	onCollapse : function() {
	    li_east = 0;
	}"
	 style="padding:1px;width:700px;">
		<t:datagrid name="ttMonthlyPlanList" fitColumns="false" queryMode = "group" idField="id" singleSelect="false" 
		pagination="true" autoLoadData="false" actionUrl="ttMonthlyPlanConfirmController.do?findTtMonthlyPlanList"  >
	     	<t:dgCol title="主键" field="id" hidden="true" sortable="false" width="100" ></t:dgCol>
	      	<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
	  		<t:dgCol title="区域" field="orgName" sortable="false" width="100" ></t:dgCol>
	  		<t:dgCol title="客户编码" field="customerCode" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="客户名称" field="customerName" sortable="false" width="100" ></t:dgCol>
	  		<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
			<t:dgCol title="操作" field="opt" sortable="false" width="100" ></t:dgCol>
			<%--<t:dgOpenOpt url="ttMonthlyPlanConfirmController.do?gottReportMonthlyPlanDetail" title=""--%>
	  		<t:dgFunOpt title="详情"  funname="operationDetail(customerCode,yearMonth)" ></t:dgFunOpt>
	  		<%-- <t:dgCol title="返还明细" field="opt" width="100" ></t:dgCol>
   			<t:dgFunOpt title="查看" funname="ttFridgeMainController.do?doDel&id={id}" /> --%>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
    var li_east = 0;
	function clickMonthlyPlanFun(rowIndex,rowData) {
        if(li_east == 0){
            $('#ttReportMonthlyPlanMain').layout('expand','east');
        }
		var orgName = rowData.orgName;
		var yearMonth = rowData.yearMonth;
		var queryParams = $('#ttMonthlyPlanList').datagrid('options').queryParams;
        queryParams.orgName = orgName;
        queryParams.yearMonth = yearMonth;
		$("#ttMonthlyPlanList").datagrid({url:"ttMonthlyPlanConfirmController.do?findTtMonthlyPlanList"});
	}
	//通过/驳回
	function approveMonthlyPlan(n,chooseNum){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var dataTemp = {
				bpmStatus : n,
				isSaleDepart : '${isSaleDepart }',
				year:year,
				month:month,
				chooseNum : chooseNum
			}
		if('1' == chooseNum){
			approveMonthlyPlanPass(n,'通过所有项目',dataTemp);
		}else if('0' == chooseNum){
			approveMonthlyPlanRejecte(n,'驳回已选项目',dataTemp);
		}else{
			tip(" ???????  -.-!! ");
		}
	}
	//确认
	function approveMonthlyPlanPass(n,message,dataTemp){
		starApproveMonthlyPlan(message,dataTemp);
	}
	//驳回
	function approveMonthlyPlanRejecte(n,message,dataTemp){
		
		var rowsData = $('#ttMonthlyPlanGatherList').datagrid('getSelections');
		if (rowsData.length == 0) {
			approveMonthlyPlanPass(n,"驳回所有项目",dataTemp);
			/* getSafeJq().dialog.confirm("未选择指定项目，会驳回所有项目，是否继续？", function(r) {
	            if (r) {
	            	approveMonthlyPlanPass(n,"驳回所有项目",dataTemp)
	            }
	        }); */
			return ;
		}
		/* if (rowsData.length != 1) {
			tip("请选择项目");
			return ;
		} */
		var orgCodes = [];
		for ( var i = 0; i < rowsData.length; i++) {
			orgCodes.push(rowsData[i].orgCode);
        }
		/*var data = {
			bpmStatus : n,
			isSaleDepart : '\\${isSaleDepart }',
			year : dataTemp.year,
			month : dataTemp.month,
			orgCode : orgCodes.join(',')
		}*/
        var data = dataTemp;
        data["orgCode"] = orgCodes.join(',');
		starApproveMonthlyPlan(message,data);
	}
	function starApproveMonthlyPlan(message,data){
		url = "ttMonthlyPlanConfirmController.do?approveMonthlyPlan";
		ajaxRequest({tips:'确定' + message + '?',url:url,params:data,callbackfun:function(data){
	    	var d = $.parseJSON(data);
            if(d.success == true) {
            	ttMonthlyPlanGatherListSearchFunction();
            } else {
            	tip(d.msg);
            }
	    }});
	}
	//打开明细---行级
	function rowLeveloperationDetail(index,rowData){
		/* var rowsData = rowData.rows;
		//$('#ttMonthlyPlanGatherList').datagrid('getSelections');
		if (rowsData.length != 1) {
			tip("请选择一条项目");
			return ;
		} */
		var data = rowData;//rowsData[0];
		var customerCode = data.customerCode;
		var yearMonth = data.yearMonth;
		createwindowExt("查看明细","ttMonthlyPlanConfirmController.do?gottReportMonthlyPlanDetail&customerCode=" + customerCode 
				+ "&yearMonth=" + yearMonth,"1000","650",{
	    	 button : [
	 	            {name : "关闭",
		            callback : function() {
		            	return true;
	            }}
	   	 ] });
	}
    //打开明细--列级
    function operationDetail(customerCode,yearMonth,index){
        var customerCode = customerCode;
        var yearMonth = yearMonth;
        createwindowExt("查看明细","ttMonthlyPlanConfirmController.do?gottReportMonthlyPlanDetail&customerCode=" + customerCode
            + "&yearMonth=" + yearMonth,"1000","650",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }
	function chooseYearAndMonthSearch(){
		var year = $('#year').val();
		if (year == '') {
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			return ;
		}
		ttMonthlyPlanGatherListSearchFunction();
	}
	//查询
	function ttMonthlyPlanGatherListSearchFunction(){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var customerCode = $('#customerCode').val();
		var customerName = $('#customerName').val();
		
		var queryParams = $('#ttMonthlyPlanGatherList').datagrid('options').queryParams;
        queryParams.customerCode = customerCode;
        queryParams.customerName = customerName;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthlyPlanGatherList").datagrid({url:"ttMonthlyPlanConfirmController.do?findTtMonthlyPlanGatherList&isSaleDepart=" + '${isSaleDepart }' });
	}
	
	//重置
	function searchReset(){
		$("#year").val('');
		$("#month").val('');
		$("#customerCode").val('');
		$("#customerName").val('');
		ttMonthlyPlanGatherListSearchFunction();
	}
</script>