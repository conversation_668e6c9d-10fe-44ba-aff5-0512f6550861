<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>照片信息</title>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
        }
        img {
            border: 0;
        }
        html,body{
            height: 100%;
            background-color: #e6e6e6;
        }
        .g-img-popup{
            width:300px;
            height:450px;
            padding: 10px;
            border-radius: 5px;
            background-color: #ccc;
            position: fixed;
            top:10px;
            display: none;
            transition: all 1s;
        }
        ul#ulTemp{
            padding: 20px;
        }
        ul#ulTemp li{
            width: 150px;
            height: 150px;
            float: left;
            margin: 5px;
        }
        #mainBox{
            width:98%;
            _height:200px;
            min-height:200px;
            float: left;
        }

    </style>
</head>
<body>

<!-- Content -->

<div >
    <input type="hidden" id="id" value="${id}" name="id">
    <div >
        <div >
            <h3 ></h3>
            <div style="margin-left: 20px;margin-top: 20px;">
                <c:forEach items="${photoList}" var="photo">
                    <div align="center">
                        <img style="width: 300px;height:200px;cursor: pointer;" src="${photo.imgPath}" >
                    </div>
                </c:forEach>
            </div>
        </div>
    </div>
</div>
<div class="g-img-popup">
</div>
<!-- Scripts -->
<script type="text/javascript">

    $(document).ready(function(){
        <c:if test="${isUp==0}">
            var pIds = "";
            $("input:checkbox[name='photo']:checked").each(function(i){
                if(0==i){
                    pIds = $(this).val();
                }else{
                    pIds += (","+$(this).val());
                }
            });
            $.ajax({
                async : false,
                cache : false,
                data:{ids:pIds,bussId:$("#id").val()},
                type : 'POST',
                url : 'photoWallController.do?updatePhotoStatus',// 请求的action路径
                success : function(data) {
                    var d = $.parseJSON(data);
                    if (d.success) {
                    }else{
                    }
                }
            });
        </c:if>
    });
    var isClickCheckBox = 0;

    function clickCheckBox(checkObj){
        var inputCheckObj = $(checkObj);
        var checked = inputCheckObj.attr("checked");
        var liObj = inputCheckObj.parents("li");
        if(checked == 'checked' || checked == 'true'){
            changeLiStyle(liObj,true);
        }else{
            changeLiStyle(liObj,false);
        }
        isClickCheckBox = 1;
    }

    function selectChange(obj){
        if(isClickCheckBox == 1){
            isClickCheckBox = 0;
            return ;
        }
        var inputCheckObj = $(obj).find("input[name='photo']");
        var checked = inputCheckObj.attr("checked");
        if(checked == 'checked' || checked == 'true'){
            inputCheckObj.removeAttr("checked");
            changeLiStyle(obj,false);
        }else{
            inputCheckObj.attr("checked",'checked');
            changeLiStyle(obj,true);
        }
    }
    function changeLiStyle(obj,trueOrFalse){
        var liObj = $(obj);
        if (trueOrFalse){
            liObj.css("border"," 2px solid red ");
        }else{
            liObj.css("border"," 2px solid black ");
        }

    }

    //放大
    function photos(src,event){
        var imagePath='${imagePath}';
        $('.g-img-popup').show();
        $('.g-img-popup').html('<img  src="'+imagePath+src+'" width="300px" height="450px"/>');
        var e = event || window.event;
        var left = e.pageX - 400;
        if (left < 0){
            left = e.pageX + 50;
        }
        var top = document.body.clientHeight;
        top = (top-450)/2+'px';
        $('.g-img-popup').css('left',left).css('top',top);
    }
    //隐藏
    function photoMouseLave(){
        $('.g-img-popup').hide();
    }
    //
    function submit(){

        var pIds = "";
        $("input:checkbox[name='photo']:checked").each(function(i){
            if(0==i){
                pIds = $(this).val();
            }else{
                pIds += (","+$(this).val());
            }
        });
        var flag=false;
        $.ajax({
            async : false,
            cache : false,
            data:{ids:pIds,bussId:$("#id").val()},
            type : 'POST',
            url : 'photoWallController.do?updatePhotoStatus',// 请求的action路径
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    flag=true;
                }else{
                    tip(d.msg,"info");
                }
            }
        });
        return flag;
    }
</script>
</body>
</html>
