<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="ttDiPreToSapLogMain" class="easyui-layout" fit="true">
    <div data-options="region:'center'" style="padding:1px;">
        <t:datagrid name="ttDiPreToSapLogList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
                    actionUrl="ttDirectPresentPoliMainController.do?findShowIntroducedIntoSAPLog" onClick="clickAfterFun" onLoadSuccess="onLoadSuccessFun" >
            <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
            <t:dgCol title="状态" field="contentStr" sortable="false" ></t:dgCol>
            <t:dgCol title="创建人" field="createName" sortable="false" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDateStr" query="true" sortable="false" ></t:dgCol>
            <t:dgToolBar title="重新处理" icon="icon-edit" url="ttDirectPresentPoliMainController.do?reStarDoAfterSapData" funname="reStarDoAfterSapData"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div data-options="region:'east',
	title:'',
	collapsed:true,
	split:true,
	border:false"
         style="padding:1px;width:400px;">
        <t:datagrid name="ttDiPreToSapLogDetailList" fitColumns="true" queryMode = "group" idField="id" singleSelect="false"
                    pagination="true" autoLoadData="false" actionUrl="ttDirectPresentPoliMainController.do?findShowIntroducedIntoSAPLogDetail">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false"  ></t:dgCol>
            <t:dgCol title="状态" field="contentstr" sortable="false" ></t:dgCol>
            <t:dgCol title="政策维护编码" field="ownNum" sortable="false" ></t:dgCol>
            <t:dgCol title="错误信息" field="errorMsg" sortable="false"  ></t:dgCol>
        </t:datagrid>
    </div>
</div>

<script type="text/javascript">
    var li_east = 0;
    $(document).ready(function(){
        layoutEast();
        //给时间控件加上样式
        addTimeCss("ttDiPreToSapLogList","createDateStr","yyyy-MM");
    });
    function  addTimeCss(grid,inputName,dataFmt){
        var inputObj = returnInputObj(grid,inputName);
        inputObj.attr("readonly",true).attr("id",inputName).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:dataFmt,onpicked:function(){return ;}});});
    }
    //返回datagrid的input的search框对象
    function returnInputObj(grid,inputName){
        return $("#" + grid + "Form").find("input[name='" + inputName + "']");
    }

    function layoutEast(){
        if(li_east == 0){
            $('#ttDiPreToSapLogMain').layout('expand','east');
            li_east ++;
        }
    }

    function onLoadSuccessFun(data){
        loadToSapLogDetailDataList('0');
    }

    function clickAfterFun(rowIndex,rowData) {
        var headLogId = rowData.id;
        loadToSapLogDetailDataList(headLogId);
    }

    function loadToSapLogDetailDataList(headLogId){
        var queryParams = $('#ttDiPreToSapLogDetailList').datagrid('options').queryParams;
        queryParams.headLogId = headLogId;
        $("#ttDiPreToSapLogDetailList").datagrid({url:"ttDirectPresentPoliMainController.do?findShowIntroducedIntoSAPLogDetail"});
    }
    //后续重新处理
    function reStarDoAfterSapData(title, url, grid, width, height){
        var grObj = $('#' + grid);
        var rowDatas = grObj.datagrid('getSelections');
        if (rowDatas == null || rowDatas.length == 0) {
            tip("请选择一条项目");
            return ;
        }
        if(rowDatas.length != 1){
            tip("请选择一条项目");
            return ;
        }
        var rowData = rowDatas[0];
        var thisData = {
            id : rowData.id
        }
        var data = ajaxPost(thisData,url);
        if (data.success){
            grObj.datagrid('reload');
            W.$('#ttDirectresentPoliMainList').datagrid('reload');
//            rowData.constructor = data.obj;
//            reloadThisRowData(grid,rowData);
        }
        tip(data.msg)
    }

    //刷新一条数据
    function reloadThisRowData(grid,rowData){
        var gridObj = $('#' + grid);
        var rowIndex = gridObj.datagrid('getRowIndex',rowData);
        var datas = {
            index : rowIndex,
            row : rowData
        }
        gridObj.datagrid('updateRow',datas);
        clickAfterFun(rowIndex,rowData);
    }

    //-------------------------共调fun

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }
</script>
