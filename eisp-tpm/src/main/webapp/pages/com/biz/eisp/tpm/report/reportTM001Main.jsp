<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div id="customer" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">

        <t:datagrid name="customerList" title="客户管理" extendTableName="${extendTableName}" idField="id"  fit="true"
                    actionUrl="reportTM001Controller.do?findReportList" queryMode="group"   fitColumns="false" pagination="true">

            <!--自定义非显示的查询条件 begin -->
            <t:dgCol title="对接人职位" field="dockPosition"  columnOrder="16" query="true"  hidden="false"  ></t:dgCol>
            <t:dgCol title="对接人" field="dockUserName"  columnOrder="17" query="true"  hidden="false"  ></t:dgCol>
            <t:dgCol title="业务组" field="businessGroup" columnOrder="18"   dictionary="business_group"  hidden="true"  ></t:dgCol>
            <!--自定义非显示的查询条件 end -->

            <!-- 工具栏操作  begin -->
            <t:dgToolBar title="查看" icon="icon-look" url="tmCustomerController.do?goTmCustomer&optype=2" funname="detail" width="1500" height="500"></t:dgToolBar>
            <t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="tmCustomerController.do?goTmCustomer&optype=1" funname="update" width="1500" height="500"></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-dataOut" url="reportTM001Controller.do?exportXls" funname="excelExport" ></t:dgToolBar>
            <!-- 工具栏操作  end -->

        </t:datagrid>
    </div>
</div>

<c:if test="${not empty includeJSP}">
    <jsp:include page="${includeJSP}" flush="true"></jsp:include>
</c:if>

<script type="text/javascript">
    //关联终端按钮触发事件
    function showTerminals(){
        var rowsData = $('#customerList').datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择查看项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再查看');
            return;
        }
        var  url = 'tmCustomerController.do?goTermCustPostMain&id=' + rowsData[0].id;
        openwindow("关联终端", url,"customerList", 680, 350);
    }

</script>

