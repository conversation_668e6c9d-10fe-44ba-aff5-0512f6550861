<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tdTemplatePartDatagrid"  fitColumns="false" title="模块列表"
                    actionUrl="tdTemplateController.do?findTdTemplatePartList&id=${id}" idField="id" fit="true"
                    queryMode="group" pageSize="20">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="状态" field="status" width="50" replace="有效_1,失效_0"></t:dgCol>
            <t:dgCol title="模块名称" field="tplPartName" width="120"></t:dgCol>
            <t:dgCol title="模块标签" field="tplPartTag" width="120"></t:dgCol>
            <t:dgCol title="替代标签" field="replaceTag" width="120"></t:dgCol>
            <t:dgCol title="是否显示" field="isShow" replace="不显示_0,显示_1"  width="70"></t:dgCol>
            <t:dgCol title="显示顺序" field="displaySort" width="70"></t:dgCol>
            <t:dgCol title="所属模板" field="tplType" width="120"></t:dgCol>
            <t:dgCol title="图片元素" field="imgElement" width="120"></t:dgCol>
            <t:dgCol title="是否带图片" field="isPhoto" width="80" replace="是_1,否_0"></t:dgCol>
            <t:dgCol title="备注" field="risk" width="200"></t:dgCol>
            <t:dgToolBar title="新增" icon="icon-add" url="tdTemplateController.do?goTdTemplatePartForm" funname="add"
                         ></t:dgToolBar>
            <t:dgToolBar title="修改" icon="icon-edit" url="tdTemplateController.do?goTdTemplatePartForm" funname="update"
                         ></t:dgToolBar>
            <t:dgToolBar title="查看" icon="icon-search" url="tdTemplateController.do?goTdTemplatePartForm"
                         funname="detail"></t:dgToolBar>
            <t:dgToolBar title="作废" icon="icon-remove" onclick="delTdtemplatePart();"></t:dgToolBar>
            <t:dgToolBar title="恢复" icon="icon-undo" onclick="undoTdtemplatePart();"></t:dgToolBar>
        </t:datagrid>

    </div>
</div>

<script>
    function delTdtemplatePart() {
        modifyStatus(0);
    }
    function undoTdtemplatePart() {
        modifyStatus(1);
    }


    function modifyStatus(status) {
        var rows = $("#tdTemplatePartDatagrid").datagrid('getSelections');
        if(rows && rows.length > 0){
            var id = rows[0].id;
            $.ajax({
                url: "tdTemplateController.do?saveTdTemplatePart",
                data: {
                    status: status,
                    id: id
                }, success: function (data) {
                    data = JSON.parse(data);
                    if (data.success) {
                        $("#tdTemplatePartDatagrid").datagrid('reload');
                    }
                    tip(data.msg);
                }
            })
        }else{
            tip("请选择一条记录操作")
        }

    }

    
</script>

