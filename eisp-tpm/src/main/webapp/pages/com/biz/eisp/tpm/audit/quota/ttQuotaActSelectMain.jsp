<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttActQuotaMainSelectList" fitColumns="true" title="待选费用明细" queryMode = "group" idField="id" pagination="true"
                    autoLoadData="true" actionUrl="ttAuditQuotaController.do?findTtActPassList&billMainId=${billMainId}" singleSelect="false">
            <t:dgCol title="主键" hidden="true" field="id"  ></t:dgCol>
            <t:dgCol title="业务主键" hidden="true" field="businessKey"></t:dgCol>
            <t:dgCol title="活动类型" field="actModeCode" query="true" dictionary="audit_act_mode_type"></t:dgCol>
            <t:dgCol title="活动类型" field="actModeName" hidden="true" ></t:dgCol>
            <t:dgCol title="活动编码" field="actCode"  query="true"></t:dgCol>
            <t:dgCol title="活动名称" field="actName" query="true" ></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" query="true"></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountName" query="true" ></t:dgCol>
            <t:dgCol title="归属事业部" field="businessUnitName" query="true" ></t:dgCol>
            <t:dgCol title="支付方式" field="paymentCode" query="true" dictionary="payment_type"></t:dgCol>
            <t:dgCol title="产品名称" field="productName" query="true" ></t:dgCol>
            <t:dgCol title="流程类型" hidden="true" field="actTypeCode" dictionary="act_type_quota"></t:dgCol>
           <%-- <t:dgCol title="流程类型" field="actTypeName"></t:dgCol>--%>
            <t:dgCol title="客户编码" field="customerCode" hidden="true" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true"  ></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate" formatter="yyyy-MM-dd" ></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd"  ></t:dgCol>
            <t:dgCol title="活动申请金额（元）" field="amount"  ></t:dgCol>
            <t:dgToolBar title="添加" icon="icon-add"  onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add"  onclick="addAllItem()"></t:dgToolBar>

            <t:dgToolBar title="查看活动明细" icon="icon-add"  onclick="showDetail()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div region="east" style="width:550px;padding:1px;">
        <t:datagrid name="ttActQuotaMainSelectedList" fitColumns="false"
                                 title="选择费用明细" queryMode = "group" idField="id" pagination="false"
                                 autoLoadData="false"  actionUrl="" singleSelect="false" >
        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
        <t:dgCol title="活动类型编码" field="actModeCode" hidden="true"></t:dgCol>
        <t:dgCol title="活动类型" field="actModeName" width="100"></t:dgCol>
        <t:dgCol title="活动编码" field="actCode" width="100" ></t:dgCol>
        <t:dgCol title="活动名称" field="actName" width="200" ></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" width="100"></t:dgCol>
        <t:dgCol title="流程类型" hidden="true" field="actTypeCode" dictionary="act_type_quota" width="250"></t:dgCol>
            <t:dgCol title="归属事业部" field="businessUnitName" width="150" ></t:dgCol>
        <%--<t:dgCol title="流程类型" field="actTypeName" width="150" ></t:dgCol>--%>
        <t:dgCol title="支付方式" field="paymentCode"  dictionary="payment_type" width="150"></t:dgCol>
        <t:dgCol title="客户编码" field="customerCode" hidden="true" ></t:dgCol>
        <t:dgCol title="客户名称" field="customerName" width="200"></t:dgCol>
        <t:dgCol title="开始时间" field="beginDate" formatter="yyyy-MM-dd" width="100" ></t:dgCol>
        <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd"  width="100"></t:dgCol>
        <t:dgCol title="活动申请金额（元）" field="amount"  width="100"></t:dgCol>
        <t:dgToolBar title="移除" icon="icon-remove"  onclick="removeItemBkw()"></t:dgToolBar>
        <t:dgToolBar title="全部移除" icon="icon-remove"  onclick="removeAllItemBkw()"></t:dgToolBar>

    </t:datagrid>

        <div id="btn_sub" onclick="submitForm()"></div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        $("#ttActQuotaMainSelectListtb_r").find("input[name='yearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        var str='<label title="申请金额汇总" style="margin-left: 239px">申请金额汇总:</label> <span style="color: red" id="allApplyAmount">0</span>';

        $("#ttActQuotaMainSelectedList_toolbar_div").parent().append(str);
        $("#ttActQuotaMainSelectedList_toolbar_div").remove();

        //双击添加
        $('#ttActQuotaMainSelectList').datagrid({
            //双击事件
            onDblClickRow :function(rowIndex,rowData){
                $("#ttActQuotaMainSelectedList").datagrid("insertRow",{row:rowData});
                loadElecteGrid();
                setAllApplyAmount();
            }
        });
        //双击移除
        $('#ttActQuotaMainSelectedList').datagrid({
            //双击事件
            onDblClickRow :function(rowIndex,rowData){
                //移除该数据
                $('#ttActQuotaMainSelectedList').datagrid('deleteRow',rowIndex);
                loadElecteGrid();
                setAllApplyAmount();
            }
        });
    })
    function showDetail() {
        var rowsData = $('#ttActQuotaMainSelectList').datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择查看项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再查看');
            return;
        }

        var actModeCode  = rowsData[0].actModeCode;

        if(actModeCode == 'quota_act_type'){
            var url1 = 'ttActQuotaMainController.do?goTtActQuotaView&load=detail&id=' + rowsData[0].businessKey;
            createdetailwindow("活动详情", url1, 850, 500);

        }else if(actModeCode == 'ad_act_type'){
            var url2 = 'ttActAdController.do?goTtActLongTermTab&id=' + rowsData[0].businessKey;
            createdetailwindow("活动详情", url2, 850, 500);

        }else if(actModeCode == 'longterm_act_type'){
            var url3 = 'ttActLongTermController.do?goTtActLongTermTab&id=' + rowsData[0].businessKey;
            createdetailwindow("活动详情", url3, 850, 500);
        }else{
            tip('产品活动没有此功能');
            return;
        }

    }
    //添加
    function addItem(){
        var seletctTarget =  $("#ttActQuotaMainSelectList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttActQuotaMainSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
        setAllApplyAmount();
    }

    //添加全部
    function addAllItem(){
        var name = "ttActQuotaMainSelectList";
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        //默认分页数
        queryParams.rows = 999999;

        $.ajax({
            url:"ttAuditQuotaController.do?findTtActPassList&billMainId=${billMainId}",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttActQuotaMainSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
        setAllApplyAmount();
    }

    function removeItemBkw() {
        var checkListTarget =  $("#ttActQuotaMainSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        var copyRows = [];
        for ( var j= 0; j < checkListTarget.length; j++) {
            copyRows.push(checkListTarget[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttActQuotaMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttActQuotaMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
        setAllApplyAmount();
    }

    function removeAllItemBkw() {
        var rowsData = $("#ttActQuotaMainSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttActQuotaMainSelectedList").datagrid("getRows");

        var copyRows = [];
        for ( var j= 0; j < rows.length; j++) {
            copyRows.push(rows[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttActQuotaMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttActQuotaMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
        $("#allApplyAmount").html("0");
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttActQuotaMainSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].id+"'";
            }
        }
        var actType=$("select[name='actType']").val();
        $('#ttActQuotaMainSelectList').datagrid("reload", {"exclusiveCodes":exclusiveCodes,"actType":actType});
    }

    function submitForm() {
        var rows = $("#ttActQuotaMainSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }
        var actCodes = [];
        var actModeCodes = [];
        for ( var i = 0; i < rows.length; i++) {
            actCodes.push(rows[i].actCode);
            actModeCodes.push(rows[i].actModeCode);
        }

        var result=false;
        $.ajax({
            async: false,
            url: "ttAuditQuotaController.do?saveTtQuotaAudit",
            data: {
                actModeCodes:actModeCodes.join(","),
                actCodes:actCodes.join(","),
                billMainId : '${billMainId}'
            },
            type: "post",
            dataType : "json",
            success : function(data) {
                if(data.success){
                    result=data.success;
                } else {
                    tip(data.msg);
                }
            },
            error:function(){
                tip("服务器异常，请稍后再试");
            }
        });

        return result;
    }
    function setAllApplyAmount() {
        var rows = $("#ttActQuotaMainSelectedList").datagrid("getRows");
        var allApplyAmount = 0;
        for ( var i = 0; i < rows.length; i++) {
            allApplyAmount =allApplyAmount+parseFloat(rows[i].amount);
        }
        $("#allApplyAmount").html(allApplyAmount);
    }
</script>

