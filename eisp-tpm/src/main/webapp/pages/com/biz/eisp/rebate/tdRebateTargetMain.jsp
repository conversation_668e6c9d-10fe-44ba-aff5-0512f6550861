<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center" style="padding:1px;">
		<t:datagrid name="tdRebateTargetList"    fitColumns="false" title="返利目标管理" actionUrl="tdRebateTargetController.do?findTdRebateTargetList"
					idField="id" fit="true" queryMode="group"  singleSelect="false" pageSize="30">
			<t:dgCol title="主键"  field="id"  hidden="true"   ></t:dgCol>
			<t:dgCol title="非直供分销商编码"  field="disbCode"     query="true"  ></t:dgCol>
			<t:dgCol title="非直供分销商名称"  field="disbName" query="true"></t:dgCol>
			<t:dgCol title="上级客户编码"  field="kunnr"       ></t:dgCol>
			<t:dgCol title="上级客户名称"  field="kunnrName" ></t:dgCol>
			<t:dgCol title="目标对象"  field="targetObject"  query="true" ></t:dgCol>
			<t:dgCol title="目标单位"  field="targetUnit" replace="箱_0,万元_1"  ></t:dgCol>
			<t:dgCol title="目标期间"  field="targetName" dictionary="rebate_target_period"  ></t:dgCol>
			<t:dgCol title="目标类型"  field="targetType" replace="季度目标_0,月度目标_1" query="true" ></t:dgCol>
			<%--<t:dgCol title="一季度"  field="targetValueQuarterOne"   ></t:dgCol>--%>
			<%--<t:dgCol title="二季度"  field="targetValueQuarterTwo"   ></t:dgCol>--%>
			<%--<t:dgCol title="三季度"  field="targetValueQuarterThree"   ></t:dgCol>--%>
			<%--<t:dgCol title="四季度"  field="targetValueQuarterFour"   ></t:dgCol>--%>
			<t:dgCol title="目标值"  field="targetValue"   ></t:dgCol>
			<t:dgCol title="开始日期"  field="startDate" formatter="yyyy-MM-dd"   ></t:dgCol>
			<t:dgCol title="结束日期"  field="endDate"  formatter="yyyy-MM-dd"  ></t:dgCol>

			<t:dgToolBar title="新增" operationCode="add" icon="icon-add" url="tdRebateTargetController.do?goTdRebateTargetForm" width="820" height="500" funname="add" ></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="tdRebateTargetController.do?goTdRebateTargetEdit" funname="update" ></t:dgToolBar>
			<t:dgToolBar title="查看" operationCode="search" icon="icon-search" url="tdRebateTargetController.do?goTdRebateTargetEdit" funname="detail" width="500" height="400"></t:dgToolBar>
			<t:dgToolBar title="删除" operationCode="remove"  icon="icon-remove" url="tdRebateTargetController.do?delTdRebateTarget" funname="deleteALLSelect"></t:dgToolBar>
            <t:dgToolBar title="导入" operationCode="dataIn" icon="icon-dataIn" onclick="importDataByXml({impName:'tdRebateTarget', gridName:'tdRebateTargetList'})" ></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="tdRebateTargetController.do?exportXls" funname="excelExport" ></t:dgToolBar>
		</t:datagrid>
	</div>
</div>

<script>

</script>
