<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<head>
	<style>
		.dtci_show_advice{display:table;line-height:26px;margin-top:10px;widtn:100%;}
		.dtci_show_advice>span{float:left;}
		.dtci_show_advice>span:first-child{width:100px;min-height:26px;text-align:right;}
		.dtci_show_advice>span>a{float:left;}
	</style>
	<style>
		#sureFilesDetail{display:table;}
		.sureFilesDetail_a{/* float:left; */padding-right:20px;margin-right:10px;position:relative;}
		.sureFilesDetail_a img{position:absolute;top:7px;right:0;}
	</style>
</head>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div region="north"  style="padding: 1px;height: 60px;">
	<fieldset style="height: 60px;padding: 10px;border: 2px solid #b9d5ee;" >
		<legend style="color: red">核销信息</legend>
		<t:formvalid formid="submitForm" layout="" dialog=""  action=""  >
			<input type="hidden" name="actCode" id="actCode" value="${vo.actCode}"   />
			<input type="hidden" name="applyIds" id="applyIds" value="${vo.applyIds}"   />
			<input type="hidden" name="auditCode" id="auditCode" value="${vo.auditCode}"   />
			<input type="hidden" name="id" id="id" value="${vo.id}"   />
			标题：<input  name="title" id="title" value="${vo.title}" datatype="*" readonly="readonly"  class="inputxt"  style="margin-right: 2%;" />
			所属公司:<input  name="unitName" id="unitName" value="${vo.unitName}" datatype="*" readonly="readonly"   class="inputxt"  />

			广告类型：<input  name="adType" id="adType"datatype="*"value="${vo.adType}" readonly="readonly" class="inputxt" style="margin-right: 2%;" />

			核销金额：<input  name="auditAmount" id="auditAmount"datatype="*"value="${vo.auditAmount}" readonly="readonly"  class="inputxt"  />

		</t:formvalid>
	</fieldset>
</div>



<div style="margin-top: 20px;">
<div id="system_org_tbaList" class="easyui-layout" style="height: 400px;width: 1500px;">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="actCheckList" title=""  actionUrl="ttOpenadAuditController.do?findTsActCheckvoList&actCode=${vo.actCode}"
					 idField="id" fit="true"  fitColumns="false"   singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="bussinessId" field="bussinessId" hidden="true"></t:dgCol>
			<t:dgCol title="活动单号" field="actCode"   width="200"></t:dgCol>
			<t:dgCol title="检查日期" field="createDate" formatter="yyyy-MM-dd" width="200"></t:dgCol>
			<t:dgCol title="检查人" field="checkUser"  width="200"></t:dgCol>
			<t:dgCol title="GPS位置信息" field="gpsAddress"  width="200"></t:dgCol>
			<t:dgCol title="周边环境" field="imgPath"  hidden="true" width="200"></t:dgCol>
			<t:dgToolBar title="图片视频" icon="icon-search"   url=""  funname="showPic"></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
</div>

<div>
	<div class="dtci_show_advice">

		<div class="dtci_show_advice">
			<span>附件</span>
			<span id="sureFilesDetail" >
			</span>
		</div>


		<%--<span>上传操作</span>--%>
		<%--<span>--%>
	         <%--<div class="dtci_bottom">--%>
				<%--<t:uploadH5 name="file_upload" buttonText="选择文件" onUploadSuccess="sureUpload" dialog="false" 	 callback=""--%>
							<%--uploader="tbAttachmentController.do?saveFiles&businessKey=${vo.actCode }&attachmentType=${attachmentType }&extendService=${extendService }" extend="${extend}" id="file_upload" formData=""></t:uploadH5>--%>
	         	<%--<div class="dtci_bottom_a"><a href="javascript:;" class="easyui-linkbutton" plain="true" id="btn_sub" iconCls="icon-upload" onclick="upload()">确定上传</a></div>--%>
	         	<%--<div></div>--%>
	         	<%--<div id="filediv" style="width: 300px;"></div>--%>
	         <%--</div>--%>
			<%--</span>--%>
	</div>
</div>


<script type="text/javascript">
    function getData() {


        var ids="";
        var rows = $("#actCheckList").datagrid('getRows');
        if(rows!=null&&rows.length!=0){
            for(var i=0;i<rows.length;i++){
                ids+=rows[i].id+',';
            }
            $("#checkIds").val(ids);
        }else{


        }

    }


    $(function(){
        sureUpload();
    })

    function deleteFile(id, extendService){
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url:"tbAttachmentController.do?delObjFile",
                    data:{fileKey:id,extendService:extendService},
                    method:"post",
                    success:function(data){
                        sureUpload();
                    }
                });
            }
        });
    }

    function sureUpload(){
        var isReadOnly = '${isReadOnly}';
        $.ajax({
            url:"tbAttachmentController.do?findAttachmentList&businessKey=${vo.actCode}&attachmentType=${attachmentType }&extendService=${extendService }",
            data:{},
            method:"post",
            success:function(data){
                var str="";
                var d = $.parseJSON(data);
                if (d.success) {
                    var rows = d.rows;
                    for(var i =0;i<rows.length;i++){
                        str += '<div class="sureFilesDetail_a"><a targe="_blank" href="tbAttachmentController.do?viewFile&fileid='
                            +rows[i].id+'" class="easyui-linkbutton l-btn l-btn-plain" plain="true" iconcls="icon-download"><span class="l-btn-left"><span class="l-btn-text icon-download l-btn-icon-left">'
                            +rows[i].attachmentTitle+'.'+rows[i].extend+'</span></span></a>';
                       /*if(isReadOnly != 'true') {
                            str += '<img onclick="deleteFile(\''
                                + rows[i].id
                                + '\')" src="resources/Validform/images/error.png" />';
                        }*/
                        str += '</div>';
                    }
                }
                if(str == "") {
                    $("#sureFiles").hide();
                } else {
                    $("#sureFiles").show();
                }
                $("#sureFilesDetail").html(str);
                $("#viewmsg").html("");
            }
        });
    }

    //展示图片
    function showPic() {
        var rows= $("#actCheckList").datagrid('getSelections');
        var bussinessId=rows[0].id;
        /*tip(bussinessId);*/

        var url="tsPictureController.do?goTrainPictureMain&bussinessId="+bussinessId;
        createwindowExt("图片详情",url,800,800, {
            button:[
                {

                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });

    }

</script>
