<%--<%@ taglib prefix="t" uri="/base-tags" %>--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="TtDrpList" fitColumns="false" title="终端查看"
                    actionUrl="${TtDrpUrl}" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="终端编码" field="terminalCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="终端名称" field="terminalName" query="true" width="150"></t:dgCol>
            <t:dgCol title="终端类型" field="terminalType" dictionary="terminal_type" query="true" width="150"></t:dgCol>
            <t:dgCol title="上级客户名称" field="customerName" query="true" width="150"></t:dgCol>
            <t:dgCol title="上级客户编码" field="customerCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="终端联系人" field="linkMan" query="true" width="150"></t:dgCol>
            <t:dgCol title="联系人电话" field="linkmanPhone" query="true" width="150"></t:dgCol>
            <t:dgCol title="省" field="province" formType="combobox"  formUrl="ttDrpController.do?findProvinceComboBox" query="true" width="150"></t:dgCol>
            <t:dgCol title="市" field="city" formType="combobox" formUrl="" query="true" width="150"></t:dgCol>
            <t:dgCol title="区" field="area" formType="combobox" formUrl="" query="true" width="150"></t:dgCol>
            <t:dgCol title="组织" field="orgCode" hidden="true" query="true"
                     replace="奶粉事业部_0102,低温事业部_0100,常温事业部_0101,长效营销中心_0104" width="150"></t:dgCol>
            <%--<t:dgCol title="组织名称" field="orgName"  width="150"></t:dgCol>--%>
            <t:dgCol title="终端地址" field="address" query="true" width="150"></t:dgCol>
            <t:dgCol title="创建人" field="createName" width="150"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" query="true" queryMode="group"  formatter="yyyy-MM-dd HH:mm:ss" width="150"></t:dgCol>
            <t:dgToolBar title="查看" icon="icon-look" url="ttDrpController.do?goTmTerminal&optype=2" funname="detail" width="1500" height="500"></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-put" url="ttDrpController.do?exportXls" funname="excelExport"></t:dgToolBar>
            <%--<t:dgToolBar operationCode="add" title="新增" height="500" width="800" icon="icon-add" url="orgPositionController.do?goOrgPositionForm" funname="add"></t:dgToolBar>--%>
            <%--<t:dgToolBar title="清除" height="500" width="800" icon="icon-edit" onclick="test()"></t:dgToolBar>--%>
            <%--<t:dgToolBar title="刷新" height="500" width="800" icon="icon-reload" onclick="reloadtest()"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="del" title="刪除"  height="500" width="800" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'orgPositionImport', gridName:'OrgPositionList'})"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="orgPositionController.do?exportXls"  funname="excelExport"></t:dgToolBar>--%>
        </t:datagrid>
    </div>
</div>
<script>

    function test() {
//        $("#TtDrpListtb_r").find("input[name='city']").combobox('clear');
        debugger;
        $("#TtDrpList").find("input[name='province']").combobox('setValue','');
    }
    function reloadtest() {

        window.location.reload();
    }

    //三级联动 选择 省 市 区
    $(function () {
        //省
        $("#province").combobox({
            valueField: 'value',
            textField: 'text',
            onSelect: function (data) {
                var id = data.value;
                city(id);
                area("-2")//传一个为空的情况强制查询数据为空
            }
        });
    });
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
  function city(id) {
      $("#city").combobox({
          url: 'ttDrpController.do?findCityComboBox&id=' + id,
          onSelect: function (data) {
              var id = data.value;
                        //通过查到的市的id传到后台查询出区
              area(id);

          }
      })
  }
  function area(id){
      $("#area").combobox({
          url: 'ttDrpController.do?findAreaComboBox&id=' + id,
          valueField: 'value',
          textField: 'text'
      })
  }
    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#TtDrpList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "orgPositionController.do?deleteOrgPosition&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#TtDrpList").datagrid("reload");
                    }
                });
            }
        });
    }
</script>
