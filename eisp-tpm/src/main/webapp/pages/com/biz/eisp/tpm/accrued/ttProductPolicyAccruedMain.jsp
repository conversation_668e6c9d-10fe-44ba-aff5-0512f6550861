<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="TtProductPolicyWithholdingList" fitColumns="false" title="产品政策预提"
                    actionUrl="${policyVo}" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="数据状态" field="xxx"  query="true" width="150"></t:dgCol>
            <t:dgCol title="预提年月" field="xxx"  query="true" width="150"></t:dgCol>
            <t:dgCol title="活动编号" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="活动名称" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="组织" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="客户名称" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="产品名称" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="预算科目" field="xxx"  width="150"></t:dgCol>
            <t:dgCol title="活动大类" field="xxx"  width="150"></t:dgCol>
            <t:dgCol title="活动细类" field="xxx"  width="150"></t:dgCol>
            <t:dgCol title="活动开始时间" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="活动结束时间" field="xxx" query="true" width="150"></t:dgCol>
            <t:dgCol title="活动申请金额（元）" field="xxx"  width="150"></t:dgCol>
            <t:dgCol title="预提金额（元）" field="xxx"  width="150"></t:dgCol>
            <t:dgCol title="实际费用金额（元）" field="xxx"  width="150"></t:dgCol>
            <%--<t:dgCol title="创建人" field="createName" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" width="150"></t:dgCol>--%>
            <t:dgCol title="最近更新人" field="updateName" width="150"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd HH:mm:ss" width="150"></t:dgCol>

            <t:dgToolBar  title="手工预提录入" height="500" width="800" icon="icon-add" url="ttProductPolicyWithholdingController.do?goTtProductPolicyWithholdingForm" funname="add"></t:dgToolBar>
            <%--<t:dgToolBar operationCode="edit" title="编辑" height="500" width="800" icon="icon-edit" url="orgPositionController.do?goOrgPositionForm" funname="update"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="del" title="刪除" height="500" width="800" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'orgPositionImport', gridName:'OrgPositionList'})"></t:dgToolBar>--%>
            <t:dgToolBar  title="导出" icon="icon-dataOut" url="ttProductPolicyWithholdingController.do?exportXls" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="日志" icon="icon-log" url="ttProductPolicyWithholdingController.do?goTtDirectIncomeLogMain" funname="detail" width="1200"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $("#TsInvoicingListtb_r").find("input[name='systemDate']").addClass("Wdate").css({
        'height': '20px',
        'width': '100px'
    }).click(function () {
        WdatePicker({dateFmt: 'yyyy-MM-dd'});
    });
    $(function () {
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='createDate']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='periodStart']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='periodEnd']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });
    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#TtProductPolicyWithholdingList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "orgPositionController.do?deleteOrgPosition&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#TtProductPolicyWithholdingList").datagrid("reload");
                    }
                });
            }
        });
    }
</script>
