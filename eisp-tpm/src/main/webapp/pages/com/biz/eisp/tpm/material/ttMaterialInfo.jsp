<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmOrgSelectMain.js"></script>
<div class="easyui-layout" fit="true">
    <div region="west" style="width:350px;">
        <t:formvalid formid="formobj" layout="div" dialog="true"  refresh="true">
            <div class="form">
                <label class="Validform_label">事业部: </label>
                <span>${vo.orgName }</span>
            </div>

            <div class="form">
                <label class="Validform_label">开始申报时间: </label>
                <span>${vo.beginDate}</span>
            </div>
            <div class="form">
                <label class="Validform_label">物料名称: </label>
                <span>${vo.materialName}</span>
            </div>
            <div class="form">
                <label class="Validform_label">结束申报时间: </label>
                <span>${vo.endDate}</span>
            </div>
            <div class="form">
                <label class="Validform_label">使用说明: </label>
                <span>${vo.description}</span>
            </div>
            <div class="form">
                <label class="Validform_label">最小包装量: </label>
                <span>${vo.minPack}EA</span>
            </div>
            <div class="form">
                <label class="Validform_label">成本价: </label>
                <span>${vo.costPrice}元</span>
            </div>
            <div class="form">
                <label class="Validform_label">物料单价: </label>
                <span>${vo.price}元</span>
            </div>

            <div class="form">
                <label class="Validform_label">可申报组织范围: </label>
                <span>${vo.reportableScope}</span>
            </div>
            <div class="form">
                <label class="Validform_label">可申报客户范围: </label>
                <span>${vo.reportableCustomerScope}</span>
            </div>
            <div class="form">
                <label class="Validform_label">排除组织范围: </label>
                <span>${vo.unReportableScope}</span>
            </div>
            <div class="form">
                <label class="Validform_label">排除客户范围: </label>
                <span>${vo.unReportableCustomerScope}</span>
            </div>
        </t:formvalid>
    </div>
    <div region="center">
        <div class="panel datagrid">
            <div class="datagrid-wrap panel-body">
                <div class="datagrid-toolbar">
                    <div class="datagrid-toolbar-search">
                    </div>
                </div>
                <div class="datagrid-view">
                    <table class="actTable" id="actTable">
                        <thead>
                        <tr>
                            <td style="display:none;">类别编码</td>
                            <td>事业部经销商分类</td>
                            <td>客户承担费用（元）</td>
                            <td>客户承担比例</td>
                            <td style="display:none"></td>
                        </tr>
                        </thead>
                        <tbody>
                        <c:if test="${not empty customerTypeList}">
                            <c:forEach items="${customerTypeList}" var="type">
                                <c:if test="${type.dictCode!=0}">
                                    <tr>
                                        <td style="display:none;">${type.dictCode}</td>
                                        <td>${type.dictValue}</td>
                                        <td><c:forEach items="${vo.priceList}" var="price"><c:if test="${price.customerTypeCode==type.dictCode}">${price.bearCost}</c:if></c:forEach></td>
                                        <td><c:forEach items="${vo.priceList}" var="price"><c:if test="${price.customerTypeCode==type.dictCode}">${price.bearScale}</c:if></c:forEach>%</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                            <c:forEach items="${customerTypeList}" var="type">
                                <c:if test="${type.dictCode==0}">
                                    <tr>
                                        <td style="display:none;">${type.dictCode}</td>
                                        <td>${type.dictValue}</td>
                                        <td><c:forEach items="${vo.priceList}" var="price"><c:if test="${price.customerTypeCode==type.dictCode}">${price.bearCost}</c:if></c:forEach></td>
                                        <td><c:forEach items="${vo.priceList}" var="price"><c:if test="${price.customerTypeCode==type.dictCode}">${price.bearScale}</c:if></c:forEach>%</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                        </c:if>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
    $(function(){
        $("input[name='beginDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='endDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

    });
    $("#orgCode").change(function(){
        var orgName=$("#orgCode").find("option:selected").text();
        $("#orgName").val(orgName);
    });
    //重置
    function resetCost(){
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            tdArr.eq(2).find("input").eq(0).val("");//费用
            tdArr.eq(3).find("input").eq(0).val("");//比例
        });
        $("#priceJson").val("");
    }


    //提交前把列表信息封装到form
    function convertDetailToForm(){
        var rows=[];
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            //console.log(tdArr);
            var customerTypeCode = tdArr.eq(0).html();//类型编码
            var customerTypeName =tdArr.eq(1).html();//类型名称
            var bearCost=tdArr.eq(2).find("input").eq(0).val();;//承担费用
            var bearScale = tdArr.eq(3).find("input").eq(0).val();//承担比例
            //组装单据
            var obj = {
                'customerTypeCode': customerTypeCode,
                'customerTypeName': customerTypeName,
                'bearScale': bearScale,
                'bearCost':bearCost
            };
            rows.push(obj);
        });
        var jsonStr=JSON.stringify(rows);
        console.log(jsonStr);
        $("#priceJson").val(jsonStr);
    }


    //计算百分比
    function countPercent(obj){
        var bearCost=$(obj).val();
        var price=$("#price").val();
        if(price==null||price==""){
            alert("请输入物料单价");
            $(obj).val("");
        }else if(bearCost!=null&&bearCost!=""){
            var trNode = $(obj).parent().parent(); //获取input的“爷爷”--tr
            var forthChildTdNode =  trNode.children().eq(3);//获取第四个td
            var firstInput = forthChildTdNode.children(); //获取第一个input
            var a=parseFloat(bearCost);
            var b=parseFloat(price);
            var value=(a/b*100).toFixed(2);
            firstInput.val(value);
        }
    }
    //计算费用
    function countPrice(obj) {
        var bearScale=$(obj).val();
        var price=$("#price").val();
        if(price==null||price==""){
            alert("请输入物料单价");
            $(obj).val("");
        }else if(bearScale!=null&&bearScale!=""){
            var trNode = $(obj).parent().parent(); //获取input的“爷爷”--tr
            var forthChildTdNode =  trNode.children().eq(2);//获取第四个td
            var firstInput = forthChildTdNode.children(); //获取第一个input
            var a=parseFloat(bearScale);
            var b=parseFloat(price);
            var value=(a*b/100).toFixed(2);
            firstInput.val(value);
        }
    }

    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }

    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }

    //选择物料
    function materialSelect(){
        safeShowDialog({
            content: "url:tdProductApiController.do?goMaterialMain",
            lock: true,
            title: "选择物料",
            width: 800,
            height: 400,
            cache: false,
            ok: function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null) {
                    alert("请选择一条物料信息");
                    return false;
                } else {
                    $("#materialName").val(selected[0].maktx);
                    $("#materialCode").val(selected[0].matnr);
                    $("#materialGroup").val(selected[0].matkl);
                    $("#costPrice").val(10.0);
                    $("#price").val(10.0*1.02);
                    return true;
                }

            },
            cancelVal: '关闭',
            cancel: true
            /* 为true等价于function(){} */
        });
    }
    //可申报组织范围选择
    function orgAllowSelect(){
        var orgCode=$("#curorgCode").val();
        var paraArr = {
            textName:'orgCode,orgName',
            inputTextName:'orgCode,orgName',
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型--不传或默认
            callBackFun : allowOrgInfo,
            pageData:{
                orgCode : orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    //排除组织范围选择
    function orgUnAllowSelect(){
        var orgCode=$("#curorgCode").val();
        var paraArr = {
            textName:'orgCode,orgName',
            inputTextName:'orgCode,orgName',
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型--不传或默认
            callBackFun : unAllowOrgInfo,
            pageData:{
                orgCode : orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    //可申报组织
    function allowOrgInfo(datas){
        var orgCodeStr = "";
        var orgNameStr="";
        for (var i = 0; i < datas.length; i++) {
            var dataObj = datas[i];
            if(orgCodeStr==""){
                orgCodeStr+=dataObj.orgCode;
                orgNameStr+=dataObj.orgName;
            }else{
                orgCodeStr+=","+dataObj.orgCode;
                orgNameStr+=","+dataObj.orgName;
            }
        }
        $("#reportableCodeScope").val(orgCodeStr);
        $("#reportableScope").val(orgNameStr);
    }

    //排除组织
    function unAllowOrgInfo(datas){
        var orgCodeStr = "";
        var orgNameStr="";
        for (var i = 0; i < datas.length; i++) {
            var dataObj = datas[i];
            if(orgCodeStr==""){
                orgCodeStr+=dataObj.orgCode;
                orgNameStr+=dataObj.orgName;
            }else{
                orgCodeStr+=","+dataObj.orgCode;
                orgNameStr+=","+dataObj.orgName;
            }
        }
        $("#unReportableCodeScope").val(orgCodeStr);
        $("#unReportableScope").val(orgNameStr);
    }
    //客户选择
    function customerSelect(type){
        safeShowDialog({
            content: "url:tmCommonMdmController.do?goSelectCustomerMain&orgId="+$("#orgId").val(),
            lock: true,
            title: "选择客户",
            width: 800,
            height: 400,
            cache: false,
            ok: function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null) {
                    tip("请选择至少一条客户信息");
                    return false;
                } else {
                    var str = "";
                    var idStr="";
                    var codeStr="";
                    $.each(selected,function(i, n) {
                        if (i == 0) {
                            str += n.customerName;
                            idStr+=n.id;
                            codeStr+=n.customerCode;
                        } else {
                            str += ",";
                            str += n.customerName;
                            codeStr+=n.customerCode;
                        }
                    });
                    if(type==1){
                        $("#reportableCustomerCode").val(codeStr);
                        $("#reportableCustomerScope").val(str);
                    }else if(type==2){
                        $("#unReportableCustomerCode").val(codeStr);
                        $("#unReportableCustomerScope").val(str);
                    }
                    return true;
                }

            },
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>
