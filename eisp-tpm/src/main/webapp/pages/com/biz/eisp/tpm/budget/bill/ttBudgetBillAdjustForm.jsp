<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>预算调整</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<div class="easyui-layout" fit="true">
	<div region="west" style="width:350px;border-right:0px;" title="调出方">
			<t:formvalid formid="budgetOut" layout="div" dialog="true" action="ttBudgetBillAdjustController.do?saveBudgetBalance" refresh="true">
				<input type="hidden" name="adjustFlag" value="-1">
				
				<div class="form">
					<label class="Validform_label">年度:</label>
					<input name="year" id="yearOut" class="inputxt" style="background:#E4E4E4" 
					       value="${budgetData.year}" readonly="readonly"/>
				</div>
				
				<div class="form">
		            <label class="Validform_label">月份:</label> 
		            <input id="monthOut" name="month" class="inputxt" style="background:#E4E4E4" value="${budgetData.month}" readonly="readonly"/>
		        </div>	
		        	
				<div class="form">
					<label class="Validform_label" name="custName">部门: </label> 
					<input id="orgOut" name="orgName" class="inputxt" style="background:#E4E4E4" value="${budgetData.orgName}" readonly="readonly"/>
					<input type="hidden" name="orgId" id="orgOutId" value="${budgetData.orgId}"/>
				</div>
				
				<div class="form">
					<label class="Validform_label">预算科目:</label>
					<input id="accountName" name="accountName" class="inputxt" style="background:#E4E4E4" value="${budgetData.accountName}" readonly="readonly"/>
					<input type="hidden" name="accountCode" id="costOut" value="${budgetData.accountCode}"/>
				</div>	
				
				<div class="form">
					<label class="Validform_label">期初金额:</label>
					<input name="periodAmount" id="perOut" class="inputxt" style="background:#E4E4E4;" readonly="readonly"
					       value="${budgetData.amount}">
				</div>	
				
				<div class="form">
					<label class="Validform_label">可用余额:</label>
					<input name="beforeAdjustBalance" id="beforeOut" class="inputxt" style="background:#E4E4E4;" 
					       value="${budgetData.balance}" readonly="readonly">
				</div>	
				
				<div class="form">
					<label class="Validform_label">调减金额:</label>
					<input name="adjustAmount" id="amountOut" type="text" class="inputxt" 
						   value="${billVo.quarter}"
						   datatype="/^([1-9][\d]{0,11}|0)(\.[\d]{1,2})?$/"
						   errormsg="只能是带两位小数或者整数或者位数不能超过12位" 
						   onblur="writeAmount()"/>
					<span style='color:red'>*</span>
					<input type="hidden" id="maxOutAmount" value="${maxAmount}">
					<span class="Validform_checktip" id="amountOutError">最大可调出金额:${maxAmount},最小为0</span>
				</div>	
								
				<div class="form">
					<label class="Validform_label">调减后余额:</label>
					<input name="afterAdjustBalance" id="afterOut" class="inputxt" style="background:#E4E4E4;" value="${billVo.quarter}" readonly="readonly">
				</div>
							
		</t:formvalid>
	</div>
	<!-- ---------------------------------------------------------------------------------------------------------------------------------- -->
	<div region="center" style="width:350px;" title="调入方">
		<t:formvalid formid="budgetIn" layout="div" dialog="true" action="ttBudgetBillAdjustController.do?saveBudgetBalance" refresh="true">
					<input type="hidden" name="adjustFlag" value="1">
		
					<div class="form">
						<label class="Validform_label">年度:</label>
						<input name="year" id="yearIn" type="text" class="Wdate" style="width:150px;"
						       value="${billVo.year}" readonly="readonly"
						       onchange="loadAmount(-1)" datatype="*"
						       onclick="WdatePicker({dateFmt:'yyyy'})"/>
						       <span style='color:red'>*</span>
						       <span class="Validform_checktip" id="yearInError"></span>
					</div>
					
					<div class="form">
			            <label class="Validform_label">月份:</label> 
						<select name="month" id="monthIn" onchange="loadAmount(-1)" datatype="*">
							<option value="">请选择</option>
							<option value="01">01</option><option value="02">02</option>
							<option value="03">03</option><option value="04">04</option>
							<option value="05">05</option><option value="06">06</option>
							<option value="07">07</option><option value="08">08</option>
							<option value="09">09</option><option value="10">10</option>
							<option value="11">11</option><option value="12">12</option>
						</select>
						<span style='color:red'>*</span>
						<span class="Validform_checktip" id="monthInError"></span>
			        </div>	
<%--
					<div class="form">
						<label class="Validform_label" name="custName">部门:</label>
						<input id="orgIn" name="orgName" onchange="loadAmount()" class="easyui-combotree" value="${targetVo.orgName }"/>
						<input type="hidden" name="orgId" id="orgInId" value="${targetVo.orgId }" datatype="*"/>
						<span style='color:red'>*</span>
						<span class="Validform_checktip" id="orgInIdError"></span>
					</div>--%>

					<div class="form">
						<label class="Validform_label">部门:</label>
						<input name="orgName" id="orgName" class="inputxt"   readonly="readonly"  readonly="readonly" value="${targetVo.orgName}" />
						<span style='color:red'>*</span>
						<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openOrgSelect();"></a>
						<input type="hidden" id="orgInId" name="orgId" value='${targetVo.orgCode}' datatype="*">
						<span class="Validform_checktip" id="orgInIdError"></span>
					</div>


					<div class="form">
						<label class="Validform_label">预算科目:</label>
						<select name="accountCode" id="costIn" onclick="validateCost()" onchange="loadAmount(-1)" datatype="*">
							<option value="">--请选择--</option>
							<c:forEach items="${financials}" var="fins">
								<option value="${fins.accountCode}">${fins.accountName}</option>
							</c:forEach>
						</select>
						<span style='color:red'>*</span>
						<span class="Validform_checktip" id="costInError"></span>
					</div>
					
					<div class="form">
						<label class="Validform_label">期初金额:</label>
						<input name="periodAmount" id="perIn" class="inputxt" style="background:#E4E4E4;" value="${billVo.quarter}" readonly="readonly">
					</div>	
					
					<div class="form">
						<label class="Validform_label">可用余额:</label>
						<input name="beforeAdjustBalance" id="beforeIn" class="inputxt" style="background:#E4E4E4;" value="${billVo.quarter}" readonly="readonly">
					</div>	
					
					<!-- 调入方：调整金额 -->
					<input type="hidden" name="adjustAmount" id="amountIn">
					
					<div class="form">
						<label class="Validform_label">调增后余额:</label>
						<input name="afterAdjustBalance" id="afterIn" class="inputxt" style="background:#E4E4E4;" readonly="readonly">
					</div>	
					
					<div class="form">
						<label class="Validform_label" name="areaName">备注: </label> 
						<textarea style="width:150px;height:100px;resize:none;" name="remark"></textarea>
					</div>	
		</t:formvalid>	
	</div>
</div>
</body>
</html>
<script type="text/javascript">

    //弹出选择部门
    function openOrgSelect() {
        safeShowDialog({
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            title : "选择企业组织",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function()
            {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#orgList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#orgInId").val("");
                    $("#orgName").val("");
                    return true;
                }
                $("#orgInId").val(rowsData.id);
                $("#orgName").val(rowsData.text);
                loadAmount();
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

	//提交前校验两边选择的年度+季度+部门+费用类型是否一样，一样不允许提交
	function validateData(){
		var yearOut = $("#yearOut").val();
		var monthOut = $("#monthOut").val();
		var orgIdOut = $("#orgOutId").val();
		var costOut = $("#costOut").val();
		
		var yearIn = $("#yearIn").val();
		var monthIn = $("#monthIn").val();
		var orgIdIn = $("#orgInId").val();
		var costIn = $("#costIn").val();
		
		if(yearOut == yearIn && monthOut == monthIn && orgIdOut == orgIdIn && costOut == costIn){
			return false;
		}
		return true;
	}

	function loadAmount(type){
		var year = $("#yearIn").val();
		var month = $("#monthIn").val();
		var orgId = $("#orgInId").val();
		var accountCode = $("#costIn").val();

		if (year != "" && month != "" && orgId != ""&& accountCode != "") {
			//判断选择的内容，是否在预算中存在
			var flag = true;
			var validateUrl = "ttBudgetBillAdjustController.do?validateChooseBudgetIsExistence&year="+ year
								+ "&month="+ month+ "&orgId="+ orgId+ "&accountCode=" + accountCode;
			$.ajax({url:validateUrl,type:"post",async:false,success:function(data){
				var d = $.parseJSON(data);
				if(d.success == false){
					flag = false;
					tip(d.msg);
					$("#perIn").val("");
					$("#beforeIn").val("");
				}
			}});
			if(flag == false){
				return false;
			}
			var url1 = "ttBudgetBillController.do?getBudgetPeriodAmount&year="+ year + "&month=" + month + "&orgId=" + orgId+ "&accountCode=" + accountCode;
			$.ajax({
				url : url1,type : "post",
				success : function(data) {
					var d = $.parseJSON(data);
					$("#perIn").val(d);
				}
			});
			var url2 = "ttBudgetBillController.do?getBudgetBalanceAjax&year="+ year
					+ "&month="+ month+ "&orgId="+ orgId+ "&accountCode=" + accountCode;
			$.ajax({
				url : url2,
				type : "post",
				success : function(data) {
					var d = $.parseJSON(data);
					if (d.success == false) {
						tip(d.msg);
					} else {
						$("#beforeIn").val(d.obj);
					}
				}
			});
		}
	}
	//计算金额
	function writeAmount(){
		var beforeIn = $("#beforeIn").val();
		if(beforeIn == ""){
			tip("请先选择调入预算");
			$("#amountOut").val("");
			return false;
		}
		//调减金额
		var amountOut = $("#amountOut").val();
		//最大金额
		var max = $("#maxOutAmount").val();
		
		//可用余额
		var beforeOut = $("#beforeOut").val();
		if (amountOut == "") {
			amountOut = 0;
		}
		if (isNaN(amountOut) || parseFloat(amountOut) < 0) {
			$("#amountOut").val("");
			return false;
		}
		if(parseFloat(amountOut) > parseFloat(max)){
			tip("调出金额不能大于最大可调出金额，请重写");
			$("#amountOut").val("");
			$("#amountOutError").addClass("Validform_wrong");
			return false;
		}
		
		//调入金额
		$("#afterOut").val(parseFloat(beforeOut)-parseFloat(amountOut));
		$("#amountIn").val(amountOut);
//		var afterIn = parseFloat(beforeIn) + parseFloat(amountOut);
        var afterIn = addNum(parseFloat(beforeIn),parseFloat(amountOut));
		var afterIn = $("#afterIn").val(afterIn);
        //调入金额保留两位小数，只能输入数字
//        $('#afterIn').numberbox({
//            min: 0,
//            precision: 2
//        });


		/*
		 * 高精减法函数
		 */
        function subtract(minuend, subtrahend) {
            var power = getMaxPowerForTen(minuend, subtrahend);
            return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
        }
        /**
         * 高精度加法函数
         */
        function addNum(summand1, summand2) {
            var power = getMaxPowerForTen(summand1, summand2);
            return (multiply(summand1, power) + multiply(summand2, power)) / power;
        }
        /**
         * 高精乘法函数
         */
        function multiply(multiplier, multiplicand) {
            var m = 0;
            var s1 = multiplier.toString();
            var s2 = multiplicand.toString();
            try {
                m += s1.split(".")[1].length;
            } catch (e) {
            }
            try {
                m += s2.split(".")[1].length;
            } catch (e) {
            }
            return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
        }
        //除法精确计算
        function highPrecisionDivision(arg1, arg2) {
            var t1 = 0, t2 = 0, r1, r2;
            try {
                t1 = arg1.toString().split(".")[1].length;
            }
            catch (e) {
            }
            try {
                t2 = arg2.toString().split(".")[1].length;
            }
            catch (e) {
            }
            with (Math) {
                r1 = Number(arg1.toString().replace(".", ""));
                r2 = Number(arg2.toString().replace(".", ""));
                return (r1 / r2) * pow(10, t2 - t1);
            }
        }
        /**
         * 获取最大次幂
         */
        function getMaxPowerForTen(arg1, arg2) {
            var r1 = 0;
            var r2 = 0;
            try {
                r1 = arg1.toString().split(".")[1].length;
            } catch (e) {
                r1 = 0;
            }
            try {
                r2 = arg2.toString().split(".")[1].length;
            } catch (e) {
                r2 = 0;
            }
            return Math.pow(10, Math.max(r1, r2));
        }
	}

	function validateCost() {
		var orgOut = $("#orgInId").val();
		if (orgOut == '') {tip("请先选择部门")}
	}
	
$(function(){
	//转入
	$("#orgIn").combotree('reload','tmOrgController.do?getParentOrg&selfId='+'');
	$("#orgIn").combotree({
		onShowPanel:function (){
		var text=$("#orgIn").combotree('getText');
			$("#orgIn").combotree('setValue',text);
			$("#orgIn").combotree('reload','tmOrgController.do?getParentOrg');
		}
	});
	$('#orgIn').combotree({
		width: 150,
		onSelect : function(node) {
			$("#orgInId").val(node.id);
		}
	});
	//转出
		$("#orgIn").combotree({
		onChange:function(n,o){
			loadAmount(-1);
		}		
	});
});

function validateAmount(){
	if($("#yearIn").val() == ""){
		$("#yearInError").addClass("Validform_wrong").attr("title","不能为空");
		return false;
	}
	if($("#quarterIn").val() == ""){
		$("#quarterInError").addClass("Validform_wrong").attr("title","不能为空");
		return false;
	}
	if($("#orgInId").val() == ""){
		$("#orgInIdError").addClass("Validform_wrong").attr("title","不能为空");
		return false;
	}else{
		$("#orgInIdError").removeClass("Validform_wrong").addClass("Validform_right").attr("title","");
	}
	if($("#costIn").val() == ""){
		$("#costInError").addClass("Validform_wrong").attr("title","不能为空");
		return false;
	}
	if($("#amountOut").val() == ""){
		$("#amountOutError").addClass("Validform_wrong").attr("title","不能为空");
		return false;
	}if(isNaN($("#amountOut").val())){
		$("#amountOutError").addClass("Validform_wrong").attr("title","不能为空");
		return false;
	}
	return true;
}

</script>