<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>定额预提公式配置</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttAccruedFormulaController.do?saveTtAccruedFormula" refresh="true">
	<input type="hidden" id="id" name="id" value="${vo.id}"/>
	<div class="form">
		<label class="Validform_label">年月: </label>
		<input name="yearMonth" id="yearMonth" datatype="*" style="width: 150px;"
					class="Wdate"  onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})"
			   readonly="readonly"  value="${vo.yearMonth}" />
		<span style="color: red;">*</span>
	</div>

	<div class="form">
		<label class="Validform_label">活动细类: </label>
		<input name="costAccountName" id="costAccountName" class="inputxt"  datatype="*" readonly="readonly"  readonly="readonly" value="${vo.costAccountName}" />
		<span style="color: red">*</span>
		<c:if test="${!isProcess}">
			<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openCostAccountSelect();"></a>
		</c:if>
		<input type="hidden" id="costAccountCode" datatype="*" name="costAccountCode" value='${vo.costAccountCode}' >
	</div>

	<div class="form">
		<label class="Validform_label">条件公式: </label>
		<input  name="formulaCon" id="formulaCon"
			   datatype="*"  class="inputxt" value="${vo.formulaCon}" />
		<span style="color: red;">*</span>
	</div>

	<div class="form">
		<label class="Validform_label">结果公式: </label>
		<input  name="formulaRel" id="formulaRel"
			   datatype="*"  class="inputxt" value="${vo.formulaRel}" />
		<span style="color: red;">*</span>
	</div>
	<input  name="createDate" id="createDate" type="hidden"
			   value="${vo.createDate}" />
	<input  name="enableStatus" id="enableStatus" type="hidden"
			value="${vo.enableStatus}" />
</t:formvalid>
</body>
</html>
<script type="text/javascript">
    //弹出选择费用科目
    function openCostAccountSelect(){
        safeShowDialog({
            content : "url:ttCostAccountController.do?goSelectTtCostAccount&param=y",
            lock : true,
            title : "选择费用科目",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tmCostAccountList').datagrid('getSelected');
                if (rowsData == '' || rowsData == null) {
                    iframe.tip("请选择一条数据");
                    return false;
                } else {
                    $("#costAccountCode" ).val(rowsData.accountCode);
                    $("#costAccountName").val(rowsData.accountName);
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

</script>