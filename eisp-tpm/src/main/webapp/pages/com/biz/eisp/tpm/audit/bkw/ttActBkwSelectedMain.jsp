<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="ttActBkwMain" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttActBkwMainList" fitColumns="false" title="宝库旺申请" queryMode = "group" idField="id" pagination="true"
	      autoLoadData="true" actionUrl="ttBkwAuditController.do?findTtActBkwMainList&billMainId=${billMainId}" >
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>

	        <t:dgCol title="活动编码" field="actCode"  query="true"  width="100" ></t:dgCol>
			<t:dgCol title="活动名称" field="actName" query="true"  width="100" ></t:dgCol>
			<t:dgCol title="流程类型" hidden="true" field="actTypeCode" dictionary="bkw_act_type" query="true"  width="100" ></t:dgCol>
			<t:dgCol title="流程类型" field="actTypeName"   width="100" ></t:dgCol>

			<t:dgCol title="客户编码" field="customerCode" hidden="true" width="100" ></t:dgCol>
			<t:dgCol title="客户名称" field="customerName" query="true"  width="100" ></t:dgCol>
			<t:dgCol title="活动大类编码" hidden="true" field="costTypeCode"  width="100" ></t:dgCol>
			<t:dgCol title="活动大类" field="costTypeName" query="true"  width="100" ></t:dgCol>

	        <t:dgCol title="开始时间" field="beginDate" formatter="yyyy-MM-dd" width="140" ></t:dgCol>
	        <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd" width="140" ></t:dgCol>


	        <t:dgCol title="费用金额" field="amount"   width="100" ></t:dgCol>

	        <t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" query="true" width="100" ></t:dgCol>
	        <t:dgCol title="审批状态" field="bpmStatus" query="true" dictionary="bpm_status" width="100" ></t:dgCol>
          	<t:dgCol title="创建人" field="createName"  width="100" ></t:dgCol>
          	<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" width="140" ></t:dgCol>

          	
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
</script>