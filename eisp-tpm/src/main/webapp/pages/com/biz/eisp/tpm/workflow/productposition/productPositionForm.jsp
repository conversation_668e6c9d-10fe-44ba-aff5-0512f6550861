<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>产品职位关系编辑</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="productPositionController.do?saveProductPosition"
             refresh="true">
    <!-- id -->
    <input name="id" type="hidden" value="${productPositionVo.id}"/>

    <div class="form">
        <label class="Validform_label">产品编码: </label>
        <input class="inputxt" name="productCode" value="${productPositionVo.productCode}"/>
        <span style="color: red;">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">职位编码: </label>
        <input class="inputxt" name="positionCode" value="${productPositionVo.positionCode}"/>
        <span style="color: red;">*</span>
    </div>
</t:formvalid>
</body>
</html>


<script type="text/javascript">

    //只能输入数字，或者保留两位小数
    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    }

    $(document).ready(function () {
        $("#cbproductCode").combobox({
            onChange: function (newValue, oldValue) {
                load();
            }
        });
    });
</script>