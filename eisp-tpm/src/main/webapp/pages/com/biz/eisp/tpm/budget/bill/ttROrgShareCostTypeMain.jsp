<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="ttActBusinessPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttOrgCostTypeList" fitColumns="true" title="预算信息" queryMode = "group"
	     idField="id" pagination="false" actionUrl="ttROrgShareCostTypeController.do?findOrgCostTypeList&year=${param.year }&orgCode=${param.orgCode }&costTypeCode=${param.costTypeCode }" onClick="clickFun">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="年" field="year" sortable="false"></t:dgCol>
	        <t:dgCol title="季度" field="quarter" sortable="false"></t:dgCol>
	        <t:dgCol title="部门编码" field="orgCode" sortable="false"></t:dgCol>
	        <t:dgCol title="部门名称" field="orgName" sortable="false"></t:dgCol>
	        <t:dgCol title="费用类型编码" field="costTypeCode"></t:dgCol>
	        <t:dgCol title="费用类型名称" field="costTypeName"></t:dgCol>
	    </t:datagrid>
	</div>
	
	<div data-options="region:'east'" style="padding:1px;width:300px;">
		<t:datagrid name="ttOrgShareList" fitColumns="true" title="部门" queryMode = "group"
	     idField="id" singleSelect="false" pagination="false" autoLoadData="false" actionUrl="ttROrgShareCostTypeController.do?findOrgShareList">
	      <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
	      	<t:dgCol title="部门编码" field="shareOrgCode"></t:dgCol>
	  		<t:dgCol title="部门名称" field="shareOrgName"></t:dgCol>
	    	<t:dgToolBar title="添加部门" icon="icon-add" url="" funname="openOrgSelect"></t:dgToolBar>
	    	<t:dgToolBar title="删除" icon="icon-remove" url="ttROrgShareCostTypeController.do?deleteOrgShare" funname="deleteALLSelect"></t:dgToolBar>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
	function clickFun(rowIndex,rowData) {
		var orgCode = rowData.orgCode;
		var year = rowData.year;
		var quarter = rowData.quarter;
		var costTypeCode = rowData.costTypeCode;
		$("#ttOrgShareList").datagrid({url:"ttROrgShareCostTypeController.do?findOrgShareList&orgCode=" + orgCode + "&year=" + year + "&quarter=" + quarter + "&costTypeCode=" + costTypeCode});
	}
	
	function openOrgSelect(){
		var seletctTarget =  $("#ttOrgCostTypeList").datagrid("getSelected");
		var title = "";
		if(seletctTarget==null || seletctTarget==""){
			tip("选择一条数据");
			return;
		}
		
		var orgCode = seletctTarget.orgCode;
		var year = seletctTarget.year;
		var quarter = seletctTarget.quarter;
		var costTypeCode = seletctTarget.costTypeCode;
		var url = "ttCostTypeController.do?goSelectedOrgMain";
		createwindowExt('企业组织管理',url,510,500,{
			ok:function(){
				iframe = this.iframe.contentWindow;
				var currentOrgCode = iframe.$("#currentOrgCode").val();
				var data = iframe.$("#orgList").datagrid("getSelections");
				var saveJson = [];
				var length = data.length;
				var rows=$("#ttOrgShareList").datagrid("getRows");
				for(var i = 0;i<length;i++){
					var json = {};
					var code = data[i].orgCode;
					if( code!= currentOrgCode){
						json.shareOrgCode = data[i].orgCode;
						json.orgCode=orgCode;
						json.year=year;
						json.quarter=quarter;
						json.costTypeCode=costTypeCode;
						var hasData=true;
						$.each(rows,function (index,row){
							 var orgCode=row.shareOrgCode;
							 if (orgCode==json.shareOrgCode) {
								 hasData=false;
							 }
						 });
						if(hasData){
							saveJson.push(json);
						}
					}else{
						tip("选择的开放部门与当前部门相同,不可再次选择");
					}
				}
				$.ajax({url:"ttROrgShareCostTypeController.do?saveOrgShare",data:{jsonVal:JSON.stringify(saveJson)},type:"post",async:false,success:function(data){
					var d = $.parseJSON(data);
					if(d.success == true){
						$("#ttOrgShareList").datagrid("reload");
					}
				}});
			}
		});
	}
</script>
