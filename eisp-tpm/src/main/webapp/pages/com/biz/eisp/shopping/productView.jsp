<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>商品清单</title>

    <link rel="stylesheet" href="pages/pic/css/alertify.core.css" />
    <link rel="stylesheet" href="pages/pic/css/alertify.default.css" id="toggleCSS" />
    <script type="text/javascript" src="pages/pic/js/jquery.min.js"></script>
    <script src="pages/pic/js/jquery.fly.min.js"></script>
    <script src="pages/pic/js/alertify.min.js"></script>
    <style type="text/css">
        .demo{width:940px; margin:5px auto 10px auto}
        .m-sidebar{position: fixed;top: 0;right: 0;background: #000;z-index: 2000;width: 35px;height: 100%;font-size: 12px;color: #fff;}
        .cart{color: #fff;text-align:center;line-height: 20px;padding: 200px 0 0 0px;}
        .cart span{display:block;width:20px;margin:0 auto;}
        .cart i{width:35px;height:35px;display:block; background:url("pages/pic/img/car.png") no-repeat;}
        #msg{position:fixed; top:300px; right:35px; z-index:10000; width:1px; height:52px; line-height:52px; font-size:20px; text-align:center; color:#fff; background:#360; display:none}

        .box{float:left; width:220px; height:310px; margin-left:5px; border:1px solid #e0e0e0; text-align:center;margin: 0 10px 10px 0;}
        .box p{line-height:5px; padding:4px 4px 10px 4px; text-align:left}
        .box:hover{border:1px solid #f90}
        .box h4{line-height:5px; font-size:14px; color:#f30;font-weight:500}
        .box h4 span{font-size:20px;}
        .u-flyer{display: block;width: 50px;height: 50px;border-radius: 50px;position: fixed;z-index: 9999;}

        .button {
            display: inline-block;
            outline: none;
            cursor: pointer;
            text-align: right;
            text-decoration: none;
            font: 16px/100% 'Microsoft yahei',Arial, Helvetica, sans-serif;
            padding: .35em .5em .5em;
            text-shadow: 0 1px 1px rgba(0,0,0,.3);
            -webkit-border-radius: .5em;
            -moz-border-radius: .5em;
            border-radius: .5em;
            -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.2);
            -moz-box-shadow: 0 1px 2px rgba(0,0,0,.2);
            box-shadow: 0 1px 2px rgba(0,0,0,.2);
        }
        .button:hover {
            text-decoration: none;
        }
        .button:active {
            position: relative;
            top: 1px;
        }
        /* orange */
        .orange {
            color: #fef4e9;
            border: solid 1px #da7c0c;
            background: #f78d1d;
            background: -webkit-gradient(linear, left top, left bottom, from(#faa51a), to(#f47a20));
            background: -moz-linear-gradient(top,  #faa51a,  #f47a20);
            filter:  progid:DXImageTransform.Microsoft.gradient(startColorstr='#faa51a', endColorstr='#f47a20');
        }
        .orange:hover {
            background: #f47c20;
            background: -webkit-gradient(linear, left top, left bottom, from(#f88e11), to(#f06015));
            background: -moz-linear-gradient(top,  #f88e11,  #f06015);
            filter:  progid:DXImageTransform.Microsoft.gradient(startColorstr='#f88e11', endColorstr='#f06015');
        }
        .orange:active {
            color: #fcd3a5;
            background: -webkit-gradient(linear, left top, left bottom, from(#f47a20), to(#faa51a));
            background: -moz-linear-gradient(top,  #f47a20,  #faa51a);
            filter:  progid:DXImageTransform.Microsoft.gradient(startColorstr='#f47a20', endColorstr='#faa51a');
        }

        .alertify-log-custom {
            background: blue;
        }
    </style>

    <!--[if lte IE 9]>
    <script src="pages/pic/js/requestAnimationFrame.js"></script>
    <![endif]-->
</head>
<body>
<div id="main">

    <div class="demo">
        <h2 class="top_title">天能广宣品商城</h2>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/001.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">29.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>T恤</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" id="custom" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/002.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">99.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>保温杯</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/003.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">399.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>刀具</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/004.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">199.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>电热水壶</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/005.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">199.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>花生油</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/006.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">299.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>空调被</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/007.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">9.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>毛巾</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/008.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">39.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>手套</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/005.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">199.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>花生油</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/006.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">299.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>空调被</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/007.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">9.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>毛巾</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar">加入购物车</a>
        </div>
        <div class="box">
            <img src="pages/pic/img/shopping/tn/008.jpg" class="img" width="215" height="190">
            <h4><span style="font-weight:bold;">39.00</span><span style="font-size:12px !important;color:black;">&nbsp;元/件</span></h4>
            <p>手套</p>
            <a href="#">商品详情</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" class="button orange addcar" onclick="addCar('001')">加入购物车</a>
        </div>
    </div>

</div>

<div class="m-sidebar">
    <a onclick="goSpCart()"><div class="cart">
        <i id="end"></i>
        <span  style="font-size:16px;cursor: pointer;">购物车</span>
    </div></a>
</div>
<div id="msg">已成功加入购物车！</div>
</div>
</body>
<script type="text/javascript">
    $(function() {
        $(".addcar").click(function(event){
            var addcar = $(this);
            reset();
            alertify.success("加入购物车成功");
            addcar.css("cursor","default").removeClass('orange').unbind('click');
            return false;
        });
    });


    function reset () {
        $("#toggleCSS").attr("href", "pages/pic/css/alertify.default.css");
        alertify.set({
            labels : {
                ok     : "OK",
                cancel : "Cancel"
            },
            delay : 3000,
            buttonReverse : false,
            buttonFocus   : "ok"
        });
    }
function goSpCart() {

    var url = "shoppingController.do?goShopcart";

    $.dialog({
        title: "购物结算",
        content: "url:"+url,
        lock: true,
        width: "700",
        height: "400",
        zIndex: 10000,
        parent: windowapi,
        cancelVal: '关闭',
    });
    
}
</script>
</html>
