<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="west" style="width:400px;">
        <t:formvalid formid="formobj" layout="div" dialog="true" action="ttDepositController.do?saveTtDepositMain"
                     refresh="true"
                     beforeSubmit="handlerSubmit">
            <input name="id" id="id" type="hidden" value="${depositValue.id}">
            <input name="splitCostJson" id="splitCostJson" type="hidden" value='${depositValue.splitCostJson}'/>
            <div class="form">
                <label class="Validform_label">客户名称: </label>
                <input type="hidden" id="customerCode" name="customerCode" value="${depositValue.customerCode}">
                <input type="text" readonly="readonly" datatype="*" class="inputxt" id="customerName"
                       name="customerName" value="${depositValue.customerName }">
                <span style="color: red;">*</span>
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="popCustomer();"></a>
            </div>


            <div class="form">
                <label class="Validform_label">产品系列: </label>
                <t:comboBox name="productCode" url="ttDepositController.do?findOrgCombox"
                            defaultVal="${depositValue.productCode}" required="true" width="150"></t:comboBox>
                <span style="color: red;">*</span>
            </div>


            <div class="form">
                <label class="Validform_label">任务开始时间: </label>
                <input name="beginDate" id="beginDate" datatype="*" class="Wdate" style="width: 150px;"
                       onchange="initMonth()"
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\')}',onpicked:function(){$('.Wdate').blur();}})"
                       readonly="readonly" value="${depositValue.beginDate}"/>
                <span style="color: red;">*</span>
            </div>


            <div class="form">
                <label class="Validform_label">任务结束时间: </label>
                <input name="endDate" id="endDate" datatype="*" class="Wdate" style="width: 150px;"
                       onchange="initMonth()"
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
                       readonly="readonly" value="${depositValue.endDate}"/>
                <span style="color: red;">*</span>
            </div>

            <div class="form">
                <label class="Validform_label">协议金额: </label>
                <input onkeyup="clearNoNum(this)" name="protocolAmount" id="protocolAmount" datatype="*" class="inputxt"
                       value="${depositValue.protocolAmount}"/>
                <span style="color: red;">*</span>
                <span>元</span>
            </div>

            <div class="form">
                <label class="Validform_label">押金金额: </label>
                <input onkeyup="clearNoNum(this)" name="depositAmount" id="depositAmount" datatype="*" class="inputxt"
                       value="${depositValue.depositAmount}"/>
                <span style="color: red;">*</span>
                <span>元</span>
            </div>

            <div class="form">
                <label class="Validform_label">支付方式: </label>
                <t:comboBox name="paymentCode" url="ttDepositController.do?findPaymentCode"
                            defaultVal="${depositValue.paymentCode}"   required="true" width="150"></t:comboBox>
                <span style="color: red;">*</span>
            </div>

            <div class="form">
                <label class="Validform_label">产品返利标准: </label>
                <input onkeyup="clearNoNum(this)" name="productRebateStandard" id="productRebateStandard" datatype="*"
                       class="inputxt" value="${depositValue.productRebateStandard}"/>
                <span style="color: red;">*</span>
                <span>%</span>
            </div>

            <div class="form">
                <label class="Validform_label">备注:</label>
                <textarea rows="3" name="remark" id="remark" style="width:150px;resize: none;height: 100px;">${depositValue.remark}</textarea>
            </div>

        </t:formvalid>
    </div>
    <div region="center">
        <div class="panel datagrid">
            <div class="datagrid-wrap panel-body">
                <div class="datagrid-toolbar">
                    <div class="datagrid-toolbar-but">
						<span style="float:left;">
							<%--<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost" onclick="costShareToProduct();">分摊费用到产品</a>--%>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost"
                               onclick="costShare();">金额均摊</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-reload"
                               onclick="resetCost();">重置</a>
						</span>
                        <span style="float:right">
						</span>
                        <div style="clear:both;float: none;height:0;display: block;"></div>
                    </div>
                    <div class="datagrid-toolbar-search">
                    </div>
                </div>
                <div class="datagrid-view">
                    <table class="actTable" id="avgTable">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>年月</td>
                            <td>月度目标金额(元)</td>
                            <td>月度产品返利标准(%)</td>
                        </tr>
                        </thead>
                        <tbody id="tbodyContent">

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">

    //只能输入数字，或者保留两位小数
    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
    }

    //选择流程类型，加载活动细类
    $(function () {

        $("select[name='actTypeCode']").change(function () {
            var actType = $("select[name='actTypeCode']").val();
            var url = "ttBudgetApiController.do?findTtCostAccountListByActType&actType=" + actType;
            $("#cbcostAccountCode").combobox("reload", url);
        });
        //协议金额保留两位小数
        $('#protocolAmount').numberbox({
            min: 0,
            precision: 2
        });

        //押金金额保留两位小数，并且只能输入数字
        $('#depositAmount').numberbox({
            min: 0,
            precision: 2
        });
        //月份费用精确到2位，并且只能输入数字
        $(".actTable").find("input[type='text']").each(function () {
            $(this).numberbox({
                min: 0,
                precision: 2
            });
        });
        //编辑时 展示月份分配金额
        if ($("#id").val() != "" || $("#id").val() != null) {
            //调用方法
            splitCostJson();
        }

    });

    //编辑时 展示月份分配金额
    function splitCostJson() {
        var splitCostJson = '${depositValue.splitCostJson}';


        if (splitCostJson == null || splitCostJson == '') {
            return false;
        }
        var parsesplitCostJson = JSON.parse(splitCostJson);
        if (parsesplitCostJson != null || parsesplitCostJson != '') {

            $.each(parsesplitCostJson, function (k, v) {
                var i = Number(k) + 1;
                var str = '<tr name="contents">'
                    + '<td>' + i + '</td>'
                    + '<td name="yearMonth">' + v.yearMonth + '</td>'
                    + '<td><input  type="text" id="amount"  value="' + v.amount + '" name="amount" id="' + v + '"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="' + v.protocolAmount + '" placeholder="请填写金额"/></td>'
                    + '<td><input type="text" name="monthProduct" id="' + v + '"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="' + v.monthProduct + '" placeholder="请填写返利标准"/></td>'
                    + '</tr>';
                $(".actTable tbody").append(str);
            });
        }
    }


</script>

<script type="text/javascript">
    function initMonth(data) {

        var beginDate = $("#beginDate").val();//获取开始时间
        if (checkObjIsNotUndefinedAndNull(beginDate)) {
            tip("请选择开始时间");
            return;
        }
        var endDate = $("#endDate").val();//获取结束时间
        if (checkObjIsNotUndefinedAndNull(endDate)) {
            tip("请选择结束时间");
            return;
        }

//        var beginDateStr =  beginDate.substring(0,4);//拆分开始时间到年

//         var endDateStr =  endDate.substring(0,4);//拆分结束时间到年

        $(".actTable tbody").html("");
        var url = "ttDepositController.do?findTtFinancialYearByYear&beginDate=" + beginDate + "&endDate=" + endDate;
        $.ajax({
            url: url,
            type: "post",
            success: function (data) {
                // 将一个json字符串转换成对象
                var d = JSON.parse(data);
                if (d.success == true) {
                    $.each(d.obj, function (k, v) {
                        var i = Number(k) + 1;
                        var str = '<tr>'
                            + '<td>' + i + '</td>'
                            + '<td name="yearMonth">' + v.yearMonth + '</td>'
                            + '<td><input type="text" name="amount" id="' + v + '"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0" placeholder="请填写金额"/></td>'
                            + '<td><input type="text" name="monthProduct" id="' + v + '"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0" placeholder="请填写返利标准"/></td>'
                            + '</tr>';
                        $(".actTable tbody").append(str);
                    });
                } else {
                    tip(d.msg);
                    return false;
                }
            }
        });
    }

    function checkObjIsNotUndefinedAndNull(obj) {
        return (typeof(obj) == 'undefined' || obj == '' || obj == null);
    }


    //金额分摊
    function costShare() {
//        $("#tbodyContent tr").each(function () {
        var year = $("#finacialYear").val();//获取选择的年
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();

//            var yearMonth = $(this).find("td[name='yearMonth']").html();


        var protocolAmount = $("#protocolAmount").val();
        if (protocolAmount == '' || protocolAmount == undefined || protocolAmount == null) {
            tip("请输入协议金额")
            return false;
        }


        var sumAmount = $("#protocolAmount").val();
        if (sumAmount == '' || sumAmount == undefined || sumAmount == null) {
            return false;
        }
        //获取到body的行元素
        var rows = $("#avgTable tbody tr");
        //计算出这个body里面一共有都少行
        var amountSize = Number(rows.size());
        //如果没有行就终止
        if (amountSize == 0) {
            return false;
        }
        //把协议金额赋值给waitingAssgin
        var waitingAssgin = sumAmount;
        var yearMonthCount = $("#tbodyContent tr").size();
        //平均数=协议金额/财年的每个月 保留2位小数
        var avgAmount = highPrecisionDivision(waitingAssgin, yearMonthCount).toFixed(2);
        //遍历行
        var totalAmount=0;
        $.each(rows, function (i, row) {
            //设置值到amount
            $(row).find("input[name='amount']").val(avgAmount);
            //高进度家法加上平均总和
            totalAmount=addNum(totalAmount,avgAmount);

        });
        // sumAmount='协议金额'  totalAmount=平均总和
        //如果平均总和不等于协议金额
        if(Number(totalAmount)!=Number(sumAmount)){
            //协议金额减去平均总和
            var num=subtract(sumAmount,totalAmount);
            //添加值到最后一个    值为 平均数+协议金额-平均总和的差
            $(rows[rows.length -1]).find("input[name='amount']").val(addNum(avgAmount,num));
            //最后平均总和= 平均总和+协议金额-平均总和的差
        }
    }


    //重置
    function resetCost() {
//         $(".actTable tbody").html("");

        var rows = $("#avgTable tbody tr");
        $.each(rows, function (k, v) {
            $(" input[name='amount'] ").val('')
            $(" input[name='monthProduct'] ").val('')
        })

    }

    //提交
    function handlerSubmit() {
        //验证月度目标金额是否大于协议金额

        //获取协议金额
        var protocolAmount = $("#protocolAmount").val();
        //获取金额均摊输入框
        var rows = $("#avgTable tbody tr");
        var sum = 0;
        var monthProduct =0;
        $.each(rows, function (k, v) {
            var amount = $(this).find("input[name='amount']").val();
            sum = addNum(sum, amount);
            monthProduct = $(this).find("input[name='monthProduct']").val();
        })

        if (monthProduct == null || monthProduct == ''||monthProduct==0) {
            tip('月度产品返利标准为空！请输入');
            return false;
        }

        if (sum > protocolAmount) {
            tip('月度目标金额之和不能大于协议金额')
            return false;
        }

        if (sum == null || sum == '') {
            tip('月度目标金额为空！，请输入');
            return false;
        }

        //验证
        var bd = $("#beginDate").val();
        var ed = $("#endDate").val();
        if (bd > ed) {
            tip("任务开始时间大于任务结束时间！请从新输入。。。");
            return false;
        }
        //组装月份分摊json
        var splitJson = [];
        $("#tbodyContent tr").each(function () {
            var splitData = {};
            splitData.yearMonth = $(this).find("td[name='yearMonth']").html();
            splitData.amount = $(this).find("input[name='amount']").val();
            splitData.monthProduct = $(this).find("input[name='monthProduct']").val();
            splitJson.push(splitData);
        });
        if (splitJson.length == 0) {
            tip("未分摊金额到月份");
            return false;
        }
        //把json对象解析成字符串
        $("#splitCostJson").val(JSON.stringify(splitJson));

        //检查分摊金额是否等于总金额
        return true;

        // 验证数据重复输入  客户+系列+任务开始时间+任务结束时间
        var customerCode = $("#customerCode").val();
        var productCode = $("#productCode").val();
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        var id = $("#id").val();
        if (customerCode == "" || productCode == "" || beginDate == "" || endDate == "") {
            return false;
        }
        var url = "ttDepositController.do?validateDataRepeat&customerCode=" + customerCode + "&productCode=" + productCode + "&beginDate=" + beginDate + "&endDate=" + endDate + "&id=" + id;
        $.ajax({
            url: url, type: "post", async: false, success: function (data) {
                var d = $.parseJSON(data);
                if (d.success == false) {
                    tip(d.msg);
                }
            }
        });

    }

    //检查总金额是否等于分摊金额总和
    function isEqualSplitCostAmount() {
        var splitCostJson = $("#splitCostJson").val();
        var protocolAmount = $("#protocolAmount").val();
        if (protocolAmount > splitCostJson) {
            tip("均摊金额不能大于协议金额");
            return false;
        }
        return true;
    }
    /************************************************************************************************************/

    //获取客户
    function popCustomer() {
        popMyClick("customerName,customerCode", "customerName,customerCode", "tmCommonMdmController.do?goCustomerSearch&singleSelect=" + true, 400, 400);
    }
    /************************************************************************************************************/
    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }
    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g, "."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); // 只能输入两个小数
    }
    /*
     * 高精减法函数
     */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精度加法函数
     */
    function addNum(summand1, summand2) {
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m = 0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();
        try {
            m += s1.split(".")[1].length;
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length;
        } catch (e) {
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
    }
    //除法精确计算
    function highPrecisionDivision(arg1, arg2) {
        var t1 = 0, t2 = 0, r1, r2;
        try {
            t1 = arg1.toString().split(".")[1].length;
        }
        catch (e) {
        }
        try {
            t2 = arg2.toString().split(".")[1].length;
        }
        catch (e) {
        }
        with (Math) {
            r1 = Number(arg1.toString().replace(".", ""));
            r2 = Number(arg2.toString().replace(".", ""));
            return (r1 / r2) * pow(10, t2 - t1);
        }
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        return Math.pow(10, Math.max(r1, r2));
    }

    function popMyClick(obj, name, url, width, height) {
        var names = name.split(",");
        var objs = obj.split(",");
        safeShowDialog({
            content: "url:" + url,
            lock: true,
            title: "选择",
            width: width == null ? 700 : width,
            height: height == null ? 400 : height,
            left: '85%',
            cache: false,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null) {
                    iframe.$.messager.alert('错误', "请至少选择一条数据", "error");
                    return false;
                } else {
                    for (var i1 = 0; i1 < names.length; i1++) {
                        var str = "";
                        $.each(selected, function (i, n) {
                            if (i == 0) {
                                str += n[names[i1]];
                            } else {
                                str += ",";
                                str += n[names[i1]];
                            }
                        });
                        if ($("#" + objs[i1]).length >= 1) {
                            $("#" + objs[i1]).val("");
                            $("#" + objs[i1]).val(str);
                        } else {
                            $("input[name='" + objs[i1] + "']").val("");
                            $("input[name='" + objs[i1] + "']").val(str);
                        }
                    }
                    $("#orgCode").val("");
                    $("#orgName").val("");
                    $("#orgCode").val(selected[0].orgCode);
                    $("#orgName").val(selected[0].orgName);
                    return true;
                }
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>