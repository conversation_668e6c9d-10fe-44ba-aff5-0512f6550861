<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true"><span style="font-size: 20px;color: red">注意：1、经销商姓名必输（可以只输姓或名）！  2、经销商审批一个，可做门头数量随之减少一个。<br/>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3、每月根据经销商下单情况动态调整（当月27日 ~ 次月5日）</span><br/><br/><br/>
    <div region="center" style="padding: 1px;">
        <t:datagrid name="dealerScoreList" title="经销商可做汽车门头查询"  actionUrl="ttAreaActApplyController.do?findCusScoreList_Ads&adsType=2"
                    checkbox="false"  fit="true" idField="id"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
            <span style="font-size: 20px;color: red">注意：1、经销商姓名必输（可以只输姓或名）。&nbsp; &nbsp;&nbsp; 2、经销商审批一个，可做门头数量随之减少一个。<br/>&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3、每月根据经销商下单情况动态调整可做门头数（当月27日 ~ 次月5日）</span><br/><br/><br/>


    </div>
            <t:dgCol title="经销商姓名" field="dealerName" query="true"  width="300" ></t:dgCol>
            <t:dgCol title="当前可做门头数(最少)" field="doorNumMin2" width="130" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="当前可做门头数(最多)" field="doorNumMax2" width="130" sortable="false" align="center" ></t:dgCol>

        </t:datagrid>
    </div>
</div>
