<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttBudgetBillDetailList" title="费用预算明细"  actionUrl="ttBudgetDetailController.do?findTtBudgetDetailList&orgCode=${param.orgCode }&costTypeCode=${param.costTypeCode }&year=${param.year }&quarter=${param.quarter }" 
	  		 idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
	  		<t:dgCol title="单号" field="voucherCode" sortable="false"></t:dgCol>
	  		<t:dgCol title="金额" field="amount" sortable="false"></t:dgCol>
	  		<t:dgCol title="余额" field="amountBalance" sortable="false"></t:dgCol>
	  		<t:dgCol title="业务类型" field="businessTypeName" sortable="false"></t:dgCol>
	  		<t:dgCol title="业务类型" field="businessTypeCode"  hidden="true" dictionary="budget_business_type"></t:dgCol>
	  		<t:dgCol title="备注" field="remark" sortable="false"></t:dgCol>
	  		<t:dgCol title="操作人" field="createName" sortable="false"></t:dgCol>
	  		<t:dgCol title="操作时间" field="createDate" sortable="false" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">
</script>
