<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttProductActWorkFlowList" checkbox="false" fitColumns="true" title="" pagination="false"
                    actionUrl="ttAuditQuotaWorkFlowController.do?findTtKeyIndicatorListV11&flagKey=${flagKey}" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol field="orgName" title="组织" ></t:dgCol>
            <t:dgCol field="yearMonth" title="年月"  ></t:dgCol>
            <t:dgCol field="finacialName" title="预算科目" ></t:dgCol>
            <t:dgCol field="amount" title="申请金额" formatterjs="numExtend"></t:dgCol>
            <t:dgCol  field="auditAmount"  title="结案金额" formatterjs="numExtend" sortable="false" ></t:dgCol>
            <t:dgCol  field="divideAmount"  title="差异金额" formatterjs="numExtend" sortable="false" ></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
//        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
    });
</script>

