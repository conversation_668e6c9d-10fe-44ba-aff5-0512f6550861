<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttActQuotaMainSelectList" fitColumns="true" title="长期待摊活动申请" queryMode = "group" idField="id" pagination="true"
                    autoLoadData="true" actionUrl="ttAuditLongtermController.do?findAuditLongtermSelectedList&billMainId=${vo.id}" singleSelect="false">
            <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
            <t:dgCol title="活动编码" field="actCode"  query="true"></t:dgCol>
            <t:dgCol title="活动名称" field="actName" query="true" ></t:dgCol>
            <t:dgCol title="结案类型" hidden="true" field="actTypeCode" dictionary="ad_long_act_type" query="true"></t:dgCol>
            <t:dgCol title="结案类型" field="actTypeName"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" hidden="true" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true"  ></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate"></t:dgCol>
            <t:dgCol title="活动申请金额（元）" field="amount"  ></t:dgCol>
            <t:dgToolBar title="添加" icon="icon-add"  onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add"  onclick="addAllItem()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div region="east" style="width:550px;padding:1px;">
        <table id="ttActQuotaMainSelectedList" class="easyui-datagrid" style="height:auto" fit="true"
               data-options="singleSelect: false,rownumbers:true,toolbar: '#tb',method: 'get',url:''">
            <thead>
            <tr>
                <th data-options="field:'id',hidden:'true'">ID</th>
                <th data-options="field:'actCode',width:100">活动编码</th>
                <th data-options="field:'actName',width:100">活动名称</th>
                <th data-options="field:'actTypeName',width:140">流程类型</th>
                <th data-options="field:'customerName',width:100">客户名称</th>

                <th data-options="field:'beginDate',width:100">开始时间</th>
                <th data-options="field:'endDate',width:100">结束时间</th>


                <th data-options="field:'amount',width:100">活动申请金额(元)</th>
            </tr>
            </thead>
        </table>
        <div id="tb" style="height:auto">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeItemBkw()">删除</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeAllItemBkw()">全部删除</a>
        </div>
    </div>
</div>
<div id="btn_sub" onclick="submitForm()"></div>
<script type="text/javascript">

    //添加
    function addItem(){
        var seletctTarget =  $("#ttActQuotaMainSelectList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttActQuotaMainSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
    }

    //添加全部
    function addAllItem(){
        var name = "ttActQuotaMainSelectList";
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }
        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        queryParams.rows = 9999999;
        $.ajax({
            url:"ttAuditLongtermController.do?findAuditLongtermSelectedList&billMainId=${vo.id}",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttActQuotaMainSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
    }

    function removeItemBkw() {
        var checkListTarget =  $("#ttActQuotaMainSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        var copyRows = [];
        for ( var j= 0; j < checkListTarget.length; j++) {
            copyRows.push(checkListTarget[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttActQuotaMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttActQuotaMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
    }

    function removeAllItemBkw() {
        var rowsData = $("#ttActQuotaMainSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttActQuotaMainSelectedList").datagrid("getRows");

        var copyRows = [];
        for ( var j= 0; j < rows.length; j++) {
            copyRows.push(rows[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttActQuotaMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttActQuotaMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttActQuotaMainSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].id+"'";
            }
        }
        var actType=$("select[name='actType']").val();
        $('#ttActQuotaMainSelectList').datagrid("reload", {"exclusiveCodes":exclusiveCodes,"actType":actType});
    }

    function submitForm() {
        var rows = $("#ttActQuotaMainSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }
        var codes = [];
        var actCodes = [];
        for ( var i = 0; i < rows.length; i++) {
            codes.push(rows[i].id);
            actCodes.push(rows[i].actCode);
        }

        var result=false;
        $.ajax({
            async: false,
            url: "ttAuditLongtermController.do?saveAuditLongtermToAudit",
            data: {
                ids:codes.join(","),
                actCodes:actCodes.join(","),
                billMainId : '${vo.id}'
            },
            type: "post",
            dataType : "json",
            success : function(data) {
                if(data.success){
                    result=data.success;
                } else {
                    tip(data.msg);
                }
            },
            error:function(){
                tip("服务器异常，请稍后再试");
            }
        });

        return result;
    }
</script>

