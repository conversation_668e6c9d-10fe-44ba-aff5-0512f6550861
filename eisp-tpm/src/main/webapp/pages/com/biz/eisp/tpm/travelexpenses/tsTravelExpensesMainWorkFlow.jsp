<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="tsTravelExpensesList" title="差旅报销"  actionUrl="tsTravelExpensesWebWorkFlowController.do?findTsTravelExpensesList&flagKey=${flagKey}"
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="标题" field="title" hidden="false" ></t:dgCol>
			<t:dgCol title="申请日期" field="applyDate" hidden="false" ></t:dgCol>
			<t:dgCol title="申请人" field="applyName" hidden="false" ></t:dgCol>
			<t:dgCol title="申请人部门" field="orgName" hidden="false" ></t:dgCol>
			<t:dgCol title="报销金额合计" field="submitAmount" hidden="false" ></t:dgCol>
			<t:dgCol title="实际报销金额" field="realAmount" hidden="false" ></t:dgCol>
			<t:dgCol title="预算科目" field="yskm" hidden="false" ></t:dgCol>
			<t:dgCol title="当前审批人" field="approverName" hidden="false" ></t:dgCol>
			<t:dgCol title="最近更新人" field="updateName" hidden="false" ></t:dgCol>
			<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd" hidden="false" ></t:dgCol>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

</script>
