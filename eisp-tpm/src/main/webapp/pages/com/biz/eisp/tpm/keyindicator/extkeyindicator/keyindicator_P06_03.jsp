<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    iframe {
        height: 480px!important;
    }
    .l-btn-left{
        font-weight: bold;
        color:red;
    }
</style>

<div style="clear:both; width: 1180px;height: 180px;">

    <t:datagrid name="tsActApplyVoListsub" fitColumns="false"  pagination="false" title=""
                actionUrl="ttActOutUploadController.do?findActWorkFlowData&businessId=${businessId}" idField="id" fit="true" >
        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="businessId" field="businessId" hidden="true"></t:dgCol>
        <t:dgCol title="门头广告编号" field="adCode" hidden="false" align="center"></t:dgCol>
        <t:dgCol title="活动编码" field="actCode" hidden="false" align="center"></t:dgCol>
        <t:dgCol title="活动类型" field="actType" hidden="false" dictionary="act_type" ></t:dgCol>
        <t:dgCol title="制作类型" field="adType" hidden="false" dictionary="ad_type" ></t:dgCol>
        <t:dgCol title="经销商" field="createName" hidden="false" width="150" align="center"></t:dgCol>
        <t:dgCol title="广告发布地址1（现场选址）" field="gpsAddress" width="260" ></t:dgCol>
        <t:dgCol title="广告发布地址2（非现场选址）" field="detailRemark" width="220" ></t:dgCol>
        <t:dgCol title="广告公司手机" field="mobilephone" hidden="false" ></t:dgCol>
        <t:dgCol title="广告公司名称" field="advName" hidden="false" ></t:dgCol>
        <t:dgToolBar title="检查范围终端" icon="icon-search" url="ttActApplyExcuteWorkFlowController.do?goSearchActTerminal" funname="showTheWithinScopeTerminal" ></t:dgToolBar>
        <t:dgToolBar title="查看照片小视频" icon="icon-search" url="tsPictureController.do?findPictureListByHWV2" funname="queryPic" ></t:dgToolBar>
        <t:dgToolBar title="查看照片小视频(新)" icon="icon-bcbgrid" url="tsPictureController.do?goPicList" funname="goPostPicture"></t:dgToolBar>
        <t:dgToolBar title="查看审批意见" icon="icon-bcbgrid" url="ttActOutUploadController.do?goAuditpage" funname="goAuditpage"></t:dgToolBar>

    </t:datagrid>


</div>

<%@include file="advMaterialInfo.jsp" %>

<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>


<div id="outerdiv2" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv2" style="position:absolute;">
        <

        <video src="" id="biVideo" style='width: 120px;height:80px;cursor: pointer;controls:controls;' ></video>
    </div>
</div>
<%@include file="showSuspiciousOpinions.jsp" %>

<%--</div>--%>
<%@include file="keyindicator_P06_init_js.jsp" %>
<script>

    function goAuditpage(title, url, id, width, height) {

        var processInstanceId =  window.parent.document.getElementById("processInstanceId").value;
        var rowsData = $('#' + id).datagrid('getRows');
        url += '&id=' + processInstanceId;
        $.dialog({
            title: "查看审批意见",
            content: "url:" + url,
            lock: true,
            width: "1250",
            height: "450",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }


    function goPostPicture(title, url, id, width, height) {
        $('#picISReaded', window.parent.document).attr("value",1);
        var rowsData = $('#' + id).datagrid('getRows');
        url += '&id=' + rowsData[0].id;
        $.dialog({
            title: "照片小视频",
            content: "url:" + url,
            lock: true,
            width: "1250",
            height: "700",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }
    function goPostPicture_BK(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 1250, 700);
    }

    $(function () {
        $('#picISReaded', window.parent.document).attr("value",0);
    });
    function queryPic(title, url, id, width, height) {
        $('#picISReaded', window.parent.document).attr("value",1);
        var rowsData = $('#' + id).datagrid('getRows');
        url += '&id=' + rowsData[0].id;
        $.dialog({
            title: "照片小视频",
            content: "url:" + url,
            lock: true,
            width: "900",
            height: "500",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function showInfo(value,row) {
        // if(row.imgType==165){
        //     var url="tsPictureController.do?download&id="+row.id;
        //     return "<a href="+url+">点击下载</a>"
        // }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;

            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }

    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }

    //展示500米范围内的终端
    function showTheWithinScopeTerminal(title, url, gname, callback){

        var thisData = {
            flagKey : getFlagKey(),
            businessKey : getBusinessKey()
        }

        var url = url + changeDataToUrlData(thisData) ;

        $.dialog({
            title: "范围终端活动",
            content: "url:" + url,
            lock: true,
            width: "800",
            height: "500",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    //重写--Override
    function getTitle(){
        return "是否疑似重复";
    }

    function setMoney(value) {
        if(value==''||value==null){
            return 0.0;
        }else{
            return value;
        }

    }

</script>
