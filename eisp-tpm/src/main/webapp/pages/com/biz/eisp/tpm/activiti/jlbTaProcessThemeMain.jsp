<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html style="overflow-x: hidden;">
<head>
    <title>提交申请</title>
    <style>
        #sureFilesDetail{display:table;}
        .sureFilesDetail_a{float:left;padding-right:20px;margin-right:10px;position:relative;}
        .sureFilesDetail_a img{position:absolute;top:7px;right:0;}
    </style>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
</head>
<body>
<t:formvalid formid="formobj" dialog="true" usePlugin="password" layout="table"  callback="@Override uploadFile"  action="#"	tiptype="3">
<input id="useType" name="useType" type="hidden"	value="0">
<table style="width:100%;height:100%" cellpadding="0" cellspacing="1" class="formtable">
    <tr >
        <td align="right" style="width: 20%;">
            <label class="Validform_label"><span style="color: red">*</span>主题:</label></td>
        <td class="value">
            <input id="name" name="name" type="text" style="width: 100%" class="inputxt" dataType="*" value="${type }">
            <span class="Validform_checktip"></span> <label
                class="Validform_label" style="display: none;">主题</label></td>
    </tr>
    <td align="right">
        <label class="Validform_label"> <span style="color: red">*</span>详情:</label>
        <br><!-- <span style="color: red">最多输入1000个字</span> -->
    </td>
    <td class="value">
        <textarea id="detail" name="detail" style="width: 100%;height:100%" rows="9" cols="80" dataType="*" >${detail }</textarea>
        <span class="Validform_checktip"></span> <label class="Validform_label"
                                                        style="display: none;">详情</label></td>
    </tr>
    <tr >
        <td align="right" style="width: 20%;">
            <label class="Validform_label"><span style="color: red">*</span>流程类型:</label></td>
        <td class="value">
            <select name="processKey" id="processKey" style="width: 100%">
            	<option value=''>---请选择---</option>
                <c:forEach var="vo" items="${processVoList}">
                    <option value="${vo.processKey}">${vo.processName}</option>
                </c:forEach>
            </select>
            <span class="Validform_checktip"></span> <label
                class="Validform_label" style="display: none;">流程类型</label></td>
    </tr>
    <tr id="uploadAttmentsFile">
        <td></td>
        <td colspan="3" class="value">
            <div class="dtci_bottom">
                <div id="sureFilesDetail" ></div>
                <t:uploadH5 name="file_upload" buttonText="选择文件" onUploadSuccess="sureUpload" dialog="false" 	 callback=""
                            uploader="tbAttachmentController.do?saveFiles&businessKey=${businessKeyMain }&attachmentType=theme" extend="*.*" id="file_upload" formData=""></t:uploadH5>
                <div class="dtci_bottom_a"><a href="javascript:;" class="easyui-linkbutton" plain="true" id="btn_sub" iconCls="icon-upload" onclick="upload()">确定上传</a></div>
                <div></div>
                <div id="filediv" ></div>
            </div>
        </td>
    </tr>
    </tr>
    </t:formvalid>
</body>
<style>
    #processKey{padding:0;}
</style>
<script type="text/javascript">
    //编写自定义JS代码
    $(document).ready(function(){
        sureUpload();
    }) ;


    function sureUpload(){
        var isReadOnly = '${isReadOnly}';
        $.ajax({
            url:"tbAttachmentController.do?findAttachmentList&businessKey=${businessKeyMain }&attachmentType=${attachmentType }&extendService=${extendService }",
            data:{},
            method:"post",
            success:function(data){
                var str="";
                var d = $.parseJSON(data);
                if (d.success) {
                    var rows = d.rows;
                    for(var i =0;i<rows.length;i++){
                        str += '<div class="sureFilesDetail_a"><a targe="_blank" href="tbAttachmentController.do?viewFile&fileid='
                            +rows[i].id+'" class="easyui-linkbutton l-btn l-btn-plain" plain="true" iconcls="icon-download"><span class="l-btn-left"><span class="l-btn-text icon-download l-btn-icon-left">'
                            +rows[i].attachmentTitle+'.'+rows[i].extend+'</span></span></a>';
                        if(isReadOnly != 'false') {
                            str += '<img onclick="deleteFile(\''
                                + rows[i].id
                                + '\')" src="resources/Validform/images/error.png" />';
                        }
                        str += '</div>';
                    }
                }
                if(str == "") {
                    $("#sureFiles").hide();
                } else {
                    $("#sureFiles").show();
                }
                $("#sureFilesDetail").html(str);
            }
        });
    }
    function deleteFile(id, extendService){
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url:"tbAttachmentController.do?delObjFile",
                    data:{fileKey:id,extendService:extendService},
                    method:"post",
                    success:function(data){
                        sureUpload();
                    }
                });
            }
        });
    }
    //关闭弹出框页面
    function close(){
        windowapi.config.parent.close();
        windowapi.close();
    }
    //关闭遮罩层
    function closePro(){
        window.top.$.messager.progress('close');
    }
    //打开遮罩
    function openPro(){
        window.top.$.messager.progress({
            title:'提示',
            msg:'正在提交中......',
            text:'提交中'
        });
    }

    //提交操作
    function doSubmit(){
        var name = $('#name').val();
        if (name == '') {
            tip("没有填写主题.");
            return false;
        }
        var detail = $('#detail').val();
        if (detail == '') {
            tip("没有填写详情.");
            return false;
        }
        var processKey = $("#processKey").val();
        if(processKey == "") {
            tip("没有选择流程类型.");
            return false;
        }

        var params = $.parseJSON('${params}');
        params.processKey = processKey;

        //执行通用提交方法
        var param = {name:name,detail:detail,businessKey:'${businessKey}',businessKeyMain:'${businessKeyMain}',fullPathName:'${fullPathName}',params:JSON.stringify(params)};

        top.$.messager.progress({
            text : '操作执行中....',
            interval : 300
        });

        $.ajax({
            type: "POST",
            url: "taProcessThemeController.do?doSubmit",
            data:param,
            //async: false,
            success: function (data) {
                top.$.messager.progress("close");
                var d = $.parseJSON(data);
                if(d.success) {
                    W.top.tip(d.msg);
                    W.reloadTable();
                    close();
                } else {
                    tip(d.msg);
                }
            },
            error:function () {
                top.$.messager.progress("close");
            }
        });
    }
</script>
