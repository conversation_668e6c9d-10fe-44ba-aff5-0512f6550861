<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<link rel="stylesheet" href="resources/codemirror/lib/codemirror.css">
<link rel="stylesheet" href="resources/codemirror/theme/eclipse.css">
<style>
    .CodeMirror { height: auto; border: 1px solid #ddd; }
    .CodeMirror-scroll { max-height: 390px; }
    .CodeMirror pre { padding-left: 7px; line-height: 1.25; }
</style>

<div fit="true">
    <textarea id="htmlContent"></textarea>
</div>

<script src="resources/codemirror/lib/codemirror.js" type="text/javascript" charset="utf-8"></script>
<script src="resources/codemirror/mode/xml/xml.js"></script>
<script>

    //获取父窗口
    var json_$ = getSafeJq();
    //获取取值控件的id
    var hiddenHtmlEditorGetId = json_$("#hiddenHtmlEditorGetId").val();

    var parentHtmlContent = json_$("#" + hiddenHtmlEditorGetId).val();

    document.getElementById("htmlContent").value = parentHtmlContent || "";
    var editor = CodeMirror.fromTextArea(document.getElementById("htmlContent"), {
        mode: "text/html",
        theme: "eclipse",
        lineNumbers: true
    });

    editor.on("change", function (instance, object) {
        console.log(editor.getValue());
        document.getElementById("htmlContent").value = editor.getValue();
    });

</script>

