<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="west" style="width:350px;">
		<t:formvalid formid="formobj" layout="div" dialog="true" action="ttFridgeMainController.do?saveFridge" beforeSubmit="convertDetailToForm" refresh="true">
			<input type="hidden" name="id" value="${vo.id }">
			<input type="hidden" name="enableStatus" value="0">
			<input type="hidden" name="fridgeDetailJson" id="fridgeDetailJson">
			<input id="orgId" type="hidden" value="${orgId}">
			<div class="form">
				<label class="Validform_label">管理属性: </label>
				<input type="hidden" value="${vo.type}" name="type">
				<t:dictSelect id="type" field="type" type="select" defaultVal="${vo.type}"
							  typeGroupCode="fridge_ascription" isView="true">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜编号: </label>
				<input name="fridgeCode" id="fridgeCode" readonly="readonly" datatype="/^[0-9a-zA-Z_]{2,30}$/" class="inputxt" value="${vo.fridgeModel}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜型号: </label>
				<input name="fridgeModel" id="fridgeModel" datatype="*" readonly="readonly"  class="inputxt" value="${vo.fridgeModel}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜规格: </label>
				<input name="fridgeStandard" id="fridgeStandard"  readonly="readonly" class="inputxt" value="${vo.fridgeStandard}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜品牌: </label>
				<input type="hidden" value="${vo.fridgeSupplier}" name="fridgeSupplier">
				<t:dictSelect id="fridgeSupplier" field="fridgeSupplier" type="select" defaultVal="${vo.fridgeSupplier}"
							  typeGroupCode="fridge_brand" isView="true">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜类型: </label>
				<t:dictSelect id="fridgeType" field="fridgeType" type="select" defaultVal="${vo.fridgeType}"
							  typeGroupCode="fridge_type" dataType="*" isView="true">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">所属客户: </label>
				<input type="hidden" name="customerCode" id="customerCode" title="上级客户" value="${vo.customerCode}"/>
				<b:search hiddenFields="customerName,customerCode,id" inputTextName="customerName,customerCode,customerId" value="${vo.customerName}"
						  inputTextId="customerName" isClear="false" parameter="orgId" name="customerList" type="3" title="客户"></b:search>
				<span style="color: red;">*</span>
			</div>


			<div class="form">
				<label class="Validform_label">冰柜总价值: </label>
				<input name="fridgeWorth" id="fridgeWorth" readonly="readonly" class="inputxt" value="${vo.fridgeWorth}" />
				<span style="color: red;">*</span>
			</div>

			<!-- 必填 -->
			<div class="form">
				<label class="Validform_label">购买类型: </label>
				<input type="hidden" value="${vo.purchaseType}" name="purchaseType">
				<t:dictSelect id="purchaseType" field="purchaseType" type="select" defaultVal="${vo.purchaseType}"
							  typeGroupCode="purchase_type" dataType="*" isView="true">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">返款总额: </label>
				<input name="totalAmount" id="totalAmount" 	onblur="sumCost()" readonly="readonly"  class="inputxt" value="${amount}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">购买时间: </label>
				<input name="purchaseDate" id="purchaseDate" datatype="*" readonly="readonly" class="inputxt" value="${vo.purchaseDate}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">投放时间: </label>
				<input name="deliveryDate" id="deliveryDate"  readonly="readonly" class="inputxt" value="${vo.deliveryDate}" />
			</div>

			<div class="form">
				<label class="Validform_label">责任人: </label>
				<input type="hidden" name="userCode" id="userCode" />
				<b:search hiddenFields="fullName,userName" inputTextName="userName,userCode" value="${vo.userName}"
						  inputTextId="userName" isClear="false" parameter="orgId" name="tmUserList" type="1" title="人员"></b:search>
			</div>


			<div class="form">
				<label class="Validform_label">开始时间: </label>
				<input type="hidden" value="${maxYearMonth}" id="maxYearMonth">
				<input name="beginRebateDate" id="beginRebateDate" datatype="*" readonly="readonly" class="Wdate" style="width: 150px;"
					   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endRebateDate\')}',onpicked:function(){$('.Wdate').blur();}})"
					   value="${vo.beginRebateDate}" onblur="initDetail()"/>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">结束时间: </label>
				<input name="endRebateDate" id="endRebateDate" datatype="*" class="Wdate" style="width: 150px;"
					   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginRebateDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
					   onblur="initDetail()" readonly="readonly" class="inputxt" value="${vo.endRebateDate}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">所属门店: </label>
				<input type="hidden" name="terminalCode" id="terminalCode" />
				<b:search hiddenFields="terminalName,terminalCode" inputTextName="terminalName,terminalCode" value="${vo.terminalName}"
						  url="tmCommonMdmController.do?goTerminalSearch" dialogUrl="tmCommonMdmController.do?findTerminalBySearchList"
						  datagridUrl="tmCommonMdmController.do?findTerminalList"
						  inputTextId="terminalName" isClear="false"  parameter="customerCode" name="ttTerminalList" type="4" title="门店"></b:search>
			</div>
		</t:formvalid>
	</div>
	<div region="center">
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost" onclick="avgCost()">费用分摊</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-reset" onclick="resetCost()">重置</a>
						</span>

						<span style="float:right">
							<span style="color:red;line-height:30px;margin-right:50px;" >返利总额：<span id="rebateAmount">0</span></span>
							<span style="color:red;line-height:30px;margin-right:50px;" >已分摊返利金额：<span id="overRebate">0</span></span>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">

					</div>
				</div>
				<div class="datagrid-view">
					<table class="actTable" id="actTable">
						<thead>
						<tr>
							<td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
							<td>序号</td>
							<td>年月</td>
							<td>返利金额</td>
							<td style="display:none"></td>
						</tr>
						<c:if test="${(vo.ttFridgeDetailVoList)!= null && fn:length(vo.ttFridgeDetailVoList) > 0}">
							<c:forEach items="${vo.ttFridgeDetailVoList}" var="voList" varStatus="idxStatus">
								<tr style="background-color:gray;">
									<td></td>
									<td>${idxStatus.index+1}</td>
									<td>${voList.yearMonth}</td>
									<td><input  name="rebateAmount" value="${voList.rebateAmount}" disabled="disabled"/></td>
									<td style="display:none">${voList.id}</td>
								</tr>
							</c:forEach>
						</c:if>
						</thead>
						<tbody>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
    $(function(){
        $("input[name='endRebateDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='beginRebateDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='deliveryDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='purchaseDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        
        sumCost();
    });

    //提交前把列表信息封装到form
    function convertDetailToForm(){
        var rows=[];
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            var yearMonth = tdArr.eq(2).html();//月份
            var rebateAmount =tdArr.eq(3).find("input").eq(0).val();//金额
            var id = tdArr.eq(4).html();//月份
            //组装单据
            var obj = {
                'id': id,
                'yearMonth': yearMonth,
                'rebateAmount': rebateAmount
            };
            rows.push(obj);
        });
        var jsonStr=JSON.stringify(rows);
        $("#fridgeDetailJson").val(jsonStr);
	}

	function initDetail(){
		var maxYearMonth = $("#maxYearMonth").val();
		var beginDate=$("#beginRebateDate").val();
		if(maxYearMonth > beginDate){
			tip("开始月份必须大于上次结束日期");
			return false;
		}
        var endDate=$("#endRebateDate").val();
		if(beginDate==null||beginDate==''||endDate==null||endDate==''){
			 return;
		}
        $(".actTable tbody").html("");
		var yearMonths=getMonthBetween(beginDate,endDate);
		if(yearMonths!=null&&yearMonths.length>0){
            $.each(yearMonths,function(k,v){
                var i=Number(k)+1;
                var str='<tr>'
                    +'<td><input type="checkbox" name="actTableId" /></td>'
                    +'<td>'+i+'</td>'
                    +'<td>'+v+'</td>'
                    +'<td><input  name="rebateAmount"   onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0"  onblur="deductionAmount();" placeholder="请填写金额"/></td>'
                    +'<td style="display:none"></td>'
                    +'</tr>';
                $(".actTable tbody").append(str);
            });
		}

	}

	//获取两月份之间的所有年月
    function getMonthBetween(start,end){
        var result = [];
        var s = start.split("-");
        var e = end.split("-");
        var min = new Date();
        var max = new Date();
        min.setFullYear(s[0],s[1]);
        max.setFullYear(e[0],e[1]);

        var curr = min;
        while(curr <= max){
            var month = curr.getMonth();//0代表12
            var flag = false;
            if(month == 0){month = 12;flag = true;}
            result.push(curr.getFullYear()+"-"+(month<10?("0"+month):month));
            if(flag){month = 0;flag = false;}
            curr.setMonth(month+1);
        }
        return result;
    }

    function resetCost(){
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
			tdArr.eq(3).find("input").eq(0).val(0);//金额
        });
        $("#overRebate").html(0);
	}

    function sumCost(){
        var totalAmount=$("#totalAmount").val();
        $("#rebateAmount").html(totalAmount);
    }

    /**
     * 均摊
     */
    function avgCost() {
        var sumAmount = $("#totalAmount").val();
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            return;
        }

        var rows = $("table tbody tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {
            return;
        }

        var waitingAssgin = sumAmount;
        var avgAmount = (waitingAssgin / amountSize).toFixed(2);
        $.each(rows, function(i, row) {
            $(row).find("input[name='rebateAmount']").val(avgAmount);
        });

        var sum = 0;
        $.each(rows, function(i, row) {
            sum = add(sum, $(row).find("input[name='rebateAmount']").val());
        });
        var residueAmount = subtract(sumAmount, sum);
        var addToElement = $("table tbody tr:eq(0) input[name='rebateAmount']");
        var num = addToElement.val();
        addToElement.val(add(num, residueAmount));

        deductionAmount();
    }

    //扣减总金额
    function deductionAmount(){
        var sumAmount = 0;
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var rebateAmount =tdArr.eq(3).find("input").eq(0).val();//金额
            sumAmount=add(Number(rebateAmount),sumAmount);
        });
        $("#overRebate").html(sumAmount);
    }

    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }

    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }

	/*
	 * 高精减法函数
	 */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }

    /**
     * 高精度加法函数
     */
    function add(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }

    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();

        try {
            m += s1.split(".")[1].length;
        }
        catch (e) {

        }
        try {
            m += s2.split(".")[1].length;
        }
        catch (e) {

        }

        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }

    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;

        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch(e) {
            r2 = 0;
        }

        // 动态求出哪一个位数最多，再得出10的n次幂
        return Math.pow(10, Math.max(r1, r2));
    }
</script>