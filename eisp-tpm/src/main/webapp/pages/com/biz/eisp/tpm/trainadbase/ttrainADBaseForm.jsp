<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>高铁普列广告基础信息维护</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="trainADBaseController.do?saveTrainADBase"
             refresh="true">
    <input type="hidden" id="id" name="id" value="${vo.id}"/>
    <div class="form">
        <label class="Validform_label">合同编码: </label>
        <input name="contractCode" id="contractCode"
               datatype="*" class="inputxt" value="${vo.contractCode}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">合作公司: </label>
        <input name="partnerCompany" id="partnerCompany"
               datatype="*" class="inputxt" value="${vo.partnerCompany}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">签订车底号 : </label>
        <input name="trainBtNumber" id="trainBtNumber"
               datatype="*" class="inputxt" value="${vo.trainBtNumber}"/>
        <span style="color: red;">*</span>
    </div>

    <%--<div class="form">
        <label class="Validform_label">车次号 : </label>
        <input name="trainNumber" id="trainNumber"
               class="inputxt" value="${vo.trainNumber}" onchange="checkTrainNumber()"/>
        <span style="color: grey;">(选填)</span>
    </div>--%>

    <div class="form">
        <label class="Validform_label">签订开始日期 : </label>
        <input name="startDate" id="startDate"
               datatype="*" class="Wdate" value="${vo.startDate}" onchange="checkStartDate()"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: red;">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">签订结束日期 : </label>
        <input name="endDate" id="endDate"
               datatype="*" class="Wdate" value="${vo.endDate}" onchange="checkEndDate()"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">广告有效月数: </label>
        <input name="advEffectPeriod" id="advEffectPeriod"
               datatype="/^[0-9]*[1-9][0-9]*$/" class="inputxt" value="${vo.advEffectPeriod}" errormsg="只能输入大于等于0的数字"/>
        <span style="color: red;">*</span>
    </div>
    <%--<div class="form">
        <label class="Validform_label">车次开始时间 : </label>
        <input name="trainStartDate" id="trainStartDate"
               class="Wdate" value="${vo.trainStartDate}" onchange="checkTrainStartDate()"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: grey;">(选填)</span>
    </div>

    <div class="form">
        <label class="Validform_label">车次结束时间 : </label>
        <input name="trainEndDate" id="trainEndDate"
               class="Wdate" value="${vo.trainEndDate}" onchange="checkTrainEndDate()"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: gray;">(选填)</span>
    </div>--%>

</t:formvalid>
</body>
</html>
<script type="text/javascript">
    //    function checkDate(obj) {
    //        var startDate = $("#startDate").val();
    //        var endDate = $(obj).val();
    //        //alert(startDate+"-----"+endDate);
    //        if (startDate > endDate) {
    //            tip("结束日期必须大于开始日期");
    //            $("#startDate").val('');
    //            $("#endDate").val('');
    //            return false;
    //        }
    //    }

    function checkTrainNumber() {
        var trainNumber = $("#trainNumber").val();
        if(trainNumber == "") {
            $("#trainStartDate").val("");
            $("#trainEndDate").val("");
        }
    }

    function checkStartDate() {
        var startDate = $("#startDate").val();
        var endDate = $("#endDate").val();
        var trainStartDate = $("#trainStartDate").val();
        var trainEndDate = $("#trainEndDate").val();

        if (endDate != "" && startDate > endDate) {
            $("#endDate").val("");
        }
        if (trainStartDate != "" && startDate > trainStartDate) {
            $("#trainStartDate").val("");
        }
        if (trainEndDate != "" && startDate > trainEndDate) {
            $("#trainEndDate").val("");
        }
    }

    function checkEndDate() {
        var startDate = $("#startDate").val();
        var endDate = $("#endDate").val();
        var trainStartDate = $("#trainStartDate").val();
        var trainEndDate = $("#trainEndDate").val();

        if (startDate != "" && endDate < startDate) {
            tip("结束时间不得小于开始时间");
            $("#endDate").val("");
            return;
        }
        if (trainStartDate != "" && endDate < trainStartDate) {
            $("#trainStartDate").val("");
        }
        if (trainEndDate != "" && endDate < trainEndDate) {
            $("#trainEndDate").val("");
        }
    }

    function checkTrainStartDate() {
        var trainNumber = $("#trainNumber").val();
        var startDate = $("#startDate").val();
        var endDate = $("#endDate").val();
        var trainStartDate = $("#trainStartDate").val();
        var trainEndDate = $("#trainEndDate").val();

        if (trainStartDate == "") {
            $("#trainEndDate").val("");
        }else{
            if (trainNumber == "") {
                tip("请先填写车次号");
                $("#trainStartDate").val("");
                return;
            }

            if (startDate == "" || endDate == "") {
                tip("请先填写合作开始时间和结束时间");
                $("#trainStartDate").val("");
                return;
            }
            if (trainStartDate < startDate || trainStartDate > endDate) {
                tip("车次时间必须在合作时间之内");
                $("#trainStartDate").val("");
                return;
            }
            if (trainEndDate != "" && trainStartDate > trainEndDate) {
                tip("车次开始时间不能晚于结束时间");
                $("#trainStartDate").val("");
                return;
            }
        }


    }

    function checkTrainEndDate() {
        var trainNumber = $("#trainNumber").val();
        var startDate = $("#startDate").val();
        var endDate = $("#endDate").val();
        var trainStartDate = $("#trainStartDate").val();
        var trainEndDate = $("#trainEndDate").val();

        if (trainNumber == "" && trainEndDate != "") {
            tip("请先填写车次号");
            $("#trainEndDate").val("");
            return;
        }

        if (trainEndDate == "") {
            if (trainStartDate=="") {

            }else{
                tip("请填写车次结束时间");
            }
        }else {
            if (startDate == "" || endDate == "") {
                tip("请先填写合作开始时间和结束时间");
                $("#trainEndDate").val("");
                return;
            }
            if (trainStartDate == "") {
                tip("请先填写车次开始时间");
                $("#trainEndDate").val("");
                return;
            }
            if (trainEndDate < startDate || trainEndDate > endDate) {
                tip("车次时间必须在合作时间之内");
                $("#trainEndDate").val("");
                return;
            }
            if (trainEndDate < trainStartDate) {
                tip("查词开始时间不得早于开始时间");
                $("#trainEndDate").val("");
                return;
            }
        }

    }
</script>