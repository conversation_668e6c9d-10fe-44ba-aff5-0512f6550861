<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tdTemplateConfigList" fitColumns="false" title="模板块配置"
                    actionUrl="tdTemplateController.do?findTdTemplateConfigList" idField="id" fit="true"
                    queryMode="group" pageSize="20">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="状态" field="status" width="50" replace="有效_1,失效_0"></t:dgCol>
            <t:dgCol title="所属模块名称" field="tplPartName" width="120"></t:dgCol>
            <t:dgCol title="优先级" field="displaySort" width="60"></t:dgCol>
            <t:dgCol title="备注" field="risk" width="200"></t:dgCol>
            <t:dgToolBar title="新增" icon="icon-add" url="tdTemplateController.do?goTdTemplateConfigForm" funname="add"></t:dgToolBar>
            <t:dgToolBar title="修改" icon="icon-edit" url="tdTemplateController.do?goTdTemplateConfigForm"
                         funname="update"></t:dgToolBar>
            <t:dgToolBar title="查看" icon="icon-search" url="tdTemplateController.do?goTdTemplateConfigForm"
                         funname="detail"></t:dgToolBar>

        </t:datagrid>

    </div>
</div>

