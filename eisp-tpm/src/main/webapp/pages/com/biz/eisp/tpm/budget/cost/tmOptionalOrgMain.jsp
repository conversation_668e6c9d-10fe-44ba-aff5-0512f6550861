<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="tmOptionalOrgForm" class="easyui-layout" fit="true">
	<div data-options="region:'center',split:true" style="padding:1px;">
		<t:datagrid name="tmOptionalOrgSelectList" fitColumns="false" fit="true" title="待选组织列表" queryMode = "group"
	     idField="id" singleSelect="false" autoLoadData="false" actionUrl="tmOrgApiController.do?findOptionalOrgSelectList">
	        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
	        <t:dgCol title="组织编码" field="orgCode"></t:dgCol>
	        <t:dgCol title="组织名称" field="orgName" query="true"></t:dgCol>
	        <t:dgCol title="组织描述" field="orgDesc"></t:dgCol>
	        <t:dgCol title="组织类型" field="orgType" dictionary="org_type" query="true"></t:dgCol>
	        <t:dgCol title="SAP编码" field="extChar2"></t:dgCol>
	        <t:dgCol title="市场级别" field="extChar1" dictionary="market_level"></t:dgCol>
	        <t:dgToolBar title="添加" icon="icon-add" url="" funname="addOrg"></t:dgToolBar>
	    </t:datagrid>
   </div>
   
   <div data-options="region:'east',split:true" style="padding:1px;width:500px">
		<t:datagrid name="tmOptionalOrgAlreadyList" fitColumns="false" fit="true" title="已选组织列表" queryMode = "group" onLoadSuccess="loadElecteGrid"
	     idField="id" singleSelect="false" pagination="false" actionUrl="tmOrgApiController.do?findOptionalOrgAlreadyList&costTypeCode=${costTypeCode }&orgCodes=${orgCodes}">
	        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
	        <t:dgCol title="组织编码" field="orgCode"></t:dgCol>
	        <t:dgCol title="组织名称" field="orgName"></t:dgCol>
	        <t:dgCol title="组织描述" field="orgDesc"></t:dgCol>
	        <t:dgCol title="组织类型" field="orgType" dictionary="org_type"></t:dgCol>
	        <t:dgCol title="SAP编码" field="extChar2"></t:dgCol>
	        <t:dgCol title="市场级别" field="extChar1" dictionary="market_level"></t:dgCol>
			<t:dgToolBar title="移除" icon="icon-remove" url="" funname="removeOrg"></t:dgToolBar>
	    </t:datagrid>
   </div>
</div>
<script type="text/javascript">
	//添加
	function addOrg() {
		var seletctTarget =  $("#tmOptionalOrgSelectList").datagrid("getSelections");
		if(seletctTarget==null || seletctTarget==""){
			tip("请至少选择一条数据");
			return false;
		}
		//添加
		for (var i = 0; i < seletctTarget.length; i++) {
			var r = seletctTarget[i];
			$("#tmOptionalOrgAlreadyList").datagrid("insertRow",{row:r});
		}
		
		loadElecteGrid();
	}
	
	function removeOrg() {
		var checkListTarget =  $("#tmOptionalOrgAlreadyList").datagrid("getSelections");
		if(checkListTarget==null || checkListTarget==""){
			tip("请至少选择一条数据");
			return false;
		}
		var selectRows = [];
		//选中数据加入数组
		for (var i = 0; i < checkListTarget.length; i++) {
			selectRows.push(checkListTarget[i]);
		}
		for (var i = 0; i < selectRows.length; i++) {
			var checkRowIndex = $("#tmOptionalOrgAlreadyList").datagrid("getRowIndex",selectRows[i]);
			//移除该数据
			$("#tmOptionalOrgAlreadyList").datagrid("deleteRow",checkRowIndex);
		}
		loadElecteGrid();
	}
	
	//加载待选
	function loadElecteGrid(){
		//加载待选列表
		var excludeOrgCode = "'-1'";//默认一个值
		var checkedTarget = $("#tmOptionalOrgAlreadyList").datagrid("getRows");
		if(checkedTarget != null && checkedTarget != ""){
			excludeOrgCode = "";
			for(var i = 0;i<checkedTarget.length;i++){
				if(excludeOrgCode != ""){
					excludeOrgCode+=",";
				}
				excludeOrgCode += "'"+checkedTarget[i].orgCode+"'";
			}
		}
		//手动加载待选
		$('#tmOptionalOrgSelectList').datagrid({
			url: "tmOrgApiController.do?findOptionalOrgSelectList",
			queryParams: {
				notInOrgCode: excludeOrgCode
			}
		});
	}
</script>