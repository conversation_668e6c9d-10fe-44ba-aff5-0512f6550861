<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttDirectresentPoliMainList" title="政策维护" checkbox="true" singleSelect="false"
				actionUrl="ttDirectPresentPoliMainController.do?findTtDirectPresentPoliMainList"
				idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
			<t:dgCol title="推送状态" field="inSapState" dictionary="dic_in_sap_state" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="政策维护编码" field="ownNum" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="签呈编号" field="num" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="开始时间" field="beginDate" query="true" sortable="false"  ></t:dgCol>
			<t:dgCol title="结束时间" field="endDate" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="客户编码" field="customerCode" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="客户名称" field="customerName"  query="true" sortable="false"  ></t:dgCol>
			<t:dgCol title="门店SAP编码" field="terminalSapcode" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="门店名称" field="terminalName" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="正品编号" field="productCode" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="正品名称" field="productName" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="正品数量" field="productQuantity" sortable="false" ></t:dgCol>
			<t:dgCol title="正品计量单位" field="productUnit" dictionary="dic_mea_unit" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="赠品编码" field="presentCode" sortable="false" ></t:dgCol>
			<t:dgCol title="赠品名称" field="presentName" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="赠品数量" field="presentQuantity" sortable="false" ></t:dgCol>
			<t:dgCol title="赠品计量单位" field="presentUnit" sortable="false" ></t:dgCol>
			<t:dgCol title="创建人" field="createBy" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="创建时间" field="createDateStr" query="true" queryMode="group" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
			<t:dgCol title="修改人" field="updateBy" query="true" sortable="false" ></t:dgCol>
			<t:dgCol title="修改时间" field="updateDateStr" query="true" queryMode="group" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
			<t:dgToolBar title="政策维护" icon="icon-add" url="ttDirectPresentPoliMainController.do?goTtDirectPresentPoliMainFrom&optype=0" funname="ownAdd"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" url="ttDirectPresentPoliMainController.do?goTtDirectPresentPoliMainFrom&optype=1" funname="ownUpdate"></t:dgToolBar>
			<t:dgToolBar title="删除" icon="icon-remove" url="ttDirectPresentPoliMainController.do?removeTtDirectPresentPoliMain" funname="deleteALLSelect"></t:dgToolBar>
			<t:dgToolBar title="推送SAP" icon="icon-dataOut" url="ttDirectPresentPoliMainController.do?introducedIntoSAP" funname="inSapFun"></t:dgToolBar>
			<t:dgToolBar title="推送日志" icon="icon-log" url="ttDirectPresentPoliMainController.do?goShowIntroducedIntoSAPLog" funname="ownDetail" width="1200"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

    $(function(){
        //日期格式查询条件
        $("input[name='beginDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); }).attr("readonly",true);
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); }).attr("readonly",true);
    });

    //政策申请
    function ownAdd(title, url, grid, width, height){
        gridname = grid;
        openWindOwn(title, url);
    }

    //修改
    function ownUpdate(title, url, grid, width, height){
        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var rowData = rowDatas[0];
        var id = rowData.id;
        if (checkThisDataCouldToDo(id)){
            url += "&id=" + id;
            openWindOwn(title, url);
        }
    }

    function inSapFun(title, url, grid, width, height){
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas == null || rowDatas.length == 0) {
            tip("请选择一条项目");
            return ;
        }
        var ids = [];
        for ( var i = 0; i < rowDatas.length; i++) {
            ids.push(rowDatas[i].id);
        }
        var id = ids.join(',');
        if (checkThisDataCouldToDo(id)){
            var thisData = {
                ids : id
            }
            var data = ajaxPost(thisData,url);
            if (data.success){
                $('#ttDirectresentPoliMainList').datagrid("reload");
            }
            tip(data.msg)
        }
	}

    //检查
    function checkThisDataCouldToDo(id){
        var falg = false;
        var thisData = {
            ids : id
        }
        var url = "ttDirectPresentPoliMainController.do?checkThisDataCouldToDo";
        var data = ajaxPost(thisData,url);
        if (data.success){
            falg = true;
        }else{
            tip(data.msg)
        }
        return falg;
    }

    function ownDetail(title, url, grid, width, height){
        createwindowExt(title,url,"800","500",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ]
        });
	}

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }

    function openWindOwn(title, url){
        createwindowExt(title,url,"1300","650",{
            button : [
				{
				    name : "保存",
					callback : function(){
                        var iframeTemp = this.iframe.contentWindow;
                        iframeTemp.saveDiectPresent();
                        return false;
					}
				},
                {name : "关闭",
                    callback : function() {
//                        var iframeTemp = this.iframe.contentWindow;
//                        iframeTemp.saveDirectPresentByClose();
                        return true;
                    }}
            ]
        });
    }

</script>
