<%--asdasdasdasdasdasdasd--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true" >
        <div data-options="region:'center'" style="padding:1px;">
    <%--<div region="center" style="padding: 1px;">--%>
    <t:datagrid name="advPictureInfoV2" title=""  actionUrl="tsPictureController.do?getPicList&id=${id}"
                idField="id" fit="true"  fitColumns="false"  pagination="false" queryMode="group">
        <%--<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="bussinessId" field="bussinessId" hidden="true"></t:dgCol>
        <t:dgCol title="门头编号" field="adCode" query="true" hidden="true" ></t:dgCol>--%>
        <t:dgCol title="类型" field="imgType"  dictionary="sfa_photo_type" width="150"></t:dgCol>
        <t:dgCol title="类型描述" field="imgTypeRemark"  width="200"></t:dgCol>
        <t:dgCol title="位置" field="place"  width="150"></t:dgCol>
        <t:dgCol title="缩略图" field="imgPath"  formatterjs="showInfo"   width="200"></t:dgCol>

    </t:datagrid>
    </div>
</div>
<script>

    function showInfo(value,row) {
        // if(row.imgType==165){
        //     var url="tsPictureController.do?download&id="+row.id;
        //     return "<a href="+url+">点击下载</a>"
        // }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;

            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }
    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }


    function videoBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        var url = "tsPictureController.do?showVideo&path="+src;
        var id ="advPictureInfoV2";
        var title="视频查看";
        createwindow(title, url, id, 5000, 800);
    }


    function showBigPic(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        var url = "tsPictureController.do?showBigPic&path="+src;
        var id ="advPictureInfoV2";
        var title="图片查看";
        createwindow(title, url, id, 1130, 500);
//        var url = "tsPictureController.do?showBigPic&path="+src;
//        var id ="advPictureInfoV2";
//        var title="图片查看";
//        createwindow(title, url, id, 1130, 500);

//        createwindow(
//            '图片查看',
//            'tsPictureController.do?showBigPic&path='+src,
//            "", "", {
//                lock : true,
//                parent : windowapi,
//                zIndex:11000,
//                width : 1200,
//                height : 500,
//                button : [ {
//                    name : '关闭',
//                    callback : function() {
//                    }
//                } ]
//            });


    }
</script>