<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>直营收入拆分</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <link rel="stylesheet" href="resources/Validform/css/divfrom.css" type="text/css"/>
    <link rel="stylesheet" href="resources/Validform/css/style.css" type="text/css"/>
    <link rel="stylesheet" href="resources/Validform/css/tablefrom.css" type="text/css"/>
    <script type="text/javascript" src="resources/Validform/js/Validform_v5.3.1_min_zh-cn.js"></script>
    <script type="text/javascript" src="resources/Validform/js/Validform_Datatype_zh-cn.js"></script>
    <script type="text/javascript" src="resources/Validform/js/datatype_zh-cn.js"></script>
</head>
<body style="overflow-y: hidden" scroll="no">
<div id="content">
    <div id="wrapper">
        <div id="steps">
            <t:formvalid formid="formobj"   layout="div" dialog="true" action="ttAuditQuotaController.do?doTtAuditModify" refresh="true" tiptype="3" >
                <input type="hidden" name="billMainId" id="billMainId" value="${billMainId}">
                <div class="form">
                    <label class="Validform_label">支付方式: </label>
                    <t:dictSelect field="paymentCode" dataType="*" typeGroupCode="payment_type" ></t:dictSelect>
                    <span style="color: red;">*</span>
                </div>

                <div class="form">
                    <label class="Validform_label">调整系数: </label>
                    <t:dictSelect field="conversion" dataType="*" typeGroupCode="conversion_factor" ></t:dictSelect>
                </div>
            </t:formvalid>
        </div>
    </div>
</div>

</body>
</html>
<script type="text/javascript">
</script>