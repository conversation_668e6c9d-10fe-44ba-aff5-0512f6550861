<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="ttFridgeMainList" fitColumns="true" title="冰柜台账及返利"
                    actionUrl="ttFridgeRebateController.do?findFridgeLedgerAndDetailList&customerCode=${customerCode}&yearMonth=${yearMonth}&purchaseType=${purchaseType}"
                    queryMode="group" pagination="true" idField="id" fit="true">
            <t:dgCol title="编号" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth"></t:dgCol>
            <t:dgCol title="冰柜编号" field="fridgeCode"></t:dgCol>
            <t:dgCol title="客户编号" field="customerCode" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"></t:dgCol>
            <t:dgCol title="购买类型" field="purchaseType" dictionary="purchase_type"></t:dgCol>
            <t:dgCol title="返利金额" field="applyAmount"></t:dgCol>
            <t:dgCol title="状态" field="rebateStatus" dictionary="rebate_status"></t:dgCol>
        </t:datagrid>
    </div>
</div>
