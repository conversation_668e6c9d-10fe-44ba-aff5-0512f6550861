<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<script type="text/javascript">
	$(function(){
        $("#erpCode").on("blur",function(){
            checkErpCode1($('#erpCode').val());
        });

        $("#cb_extChar41").combobox("reload","tmCustomerExtendMdmController.do?findOrgList");
        $("#cb_extChar41").combobox({
            onSelect:function(node){
                $("#extChar41").val(node.value);
                $("#ct_orgId").combotree('clear');
            }
        });
	});

    //执行远程请求
    function checkErpCode1(value){
        if(value==null||value==""){
            return;
        }
        $.ajax({
            url : "tmCustomerController.do?getErpCodeInfo",
            type : 'post',
            data : {"erpCode":value},
            cache : false,
            success : function(data) {
                if (data.success) {
                    var obj =data.obj;
                    autoLoadData(obj);

                    $("#cb_extChar41").combobox('setValue',obj.extChar41);
                    $("#ct_orgId").combotree('setValue',obj.orgName);

                }
            }
        });
    }
</script>