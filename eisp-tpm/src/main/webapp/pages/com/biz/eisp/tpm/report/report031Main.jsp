<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report001List" fitColumns="false" title="流程实例"
                    actionUrl="report031Controller.do?findReportList" idField="processInstanceId" fit="true">
            <t:dgCol title="活动类型" field="actModeCode" query="true" replace="产品政策_0,直营搭赠_1,直营价格_2,长期待摊_3,广告费_4,定额_5,物料_6,结案_7,专案_8,产品政策变更_9"></t:dgCol>

            <t:dgCol title="年月" field="yearMonth" hidden="true" query="true"></t:dgCol>

            <t:dgCol title="任务编号" field="taskId" hidden="true"></t:dgCol>
            <t:dgCol title="流程编号" field="processInstanceId" query="true"></t:dgCol>
            <t:dgCol title="流程key" field="processKey" query="true"></t:dgCol>
            <t:dgCol title="流程名称" field="processName" query="true"></t:dgCol>
            <t:dgCol title="流程标题" field="processTitle" query="true"></t:dgCol>
            <t:dgCol title="发起人" field="fullName"></t:dgCol>
            <t:dgCol title="发起人职位名称" field="positionName"></t:dgCol>
            <t:dgCol title="发起人职位编码" field="positionCode"></t:dgCol>
            <t:dgCol title="发起人组织编码" field="orgCode" query="true"></t:dgCol>
            <t:dgCol title="发起人组织名称" field="orgName"></t:dgCol>
            <t:dgCol title="当前任务名称" field="taskName"></t:dgCol>
            <t:dgCol title="当前处理人" field="assignee" sortable="true"></t:dgCol>
            <t:dgCol title="当前处理人姓名" field="handName" sortable="true"></t:dgCol>
            <t:dgCol title="开始时间" field="startTime"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"></t:dgCol>
            <t:dgCol title="流程状态" field="runtimeStatus" query="true" replace="处理中_doing,完成_completed,驳回_reject,流程追回_recover"></t:dgCol>
            <t:dgCol title="状态" sortable="false" field="isSuspended" query="true" replace="已结束_finished,启动_false,暂停_true"
                     style="background:red;_true"></t:dgCol>

            <t:dgCol title="备注" field="remark"></t:dgCol>


            <t:dgToolBar operationCode="processBack" icon="icon-process_back" id="callbackId" url="taProcessInstanceController.do?callBackProcess" exp="runtimeStatus#eq#'doing'" funname="systemConfirm" params="processInstanceId" title="流程追回"></t:dgToolBar>
            <t:dgToolBar operationCode="view" icon="icon-look" id="processView" url="taTaskController.do?goInstanceHandleTabForm&isView=true&isReadFlag=false" funname="view" params="processInstanceId" title="查看" height="500" width="1200"></t:dgToolBar>
            <t:dgToolBar operationCode="copyView" icon="icon-look" id="copyView" url="taCarbonCopyController.do?goCopyPersonInfoMain" funname="view" params="processInstanceId" title="查看抄送" width="600" height="400"></t:dgToolBar>
            <t:dgToolBar title="批量驳回" operationCode="batch" icon="icon-putout" url=""  funname="reject"></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="dataout" icon="icon-dataOut" url="report031Controller.do?export031"  funname="excelExport"></t:dgToolBar>
        	<t:dgToolBar title="签字打印导出" operationCode="print" icon="icon-dataOut" url="report031Controller.do?exportAuditPrint"  funname="export7Data"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    function reject(){
        var queryParams = $('#report001List').datagrid('options').queryParams;
        $('#report001Listtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        if(queryParams.actModeCode == "" || queryParams.actModeCode == undefined){
            var actModeCode = $("select[name='actModeCode']").val();
            if(actModeCode == ''){
                tip("批量驳回,必须选择查询条件:活动类型","error");
                return;
            }else{
                queryParams.actModeCode = actModeCode;
            }
        }

        if(queryParams.runtimeStatus == "" || queryParams.runtimeStatus == undefined){
            var runtimeStatus = $("select[name='runtimeStatus']").val();
            if(runtimeStatus == '' ){
                tip("批量驳回,必须选择查询条件:流程状态");
                return;
            }else if(runtimeStatus == 'doing'){
                queryParams.runtimeStatus = runtimeStatus;
            }else{
                tip("批量驳回,必选选择查询条件:流程状态-处理中","error");
            }
        }

        if(queryParams.yearMonth == "" || queryParams.yearMonth == undefined){
            var yearMonth = $("input[name='yearMonth']").val();
            if(yearMonth == ''){
                tip("批量驳回,必须选择查询条件:年月","error");
                return;
            }else{
                queryParams.yearMonth = yearMonth;
            }
        }
        queryParams.rows = '500';

        var url = "report031Controller.do?batchReject";
        $.ajax({
            url:url,
            type:"post",
            data:queryParams,
            success:function(data){
                var d = $.parseJSON(data);
                tip(d.msg);
            }
        });


    }
    $(function () {
        $("#report001ListForm").find("label").eq(0).attr("style","color:red");
        $("select[name='actModeCode']").find("option[value='0']").attr("selected",true);
        $("#report001Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("select[name='actModeCode']").change(function(){report001Listsearch();});
    });
    
    function export7Data(){
    	var report001ListRow = $("#report001List").datagrid("getSelected");
        if (report001ListRow == null || report001ListRow == "") {
            tip("请选择一条需要导出的数据");
            return false;
        }
        if(report001ListRow.length > 1){
    	    tip("只能选择一条数据进行导出","error");
    	    return false;
    	}
        if(report001ListRow.actModeCode != "7")
        {
        	tip("只能选择一条结案活动进行导出","error");
     	    return false;
        }
        window.location.href = "report031Controller.do?exportAuditPrint&processInstanceId="+report001ListRow.processInstanceId;
        //EispExcelExport("report031Controller.do?exportAuditPrint&processInstanceId="+report001ListRow.processInstanceId,"report001List");
    }
</script>
