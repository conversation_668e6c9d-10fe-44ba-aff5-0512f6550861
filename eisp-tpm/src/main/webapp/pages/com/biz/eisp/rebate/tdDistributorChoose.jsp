<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center" style="padding:1px;">
		<t:datagrid name="tdDistributorList"    fitColumns="false" title="非直供终端" actionUrl="tdDistributorSalesController.do?findTdDistributorList"
					idField="id" fit="true" queryMode="group"  singleSelect="false">
			<t:dgCol title="主键"  field="id"  hidden="true"   ></t:dgCol>
			<t:dgCol title="非直供编码"  field="terminalCode"     query="true"  ></t:dgCol>
			<t:dgCol title="非直供名称"  field="terminalName" query="true"></t:dgCol>
			<t:dgCol title="上级客户编码"  field="customerCode"     query="true"  ></t:dgCol>
			<t:dgCol title="上级客户SAP编码"  field="erpCode"     query="true"  ></t:dgCol>
			<t:dgCol title="上级客户名称"  field="customerName" query="true"></t:dgCol>

		</t:datagrid>
	</div>
</div>

<script>
	$(function () {
		$("input[name*='Date']").addClass("Wdate").css({
			'height': '20px',
			'width': '150px'
		}).removeAttr("onfocus").on("focus", function () {
			WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'});
		});
	});
</script>
