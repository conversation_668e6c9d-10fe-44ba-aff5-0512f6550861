<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>上账</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
    <script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
    <style>
    </style>
</head>
<body>
<div class="easyui-layout" fit="true" id="layoutId">

    <%--待处理上账数据模块开始--%>
    <div id="westDiv" data-options="region:'center'" style='padding:1px;'>
        <div class="easyui-layout" fit="true">
            <%--列表数据--%>
            <div id="accountingDiv" data-options="region:'center'" style="padding:1px;width:100%;">
                <div class="datagrid-wrap panel-body">
                    <div class="datagrid-view" style="widows:100%;height: 100%; overflow: auto;">
                        <table class="actTable" id="pending_accounting_list">
                            <thead>
                            <tr>
                                <td>序号</td>
                                <td>上账编号</td>
                                <td>支付方式</td>
                                <td>上账金额</td>
                                <td>财务凭证号</td>
                                <td>上账人</td>
                                <td>上账时间</td>
                            </tr>
                            </thead>
                            <tbody id="accountingTbody">
                            <c:if test="${not empty voList}">
                                <c:forEach items="${voList}" var="vo" varStatus="vs">
                                    <tr onclick="check(this)" id="${vo.accountingCode}">
                                        <td>${vs.index + 1}</td>
                                        <td>${vo.accountingCode}</td>
                                        <td>${vo.paymentName}</td>
                                        <td>${vo.accountingAmount}</td>
                                        <td>${vo.financeVoucherCode}</td>
                                        <td>${vo.updateName}</td>
                                        <td>${vo.updateDate}</td>
                                    </tr>
                                </c:forEach>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%--待处理上账数据模块结束--%>
    <c:if test="${not empty voucherList}">
    <%--凭证数据模块开始--%>
    <div id="pingzhengdiv" data-options="region:'east'" title="凭证" split="true"  style="width:700px;padding:1px;">
        <div class="easyui-layout" fit="true">
            <%--列表数据--%>
            <div data-options="region:'center'" style="padding:1px;">
                <div class="datagrid-wrap panel-body">
                    <div class="datagrid-view" style="height: 100%; overflow: auto;">
                        <table class="actTable" id="voucherList">
                            <thead>
                            <tr>
                                <td>发票号</td>
                                <td>税码</td>
                                <td>结案金额（元）（不含税）</td>
                                <td>结案金额上账会计科目名称</td>
                                <td>税金（元）</td>
                                <td>进项税转出金额</td>
                                <td>上账金额（元）</td>
                                <td>上账客户名称</td>
                                <td>SAP成本中心</td>
                                <td>备注</td>
                                <td>抬头文本</td>
                                <td>凭证附件张数</td>
                            </tr>
                            </thead>
                            <tbody id="voucherTbody">
                            <c:forEach items="${voucherList}" var="o">
                                <tr>
                                    <td id="invoiceNum">${o.invoiceNum}</td>
                                    <td id="taxNum">
                                        <c:forEach items="${ttTaxConfigList}" var="tax">
                                            <c:if test="${tax.taxcodeCode==o.taxNum}">${tax.taxcodeName}</c:if>
                                        </c:forEach>
                                    </td>
                                    <td id="auditAmount">${o.auditAmount}</td>
                                    <td id="auditAccountName">${o.auditAccountName}</td>
                                    <td id="taxAmount">${o.taxAmount}</td>
                                    <td id="inputTaxAmount">${o.inputTaxAmount}</td>
                                    <td id="accountingAmount">${o.accountingAmount}</td>
                                    <td id="accountingCustomerName">${o.accountingCustomerName}</td>
                                    <td id="costCenter">${o.costCenter}</td>
                                    <td id="remark">${o.remark}</td>
                                    <td id="ttwb">${o.ttwb}</td>
                                    <td id="yema">${o.yema}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--凭证数据模块结束--%>
    </c:if>
</div>
</body>
</html>

<script type="text/javascript">

    //选中当前tr
    function check(obj){
        $("#accountingTbody").children("tr").removeClass("active");
        $(obj).attr("class","active");
        var accCode = $(obj).attr("id");
        $.ajax({
            async: false,
            url: "ttAccounting2Controller.do?getVoucherByAccountCode",
            data: {'accountingCode':accCode},
            type: "post",
            dataType : "json",
            success : function(d) {
                if (d.success) {
                    refreshVooucher(d.obj);
                }else{
                    tip(d.msg,'error');
                }
            }
        });
    }
    //刷新右边凭证数据
    function refreshVooucher(rows) {
        if(rows != null && rows.length > 0){
            var trStrs = "";
            for (var i = 0; i < rows.length; i++) {
                var data = rows[i];
                var obj = new Object();
                obj.id = data.id;
                obj.invoiceNum= returnNotUndefinedData(data.invoiceNum);//发票号
                obj.taxNum = returnNotUndefinedData(data.taxNum);//税码
                obj.auditAmount = returnNotUndefinedData(data.auditAmount);//结案金额（元）（不含税）
                obj.auditAccountCode = returnNotUndefinedData(data.auditAccountCode);//结案金额上账会计科目编码
                obj.auditAccountName = returnNotUndefinedData(data.auditAccountName);//结案金额上账会计科目名称
                obj.taxAmount = returnNotUndefinedData(data.taxAmount);//税金（元）
                obj.inputTaxAmount = returnNotUndefinedData(data.inputTaxAmount);//进项税转出金额
                obj.accountingAmount = returnNotUndefinedData(data.accountingAmount);//上账金额（元）
                obj.accountingCustomerCode = returnNotUndefinedData(data.accountingCustomerCode);//上账客户编码
                obj.accountingCustomerName = returnNotUndefinedData(data.accountingCustomerName);//上账客户名称
                obj.costCenter = returnNotUndefinedData(data.costCenter);//SAP成本中心
                obj.remark = returnNotUndefinedData(data.remark);//备注
                obj.accountingCode = returnNotUndefinedData(data.accountingCode);
                //构造组成行数据
                trStrs += addVoucherRowData(obj);
            }
            if (trStrs != '') {
                $("#voucherTbody").html(trStrs);
            }
        }
    }

    //添加凭证行
    function addVoucherRowData(o){
        var str = '<tr> ';
        str += '<td id="invoiceNum">' + o.invoiceNum + '</td>';//发票号
        str += '<td id="taxNum">'+gettaxNumMode(o.taxNum)+ '</td>';//税码
        str += '<td id="auditAmount">' + o.auditAmount + '</td>';//结案金额（元）（不含税）
        str += '<td id="auditAccountName">' + o.auditAccountName + '</td>';//结案金额上账会计科目名称
        str += '<td id="taxAmount">' + o.taxAmount + '</td>';//税金（元）
        str += '<td id="inputTaxAmount">' + o.inputTaxAmount + '</td>';//进项税转出金额
        str += '<td id="accountingAmount">' + o.accountingAmount + '</td>';//上账金额（元）
        str += '<td id="accountingCustomerName">' + o.accountingCustomerName;
        str += '</td>';//上账客户名称
        str += '<td id="costCenter">' + o.costCenter + '</td>';//SAP成本中心
        str += '<td id="remark">' + o.remark + '</td>';//备注
        str += '</tr> ';
        return str;
    }

    function gettaxNumMode(v){
        var name = "";
        <c:forEach items="${ttTaxConfigList}" var="tax">
            <c:if test="${tax.taxCode==v}">name = '${tax.taxName}';</c:if>
        </c:forEach>
        return name;
    }
</script>