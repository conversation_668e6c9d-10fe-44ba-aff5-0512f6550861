<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>期初费用预算</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttBudgetCustomerController.do?saveBudgetCustomerClose" 
             refresh="true" beforeSubmit="validateAmount()" callback="refresh" >
	<input type="hidden" name="budgetCustomerCode" value="${vo.budgetCustomerCode}">
	<input type="hidden" id="maxAmount" value="${maxAmount}">
	<div class="form">
		<label class="Validform_label">停用金额: </label>
		<input name="amount" onblur="validateAmount()" id="amount" datatype="/^(([1-9]\d*)|\d)(\.\d{1,2})?$/" errormsg="只能输入数字，带两位小数" class="inputxt"/>
		<span style="color: red;">*</span>
		<span class="Validform_checktip" id="error">最大可停用金额：${maxAmount}</span>
	</div>
</t:formvalid>
</body>
</html>
<script type="text/javascript">
function validateAmount(){
	var amount = $("#amount").val();
	var useAmount = $("#maxAmount").val();
	if(amount == ""){
		return false;
	}
	if(parseFloat(amount) > parseFloat(useAmount)){
		tip("停用金额不能大于最大停用金额");
		$("#error").addClass("Validform_wrong").attr("title","请填写信息");
		$("#amount").val("");
		return false;
	}else{
		$("#error").removeClass("Validform_wrong");
	}
}
</script>