<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>返利目标管理</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

		<style>
			#steps form div.form {float:left;width:260px !important;height:26px;padding-left:130px !important;}
			#steps form div.form .Validform_label {width:130px;margin-right:5px;}
			.formDiv {float:left;}
		</style>

</head>
<body class="easyui-layout">
<div data-options="region:'north'" style="height:210px">
	<t:formvalid formid="formobj" layout="div" beforeSubmit="fetchTableValues();" dialog="true" refresh="true"
				 action="tdRebateTargetController.do?saveTdRebateTarget">

	<div>
		<input id="id" name="id" type="hidden" value="${tdRebateTargetVo.id}">

		<div class="form">
			<label class="Validform_label">非直供编码: </label>
			<input name="disbCode" class="inputxt" id="disbCode" readonly="readonly" datatype="s2-30">

			<t:choose
					hiddenName="disbCode" hiddenid="terminalCode"
					url="tdDistributorSalesController.do?goTdDistributorChoose" name="tdDistributorList"
					icon="icon-search"
					title="非直供终端" textname="terminalName,erpCode,customerName" inputTextname="terminalName,erpCode,kunnrName" isclear="true"
					width="400" height="400">
			</t:choose>
			<span style="color: red">*</span>
		</div>

		<div class="form">
			<label class="Validform_label">非直供名称: </label>
			<input name="disbName" class="inputxt" id="terminalName" readonly="readonly" datatype="*2-30">
			<span style="color: red">*</span>
		</div>

		<div class="form">
			<label class="Validform_label">上级客户编码: </label>
			<input name="kunnr" class="inputxt"   id="erpCode" readonly="readonly" datatype="s2-30">
			<span style="color: red">*</span>


		</div>


		<div class="form">
			<label class="Validform_label">上级客户名称: </label>
			<input name="kunnrName" id="customerName" class="inputxt" readonly="readonly" datatype="*"  >
			<span style="color: red">*</span>

		</div>


		<div class="form">
			<label class="Validform_label">目标单位: </label>
			<select name="targetUnit">
				<option value=0
						<c:if test="${tdRebateTargetVo.targetUnit ==0}">selected</c:if>
				>箱
				</option>
				<option value=1
						<c:if test="${tdRebateTargetVo.targetUnit >=1}">selected</c:if>
				>万元
				</option>
			</select>
			<span style="color: red">*</span>
		</div>

		<div class="form">
			<label class="Validform_label">目标类型: </label>
			<select name="targetType" id="targetType">
				<option value=0
						<c:if test="${tdRebateTargetVo.targetType ==0}">selected</c:if>
				>季度目标
				</option>
				<option value=1
						<c:if test="${tdRebateTargetVo.targetType >=1}">selected</c:if>
				>月度目标
				</option>
			</select>
			<span style="color: red">*</span>
		</div>

		<div class="form" id="startDateDiv">
			<label class="Validform_label">开始日期:</label>
			<input onclick="checkDateStart('endDateInput','ymd')"
				   class="inputxt Wdate" readonly="readonly"
				   name="startDate" id="startDateInput" value="${tdRebateTargetVo.startDate}" datatype="*"/>
			<span style="color: red">*</span>
		</div>

		<div class="form" id="endDateDiv">
			<label class="Validform_label">结束日期:</label>
			<input onclick="checkDateEnd('startDateInput','ymd')"
				   class="inputxt Wdate" readonly="readonly"
				   name="endDate" id="endDateInput" value="${tdRebateTargetVo.endDate}" datatype="*"/>
			<span style="color: red">*</span>
		</div>



		<div class="form">
				<span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
						   onclick="openSeries()">添加目标对象</a>
						<a iconcls="icon-remove" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
						   onclick="seriesDelete()">删除</a>
					</div>
					<div class="seriesArea" id="seriesDiv"></div>
				</span>
		</div>

	</div>


	<input type="hidden" name="tableJson">

		</div>

	</t:formvalid>

</div>
<div data-options="region:'center'">
	<table id="dg"></table>


</div>
</body>


<!--模板引擎laytpl-->
<script src="resources/laytpl/laytpl.js"></script>
<script src="resources/tools/Map.js"></script>
<!--开始日期与结束日期校验js-->
<script src="resources/shop/js/common.js"></script>



<script>
	//系列
	var seriesMap = new Map();

	//打开选择系列
	function openSeries() {
		$.dialog({
			title : "添加系列",
			content : "url:tdMaterialController.do?tmProductSeriesChoose",
			lock : true,
			width : "500",
			height : "400",
			zIndex : 10000,
			parent : windowapi,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rows = iframe.$("#prdList").datagrid('getSelections');
				var json = [];
				$(rows).each(function (i, o) {
					var item = {
						seriesId:o.id,
						seriesCode: o.productCode,
						seriesName: o.text,
					};
					json.push(item);
					//往表格中添加一行
					var targetType= $('#targetType').children('option:selected').val();
					if(targetType=0){
						$("#dg").datagrid('appendRow',{
							targetObject:o.productCode,
							targetObjectName:o.text,
							quarterOne:0,
							quarterTwo:0,
							quarterThree:0,
							quarterFour:0
						});

					}else{
						$("#dg").datagrid('appendRow',{
							targetObject:o.productCode,
							targetObjectName:o.text,
							plJan:0,
							plJan:0,
							plFeb:0,
							plMar:0,
							plApr:0,
							plMay:0,
							plJun:0,
							plJul:0,
							plAug:0,
							plSep:0,
							plOct:0,
							plNov:0,
							plDec:0
						});

					}

					//开始编辑
					var rowDatas=$("#dg").datagrid('getRows');
					$("#dg").datagrid('beginEdit',rowDatas.length-1);
				});


			},
			cancelVal : '关闭',
			cancel : true
		});
	}



	$(function () {
		initQuarterTable();
		$('#targetType').change(function(){
			//alert($(this).children('option:selected').val());
			var p1=$(this).children('option:selected').val();//这就是selected的值
			if(p1==0){
				initQuarterTable();
			}else{
				initMonthtable();
			}
		})


	});


	function initQuarterTable(){
		//初始化datagrid

		$('#dg').datagrid({
			url:'',
			fit:true,
			columns:[[
				{field:'targetObject',title:'目标对象编码',hidden:true},
				{field:'targetObjectName',title:'目标对象',width:120},
				{field:'quarterOne',title:'第一季度',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'quarterTwo',title:'第二季度',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'quarterThree',title:'第三季度',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'quarterFour',title:'第四季度',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'quarterFour',title:'汇总',editor:{type:'numberbox',options:{min:0}},width:70}
			]]
		});
		deleteAllRows();

	}


	function initMonthtable(){

		$('#dg').datagrid({
			url:'',
			fit:true,
			columns:[[
				{field:'targetObject',title:'目标对象编码',hidden:true},
				{field:'targetObjectName',title:'目标对象',width:120},
				{field:'plJan',title:'一月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plFeb',title:'二月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plMar',title:'三月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plApr',title:'四月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plMay',title:'五月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plJun',title:'六月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plJul',title:'七月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plAug',title:'八月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plSep',title:'九月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plOct',title:'十月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plNov',title:'十一月',editor:{type:'numberbox',options:{min:0}},width:70},
				{field:'plDec',title:'十二月',editor:{type:'numberbox',options:{min:0}},width:70}
			]]
		});
		deleteAllRows();

	}


      /**
	   * 获取表格数据
	   * */
	function fetchTableValues() {
		var tableData=[];
       var allRows=$('#dg').datagrid('getRows');
		if (allRows.length > 0) {
			for ( var i = 0; i < allRows.length; i++) {
				$("#dg").datagrid('endEdit',i);
				var targetType= $('#targetType').children('option:selected').val();
				var tableItem;
				if(targetType==0){
					 tableItem={
						targetObject:allRows[i].targetObject,
						targetObjectName:allRows[i].targetObjectName,
						quarterOne:allRows[i].quarterOne,
						quarterTwo:allRows[i].quarterTwo,
						quarterThree:allRows[i].quarterThree,
						quarterFour:allRows[i].quarterFour
					};

				}else{
					 tableItem={
						targetObject:allRows[i].targetObject,
						targetObjectName:allRows[i].targetObjectName,
						 plJan:allRows[i].plJan,
						 plFeb:allRows[i].plFeb,
						 plMar:allRows[i].plMar,
						 plApr:allRows[i].plApr,
						 plMay:allRows[i].plMay,
						 plJun:allRows[i].plJun,
						 plJul:allRows[i].plJul,
						 plAug:allRows[i].plAug,
						 plSep:allRows[i].plSep,
						 plOct:allRows[i].plOct,
						 plNov:allRows[i].plNov,
						 plDec:allRows[i].plDec
					};
					console.log("plJan:"+tableItem.plJan);

				}


				console.log("targetObject:"+tableItem.targetObject);
				console.log("targetObjectName:"+tableItem.targetObjectName);
				tableData.push(tableItem);
			}

			$("input[name='tableJson']").val(JSON.stringify(tableData));
			console.log(tableData)
			console.log(JSON.stringify(tableData));
		}
	}

	function deleteAllRows() {
		var rows = $('#dg').datagrid("getRows");
		if (rows.length > 0){
			for (var i = rows.length - 1; i >= 0; i--) {
				var index = $('#dg').datagrid('getRowIndex',rows[i]);
				$('#dg').datagrid('deleteRow', index);
			}

		}
	}

    /**
	 * 删除表格数据
	 */
	function seriesDelete(){
		var rows = $('#dg').datagrid("getSelections");
		for (var i = rows.length - 1; i >= 0; i--) {
			var index = $('#dg').datagrid('getRowIndex',rows[i]);
			$('#dg').datagrid('deleteRow', index);
		}
	}



</script>
</html>