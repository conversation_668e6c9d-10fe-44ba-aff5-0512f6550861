<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="west" style="width:420px;">
		<t:formvalid formid="formobj" layout="div" dialog="true" action="ttFridgeMainController.do?saveFridge" beforeSubmit="convertDetailToForm" refresh="true">
			<input type="hidden" name="id" value="${vo.id }">
			<input type="hidden" name="enableStatus" value="0">
			<input type="hidden" name="fridgeDetailJson" id="fridgeDetailJson">
			<input id="orgId" type="hidden" value="${orgId}">

			<!-- 是否已经返利 0是 1否 -->
			<input id="isRebate" type="hidden" value="${isRebate}">
			<!-- 返利金额 -->
			<input id="ra" type="hidden" value="${rebateAmount}">

			<input id="maxYearMonth" name="maxYearMonth" type="hidden" value="${maxYearMonth}">

			<input id="beginMaxMonth" type="hidden" value="${beginMaxMonth}">

			<input id="isDetail" type="hidden" value="${isDetail}">




			<div class="form">
				<label class="Validform_label">业务归属: </label>
				<t:dictSelect id="type" field="type" type="select" defaultVal="${vo.type}"
							  typeGroupCode="fridge_ascription" dataType="*" extend="onchange='changeType();'">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜编号: </label>
				<input name="fridgeCode" id="fridgeCode" datatype="/^[0-9a-zA-Z_]{2,30}$/" class="inputxt" value="${vo.fridgeCode}"
					   ajaxUrl="ttFridgeMainController.do?validFridgeCode&id=${vo.id}" <c:if test="${opType == 1}">readonly="readonly"</c:if>/>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜型号: </label>
				<input name="fridgeModel" id="fridgeModel" datatype="*"  class="inputxt" value="${vo.fridgeModel}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜规格: </label>
				<input name="fridgeStandard" id="fridgeStandard" class="inputxt" value="${vo.fridgeStandard}" />
			</div>

			<div class="form">
				<label class="Validform_label">冰柜品牌: </label>
				<t:dictSelect id="fridgeSupplier" field="fridgeSupplier" type="select" defaultVal="${vo.fridgeSupplier}"
							  typeGroupCode="fridge_brand" dataType="*">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">冰柜类型: </label>
				<t:dictSelect id="fridgeType" field="fridgeType" type="select" defaultVal="${vo.fridgeType}"
							  typeGroupCode="fridge_type" dataType="*">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">所属客户: </label>
				<input type="hidden" name="customerCode" id="customerCode" datatype="*" title="客户"  value="${vo.customerCode}"/>
				<input type="hidden" name="orgCode" name ="orgCode" id="orgCode" datatype="*"  value="${vo.orgCode}"/>
				<input type="hidden" name="orgName" id="orgName" datatype="*"  value="${vo.orgName}"/>

				<b:search hiddenFields="customerName,customerCode,orgCode,orgName" inputTextName="customerName,customerCode,orgCode,orgName" value="${vo.customerName}"
						  dialogUrl="tmCommonMdmController.do?findCustomerBySearchList"
						  url="tmCommonMdmController.do?goCustomerSearch&orgId=2A4ECC746B073039E050F10AC8242D4C" isClear="false"  fun="cleanTerminal"
						  inputTextId="customerName" name="ttCustomerList" type="3" title="选择客户" needQuery="false"></b:search>

				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">客户合作性质: </label>
				<input name="nature" id="nature"
					   onkeydown="keyCode(event)" onkeyup="verifyInput(this)" onblur="sumCost()"  class="inputxt" value="${vo.nature}" />
			</div>


			<div class="form">
				<label class="Validform_label">冰柜总价值: </label>
				<input name="fridgeWorth" id="fridgeWorth"
					   onkeydown="keyCode(event)" onkeyup="verifyInput(this)" onblur="sumCost()"  class="inputxt" value="${vo.fridgeWorth}" />
			</div>

			<!-- 必填 -->
			<div class="form">
				<label class="Validform_label">购买类型: </label>
				<t:dictSelect id="purchaseType" field="purchaseType" type="select" defaultVal="${vo.purchaseType}"
							  typeGroupCode="purchase_type" dataType="*"  extend="onchange='changePurchaseType();'">
				</t:dictSelect>
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">返款总额: </label>
				<input name="totalAmount" id="totalAmount" datatype="/^-?[1-9]+(\.\d+)?$|^-?0(\.\d+)?$|^-?[1-9]+[0-9]*(\.\d+)?$/"
					   onkeydown="keyCode(event)" onkeyup="verifyInput(this)" onblur="sumCost()" class="inputxt" value="${vo.totalAmount}" />
				<span  id="totalAmountRequired"  style="color: red;">*</span>
				<span id="ta" class="Validform_checktip"></span>
			</div>

			<div class="form">
				<label class="Validform_label">购买时间: </label>
				<input name="purchaseDate" id="purchaseDate" datatype="*" readonly="readonly" class="inputxt" value="${vo.purchaseDate}" />
				<span id="purchaseRequired" style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">投放时间: </label>
				<input name="deliveryDate" id="deliveryDate"  readonly="readonly" class="inputxt" value="${vo.deliveryDate}" />
			</div>

			<div class="form">
				<label class="Validform_label">责任人: </label>
				<input type="hidden" name="userCode" id="userCode"  value="${vo.userCode}"/>
				<b:search hiddenFields="fullName,userName" inputTextName="userName,userCode" value="${vo.userName}"
						  dialogUrl="tmCommonMdmController.do?findUserBySearchList"
						  url="tmCommonMdmController.do?goUserSearch&orgId=2A4ECC746B073039E050F10AC8242D4C"
						  inputTextId="userName" isClear="false" name="tmUserList" type="1" title="人员" needQuery="false"></b:search>
			</div>


			<div class="form">
				<label class="Validform_label">开始日期: </label>
				<input name="beginRebateDate" id="beginRebateDate" datatype="*" readonly="readonly" class="Wdate" style="width: 150px;"
					   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'%y-%M-{%d}',maxDate:'#F{$dp.$D(\'endRebateDate\')}',onpicked:function(){$('.Wdate').blur();initDetail();}})"
					 value="${vo.beginRebateDate}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">结束日期: </label>
				<input name="endRebateDate" id="endRebateDate" datatype="*" class="Wdate" style="width: 150px;"
					   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginRebateDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();initDetail();}})"
					    readonly="readonly" class="inputxt" value="${vo.endRebateDate}" />
				<span style="color: red;">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">所属门店: </label>
				<input type="hidden" name="terminalCode" id="terminalCode" value="${vo.terminalCode}"/>
				<b:search hiddenFields="terminalName,terminalCode" inputTextName="terminalName,terminalCode" value="${vo.terminalName}"
						  url="tmCommonMdmController.do?goTerminalSearch" dialogUrl="tmCommonMdmController.do?findTerminalBySearchList"
						  inputTextId="terminalName" isClear="false"  parameter="customerCode" name="ttTerminalList" type="4" title="门店"></b:search>
			</div>

			<div class="form">
				<label class="Validform_label">备注:</label>
				<textarea  style="border:1px solid #6C94C2;width:150px;height:100px;resize:none;" name="remark" id="remark">${vo.remark}</textarea>
			</div>
		</t:formvalid>
	</div>
	<div region="center">
		<div class="panel datagrid" id="costDg">
			<div class="datagrid-wrap panel-body">
				<div class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<c:if test="${ optType eq detail}">
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost" onclick="avgCost()">费用分摊</a>
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-reset" onclick="resetCost()">重置</a>
							</c:if>
						</span>

						<span style="float:right">
							<span style="line-height:30px;margin-right:50px;" >
								返利总额：<span style="color:red;" id="rebateAmount">${vo.totalAmount}</span>
							可分摊返利金额：<span style="color:red;" id="overRebate">0</span>
							</span>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">

					</div>
				</div>
				<div class="datagrid-view">
					<table class="actTable" id="actTable">
						<thead>
						<tr>
							<td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
							<td>序号</td>
							<td>年月</td>
							<td>返利金额</td>
							<td style="display:none"></td>
						</tr>
						</thead>
						<tbody>
							<c:if test="${(vo.ttFridgeDetailVoList)!= null && fn:length(vo.ttFridgeDetailVoList) > 0}">
								<c:forEach items="${vo.ttFridgeDetailVoList}" var="voList" varStatus="idxStatus">
									<tr
											<c:choose>
												<c:when test="${voList.rebateStatus == 0 }">notDel="1"</c:when>
												<c:otherwise>notDel="0"</c:otherwise>
											</c:choose>
									>
										<td>
											<input type="checkbox" name="actTableId" />
										</td>
										<td name="numbers">${idxStatus.index+1}</td>
										<td>${voList.yearMonth}</td>
										<td>
											<input  name="rebateAmount"   onkeydown="keyCode(event)"
													onkeyup="verifyInputRebate(this)" value="${voList.rebateAmount}"
													onblur="deductionAmount();" placeholder="请填写金额" <c:if test="${voList.rebateStatus == 0}">disabled="disabled"</c:if> />
										</td>
										<td style="display:none">${voList.id}</td>
									</tr>
								</c:forEach>
							</c:if>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
    $(function(){
        $("input[name='endRebateDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='beginRebateDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='deliveryDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='purchaseDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        changeType();


        $('#fridgeWorth').numberbox({
            min:0,
            precision:2
        });

        //如果该条冰柜已经返利:1有返利记录的月份不可修改
		//				   2时间一定要包含有返利记录及其之前的时间，返利总额要大于等于已返利金额（开始时间到最新返利记录）
		//				   3如果修改客户，需要从之前的返利结束，生成新的返利记录，进行返利

		var isRebate = $("#isRebate").val();
		if(isRebate != '' && isRebate == 0){//已经返利s
            removeClickDateCssAndEvent('beginRebateDate');//取消开始时间的选择
		}

		var isDetail = $("#isDetail").val();
		if(isDetail != '' && isDetail == 0){//没有明细
            initDetail();
		}

		$("#totalAmount").change(function(){
			var amount = $("#ra").val();
			var totalAmount = $("#totalAmount").val();
			if(Number(amount) > Number(totalAmount)){
			    tip("返款总额不能小于冻结金额","error");
                $("#totalAmount").val("");
			}
		});
    });
    function removeClickDateCssAndEvent(id){
        $("#" + id).attr("onclick",'').unbind("click").removeClass("Wdate");
    }
    function validateMaxYearMonth(endDate){
		var maxYearMonth = $("#maxYearMonth").val();
		endDate = new Date(endDate).format("yyyy-MM");
		if(endDate <= maxYearMonth){
		    tip("结束日期不能小于等于:"+maxYearMonth,"error");
		    $("#endRebateDate").val("");
		    return false;
		}
		return true;
	}

    function changeType(){
        var type=$("#type").val();
        if(Number(type)==1){
            $("#purchaseType option[value=10]").attr("selected", "selected");
            $("#costDg").hide();
            $("#purchaseDate").removeAttr("datatype");
            $("#purchaseDate").removeAttr("nullmsg");
            $("#totalAmount").removeAttr("datatype");
            $("#totalAmount").removeAttr("nullmsg");
            $("#purchaseRequired").hide();
            $("#totalAmountRequired").hide();
		}else{
            $("#costDg").show();
            $("#purchaseDate").attr("datatype","*")
            $("#totalAmount").attr("datatype","*")
            $("#purchaseRequired").show()
            $("#totalAmountRequired").show();
		}
	}

	//选择客户后清空门店
	function cleanTerminal(obj){
		$("#terminalCode").val("");
		$('#terminalName').searchbox('setValue', '');
        $.ajax({
            url: "ttFridgeMainController.do?getNatureByCustomerCode",
            data:{customerCode : obj.customerCode},
            type: "POST",
            dataType:'json',
            success:function(data){
                $("#nature").val(data.extChar10);
            },
            error:function(er){
                alert("获取客户合作性质异常");
            }
        });
    }

    function changePurchaseType(){
        var type=$("#type").val();
        var purchaseType=$("#purchaseType").val();
        if(Number(type)==1&&Number(purchaseType)!=10){
			newTip("业务归属为资产管理购买类型必须只能选固定资产");
            $("#purchaseType option[value=10]").attr("selected", "selected");
        }
	}

    //提交前把列表信息封装到form
    function convertDetailToForm(){
        var type=$("#type").val();
        var ra = Number($("#ra").val());
        if(Number(type)==1){
            return ;
		}
        var rows=[];
        var tr= $(".actTable tbody tr");
        var sumAmount=0;
        tr.each(function(trindex,tritem) {//遍历每一行
            var notDel = $(this).attr("notDel");
            if(notDel != "1"){
                var tdArr = $(this).children();
                var yearMonth = tdArr.eq(2).html();//月份
                var rebateAmount =tdArr.eq(3).find("input").eq(0).val();//金额
                sumAmount=add(sumAmount,Number(rebateAmount));
                var id = tdArr.eq(4).html();//月份
                //组装单据
                var obj = {
                    'id': id,
                    'yearMonth': yearMonth,
                    'rebateAmount': rebateAmount
                };
                rows.push(obj);
            }
        });
        var total=$("#totalAmount").val();
        if(Number(sumAmount)!=(Number(total)-ra)){
			newTip("已分摊金额与返款总额不一致，请检查");
			return false;
		}
        var jsonStr=JSON.stringify(rows);
        $("#fridgeDetailJson").val(jsonStr);
	}

	function initDetail(){
        var beginDate=$("#beginRebateDate").val();
        var endDate=$("#endRebateDate").val();
        if(beginDate==null||beginDate==''||endDate==null||endDate==''){
            return;
        }
        //校验可以选择的最小月份
		var result = validateMaxYearMonth(endDate);
		if(!result){
		    return false;
		}
        /*$(".actTable tbody").html("");*/
        var trs = $(".actTable tbody tr");
        var num = 0;
        $.each(trs,function(){
            var notDel = $(this).attr("notDel");
            if(notDel != "1"){
                $(this).remove();
			}else{
                num++;
			}
		});
        var isRebate = $("#isRebate").val();
        if(isRebate != '' && isRebate == 0){//已经返利
			beginDate = $("#beginMaxMonth").val();
        }
		var yearMonths=getMonthBetween(beginDate,endDate);
		if(yearMonths!=null&&yearMonths.length>0){
            $.each(yearMonths,function(k,v){
                var i=Number(num)+1;
                num++;
                var str='<tr notDel="0">'
                    +'<td><input type="checkbox" name="actTableId" /></td>'
                    +'<td>'+i+'</td>'
                    +'<td>'+v+'</td>'
                    +'<td><input  name="rebateAmount" onkeydown="keyCode(event)" onkeyup="verifyInputRebate(this)" value="0"  onblur="deductionAmount();" placeholder="请填写金额"/></td>'
                    +'<td style="display:none"></td>'
                    +'</tr>';
                $(".actTable tbody").append(str);
            });
		}
	}

	//获取两月份之间的所有年月
    function getMonthBetween(start,end){
        var result = [];
        var s = start.split("-");
        var e = end.split("-");
        var min = new Date();
        var max = new Date();
        min.setFullYear(s[0],Number(s[1])-1);
        max.setFullYear(e[0],Number(e[1])-1);
        var curr = min;
        while(curr <= max){
            var month = curr.getMonth();//0~11
            result.push(curr.getFullYear()+"-"+( (month)<9?("0"+(month+1)):month+1));
            curr.setMonth(month+1);
        }
        return result;
    }

    function resetCost(){
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
			var notDel = $(this).attr("notDel");
			if(notDel != "1"){
                var tdArr = $(this).children();
                tdArr.eq(3).find("input").eq(0).val(0);//金额
            }
        });
        deductionAmount();
	}

    function sumCost(){
        var totalAmount=$("#totalAmount").val();
        var fridgeWorth = $("#fridgeWorth").val();
        if(fridgeWorth != ''){
            if(Number(totalAmount) > Number(fridgeWorth)){
                tip("返款总额不能大于冰柜总价值","error");
                $("#totalAmount").val("");
            }
        }
        $("#rebateAmount").html(totalAmount);
    }
    /**
     * 均摊
     */
    function avgCost() {
        var sumAmount = $("#totalAmount").val();//填写总金额
		var ra = Number($("#ra").val());//返利金额
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            return;
        }
        var amountSize = 0;
        var rows = $(".actTable tbody tr");
        $.each(rows,function(){
			var notDel = $(this).attr("notDel");
			if(notDel != "1"){amountSize++;}
		});
        if (amountSize == 0) {return;}

        var waitingAssgin = subtract(sumAmount,ra);
        var avgAmount = (waitingAssgin / amountSize).toFixed(2);
        var totalAmount=0;
        $.each(rows, function(i, row) {
            var notDel = $(this).attr("notDel");
            if(notDel != "1"){
                totalAmount=add(totalAmount,avgAmount);
                $(row).find("input[name='rebateAmount']").val(avgAmount);
            }
        });
        if(Number(totalAmount)!=Number(waitingAssgin)){
			var num=subtract(waitingAssgin,totalAmount);
            $(rows[rows.length -1]).find("input[name='rebateAmount']").val(add(avgAmount,num));
		}
        deductionAmount();
    }
    //扣减总金额
    function deductionAmount(){
        var sumAmount = 0;
        var tolAmount = $("#totalAmount").val();
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            var rebateAmount =tdArr.eq(3).find("input").eq(0).val();//金额
            sumAmount=add(Number(rebateAmount),sumAmount);
        });
        sumAmount = subtract(tolAmount, sumAmount);
        $("#overRebate").html(sumAmount);
    }

    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }

    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }
    /**
     * 页面输入验证
     */
    function verifyInputRebate(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g, "."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); // 只能输入两个小数
        var sumAmount = 0;
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            var rebateAmount =tdArr.eq(3).find("input").eq(0).val();//金额
            sumAmount=add(Number(rebateAmount),sumAmount);
        });
        var totalAmount=$("#totalAmount").val();
        if(Number(totalAmount)<sumAmount){
            newTip("返利总金额不能大于返款总额");
            obj.value =0;
        }
    }
	/*
	 * 高精减法函数
	 */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精度加法函数
     */
    function add(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();
        try {
            m += s1.split(".")[1].length;
        }
        catch (e) {
        }
        try {
            m += s2.split(".")[1].length;
        }
        catch (e) {
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;
        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch(e) {
            r2 = 0;
        }
        // 动态求出哪一个位数最多，再得出10的n次幂
        return Math.pow(10, Math.max(r1, r2));
    }
</script>