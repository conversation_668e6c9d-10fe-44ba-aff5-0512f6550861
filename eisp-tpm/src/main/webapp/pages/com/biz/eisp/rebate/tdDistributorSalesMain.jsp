<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center" style="padding:1px;">
		<t:datagrid name="tdDistributorSalesList"    fitColumns="false" title="非直供进货数据" actionUrl="tdDistributorSalesController.do?findTdDistributorSalesList"
					idField="id" fit="true" queryMode="group"  singleSelect="false" pageSize="30">
			<t:dgCol title="主键"  field="id"  hidden="true"   ></t:dgCol>
			<t:dgCol title="非直供编码"  field="disbCode"     query="true"  ></t:dgCol>
			<t:dgCol title="非直供名称"  field="disbName" query="true"></t:dgCol>
			<t:dgCol title="上级客户编码"  field="kunnr"   ></t:dgCol>
			<t:dgCol title="上级客户名称"  field="kunnrName"   ></t:dgCol>
			<t:dgCol title="产品编码"  field="productCode" query="true" ></t:dgCol>
			<t:dgCol title="产品名称"  field="productName"  query="true"></t:dgCol>
			<t:dgCol title="进货数量"  field="buyNum"  ></t:dgCol>
			<t:dgCol title="进货日期"  field="buyDate"  formatter="yyyy-MM-dd"  ></t:dgCol>
			<t:dgToolBar title="新增" operationCode="add" icon="icon-add" url="tdDistributorSalesController.do?goTdDistributorSalesForm" funname="add" ></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="tdDistributorSalesController.do?goTdDistributorSalesForm" funname="update" ></t:dgToolBar>
			<%--<t:dgToolBar title="批量删除"  icon="icon-remove" url="tdDistributorSalesController.do?deleteTdDistributorSales" funname="deleteALLSelect"></t:dgToolBar>--%>
			<t:dgToolBar title="查看" operationCode="search" icon="icon-search" url="tdDistributorSalesController.do?goTdDistributorSalesForm" funname="detail"></t:dgToolBar>
			<t:dgToolBar title="导入" operationCode="dataIn" icon="icon-dataIn" onclick="importDataByXml({impName:'tdDistributorSales', gridName:'tdDistributorSalesList'})" ></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="tdDistributorSalesController.do?exportXls" funname="excelExport" ></t:dgToolBar>
		</t:datagrid>
	</div>
</div>

<script>
	$(function () {
		$("input[name*='Date']").addClass("Wdate").css({
			'height': '20px',
			'width': '150px'
		}).removeAttr("onfocus").on("focus", function () {
			WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'});
		});
	});
</script>
