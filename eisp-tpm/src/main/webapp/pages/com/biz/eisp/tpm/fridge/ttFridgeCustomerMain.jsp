<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttFridgeMainList" fitColumns="true" title="经销商冰柜一览表1"
					actionUrl="ttFridgeMainController.do?findTtFridgeCustomerList" queryMode="group" pagination="true" idField="customerCode" fit="true">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="销售部" field="saleOrgName" query="true"></t:dgCol>
			<t:dgCol title="所属组织" field="orgCode" hidden="true" query="true"></t:dgCol>
			<t:dgCol title="所属组织" field="orgName"></t:dgCol>
			<t:dgCol title="客户编号" field="customerCode" query="true"></t:dgCol>
			<t:dgCol title="客户名称" field="customerName" query="true"></t:dgCol>
			<t:dgCol title="冰柜类型" field="fridgeType" query="true" dictionary="fridge_type"></t:dgCol>
			<t:dgCol title="冰柜业务归属" field="type" query="true" dictionary="fridge_ascription"></t:dgCol>
			<t:dgCol title="数量(台)" field="fridgeNumber" ></t:dgCol>
			<t:dgCol title="累计应返金额" field="totalBackAmount" formatterjs="numExtend"></t:dgCol>
			<t:dgCol title="累计已申请返利金额" field="totalBackedAmount" formatterjs="numExtend"></t:dgCol>
			<t:dgCol title="累计可申请返利金额" field="totalWaiteAmount" formatterjs="numExtend"></t:dgCol>

			<t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttFridgeMainController.do?exportXlsTtFridgeCustomerList" funname="excelExport"></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
    $(function(){
        $("input[name='orgCode']").combotree({
            width : 200,
            url : 'tmOrgController.do?getParentOrg&pid=${orgId}'
        });
    });
</script>
