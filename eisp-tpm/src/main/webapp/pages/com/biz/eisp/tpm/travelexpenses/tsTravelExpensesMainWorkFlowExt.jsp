<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="tsTravelExpensesList" title="差旅报销"  actionUrl="tsTravelExpensesWebWorkFlowController.do?findTsTravelExpensesList&flagKey=${flagKey}"
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="标题" field="title" hidden="false" ></t:dgCol>
			<t:dgCol title="申请日期" field="applyDate" hidden="false" ></t:dgCol>
			<t:dgCol title="申请人" field="applyName" hidden="false" ></t:dgCol>
			<t:dgCol title="申请人部门" field="orgName" hidden="false" ></t:dgCol>
			<t:dgCol title="报销金额合计" field="submitAmount" hidden="false" ></t:dgCol>
			<t:dgCol title="实际报销金额" field="realAmount" hidden="false" ></t:dgCol>
			<t:dgCol title="预算科目" field="yskm" hidden="false" ></t:dgCol>
			<t:dgCol title="当前审批人" field="approverName" hidden="false" ></t:dgCol>
			<t:dgCol title="最近更新人" field="updateName" hidden="false" ></t:dgCol>
			<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd" hidden="false" ></t:dgCol>
			<t:dgToolBar title="填报实际报销金额"  icon="icon-edit" url="tsTravelExpensesWebWorkFlowController.do?goTravelExpensesFormWorkFlow&flagKey=${flagKey}&optype=2" width="1000" height="700" funname="updateTravelExpenses"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

    //修改
    function updateTravelExpenses(title, url, grid, width, height){
        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var rowData = rowDatas[0];
        var id = rowData.id;
//        if (checkThisDataCouldToUpdate(id)){
            url += "&id=" + id;
            openWindOwn(title, url);
//        }
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }

    function openWindOwn(title, url){
        createwindowExt(title,url,"1000","700",{
            lock : true,
            parent : windowapi,
            zIndex:12000,
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }

</script>
