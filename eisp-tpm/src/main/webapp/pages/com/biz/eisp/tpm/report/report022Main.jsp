<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report022List" fitColumns="false" title="费用池监控表"
                    pagination="true" autoLoadData="true" actionUrl="report022Controller.do?findReportList" idField="id" fit="true">

            <t:dgCol title="基础信息"  field=""  colspan="3"></t:dgCol>
            <t:dgCol title="费用池折扣明细"  field=""  colspan="4"></t:dgCol>
            <t:dgCol title="费用池折扣剩余" field=""   colspan="2"></t:dgCol>
            <t:dgCol title="预估折扣" field=""   colspan="3"></t:dgCol>

            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth"  width="120" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="事业部" field="sybOrgName"  width="200" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="支付方式" field="payType" replace="货补_1,折扣_2" width="100" query = "true" sortable="false"></t:dgCol>


            <t:dgCol title="上月剩余金额" field="untilLastMonthRemainAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="本月已导入金额" field="currentMonthImportAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="本月已使用金额" field="currentMonthUseAmount" width="100" sortable="false"></t:dgCol>
            <t:dgCol title="导出费用转其他形式核销" field="currentMonthOtherUseAmount" width="150" sortable="false"></t:dgCol>



            <t:dgCol title="截止当前剩余" field="untilCurrentMonthRemainAmount" width="150" sortable="false" ></t:dgCol>
            <t:dgCol title="预计剩余折扣使用天数" field="expectRemainDiscountUseDays" width="150" sortable="false"></t:dgCol>


            <t:dgCol title="预估本月费用池使用金额" field="expectCurrentMonthCostPoolUseAmount" width="150" sortable="false" ></t:dgCol>
            <t:dgCol title="预估折扣率" field="expectDiscountRate" width="100" sortable="false" ></t:dgCol>
            <t:dgCol title="日均折扣金额" field="perDayDiscountAmount" width="100" sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report022Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report022Listtb_r").find("input[name='yearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'120px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM',maxDate:'%y-%M'});});
    });
    function report022Listsearch() {
        var yearMonth = $("#report022Listtb_r").find("input[name='yearMonth']").val();


        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report022List").datagrid('options').queryParams;
        $("#report022Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report022List").datagrid({url:'report022Controller.do?findReportList'});
    }
</script>
