<%--asdasdasdasdasdasdasd--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<div style="clear:both; width: 1080px;height: 150px;">

    <t:datagrid name="advList" title=""  actionUrl="ttActOutUploadController.do?findAdvInfo&actCode=${actCode}"
                idField="id" fit="true"  fitColumns="false"  pagination="false" >
        <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="门头广告编号" field="adCode"  width="120" align="center"></t:dgCol>


        <t:dgCol title="材料" field="materialName"  width="130" align="center"></t:dgCol>
        <t:dgCol title="长(m)" field="mlength"  width="80" align="center"></t:dgCol>
        <t:dgCol title="高(m)" field="mwidth"  width="80" align="center"></t:dgCol>
        <t:dgCol title="数量" field="nums"  width="50" align="center"></t:dgCol>
        <t:dgCol title="面积(㎡)" field="mspace"   width="100"  align="center"></t:dgCol>
        <t:dgCol title="单价(元/㎡)" field="money" formatterjs="setMoney"  width="100"  align="center"></t:dgCol>
        <t:dgCol title="总金额" field="totalAmount" formatterjs="setMoney"  width="100" align="center" ></t:dgCol>

        <t:dgCol title="店铺名称" field="terminalName"  width="195" align="center"></t:dgCol>
        <t:dgCol title="店铺编号" field="terminalCode"  width="73" align="center"></t:dgCol>
    </t:datagrid>

</div>
<script>
    <%--alert(${hint});--%>
    <%--$(function () {--%>
        <%--$('#mjhint', window.parent.document).attr("value",${hint});--%>
    <%--});--%>
    function setMoney(value) {
        if(value==''||value==null){
            return 0.0;
        }else{
            return value;
        }
    }
</script>