<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
</style>

<div id="ttActBusinessPlanMain" class="easyui-layout" fit="true">
	<!-- <div data-options="region:'north',title:'月度计划明细'" style="padding:1px;height:auto;" >
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							
						</span>
						<span style="float:right">
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">
            			<input type="tehiddenxt" name="yearMonth" id="yearMonth" hidden="hidden" />
            			<input type="hidden" name="customerCode" id="customerCode" hidden="hidden" />
					</div>
				</div>
				<div class="datagrid-view">
				</div>
			</div>
		</div>
	</div> -->
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttAddMonthlyPlanDetailList" fitColumns="false" title="月度计划明细" queryMode = "group" idField="id" pagination="true" 
	     actionUrl="ttMonthlyPlanConfirmController.do?findttAddMonthlyPlanDetailList&customerCode=${monthlyPlanVo.customerCode }&yearMonth=${monthlyPlanVo.yearMonth }" onClick="clickCustomerFun">
	        <t:dgCol title="主键" hidden="true" field="id" sortable="false" width="100" ></t:dgCol>
	        <t:dgCol title="客户编码" field="customerCode" sortable="false" width="70" ></t:dgCol>
	        <t:dgCol title="客户名称" field="customerName" sortable="false" width="90" ></t:dgCol>
	        <t:dgCol title="组织编号" field="orgCode" sortable="false" width="90" ></t:dgCol>
	        <t:dgCol title="组织名称" field="orgName" sortable="false" width="70" ></t:dgCol>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="90" ></t:dgCol>
	        <t:dgCol title="产品编号" field="productCode" sortable="false" width="90" ></t:dgCol>
	  		<t:dgCol title="产品名称" field="productName" sortable="false" width="90" ></t:dgCol>
	  		<t:dgCol title="单价（元）" field="price" sortable="false" width="80" ></t:dgCol>
	  		<t:dgCol title="计划销量（EA）" field="planSales" sortable="false" width="90" ></t:dgCol>
	  		<t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false" width="80" ></t:dgCol>
	  		<t:dgCol title="计划销额（元）" field="totalPlanSales" sortable="false" width="90" ></t:dgCol>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
$(document).ready(function(){});
</script>