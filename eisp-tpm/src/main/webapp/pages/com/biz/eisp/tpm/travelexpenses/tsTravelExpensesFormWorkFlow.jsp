<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>差旅报销</title>
<t:base type="jquery,easyui,tools,DatePicker,handsontable"></t:base>
</head>
<div id="travelExpensesApp" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<div class="datagrid-wrap panel-body">
			<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
				<div class="datagrid-toolbar-but">
					<span style="float:left;">
						<a href="#" class="easyui-linkbutton" plain="true" icon="icon-save" onclick="saveAllDataInfo()">保存</a>
					</span>
					<span id="customer_span_temp_info" style="float:right">
					</span>
					<div style="clear:both;float: none;height:0;display: block;"></div>
				</div>
				<div class="datagrid-toolbar-search" id="travelHeadDiv">
					<input type="text" id="id" class="input" hidden="hidden" style="display: none;" value="${vo.id}" />
					<label><span style="padding-left:5px; color: red">*</span>标题</label>
					<input type="text" id="title" class="input" disabled="disabled" readonly="readonly" value="${vo.title}" />
					<label><span style="padding-left:5px; color: red">*</span>出差类型</label>
					<t:dictSelect id="expensesType"  field="expensesType" typeGroupCode="dic_expenses_Type" defaultVal="${vo.expensesType}" ></t:dictSelect>
					<%--<input type="text" id="" class="input" value=""/>--%>
					<label><span style="padding-left:5px; color: red">*</span>费用预算余额</label>
					<input type="text" id="budgetAmount" class="input" style="width:70px;background-color: #f5f5f5" disabled="disabled" readonly="readonly" value="${vo.budgetAmount}" />
					<label><span style="padding-left:5px; color: red">*</span>报销申请金额（元）：</label>
					<input type="text" id="submitAmount" class="input" style="width:70px;background-color: #F5F5F5" disabled="disabled" readonly="readonly" value="${vo.submitAmount}" />
					<label><span style="padding-left:5px; color: red">*</span>实际报销金额（元）：</label>
					<input type="text" id="realAmount" class="input" style="width:70px;background-color: #F5F5F5" disabled="disabled" readonly="readonly" value="${vo.realAmount}" />
					<div style="padding-top: 5px;" >
						<label><span style="padding-left:5px; color: red">*</span>报销事由</label><br>
						<textarea style="padding-left:5px;" id="reason" cols="150" rows="5" disabled="disabled" >${vo.reason}</textarea>
					</div>
					<br>
					<hr >
					<div style="padding-top: 5px;" >
						<label><span style="padding-left:5px; color: red">*</span>报销明细：</label>
					</div>
				</div>
			</div>
			<div class="datagrid-view" style="width: 100% ;height: 400px; overflow: auto;">
				<div id="example"></div>
			</div>
			<div class="datagrid-footer" style="width: 100% ;height: 100px; overflow: auto;">
				<label>附件</label>
			</div>
		</div>
	</div>
</div>
<div id="model" hidden="hidden" >
	<table>
		<td title="id" file="id" type="text" readonly="true" hiddenColumn="true" ></td>
		<td title="起始日期" file="startDate" type="date" readonly="true" dateFormatter="YYYY-MM-DD" ></td>
		<td title="到达日期" file="endDate" type="date" readonly="true" dateFormatter="YYYY-MM-DD" ></td>
		<td title="起始地" file="startAddress" type="text" readonly="true" ></td>
		<td title="目的地" file="endAddress" type="text" readonly="true" ></td>
		<td title="地区类别" file="areaType" type="text" readonly="true" ></td>
		<td title="交通工具" file="vehicle" type="text" readonly="true" ></td>
		<td title="长途交通费" file="longDistanceFee" type="numeric" readonly="true" formatter="0,0.00" ></td>
		<td title="天数" file="travelNum" type="numeric" readonly="true" ></td>
		<td title="住宿费" file="hotelFee" type="numeric" readonly="true" formatter="0,0.00" ></td>
		<td title="餐补费" file="mealFee" type="numeric" readonly="true" formatter="0,0.00" ></td>
		<td title="市内交通费用" file="shortDistanceFee" type="numeric" readonly="true" formatter="0,0.00" ></td>
		<td title="其他费用" file="otherFee" type="numeric" readonly="true" formatter="0,0.00" ></td>
		<td title="票据张数" file="billNum" type="numeric" readonly="true" ></td>
		<td title="报销金额" file="expensesAmount" type="numeric" readonly="true" formatter="0,0.00" ></td>
		<td title="实报金额" file="realAmount" type="numeric" formatter="0,0.00" ></td>
	</table>
</div>
<script>
    var container = document.getElementById('example');
	var hot;
	var globMap = new HashKey();
	var headerArrKey = "headerArr";
	var columnsKey = "columns";
	var hiddenColumnsKey = "hiddenColumns";
	var switchMapKey = "switchMapKey";
	var columnsArrKey = "columnsArr";
	var fileKeyAndTitleMapKey = "fileKeyAndTitleMap";
	var reg_ymd = /^([0-9]{4})(-)([0-9]{1,2})(-)([0-9]{1,2})$/;//验证yyyy-MM-dd格式

	var datasTemp = [];

    $(document).ready(function(){
        initSomePropeTyToDisable();
        initGlobMap();
        initHandsontable();
//        initHandsontableRightMenu();
        loadData();
	});

    function initSomePropeTyToDisable(){
        $('#expensesType').attr("disabled",true);
        $('#expensesType').css("width","50px");
	}

    function getOptype(){
        return '${optype}';
	}

	function getFlagKey(){
        return '${flagKey}';
	}

	function loadData(){
        if(getOptype() == '0'){
            initAddOneRow();
		}else{
            setTimeout(loadDataAll,1000);
		}
	}

	function loadDataAll(){
	    var id = $('#id').val();
	    if(!checkIsNotUndefinedAndNullAndNullValue(id)){
			return ;
		}
		var data = {
			headId : id,
			flagKey : getFlagKey()
		}
		var url = 'tsTravelExpensesWebWorkFlowController.do?findTsTravelExpensesDetailList';
	    var d = ajaxPost(data,url);
	    if(d.success){
			var objs = d.obj;
			if(checkIsNotUndefinedAndNullAndNullValue(objs)){
                hot.loadData(objs);
                hot.updateSettings({maxRows:objs.length});
			}
		}else{
            tip(d.msg);
		}
	}

	//初始化全局map
	function initGlobMap(){
	    //初始化Handsontable所需的表头和对应列组合和其他后续需要字段集合
	    initHeaderArrAndColumnsAndOtherMap();
	    //初始化switch所需的对应的key和value字段
	    initSwitchKeyAssociatedValueFile();
	}

	//初始化switch所需的对应的key和value字段
	function initSwitchKeyAssociatedValueFile(){
		var map = new HashKey();
        map.set("startDate","endDate");
        map.set("endDate","startDate");
        globMap.set(switchMapKey,map);
	}

	//初始化Handsontable所需的表头和对应列组合
	function initHeaderArrAndColumnsAndOtherMap(){
        var headerArr = [];
        var columns = [];
        var hiddenColumns = [];
		var columnsArr = [];
		var fileKeyAndTitleMap = new HashKey();

		var i = 0;
        $('#model td').each(function(){
            var thisObj = $(this);

            var title = getPropertyValueByAttr(thisObj,'title');
            var file = getPropertyValueByAttr(thisObj,'file');
            var type = getPropertyValueByAttr(thisObj,'type');
            var formatter = getPropertyValueByAttr(thisObj,'formatter');
            var dateFormatter = getPropertyValueByAttr(thisObj,'dateFormatter');
            var readOnly = getPropertyValueByAttr(thisObj,'readOnly');
			var hiddenColumn = getPropertyValueByAttr(thisObj,'hiddenColumn');

            headerArr.push(title);

            var columnObj = new Object();
            columnObj.data = file;

            if(!checkIsNotUndefinedAndNullAndNullValue(type)){
                type = 'text';
            }
            columnObj.type = type;

            if(checkIsNotUndefinedAndNullAndNullValue(formatter)){
                var obj = new Object();
                obj.pattern = formatter;
                columnObj.numericFormat = obj;
//                columnObj.numericFormat.pattern = obj;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(dateFormatter)){
                columnObj.dateFormat = dateFormatter;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(readOnly)){
                columnObj.readOnly = readOnly;
            }

            if(checkIsNotUndefinedAndNullAndNullValue(hiddenColumn)){
                hiddenColumns.push(i);
			}else{
                columnsArr.push(file);
                fileKeyAndTitleMap.set(file,title);
			}

            columns.push(columnObj);
            i ++ ;
        });
        globMap.set(headerArrKey,headerArr);
        globMap.set(columnsKey,columns);
        globMap.set(hiddenColumnsKey,hiddenColumns);
        globMap.set(columnsArrKey,columnsArr);
        globMap.set(fileKeyAndTitleMapKey,fileKeyAndTitleMap);
	}

    function initHandsontable(){
	    var headerArr = globMap.get(headerArrKey);
	    var columns = globMap.get(columnsKey);
	    var hiddenColumns = globMap.get(hiddenColumnsKey);
        hot = new Handsontable(container, {
            data : datasTemp,
            rowHeaders : true,
            colHeaders : headerArr,
            columns : columns,
            hiddenColumns : {
                columns: hiddenColumns,
                indicators: true
			},
            afterChange : function(changes, source){afterChangeCallBackFun(changes,source);}
        });
	}

	//添加右键菜单
	function initHandsontableRightMenu(){
        hot.updateSettings({
            contextMenu: {
                callback: function (key, options) {
                    if (key === 'remove_row') {
                        setTimeout(function () {starSumAllExpensesAmount("expensesAmount");},100);
                    }
                },
                items: {
                    "row_above": {
                        name: '向上添加一行',
//                    disabled: function () {
//                        //如果选中的为第一行则失效
//                        return hot.getSelected()[0] === 0;
//                    }
                    },
                    "row_below": { name: '向下添加一行' },
                    "hsep1": "---------",
                    "remove_row": {
                        name: '移除选中行？',
//                    	disabled: function () {
//
//                            return false;
//						}
                    }
//                    "hsep2": "---------",
//                    "about": {name: '关于菜单'}
                }
            }
        });
	}

	function initAddOneRow(){
	    setTimeout(addOneRow,1000);
	}

	//添加一行
	function addOneRow(){
        hot.alter('insert_row', 0);
	}

	//钩子型回调函数---有新回调函数请修改
    function afterChangeCallBackFun(changesArr, source){
        if(source == 'edit' || source == 'Autofill.fill' ){
            for(var i = 0 ; i < changesArr.length ; i ++ ){
               var changes = changesArr[i];
                //选择执行函数
                switchFunDoIt(changes);
			}
        }
    }

    //选择执行函数
    function switchFunDoIt(changes){
        //如果改变前和改变后二者相等则不处理
        if(changes[2] == changes[3]){
			return true;
        }
        var falg = false;
        var fildName = changes[1];
        var mapTemp = globMap.get(switchMapKey);
		switch (fildName){
			case 'startDate' : falg = checkTheDateIsOkStarDate(changes,mapTemp.get(fildName)); break;
            case 'endDate' : falg = checkTheDateIsOkEndDate(changes,mapTemp.get(fildName));break;
            case 'expensesAmount' : falg = sumAllExpensesAmount(changes);break;
            case 'realAmount' : falg = sumAllRealAmount(changes);break;
			default : break;
		}
		return falg;
	}

    //检查日期是否正确--起始日期
    function checkTheDateIsOkStarDate(changes,targFile) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
//        var changeOblValue = changes[2];//暂不用--原数据
        var changeValue = changes[3];//变化数据
		if(!checkTheDateIsNullAndReg(changeValue,reg_ymd)){
			return false;
		}
        var targValue = hot.getDataAtRowProp(rowNum,targFile);
        if(!checkTheDateIsNullAndReg(targValue,reg_ymd)){
            return false;
        }

        if(changeValue > targValue){
            //改变样式和提示
			checngeTheClassAndTip(changes,'起始日期不能大于到达日期');
            return false;
		}
		return true;
    }

    //检查日期是否正确--到达日期
    function checkTheDateIsOkEndDate(changes,targFile) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
//        var changeOblValue = changes[2];//暂不用--原数据
        var changeValue = changes[3];//变化数据
        if(!checkTheDateIsNullAndReg(changeValue,reg_ymd)){
            return false;
        }
        var targValue = hot.getDataAtRowProp(rowNum,targFile);
        if(!checkTheDateIsNullAndReg(targValue,reg_ymd)){
            return false;
        }

        if(changeValue < targValue){
            //改变样式和提示
            checngeTheClassAndTip(changes,'到达日期不能小于起始日期');
            return false;
        }
        return true;
    }

    //汇总报销金额
    function sumAllExpensesAmount(changes){
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
        var changeValue = changes[3];//变化数据
		//验证数据正确性
        /*if(!checkTheDateIsNullAndReg(changeValue,reg)){
            return false;
        }*/
        starSumAllExpensesAmount(fildName,'submitAmount');
	}

	//汇总实际金额
	function sumAllRealAmount(changes) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
        var changeValue = changes[3];//变化数据
        //验证数据正确性
		/*if(!checkTheDateIsNullAndReg(changeValue,reg)){
		 return false;
		 }*/
        starSumAllExpensesAmount(fildName,'realAmount');
    }


	//根据属性名开始汇总所有的金额数据
	function starSumAllExpensesAmount(fildName,targId){
		//数据集
        var valArr = hot.getDataAtProp(fildName);
        var result = parseFloat(0);
        for(var i in  valArr){
            var val = valArr[i];
            if(!checkIsNotUndefinedAndNullAndNullValue(val)){
                val = 0;
            }
            result = add(result,val);
        }
        $('#' + targId).val(Number(result.toFixed(2)));
	}



    //改变样式和提示
    function checngeTheClassAndTip(changes,message) {
        var rowNum = changes[0];//行号
        var col = getCellColNumByRowNumAndFildName(rowNum,changes[1]);//列号
		var cell = hot.getCell(rowNum,col);
		var cellObj = $(cell);
        cellObj.addClass('htInvalid');
//        var seeRowNum = hot.getRowHeader(rowNum);
//        message = '第' + seeRowNum + '行,' + message;
        tip(message);
    }

    //获取列号
    function getCellColNumByRowNumAndFildName(rowNum,fildName){
		var colNum ;//列号
		//读取meta对象
        var metaCells = hot.getCellMetaAtRow(rowNum);
        for(var i = 0 ; i < metaCells.length ; i ++ ){
            var metaCell = metaCells[i];
            //通过对象关键字匹配
            if( metaCell.prop == fildName ){
                colNum = metaCell.col;
                break;
            }
        }
		return colNum;
	}

    //检查数据是否为空和格式正确
    function checkTheDateIsNullAndReg(dateValue,reg){
        if(!checkIsNotUndefinedAndNullAndNullValue(dateValue)){
			return false;
		}
        if(!dateValue.match(reg)){
            return false;
        }
        return true;
	}


	//保存数据
	function saveAllDataInfo(){

        var title = $('#title').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(title)){
            tip('标题不能为空');
			return ;
		}

		var expensesType = $('#expensesType').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(expensesType)){
            tip('出差类型不能为空');
            return ;
        }

		var budgetAmount = $('#budgetAmount').val();
		if(!checkIsNotUndefinedAndNullAndNullValue(budgetAmount)){
            tip('预算金额不能为空');
            return ;
		}

        var submitAmount = $('#submitAmount').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(submitAmount)){
            tip('报销金额不能为空');
            return ;
        }
        var realAmount = $('#realAmount').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(realAmount)){
            tip('实报金额不能为空');
            return ;
        }

        var reason = $('#reason').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(reason)){
            tip('报销事由不能为空');
            return ;
        }

        var id = $('#id').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(id)){
            tip('关键key丢失');
            return ;
		}

        //检查并得到明细数据
		var info = checkAndGetTheDataDetailData();
        if(!checkIsNotUndefinedAndNullAndNullValue(info)){
            return ;
		}
		var data = {
            id : id,
            realAmount : realAmount,
            flagKey : getFlagKey(),
            info : JSON.stringify({dataList:info})
		}
		var url = 'tsTravelExpensesWebWorkFlowController.do?updateTheTsTravelExpensesSomeInfoInWorkFlow';
        var d = ajaxPost(data,url);
        if(d.success){
            var obj = d.obj;
            if(checkIsNotUndefinedAndNullAndNullValue(obj)){
                //重新加载明细数据
                loadDataAll();
//                W.$('#tsTravelExpensesList').datagrid('reload');
			}else{
                tip("返回的关键值丢失");
			}
		}
		tip(d.msg);
	}

	//检查并得到明细数据
	function checkAndGetTheDataDetailData(){
	    //读取明细数据--所有
        var detailDataObjArr = hot.getSourceData()
		//读取列file数组集合
		var columnsArr = globMap.get(columnsArrKey);
        //读取file与title对应的集合map
        var fileKeyAndTitleMap = globMap.get(fileKeyAndTitleMapKey);

        //循环遍历
		for(var i = 0 ; i < detailDataObjArr.length ; i ++ ){
		    //数据对象
			var detailDataObj = detailDataObjArr[i];
			//遍历file字段
			for(var j in columnsArr){
			    var file = columnsArr[j];
			    //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                //检查为空
                if(checkIsNotUndefinedAndNullAndNullValue(objValue) ){
                    detailDataObj.pnum = hot.getRowHeader(i);
				}else{
                    var seeRowNum = hot.getRowHeader(i);
                    var message = '第' + seeRowNum + '行,' + fileKeyAndTitleMap.get(file) + "不能为空!";
                    tip(message);
                    return '';
				}
			}
		}
		return detailDataObjArr;
	}

    //---------------------共用--------------------------------//

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }

    //获取属性值
    function getPropertyValueByAttr(obj,property){
        return obj.attr(property);
    }

    //处理键值对
    function HashKey(){
        var data = {};
        this.set = function(key,value){   //set方法
            data[key] = value;
        };
        this.unset = function(key){     //unset方法
            delete data[key];
        };
        this.get = function(key){     //get方法
            return data[key] || "";
        }
        this.returnKey = function(){
            //获得对象所有属性的数组
            return Object.getOwnPropertyNames(data);
        }
    }

    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }

    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }

    //--------------------高精度计算函数star------------------------------//
    /**
     * 高精度加法函数
     */
    function add(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
	/*
	 * 高精减法函数
	 */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();

        try {
            m += s1.split(".")[1].length;
        }
        catch (e) {

        }
        try {
            m += s2.split(".")[1].length;
        }
        catch (e) {

        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;

        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch(e) {
            r2 = 0;
        }

        // 动态求出哪一个位数最多，再得出10的n次幂
        return Math.pow(10, Math.max(r1, r2));
    }

    //--------------------高精度计算函数end------------------------------//

</script>