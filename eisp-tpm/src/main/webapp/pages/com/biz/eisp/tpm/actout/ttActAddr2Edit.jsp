<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>修改 广告发布地址2  </title>
    <t:base type="jquery,easyui,tools"></t:base>
    <style>
        input {
            font-size: 16px !important;
            font-family: 微软雅黑 !important;
            width: 300px !important;
        }
        textarea {
            font-size: 16px !important;
            font-family: 微软雅黑 !important;
            width: 300px !important;
        }

        label {
            font-size: 16px !important;
            font-family: 微软雅黑 !important;
        }
        .Validform_label1{
            width:160px !important;
            float:left;
        }
        .inputxt1{
            width:300px !important;
            height:80px !important;

        }
    </style>
</head>
<body>

<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"  action="ttActOutUploadController.do?saveAdvAddr2&hwId=${bean.id}">
    <!-- 自定义表单 begin -->

                <input type="hidden" name="id" value="${bean.id}"/>
                <div class="form">
                    <label class="Validform_label1" name="actCode">活动单号: </label>
                    <input id="actCode" name="actCode" value="${bean.actCode}"  readonly="readonly" class="inputxt" onClick="choose(this);" />
                </div>
                <div class="form">
                    <label class="Validform_label1" name="advCode">门头广告编号: </label>
                    <input id="advCode" name="advCode" value="${bean.advCode}"  readonly="readonly" disabled="disabled" class="inputxt" />
                </div>
                <div class="form">
                    <label class="Validform_label1" name="terminalName">店铺名称: </label>
                    <input id="terminalName" name="terminalName" value="${bean.terminalName}" disabled="disabled" readonly="readonly" class="inputxt" />
                </div>
                <div class="form">
                    <label class="Validform_label1" name="remarks">发布地址2（原值）: </label>
                    <textarea  id="remarks1" name="remarks1"  disabled="disabled" readonly="readonly" class="inputxt1"  >${bean.remarks}</textarea>
                </div><br />
                <div class="form">
                    <label class="Validform_label1" name="remarks">发布地址2（拟改）: </label>
                    <textarea id="remarks" name="remarks" class="inputxt1"  />${bean.remarks} </textarea><br />
                    <span style="color: red;font-size: 14px;font-family: 微软雅黑">注意：内容不能包含：' < > %等字符。</span>
                </div>


    <!-- 自定义表单 end-->
</t:formvalid>

</body>
<script type="text/javascript">

</script>
</html>
