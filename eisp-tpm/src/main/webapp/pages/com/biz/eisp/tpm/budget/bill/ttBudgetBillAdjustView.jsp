<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>预算调整</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<div class="easyui-layout" fit="true">
	<div region="west" style="width:350px;border-right:0px;" title="调出方">
			<t:formvalid formid="budgetOut" layout="div" dialog="true" action="" refresh="true">
				<input type="hidden" name="adjustFlag" value="-1">
				<div class="form">
					<label class="Validform_label">年度:</label>
					<input name="year" id="yearOut" type="text" class="Wdate" style="width:150px" 
					       value="${billOut.year}" disabled="disabled"/>
				</div>
				
				<div class="form">
		            <label class="Validform_label">季度:</label> 
					<select name="quarter" disabled="disabled">
						<option value="">请选择</option>
						<option value="1" <c:if test="${billOut.quarter == 1}">selected='selected'</c:if> >1</option>
						<option value="2" <c:if test="${billOut.quarter == 2}">selected='selected'</c:if> >2</option>
						<option value="3" <c:if test="${billOut.quarter == 3}">selected='selected'</c:if> >3</option>
						<option value="4" <c:if test="${billOut.quarter == 4}">selected='selected'</c:if> >4</option>
					</select>
		        </div>	
		        	
				<div class="form">
					<label class="Validform_label" name="custName">部门: </label> 
					<input name="orgOut" class="inputxt" value="${billOut.orgName}" disabled="disabled"/>
				</div>
				
				<div class="form">
					<label class="Validform_label">费用类型:</label>
					<select name="costTypeCode" disabled="disabled">
						<option value="">--请选择--</option>
						<c:forEach items="${costs}" var="cost">
							<option <c:if test="${billOut.costTypeCode == cost.costTypeCode}">selected='selected'</c:if> >${cost.costTypeName}</option>
						</c:forEach>
					</select>
				</div>	
				
				<div class="form">
					<label class="Validform_label">期初金额:</label>
					<input name="periodAmount" value="${billOut.periodAmount}" class="inputxt" style="width: 150px;background:#E4E4E4;" disabled="disabled">
				</div>	
				
				<div class="form">
					<label class="Validform_label">可用余额:</label>
					<!-- 可用余额-对应字段：调整前余额 -->
					<input name="beforeAdjustBalance" class="inputxt" style="width: 150px;background:#E4E4E4;" value="${billOut.beforeAdjustBalance}" disabled="disabled">
				</div>	
				
				<div class="form">
					<label class="Validform_label">调减金额:</label>
					<input type="text" class="inputxt" 
						   style="width: 150px" value="${billOut.adjustAmount}" disabled="disabled"/>
					<span style='color:red'>*</span>
				</div>	
								
				<div class="form">
					<label class="Validform_label">调减后余额:</label>
					<input name="afterAdjustBalance" class="inputxt" style="width: 150px;background:#E4E4E4;" value="${billOut.afterAdjustBalance}" disabled="disabled">
				</div>
																					
		</t:formvalid>
	</div>
	<!-- ---------------------------------------------------------------------------------------------------------------------------------- -->
	<div region="center" style="width:350px;" title="调入方">
		<t:formvalid formid="budgetIn" layout="div" dialog="true" action="" refresh="true">
					<input type="hidden" name="adjustFlag" value="1">
		
					<div class="form">
						<label class="Validform_label">年度:</label>
						<input name="year" type="text" class="Wdate" style="width:150px" 
						       value="${billIn.year}" disabled="disabled"/>
					</div>
					
					<div class="form">
			            <label class="Validform_label">季度:</label> 
						<select name="quarter" disabled="disabled">
							<option value="">请选择</option>
							<option value="1" <c:if test="${billIn.quarter == 1}">selected='selected'</c:if> >1</option>
							<option value="2" <c:if test="${billIn.quarter == 2}">selected='selected'</c:if> >2</option>
							<option value="3" <c:if test="${billIn.quarter == 3}">selected='selected'</c:if> >3</option>
							<option value="4" <c:if test="${billIn.quarter == 4}">selected='selected'</c:if> >4</option>
						</select>
			        </div>	
			        
					<div class="form">
						<label class="Validform_label" name="custName">部门:</label> 
						<input name="orgIn" class="inputxt" value="${billIn.orgName }" disabled="disabled"/>
					</div>
					
					<div class="form">
						<label class="Validform_label">费用类型:</label>
						<select name="costTypeCode" disabled="disabled">
							<option value="">--请选择--</option>
							<c:forEach items="${costs}" var="cost">
								<option <c:if test="${billIn.costTypeCode == cost.costTypeCode}">selected='selected'</c:if>>${cost.costTypeName}</option>
							</c:forEach>
						</select>
					</div>
					
					<div class="form">
						<label class="Validform_label">期初金额:</label>
						<input name="periodAmount" class="inputxt" style="width:150px;background:#E4E4E4;" value="${billIn.periodAmount}" disabled="disabled">
					</div>	
					
					<div class="form">
						<label class="Validform_label">可用余额:</label>
						<input name="beforeAdjustBalance" class="inputxt" style="width:150px;background:#E4E4E4;" value="${billIn.beforeAdjustBalance}" disabled="disabled">
					</div>	
					
					<!-- 调入方：调整金额 -->
					<input type="hidden" name="adjustAmount" id="amountIn">
					
					<div class="form">
						<label class="Validform_label">调增后余额:</label>
						<input name="afterAdjustBalance" value="${billIn.afterAdjustBalance}" class="inputxt" style="width: 150px;background:#E4E4E4;" disabled="disabled">
					</div>	
					
					<div class="form">
						<label class="Validform_label" name="areaName">备注: </label> 
						<textarea style="width:150px;height:100px;resize:none;" disabled="disabled">${billIn.remark}</textarea>
					</div>	
		</t:formvalid>	
	</div>
</div>
</body>
</html>