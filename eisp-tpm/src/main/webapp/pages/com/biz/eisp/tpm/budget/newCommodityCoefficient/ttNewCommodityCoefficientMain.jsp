<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="TtNewCommodityCoefficientList" fitColumns="false" title="货补系数配置"
                    actionUrl="${url}" idField="id" fit="true">

            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="事业部编码" field="businessUnitCode" width="150"></t:dgCol>
            <t:dgCol title="事业部名称" field="businessUnitName" width="150"></t:dgCol>
            <t:dgCol title="事业部" field="businessUnit" hidden="true" replace="奶粉事业部_0102,低温事业部_0100,常温事业部_0101,长效营销中心_0104" query="true" width="150"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" query="true" width="150"></t:dgCol>
            <t:dgCol title="系数" field="coefficient"  width="150"></t:dgCol>
            <t:dgCol title="创建人" field="createName" query="true" width="150"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" queryMode="group" query="true" formatter="yyyy-MM-dd HH:mm:ss"  width="150"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName" query="true" width="150"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" queryMode="group" query="true" formatter="yyyy-MM-dd HH:mm:ss"  width="150"></t:dgCol>

            <t:dgToolBar operationCode="add" title="新增" height="500" width="800" icon="icon-add" url="ttNewCommodityCoefficientController.do?goTtNewCommodityCoefficientForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑" height="500" width="800" icon="icon-edit" url="ttNewCommodityCoefficientController.do?goTtNewCommodityCoefficientForm" funname="ownUpdate"></t:dgToolBar>
            <t:dgToolBar operationCode="del" title="刪除"  height="500" width="800" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>
            <%--<t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'costTypePositionImport', gridName:'TtNewCommodityCoefficientList'})"></t:dgToolBar>--%>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttNewCommodityCoefficientController.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $(function () {
        $("input[name='yearMonth']").removeAttr("onfocus");
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");
        $("input[name='updateDate_begin']").removeAttr("onfocus");
        $("input[name='updateDate_end']").removeAttr("onfocus");

        $("input[name='yearMonth']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM'});
        });

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });

        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });

        $("input[name='updateDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='updateDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    });

    function getNowYearMonth(){
        return '${nowYearMonth}';
    }

    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#TtNewCommodityCoefficientList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "ttNewCommodityCoefficientController.do?deleteTtNewCommodityCoefficient&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#TtNewCommodityCoefficientList").datagrid("reload");
                    }
                });
            }
        });
    }


    function ownUpdate(title, url, id, width, height) {
        gridname = id;
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }

        if(rowsData[0].yearMonth < getNowYearMonth() ){
            tip('只有当前年月之前的数据可编辑');
            return ;
        }

        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            url+="&accessEntry="+accessEntry;
        }

        url += '&id=' + rowsData[0].id;
        if (!width || width=="" || width=='null' || !height || height=="" || height=='null')
            createwindowSmall(title, url, width, height);
        else
            createwindow(title, url, width, height);
    }
</script>
