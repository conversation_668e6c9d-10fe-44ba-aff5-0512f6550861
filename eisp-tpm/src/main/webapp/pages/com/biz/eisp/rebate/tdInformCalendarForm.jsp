<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>新增</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
		<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
			 action="informCalendarController.do?saveInformCalendarForm" beforeSubmit="checkmonth();">
			<input id="id" name="id" type="hidden" value="${informCalendarVo.id }">
				<div class="form">
					<label class="Validform_label"><span style="color:red;">*</span>年份: </label>
					<input name="year" class="inputxt Wdate" value="${informCalendarVo.year}" dataType="*"
					onclick="WdatePicker({dateFmt:'yyyy'})" readonly />
				</div>
				<div class="form">
					<label class="Validform_label"><span style="color:red;">*</span>月份: </label>
					<input name="month" id="month" class="inputxt Wdate" value="${informCalendarVo.month}" dataType="*"
					onclick="WdatePicker({dateFmt:'MM'})" readonly />
				</div>
				
				<div class="form">
					<label class="Validform_label"><span style="color:red;">*</span>季度: </label>
					<select name="season" id="season">
                		<option value="第一季度" <c:if test="${informCalendarVo.season == '第一季度'}">selected="selected"</c:if> >第一季度</option>
                		<option value="第二季度" <c:if test="${informCalendarVo.season == '第二季度'}">selected="selected"</c:if> >第二季度</option>
                		<option value="第三季度" <c:if test="${informCalendarVo.season == '第三季度'}">selected="selected"</c:if> >第三季度</option>
                		<option value="第四季度" <c:if test="${informCalendarVo.season == '第四季度'}">selected="selected"</c:if> >第四季度</option>
            		</select>
				</div>
				<div class="form">
					<label class="Validform_label"><span style="color:red;">*</span>上传时间: </label>
					<input readonly="readonly" type="hidden" id="uploadDateList" name="uploadDateList" class="inputxt" value="${informCalendarVo.uploadDateList}">
					<a iconcls="icon-search" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
					   onclick="openCalendar()"></a>
				</div>

				<div class="form">
					<label class="Validform_label"><span style="color: red">*</span>应上传天数: </label>
					<input datatype="*" readonly="readonly" id="uploadDay"  name="uploadDay" class="inputxt" value="${informCalendarVo.uploadDay}">
				</div>


		</t:formvalid>

</body>
<script>
function openCalendar() {
	
	$.dialog({
		title: "上传时间",
		content: "url:informCalendarController.do?goCalendar&id="+$("#id").val(),
		lock: true,
		width: "800",
		height: "500",
		zIndex: 10000,
		queryParams: { uploadDateList: $("#uploadDateList").val() },
		parent: windowapi,
		ok: function () {
			iframe = this.iframe.contentWindow;
			var dateList = iframe.dateList;
			var strs= new Array(); 
			strs=dateList.split(",");
			$("#uploadDateList").val(dateList);
			$("#uploadDay").val(strs.length-1);
		},
		cancelVal: '关闭',
		cancel: true
	});
}
function checkmonth() {
	var seasoncheck = $("#season").val();
	var monthchck = $("#month").val();
	if(seasoncheck == "第一季度"){
		if(monthchck>3){
			$.messager.alert('错误', "季度与月份不匹配");
			return false;
		}
	}
	else if(seasoncheck == "第二季度"){
		if((monthchck <= 3) || (monthchck > 6)){
			$.messager.alert('错误', "季度与月份不匹配");
			return false;
		}
	}else if(seasoncheck == "第三季度"){
		if((monthchck <= 6) || (monthchck > 9)){
			$.messager.alert('错误', "季度与月份不匹配");
			return false;
		}
	}else{
		if((monthchck <= 9) ){
			$.messager.alert('错误', "季度与月份不匹配");
			return false;
		}
	}
}
</script>
</html>