<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    .datagrid-toolbar-search form div label{
        width:120px;
    }
</style>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="TtManagementCostAccountList" fitColumns="false" title="SAP预算科目管理"
                    actionUrl="${ttManagementCostAccountList}" idField="id" fit="true">

            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="SAP预算科目编码" field="accountCode" query="true"></t:dgCol>
            <t:dgCol title="SAP预算科目名称" field="accountName" query="true"></t:dgCol>
            <t:dgCol title="财务科目ERP编码" field="financialCode" query="true"></t:dgCol>
            <t:dgCol title="SAP预算科目类型" field="accountType" dictionary="budget_type" query="true"></t:dgCol>
            <t:dgCol title="备注" field="remark" query="true"></t:dgCol>
            <t:dgCol title="生效状态" field="enableStatus" dictionary="enable_status" query="true"></t:dgCol>
            <t:dgCol title="创建人" field="createName"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>

            <t:dgToolBar operationCode="new" title="录入SAP预算科目" icon="icon-add" url="ttManagementCostAccountController.do?goTtManagementCostAccountForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttManagementCostAccountController.do?goTtManagementCostAccountForm" funname="update"></t:dgToolBar>
            <t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="" funname="deleteData"></t:dgToolBar>
            <t:dgToolBar operationCode="start" title="启用" icon="icon-start"  onclick="startOrStop(0)"></t:dgToolBar>
            <t:dgToolBar operationCode="stop" title="停用" icon="icon-stop" onclick="startOrStop(1)" ></t:dgToolBar>
            <%--<t:dgToolBar operationCode="view" title="查看关联活动大类" icon="icon-look" url="" funname="viewCostType()"></t:dgToolBar>--%>
            <t:dgToolBar operationCode="out" title="导出"  icon="icon-dataOut" url="ttManagementCostAccountController.do?exportXls"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttManagementCostAccountController.do?goManagementCostAccountLogMain" funname="detailLog" width="1200" height="460"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $("#TtManagementCostAccountListb_r").find("input[name='systemDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

    $(function () {
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    });


    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#TtManagementCostAccountList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "ttManagementCostAccountController.do?deleteManagementCostAccount&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#TtManagementCostAccountList").datagrid("reload");
                    }
                });
            }
        });
    }

    //启用停用
    function startOrStop(flag){
        var seleted = $("#TtManagementCostAccountList").datagrid('getSelected');
        if(seleted == null){
            tip("请选择一条要操作的数据");
            return;
        }
        var tipmMsg = "";
        if(flag == 0){
            tipmMsg = "确定要启用该数据吗?"
            if(seleted.enableStatus == flag){
                tip("已经处于启用状态,无需再次启用");
                return false;
            }
        }else{
            if(seleted.enableStatus == flag){
                tip("已经处于停用状态,无需再次停用");
                return false;
            }
            tipmMsg = "确定要停用该数据吗?"
        }
        $.messager.confirm('操作提示',tipmMsg,function(r){
            if (r){
                $.ajax({
                    type : "POST",
                    url : "ttManagementCostAccountController.do?isEnableStatus",
                    data : {
                        "id" : seleted.id,
                        "enableStatus": flag
                    },
                    dataType : "json",
                    success : function(data) {
                        tip(data.msg);
                        $("#TtManagementCostAccountList").datagrid('reload');
                    },
                    error:function(){
                        tip("服务器异常，请稍后再试");
                    }
                });
            }
        });
    }

</script>
