<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<div>
    <label title="试算积分">试算积分</label>
    <input id="trialIntegral" style="width: 100px" type="text" name="trialIntegral" class="inuptxt" onKeyUp="this.value=this.value.replace(/[^\.\d]/g,'');if(this.value.split('.').length>2){this.value=this.value.split('.')[0]+'.'+this.value.split('.')[1]}" >
    <a id="checkTheTrialIntegral" href="#" class="easyui-linkbutton l-btn l-btn-plain"
       plain="true" icon="icon-search" onclick="checkTheTrialIntegral()">检测</a>
    <span id="showStyle"></span>
</div>
<script>
    //检查预算积分
    function checkTheTrialIntegral(){
        var flag = false;
        var trialIntegral = $('#trialIntegral').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(trialIntegral)){
            tip("请填写测算积分");
            return ;
        }

        var thisData = {
            flagKey : getFlagKey(),
            businessKey : getBusinessKey(),
            trialIntegral : trialIntegral
        }

        var url = "ttActApplyExcuteWorkFlowController.do?checkTheTrialIntegral";
        var d = ajaxPost(thisData,url);
        var class1;
        var oldClass1;
        var title;
        var spanHtml = '';
        if(d.success){
            if(d.obj.success){
                flag = true;
            }else{
                flag = false;
                spanHtml = d.obj.msg;
//                confirm(d.obj.msg);
            }
            getShowReferenceData();
        }else{
            flag = false;
            spanHtml = d.msg;
            tip(d.msg);
        }
        if(flag){
            title = "检测通过";
            class1 = "Validform_checktip Validform_right";
            oldClass1 = "Validform_checktip Validform_wrong";
        }else{
            title = "检测不通过";
            oldClass1 = "Validform_checktip Validform_right";
            class1 = "Validform_checktip Validform_wrong";
        }

        changeTheTipStyle(oldClass1,class1,title,spanHtml);
    }

    //添加样式
    function changeTheTipStyle(olcClass1,class1,title,spanHtml) {
        var showObj = $('#showStyle');
        showObj.removeClass(olcClass1).addClass(class1);
        showObj.attr("title",title);
        showObj.html(spanHtml);
    }

</script>