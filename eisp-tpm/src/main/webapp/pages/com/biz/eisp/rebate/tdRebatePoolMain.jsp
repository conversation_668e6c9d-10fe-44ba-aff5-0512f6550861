<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="dtRebatePoolList"  fitColumns="false" title="费用池管理"
                    actionUrl="rebatePoolController.do?findTdRebatePoolSumList" idField="id" fit="true" queryMode="group" pageSize="30">
			<t:dgCol title="" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="经销商编号" field="dealerId" query="true"></t:dgCol>
			<t:dgCol title="经销商名称" field="dealerName" query="true"></t:dgCol>
			<t:dgCol title="折扣余额" field="discountSum" ></t:dgCol>
			<t:dgCol title="货补余额" field="supplementSum"  ></t:dgCol>
			<t:dgCol title="合计" field="sumMoneyOrNum" ></t:dgCol>

			<t:dgToolBar title="手动上账" operationCode="add" icon="icon-add" url="rebatePoolController.do?goRebatePoolForm" funname="add" height="400" width="400"></t:dgToolBar>
            <t:dgToolBar title="批量上账" operationCode="dataIn" icon="icon-dataIn" onclick="importDataByXml({impName:'tdRebatepool', gridName:'dtRebatePoolList'})" ></t:dgToolBar>
	        <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="rebatePoolController.do?exportRebatePoolSumXls" funname="excelExport" ></t:dgToolBar>
            <t:dgToolBar title="费用上账明细" operationCode="search_reb" icon="icon-search" url="rebatePoolController.do?goRebatePoolDetailMain" funname="goRebatePoolDetail" height="450" width="900"></t:dgToolBar>
            <t:dgToolBar title="费用使用明细" operationCode="search_use" icon="icon-search" url="rebatePoolController.do?goCostUseForRebateMain" funname="goCostUse" height="450" width="900"></t:dgToolBar>
        </t:datagrid>

    </div>
</div>

<script>

    //查看上账明细
    function goRebatePoolDetail(titlex, urlx, idx, widthx, heightx) {
        var rowsData = $('#dtRebatePoolList').datagrid('getSelections');
        if (rowsData.length == 0) {
            tip('请选择一条记录');
            return;
        }

        if (null != rowsData[0].dealerId) {
            var selectdealerId = rowsData[0].dealerId;
        }
        $.dialog({
            content: 'url:' + urlx + '&dealerId=' + selectdealerId,
            title: titlex,
            cache: false,
            lock: false,
            width: widthx,
            height: heightx,
            zIndex: 999
        });
    }

    //查看费用使用明细
    function goCostUse(titlex, urlx, idx, widthx, heightx) {
        var rowsData = $('#dtRebatePoolList').datagrid('getSelections');

        if (rowsData.length == 0) {
            tip('请选择一条记录');
            return;
        }

        if (null != rowsData[0].dealerId) {
            var selectdealerId = rowsData[0].dealerId;
        }
        $.dialog({
            content: 'url:' + urlx + '&dealerId=' + selectdealerId,
            title: titlex,
            cache: false,
            lock: true,
            width: widthx,
            height: heightx,
            zIndex: 999
        });
    }
</script>


