<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="dtRebatePoolDetailList"  fitColumns="false" title="费用池上账明细"
                    actionUrl="rebatePoolController.do?findTdRebatePoolDetailList&dealerId=${selectdealerId}"  idField="id" fit="true" queryMode="group">
            <t:dgCol title="编号" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="经销商编码" field="dealerId" ></t:dgCol>
            <t:dgCol title="经销商名称" field="dealerName" ></t:dgCol>
            <t:dgCol title="客户编码" field="sapSn" ></t:dgCol>
            <t:dgCol title="客户名称" field="sapName" ></t:dgCol>
            <t:dgCol title="活动编号" field="activityCode" ></t:dgCol>
            <t:dgCol title="活动名称" field="activityName" ></t:dgCol>
            <t:dgCol title="活动来源" field="activitySource"></t:dgCol>
            <t:dgCol title="核销单号" field="verifCode" ></t:dgCol>
            <t:dgCol title="费用类型" field="costType" replace="核销_1,返利_2,手工入账_3" query="true"></t:dgCol>
            <t:dgCol title="费用科目" field="costCourse"></t:dgCol>
            <t:dgCol title="活动开始" field="activityStart"></t:dgCol>
            <t:dgCol title="活动结束" field="activityEnd"></t:dgCol>
            <t:dgCol title="核销金额" field="verifMoney"></t:dgCol>
            <t:dgCol title="上账金额" field="moneyOrNum"></t:dgCol>
            <t:dgCol title="支付方式" field="payType" dictionary="pay_type" ></t:dgCol>
            <t:dgCol title="物料编号" field="matnr" ></t:dgCol>
            <t:dgCol title="物料名称 " field="maktx" ></t:dgCol>
            <t:dgCol title="上账人" field="createName" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
            <t:dgCol title="状态" field="status" replace="启用_1,停用_0"></t:dgCol>
            <t:dgCol title="备注" field="remark" ></t:dgCol>
            <t:dgToolBar title="导出费用上账明细" icon="icon-dataOut" url="rebatePoolController.do?exportRebatePoolDetailXlsForBack&dealerId=${selectdealerId}" funname="excelExport" ></t:dgToolBar>
        </t:datagrid>

    </div>
</div>

<script>

$(function () {
    <%--var selectdealerId = "${selectdealerId}";--%>
    <%--console.log(selectdealerId);--%>
    <%--$("#dtRebatePoolDetailList").datagrid('reload', {--%>
        <%--dealerId: 'selectdealerId'--%>
    <%--});--%>





//    $("input[name='dealerId']").val(selectdealerId);
//    dtRebatePoolDetailListsearch();
})
</script>


