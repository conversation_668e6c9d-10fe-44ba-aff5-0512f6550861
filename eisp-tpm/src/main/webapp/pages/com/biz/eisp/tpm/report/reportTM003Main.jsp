<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_roleList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="tmUserList" fitColumns="true" title="企业用户管理"  extendTableName="${extendTableName}"
                    actionUrl="reportTM003Controller.do?findReportList" idField="id" queryMode = "group">

            <t:dgCol title="所属组织" field="orgName" query="true" columnOrder="8"></t:dgCol>
            <t:dgCol title="职位名称" field="positionName" query="true" columnOrder="8"></t:dgCol>
            <t:dgCol title="职位编码" field="positionCode" query="true" columnOrder="8"></t:dgCol>
            <t:dgCol title="职位级别" field="positionLevel" dictionary="position_level" columnOrder="8"></t:dgCol>
            <t:dgCol title="上级职位名称" field="parentPositionName" query="true" columnOrder="8"></t:dgCol>
            <t:dgCol title="上级用户名" field="parentUserName" query="true" columnOrder="8"></t:dgCol>

            <t:dgToolBar title="查看" icon="icon-look" url="tmUserController.do?goTmUserForm&optype=2" funname="detail"></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-dataOut" url="reportTM003Controller.do?exportXls" funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<c:if test="${not empty includeJsp}">
    <jsp:include page="${includeJsp}" flush="true"></jsp:include>
</c:if>
<script type="text/javascript">

    $(document).ready(function(){
        $("input[name='createDate']").datetimebox({
            showSeconds:true
        });
    });
</script>

