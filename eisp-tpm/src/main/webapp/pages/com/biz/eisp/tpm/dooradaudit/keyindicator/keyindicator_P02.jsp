<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="P02List" checkbox="false" fitColumns="true"
                    pagination="false"  actionUrl="ttProductActWorkFlowController.do?findKeyindicatorP02&flagKey=${flagKey}" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName" sortable="false"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" sortable="false"></t:dgCol>
            <t:dgCol title="月度销售计划" field="monthlySalePlanAmount" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="预算科目" field="accountName" sortable="false"></t:dgCol>
            <t:dgCol title="管理版预算金额" field="budgetAmount" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="费用额" field="costAmount" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额" field="targetAmount" formatterjs="numExtend" sortable="false"></t:dgCol>
            <t:dgCol title="费率" field="costRateStr" sortable="false"></t:dgCol>
            <t:dgCol title="结余金额" field="finalAmount" formatterjs="numExtend" sortable="false" ></t:dgCol>
            <t:dgCol title="同比销量" field="yearOnYearSalesQuantity" sortable="false" ></t:dgCol>
            <t:dgCol title="同比销售额" field="yearOnYearSalesAmount" formatterjs="numExtend" sortable="false" ></t:dgCol>
            <t:dgCol title="环比销量" field="chainSalesQuantity" sortable="false" ></t:dgCol>
            <t:dgCol title="环比销售额" field="chainSalesAmount" formatterjs="numExtend" sortable="false" ></t:dgCol>
        </t:datagrid>
    </div>
</div>