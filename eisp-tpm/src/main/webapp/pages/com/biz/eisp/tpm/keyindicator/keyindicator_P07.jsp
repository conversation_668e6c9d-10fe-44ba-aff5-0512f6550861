<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="doorAuditList" title="门头广告核销列表"  actionUrl="ttDooradAuditController.do?findDoorAdAuditList"
                   idField="id" fit="true"  fitColumns="false" queryMode="group" singleSelect="false">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="状态" field="bpmStatus" dictionary="bpm_status"  width="80" ></t:dgCol>
            <t:dgCol title="核销单号" field="auditCode"   width="200"></t:dgCol>
            <t:dgCol title="标题" field="title"   width="200"></t:dgCol>
            <t:dgCol title="活动单号" field="actCode"    width="200"></t:dgCol>
            <t:dgCol title="活动执行单号" field="actExcuteCode"   width="200"></t:dgCol>
            <t:dgCol title="活动名称" field="actName"   width="200"></t:dgCol>
            <t:dgCol title="年月" field="createDate" formatter="yyyy-MM"   width="200"></t:dgCol>
            <t:dgCol title="申请核销金额" field="applyAuditMoney"  width="200"></t:dgCol>
            <t:dgCol title="实际核销金额" field="actualAuditMoney"   width="200"></t:dgCol>

        </t:datagrid>
    </div>
    <input type="text">
</div>