<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttQuotaActTerminalMainList" checkbox="false" fitColumns="true"  title="" pagination="false"
                    actionUrl="ttQuotaActWorkFlowController.do?findTtQuotaActMaterialMainList&flagKey=${flagKey}&id=${id}" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol field="materialCode" title="物料编码" ></t:dgCol>
            <t:dgCol field="materialName" title="物料名称"  ></t:dgCol>
            <t:dgCol field="price" title="单价"></t:dgCol>
            <t:dgCol field="customerPrice" title="客户承担价格"  ></t:dgCol>
            <t:dgCol field="quantity" title="数量"></t:dgCol>
            <t:dgCol field="amount" title="费用金额"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
//        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
    });
</script>

