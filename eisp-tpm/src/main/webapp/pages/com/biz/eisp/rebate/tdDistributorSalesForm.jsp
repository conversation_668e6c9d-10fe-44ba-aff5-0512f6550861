<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>非直供进货数据</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
	<style type="text/css">
		.inputxt{width:180px !important}
	</style>
</head>
<body style="overflow-y: hidden" scroll="no">
		<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
			 action="tdDistributorSalesController.do?saveTdDistributorSales">
			<input id="id" name="id" type="hidden" value="${tdDistributorSalesVo.id}">

			<div class="form">
				<label class="Validform_label">非直供编码: </label>
				<input name="disbCode" class="inputxt" id="disbCode" value="${tdDistributorSalesVo.disbCode}" readonly="readonly" datatype="s2-30">

				<t:choose
						hiddenName="disbCode" hiddenid="terminalCode"
						url="tdDistributorSalesController.do?goTdDistributorChoose" name="tdDistributorList"
						icon="icon-search"
						title="非直供终端" textname="terminalName,erpCode,customerName" inputTextname="terminalName,erpCode,kunnrName" isclear="true"
						width="400" height="400">
				</t:choose>
				<span style="color: red">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">非直供名称: </label>
				<input name="disbName" class="inputxt" id="terminalName" value="${tdDistributorSalesVo.disbName}" readonly="readonly" datatype="*2-30">
				<span style="color: red">*</span>
			</div>

			<div class="form">
				<label class="Validform_label">上级客户编码: </label>
				<input name="kunnr" class="inputxt"   id="erpCode" value="${tdDistributorSalesVo.kunnr}" readonly="readonly" datatype="s2-30">
				<span style="color: red">*</span>


			</div>


			<div class="form">
				<label class="Validform_label">上级客户名称: </label>
				<input name="kunnrName" id="customerName" class="inputxt" readonly="readonly" datatype="*"  value="${tdDistributorSalesVo.kunnrName}" >
				<span style="color: red">*</span>

			</div>


			<div class="form">
				<label class="Validform_label">产品编码: </label>
				<input name="productCode" class="inputxt" id="productCode" value="${tdDistributorSalesVo.productCode}">
				<t:choose
						hiddenName="productCode" hiddenid="sn"
						url="tdProductController.do?goTdProductMain&type=choose&singleSelect=1" name="productListChoose"
						icon="icon-search"
						title="产品管理" textname="fullName" inputTextname="fullName" isclear="true"
						width="400" height="400">
				</t:choose>
			</div>
			<div class="form">
				<label class="Validform_label">产品名称: </label>
				<input name="productName" class="inputxt" id="fullName" value="${tdDistributorSalesVo.productName}">
			</div>
			<div class="form">
				<label class="Validform_label">进货数量: </label>
				<input name="buyNum" class="inputxt" value="${tdDistributorSalesVo.buyNum}">
			</div>
			<div class="form">
				<label class="Validform_label">进货日期: </label>
				<input name="buyDate" class="inputxt Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" value="${tdDistributorSalesVo.buyDate}" >
			</div>

		</t:formvalid>

</body>

<script>

</script>
</html>