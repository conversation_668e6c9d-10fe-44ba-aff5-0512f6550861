<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
    <div data-options="region:'center'" style="padding:1px;">
        <t:datagrid name="ttReportTerList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
                    actionUrl="${TerList}">
            <%--门店别--%>
            <t:dgCol title="基本信息"  field=""  colspan="30"></t:dgCol>
            <t:dgCol title="申请信息"  field=""  colspan="8"></t:dgCol>
            <t:dgCol title="结案信息" field=""   colspan="3"></t:dgCol>
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="审批状态" field="approvalStatus" sortable="false" replace="审批通过_3,关闭_6" dictionary="bpm_status" query="true"></t:dgCol>
            <t:dgCol title="活动类型" field="actType"  sortable="false" query="true" replace="定额活动_quotaacttype,长期待摊费用_longtermacttype,广告费申请_adacttype"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程提交日期" field="flowDate"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程编号" field="processCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="提交主题" field="submitTheme"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="业务费用归类" field="expenseClassification" dictionary="business_cost_type" sortable="false"  query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="费用归属事业部" field="costSyb"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  sortable="false"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品层级编码" field="productHierarchyCode"  sortable="false" ></t:dgCol>
            <t:dgCol title="产品层级名称" field="productHierarchyName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="门店编号" field="terminalCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="门店名称" field="terminalName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="重点门店" field="emphasisTerminal"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动编号" field="actCode" query="true"  sortable="false" ></t:dgCol>
            <t:dgCol title="活动名称" field="actName"  sortable="false" ></t:dgCol>
            <t:dgCol title="预算科目编码" field="financialCode"  sortable="false"></t:dgCol>
            <t:dgCol title="预算科目名称" field="financialName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode"  sortable="false"></t:dgCol>
            <t:dgCol title="活动大类名称" field="costTypeName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类编码" field="costAccountCode"  sortable="false"></t:dgCol>
            <t:dgCol title="活动细类名称" field="costAccountName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="开始时间" field="beginTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"  sortable="false" query="true"></t:dgCol>
            <%--申请信息--%>
            <t:dgCol title="申请数量" field="applyNum"  sortable="false" ></t:dgCol>
            <t:dgCol title="活动总金额" field="actAggregateAmo"  sortable="false" ></t:dgCol>
            <t:dgCol title="公司承担金额" field="companyAmo"  sortable="false"></t:dgCol>
            <t:dgCol title="陈列类型" field="displayType" dictionary="display_type"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="标准" field="standard"  sortable="false"></t:dgCol>

            <t:dgCol title="门店销售额" field="tarminalSale"  sortable="false"></t:dgCol>
            <t:dgCol title="门店销售费率" field="applyRateStr"  sortable="false"></t:dgCol>
            <t:dgCol title="备注" field="applyRemark"  sortable="false"></t:dgCol>
            <%--<t:dgCol title="门店结案费率" field="actualRateStr"  sortable="false"></t:dgCol>--%>


            <%--结案信息--%>
            <t:dgCol title="结案数量" field="actualNum"  sortable="false"></t:dgCol>
            <t:dgCol title="结案金额" field="actualAmount"  sortable="false"></t:dgCol>
            <%--<t:dgCol title="门店销售额" field="terminalSale"  sortable="false"></t:dgCol>--%>
            <t:dgCol title="备注" field="actualRemark"  sortable="false"></t:dgCol>
            <t:dgToolBar  title="导出" icon="icon-dataOut" url="report002Controller.do?exportXlsTer"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="查看销售额(含/不含)+费率(含/不含)" icon="icon-look" url=""  funname="viewData"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        $("#ttReportTerListtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("#ttReportTerListtb_r").find("input[name='flowDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#ttReportTerListtb_r").find("input[name='beginTime']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#ttReportTerListtb_r").find("input[name='endTime']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });

    function viewData(){
        var st =  $("#ttReportTerList").datagrid("getSelections");
        var title = "";
        if(st==null || st==""){
            tip("必须选择一条数据");
            return;
        }
        var url = "report002Controller.do?goTerSale&id="+st[0].id+"&terminalCode="+st[0].terminalCode;
        createwindow('查看',url,400,400);
    }
</script>
