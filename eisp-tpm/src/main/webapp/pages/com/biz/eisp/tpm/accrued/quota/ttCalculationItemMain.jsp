<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true" id="tdTemplateList">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttAccruedFormulaList" title="公式计算过程"  actionUrl="ttAccruedFormulaController.do?findTtAccruedFormulaItemList"
					idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="活动编码" field="actCode"  query="true" ></t:dgCol>
			<t:dgCol title="活动名称" field="actName" query="true" ></t:dgCol>
			<t:dgCol title="预提年月" field="yearMonth" query="true" ></t:dgCol>
			<t:dgCol title="是否满足条件" field="calculateType"  dictionary="yesorno"  query="true" ></t:dgCol>
			<t:dgCol title="计算结果" field="money"  ></t:dgCol>
			<t:dgCol title="操作" field="opt"></t:dgCol>
			<t:dgFunOpt title="计算过程" funname="expendTemplatePart(id)"></t:dgFunOpt>
			<t:dgToolBar  title="导出" operationCode="export" icon="icon-dataOut" url="ttAccruedFormulaController.do?exportXls" funname="excelExport"></t:dgToolBar>
		</t:datagrid>
	</div>
	<div data-options="region:'east',
		title:'',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
		 style="width: 800px; overflow: hidden;">
		<div class="easyui-panel" style="padding: 0;" fit="true" border="false" id="tdTemplatePartList"></div>
	</div>
</div>
<script type="text/javascript">
    function expendTemplatePart(id){
        if(li_east == 0){
            $('#tdTemplateList').layout('expand','east');
        }
        $('#tdTemplatePartList').panel("refresh", "ttAccruedFormulaController.do?goTtAccruedFormulaDetail&headId="+id);
    }

    $(document).ready(function(){
        //日期格式查询条件
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
    });
</script>
