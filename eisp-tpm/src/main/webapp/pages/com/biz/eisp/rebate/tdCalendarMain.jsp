<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="resources/jquery/calendar.js"></script>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center'" style="padding: 1px;">
		<div class="easyui-layout" data-options="fit:true">
			
			<div data-options="region:'center'" style="padding: 1px;">
        		<div id="calendar" class="easyui-fullCalendar" fit="true"></div>
        	</div>
        </div>
	</div>
</div>
<style type="text/css">
td{
	width: 14.28%;
}
.calendar-holidy{
	background-color:#E8EAEA;
}
#tip{
 	border:1px solid black;
	border-radius:3px;
 	background-color: #E8EAEA;
 	display: block;
 	width: 150px;
}
</style>
<script>
	//document.getElementsByClassName('calendar-selected');
	
	var dateList = '';
	var uploadDateList="${uploadDateList}";
	var _year;
	var _month;
	$(function(){
		$("#calendar").fullCalendar({
			width : 800,
			height : 500,
			//保存选中的日期，并改变样式
			onSelect : function (date, target) {
				var _date = $(target).attr('abbr')+',';
				//若该日期已选中，再次点击取消选中
				if(dateList.indexOf(_date) >= 0){
					dateList = dateList.replace(_date,'');
				}else{
					dateList += _date;
				}
			},
			//切换年月的时候显示当月选中的日期，以及读出数据库中已存入信息
			onChange : function (year, month) {
				_year = year;
				_month = month;
				//显示当月选中的日期
				var tdList = document.getElementsByTagName("td");
				for(var i = 1; i < tdList.length; i++){
					if(dateList.indexOf($(tdList[i]).attr('abbr')+',') >= 0){
						$(tdList[i]).addClass('calendar-selected').css('backgroundColor', '#E0ECFF');
					}
				}
				//对数据库中已存入信息
				findHolidays();
			}
		});
	})

	function findHolidays(){
	 if(uploadDateList){
		 var strs= new Array(); 
			strs=uploadDateList.split(","); //字符分割 
			for (i=0;i<strs.length ;i++ ){
				$("table td[abbr='"+strs[i]+"']").addClass('calendar-selected').css('backgroundColor', '#E0ECFF');
			}
	 }
		
	}
</script>
