<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>期初费用预算</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true"
 action="ttBudgetPeriodController.do?editTtBudgetPeriodAmount" refresh="true" >
	<input type="hidden" name="id" value="${vo.id }">
	
	<input type="hidden" id="minAmount" value="${minAmount}"/>
		
	<div class="form">
		<label class="Validform_label">期初金额: </label>
		<input name="amount" id="amount" onblur="validateAmount()" datatype="/^(([1-9]\d{0,11})|\d)(\.\d{1,2})?$/" errormsg="只能输入大于等于0的数字,最大不超过千亿,不超过两位小数"  class="inputxt" value="${vo.amount}" />
		<span style="color: red;">*</span>
		<span class="Validform_checktip" id="error">
			最小金额:${minAmount}
		</span>
	</div>

</t:formvalid>
</body>
</html>
<script type="text/javascript">
function validateAmount(){
	var min = $("#minAmount").val();
	if(min != ""){
		var amount = $("#amount").val();
		if(parseFloat(amount) < parseFloat(min)){
			$("#error").addClass("Validform_wrong");
			$("#error").attr("title","编辑金额不能小于最低金额");
			$("#amount").val("");
			tip("编辑金额不能小于最低金额");
		}
	}
}
</script>