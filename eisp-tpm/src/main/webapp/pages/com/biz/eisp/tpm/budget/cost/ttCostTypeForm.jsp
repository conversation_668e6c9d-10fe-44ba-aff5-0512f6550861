<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>活动大类</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttCostTypeController.do?saveCostType" refresh="true" beforeSubmit="validateShareOrg()">
 	<input id="id" name="id" type="hidden" value="${costType.id }"  />
    <input name="actPositionCode" type="hidden" value="${costType.actPositionCode}" class="inputxt"></input>
    <input name="actRoleCode" type="hidden" value="${costType.actRoleCode}" class="inputxt"></input>

	 <div class="form">
	      <label class="Validform_label">活动大类编号: </label>
	      <input id="costTypeCode" name="costTypeCode"
                 class="inputxt" value="${costType.costTypeCode }"
                 dataType="/^[0-9a-zA-Z\,`~·~\<\>，。？\-：；《》【】‘’!！（）“”—……、\?\|\{\}\[\]\\\.\/\(\)]{1,30}$/"
                 ajaxUrl = "ttCostTypeController.do?validateCode&id=${costType.id}"
          >
	 </div>
    
     <div class="form">
        <label class="Validform_label">活动大类名称:</label>
        <input  ajaxUrl="ttCostTypeController.do?validTtCostTypeName&id=${costType.id}"
        		name="costTypeName" 
	            datatype="/^[0-9a-zA-Z\u4e00-\u9fa5,\.\/-]{1,30}$/" 
                class="inputxt" value="${costType.costTypeName }" 
                errormsg="只能录入汉字，数字，大小写英文" />
        <span style="color: red">*</span>
    </div>
    
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">财务费用归类: </label>
		<t:dictSelect id="costClassify" field="costClassify" type="select" defaultVal="${costType.costClassify}"
			 typeGroupCode="costType"  hasLabel="true" title="费用归类" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>    
	<%--<div class="form">
		<label class="Validform_label">业务费用归类: </label>
		<t:dictSelect id="businessCostType" field="businessCostType" type="select" defaultVal="${costType.businessCostType}"
			 typeGroupCode="business_cost_type"  hasLabel="true" title="业务费用归类" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>      --%>
     <div class="form">
        <label class="Validform_label">关联预算科目: </label>
        <t:comboBox url="ttFinancialAccountController.do?getFinancialAccountForCombobox"
        		    required="true" name="financialAccountCode"  width="150" defaultVal="${costType.financialAccountCode }">
        </t:comboBox>
        <input type="hidden" id="financialAccountName" name="financialAccountName" value="${vo.financialAccountName}">
        <span style="color: red;">*</span>
    </div>
    
	 <div class="form">
        <label class="Validform_label">关联活动细类: </label>
        <textarea name="costAccountName" id="costAccountName" readonly="readonly" style="resize:none;width:150px;height:60px;">${costType.costAccountName}</textarea>
        <span style="color: red">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openCostAccountSelect();"></a>
        <input type="hidden" id="costAccountCode" name="costAccountCode" value='${costType.costAccountCode}' datatype="*">
        <span class="Validform_checktip" id="errorAccount"></span>
    </div>
    
    <!-- 可选部门 -->
    <div class="form">
        <label class="Validform_label">可选部门: </label>
        <textarea name="orgNames" id="orgNames" readonly="readonly" datatype="*" style="resize:none;width:150px;height:60px;">${costType.orgNames}</textarea>
        <span style="color: red">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openOrgSelect(0);"></a>
        <input type="hidden" id="orgCodes" name="orgCodes" datatype="*" value="${costType.orgCodes}">
        <span class="Validform_checktip" id="errorOrg"></span>
    </div>
    
    <!-- 是否滚动,默认0 -->
    <input type="hidden" name="hasBudgetRoll" value="0">
    <input type="hidden" name="hasCustomerBudget" value="0">
    <input type="hidden" name="hasQuota" value="0">

     <div class="form">
        <label class="Validform_label">备注: </label>
        <textarea name="remark" style="resize:none;width:150px;height:60px;">${costType.remark }</textarea>
    </div>
</t:formvalid>
</body>
<script type="text/javascript">
	$(function(){
		var has = $("#hasOrgShare").val();	
		if(has == '1'){
			$("#orgShareForm").show();
			$("#orgShareCodes").attr("datatype","*");
		}else{
			$("#orgShareCodes").removeAttr("datatype");
		}
	});
	function validateShareOrg(){
		var has = $("#hasOrgShare").val();
		if(has == '0'){
			return true;
		}
		var orgShare = $("#orgShareCodes").val();
		if(orgShare == ""){
			return false;
			$("#errorShare").addClass("Validform_wrong").attr("title","请填写信息");
		}else{
			$("#errorShare").addClass("Validform_right").removeAttr("title");
		}
	}

	function showDiv(){
		var isOrNo = $("#hasOrgShare").val();
		if(isOrNo == "1"){
			$("#orgShareNames").val("");
			$("#orgShareCodes").val("");
			$("#orgShareForm").show();
			$("#orgShareCodes").attr("datatype","*");
		}else{
			$("#orgShareCodes").removeAttr("datatype");
			$("#orgShareForm").hide();
		}
	}
	
	function openCostAccountSelect(){
		/* popSelectClick("costAccountCode", "costAccountName","accountName",
				"ttCostTypeController.do?goSelectCostAccountMain&costTypeId=${costType.id}&accessEntry="+accessEntry,
						"ttCostAccountSelectedList",'undefined',1000,450); */
 		safeShowDialog({
			content:"url:ttCostTypeController.do?goSelectCostAccountMain&costTypeId=${costType.id}&accessEntry="+accessEntry,
			lock:true,
			title:'选择',
			width:1000,
			height:450,
			left:'85%',
			cache:false,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rowsData = iframe.$('#ttCostAccountSelectedList').datagrid('getRows');
				if ((rowsData == '' || rowsData == null) && validFunction == null) {
					tip("请至少选择一条数据");
					return false;
				} else {
					$("#costAccountCode").val(JSON.stringify(rowsData));
					var str1 = "";
					var str2 = "";
					$.each(rowsData, function(i, n) {
						if (i == 0){
							str1 += n.accountName;
							str2 += n.accountCode;
						}else {
							str1 += ",";
							str2 += ",";
							str1 += n.accountName;
							str2 += n.accountCode;
						}
					});
					$("#costAccountName").val(str1);
					$("#costAccountCode").val(str2);
					$("#errorAccount").removeClass("Validform_wrong").addClass("Validform_right");
					return true;
				}
			},
			cancelVal : '关闭',
			cancel : true
		});
	}
	//可选部门弹窗
	function openOrgSelect(type){
		var costTypeCode = '${costType.costTypeCode }';
		var orgCodes = $("#orgCodes").val();
        var orgCode = $('#orgCode').val();
        var paraArr = {
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型--不传或默认
            isCouldRemove:true,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            callBackFun : saveLog,
            pageData:{
                orgCode : orgCodes
            }
        }
        openChooseOrgSelect(paraArr);
/*
		var url = "tmCommonMdmController.do?gotmOrgSelectMain";
		createwindowExt('选择',url,1000,500,{
			ok:function(){
				var codes = [];
				var names = [];
				iframe = this.iframe.contentWindow;
				var data = iframe.$("#tmHasSelectedOrg").datagrid("getRows");
				var length = data.length;
				for(var i = 0;i<length;i++){
					var code = data[i].orgCode;
					var name = data[i].orgName;
					codes.push(code);
					names.push(name);
				}
				$("#orgNames").val("");
				$("#orgCodes").val("");
				$("#orgNames").val(names.join(','));
				$("#orgCodes").val(codes.join(','));
				$("#errorOrg").removeClass("Validform_wrong").addClass("Validform_right");
			}
		});*/
	}
	function saveLog(data){
	    var orgNames = [];
	    var orgCodes = [];
        $.each(data,function(){
            var obj = $(this);
            var orgName = obj[0].orgName;
            var orgCode = obj[0].orgCode;
            orgNames.push(orgName);
            orgCodes.push(orgCode);
        });
        $("#orgNames").val(orgNames.join(","));
        $("#orgCodes").val(orgCodes.join(","));
    }
</script>
</html>



