<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="FridgeRebateStatementList" fitColumns="false" title="冰柜台账及返利报表"
                    actionUrl="ttFridgeRebateStatementController.do?fridgeRebateStatementList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="销售部" field="saleDept" width="150"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName" query="true" width="150"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="客户名称" field="customerNames" query="true"  hidden="true" width="200" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="false" width="150"></t:dgCol>
            <t:dgCol title="客户合作性质" field="nature" dictionary = "cooperative_type" query="true" width="150"></t:dgCol>
            <t:dgCol title="所属门店编码" field="terminalCode" width="150" query="true"></t:dgCol>
            <t:dgCol title="所属门店名称" field="terminalName" width="150" query="true"></t:dgCol>
            <t:dgCol title="冰柜编码" field="fridgeCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="冰柜品牌" field="fridgeSupplier" dictionary="fridge_brand" query="true" width="150"></t:dgCol>
            <t:dgCol title="冰柜类型" field="fridgeType" dictionary="fridge_type" width="150"></t:dgCol>
            <t:dgCol title="冰柜型号" field="fridgeModel"  query="true" width="150"></t:dgCol>
            <t:dgCol title="冰柜规格" field="fridgeStandard" query="true" width="150"></t:dgCol>
            <t:dgCol title="冰柜总价值" field="fridgeWorth" width="150"></t:dgCol>
            <t:dgCol title="购买时间" field="purchaseDate" formatter="yyyy-MM-dd" query="true" width="150"></t:dgCol>
            <t:dgCol title="购买类型"  field="purchaseType" dictionary="purchase_type" query="true"   hidden="false"  queryMode="single"  ></t:dgCol>

            <t:dgCol title="开始返利日期" formatter="yyyy-MM-dd" field="beginRebateDate" width="150"></t:dgCol>
            <t:dgCol title="结束返利日期" formatter="yyyy-MM-dd" field="endRebateDate" width="150"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" query="true" formatter="yyyy-MM" queryMode="group" width="150"></t:dgCol>

            <t:dgCol title="返款总额" field="rebateAmount" width="150"></t:dgCol>
            <t:dgCol title="已返利金额" field="applyAmount" width="150"></t:dgCol>
            <t:dgCol title="剩余返利金额" field="balance"  width="150"></t:dgCol>
            <t:dgCol title="返利状态" field="rebateStatus"  dictionary="rebate_status" query="true" width="150"></t:dgCol>
            <t:dgCol title="备注" field="remark" width="150"></t:dgCol>

            <t:dgToolBar operationCode="add" title="批量提交返利申请" icon="icon-add" url="ttFridgeRebateController.do?goTtFridgetBatchMain" funname="openSubit"></t:dgToolBar>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttFridgeRebateStatementController.do?exportXls" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar operationCode="dataDetailOut" title="导出返利明细" icon="icon-dataOut" url="ttFridgeRebateStatementController.do?exportRebateDetailXls" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="导入" operationCode="datain" icon="icon-add" url=""
                         onclick="importDataByXmlDMS({gridName:'ttFridgeMainList'});"
                         height="450" width="900"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script>

    $(document).ready(function () {
        $("input[name='customerNames']").attr("readonly",true).attr("id","customerNames").click(function(){addCustomers();}).parent().append("<input type='hidden' name='customerCodes' id='customerCodes'/>");
    });

    //年月格式化
    $("#FridgeRebateStatementListtb_r").find("input[name='purchaseDate']").addClass("Wdate").css({
        'height': '20px',
        'width': '100px'
    }).click(function () {
        WdatePicker({dateFmt: 'yyyy-MM-dd'});
    });
    $("#FridgeRebateStatementListtb_r").find("input[name='yearMonth_begin']").addClass("Wdate").css({
        'height': '20px',
        'width': '100px'
    }).click(function () {
        WdatePicker({dateFmt: 'yyyy-MM'});
    });
    $("#FridgeRebateStatementListtb_r").find("input[name='yearMonth_end']").addClass("Wdate").css({
        'height': '20px',
        'width': '100px'
    }).click(function () {
        WdatePicker({dateFmt: 'yyyy-MM'});
    });
    $(function () {
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='createDate']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='periodStart']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });

    $("#dmsNoticeListtb_r").find("input[name='periodEnd']").addClass("Wdate")
        .css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd'
        });
    });
    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#FridgeRebateStatementList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "costTypePositionController.do?deleteCostTypePosition&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#FridgeRebateStatementList").datagrid("reload");
                    }
                });
            }
        });
    }

    //可选部门弹窗
    function openSubit(){
        safeShowDialog({
            content : "url:ttFridgeRebateController.do?goTtFridgetBatchMain",
            lock : true,
            title : "审批进度",
            width : 1200,
            height : 800,
            left :'50%',
            cache : true,
            cancel:true,
            button:[{name:"确定",callback:function(){
                var flag = false;
                iframe = this.iframe.contentWindow;
                iframe.aaa();
                $("#ttFridgeMainList").datagrid("reload");
                return flag;
            }}],cancelVal:'关闭'
        });
    }
    function importDataByXmlDMS(cfg) {
        gridname=cfg.gridName;
        $.dialog({
            content:"url:ttFridgeRebateImportController.do?importCg",
            lock : true,
            title: "冰柜台账及返利导入",
            max:false,
            width:660,
            height:400,
            button: [
                {
                    name: "确定",
                    callback: function(){
                        iframe = this.iframe.contentWindow;
                        iframe.upload();
                        return false;
                    }
                },

                {
                    name: "取消",
                    callback: function(){
                        this.close();
                    }
                }
            ]
        }).zindex();
    }
    
    function addCustomers () {
        var paraArr = {
            textName: 'customerCode,customerName',
            inputTextName: 'customerCodes,customerNames',
            searchType:'1',//查询类型？？
            encapsulationType:'input',//封装类型
            pageData:{
                customerCodes : customerCodes
            }
        }
        openChooseCustomerSelect(paraArr);
    }

    //-----------选择组织end---------------//
    //-----------选择客户star--------------//
    function openChooseCustomerSelect(dataArr){
        createwindowExt("添加客户","tmCommonMdmController.do?goSelectCustomerMain&exclusiveCodes=" + dataArr.pageData.customerCodes,1000,600,{
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#ttCustomerSeletedList').datagrid('getRows');
                if (rowsData == '' || rowsData == null) {
                    iframe.tip("请添加已选客户");
                    return false;
                }
                //返回赋值
                return returnAssign(iframe,rowsData,dataArr);
            },
            cancelVal : '关闭',
            cancel : true
        });
        safeShowDialog(myOptions);
    }
    //-----------选择客户end---------------//
    //-----------选择产品star--------------//
</script>
