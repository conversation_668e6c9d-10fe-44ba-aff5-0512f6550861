<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div id="system_org_orgList" class="easyui-layout" fit="true">
	<div region="center">
		<t:datagrid name="customer_dg" actionUrl="" autoLoadData="false" onClick="clickCustomer"
	  		idField="customerCode" fit="true"  fitColumns="true"  >
 		    <t:dgCol field="customerCode" title="客户编码" width="200" ></t:dgCol> 
  		    <t:dgCol field="customerName" title="客户名称" width="200" ></t:dgCol>
		    <t:dgCol field="amount" title="期初金额" editor="{type:'numberbox',options:{precision:2,min:0}}" width="200" ></t:dgCol>
		    <t:dgCol field="remark" title="备注" editor="{type:'text'}" width="200" ></t:dgCol>
		</t:datagrid>
				<div id="customer_dgtb" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-add" onclick="addCustomer()">添加客户</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="deleteCustomer()">移除客户</a>
						</span>
							<span style="float:right">
							<span style="color:red;line-height:30px;margin-right:50px;" >可分配金额：<span id="availableAmount">${vo.availableAmount}</span></span>
						</span>
						<span style="float:right">
							<span style="color:red;line-height:30px;margin-right:50px;" >已分配金额：<span id="assignedAmount">${vo.assignedAmount}</span></span>
						</span>
					
						<span style="float:right">
							<span style="color:red;line-height:30px;margin-right:50px;" >可用余额：<span id="balance">${vo.balance}</span></span>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">
					</div>
				</div>	
	</div>
	
	<div region="west"  style="width: 300px;">
	<t:formvalid formid="formobj" layout="div" dialog="true" action="ttBudgetCustomerController.do?decomposeBudgetCustomer"
	 refresh="true" beforeSubmit="validateOnly()" callback="refresh">
	 	<div class="form">
			<label class="Validform_label">年度: </label>
			 <input readonly="readonly" class="inputxt" name="year" value="${vo.year}">
		    <span style="color: red">*</span>
		</div>
			<div class="form">
			<label class="Validform_label">季度: </label>
			 <input readonly="readonly" class="inputxt" name="quarter" value="${vo.quarter}">
		    <span style="color: red">*</span>
	   </div>
		<div class="form">
			<label class="Validform_label">组织: </label>
			 <input readonly="readonly"  type="hidden" name="orgCode" value="${vo.orgCode}">
			 <input readonly="readonly" class="inputxt" name="orgName" value="${vo.orgName}">
		    <span style="color: red">*</span>
		</div>
			<div class="form">
			<label class="Validform_label">费用类型: </label>
		    <input readonly="readonly" type="hidden" name="costTypeCode" value="${vo.costTypeCode}">
		       <input readonly="readonly" class="inputxt" name="costTypeName" value="${vo.costTypeName}">
		    <span style="color: red">*</span>
		</div>
		<input type="hidden" name="tableJson">
		
	</t:formvalid>
	</div>
</div>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script src="resources/tools/Map.js"></script>
<script type="text/javascript">
var customerMap=new Map();
function clickCustomer(rowIndex, rowData){
	//开始编辑
	//console.log("rowIndex:"+rowIndex);
	$("#customer_dg").datagrid('beginEdit',rowIndex);
		
}
/**
 * 删除客户
 */
function deleteCustomer(){
	var rows = $('#customer_dg').datagrid("getSelections");
	for (var i = rows.length - 1; i >= 0; i--) {
		var index = $('#customer_dg').datagrid('getRowIndex',rows[i]);
	     var row=rows[i];
	 	if(customerMap.containsKey(row.customerCode)){
	 	    customerMap.remove(row.customerCode);
			$('#customer_dg').datagrid('deleteRow', index);		
	 	} 
	}	
}
	
/**
 * 添加客户
 */
function addCustomer(){
	createwindowExt("添加客户",
			"ttBudgetCustomerController.do?goSelectCustomerMain&orgCode=${vo.orgCode}",
			1000,600,{
		            ok : function() {
		                iframe = this.iframe.contentWindow;
		                var rowsData = iframe.$('#ttCustomerSeletedList').datagrid('getRows');
		    			if (rowsData == '' || rowsData == null) {
		    				tip("请添加已选客户");
		    				return false;
		    			} 
		    			for(var i=0;i<rowsData.length;i++){			
		    				if(!customerMap.containsKey(rowsData[i].customerCode)){
		    					$('#customer_dg').datagrid('appendRow',{
				    				"customerCode": rowsData[i].customerCode,
			    				      "customerName":rowsData[i].customerName
			    				});	
		    					customerMap.put(rowsData[i].customerCode,rowsData[i].customerName);
								var rowDatas=$("#customer_dg").datagrid('getRows');
								$("#customer_dg").datagrid('beginEdit',rowDatas.length-1);	
		    					
		    				}
			    					
		    			}	    			    		
		                return true;
		            },
		            cancelVal : '关闭',
		            cancel : true
		     });
	
}
	
	
/**
 * 获取表格数据，验证
 */
function validateOnly(){
	var tableData=[];
	var totalAmount=0;
       var allRows=$('#customer_dg').datagrid('getRows');
		if (allRows.length > 0) {
			for ( var i = 0; i < allRows.length; i++) {
				$("#customer_dg").datagrid('endEdit',i);					
					var tableItem={
						customerCode:allRows[i].customerCode,
						customerName:allRows[i].customerName,
						amount:allRows[i].amount,
						remark:allRows[i].remark
					};
				tableData.push(tableItem);
			}

			$("input[name='tableJson']").val(JSON.stringify(tableData));		
	}	
}
</script>

