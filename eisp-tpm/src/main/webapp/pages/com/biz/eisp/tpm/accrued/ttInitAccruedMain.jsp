<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="ttInitAccruedList" fitColumns="false" title="奶粉往期预提"
                    actionUrl="ttInitAccruedController.do?findTtInitAccruedList" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="预提年月" field="accruedYearMonth"  query="true" ></t:dgCol>
            <t:dgCol title="活动年月" field="actYearMonth" query="true" ></t:dgCol>
            <t:dgCol title="预提类型" field="accruedType" dictionary="accrued_type"  query="true" ></t:dgCol>
            <t:dgCol title="往期预提类型" field="accruedDataType" dictionary="milk_init_accrued_type"  query="true" ></t:dgCol>
            <t:dgCol title="组织" field="orgName" query="true" ></t:dgCol>
            <t:dgCol title="SAP成本中心" field="costCenter"  query="true" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" query="true" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true" ></t:dgCol>
            <t:dgCol title="财务科目ERP编码" field="finacialAccountCode" query="true"></t:dgCol>
            <t:dgCol title="预算科目编码" field="finacialCode"  ></t:dgCol>
            <t:dgCol title="预算科目" field="finacialName" query="true" ></t:dgCol>
            <t:dgCol title="产品名称" field="productName"  ></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode"  ></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeName" query="true" ></t:dgCol>
            <t:dgCol title="活动细类编码" field="costAccountCode" ></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountName" query="true" ></t:dgCol>
            <t:dgCol title="活动开始时间" field="beginDate"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate"  ></t:dgCol>
            <t:dgCol title="支付方式" field="paymentCode"  dictionary="payment_type"></t:dgCol>
            <t:dgCol title="预提金额(元)" field="accruedAmount"  ></t:dgCol>
            <t:dgCol title="创建人" field="createName" hidden="true"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" ></t:dgCol>
            <t:dgCol title="备注" field="remark"></t:dgCol>

            <t:dgToolBar operationCode="add" title="新增" height="600" width="400" icon="icon-add" url="ttInitAccruedController.do?goTtInitAccruedForm" funname="add"></t:dgToolBar>
            <%--<t:dgToolBar operationCode="importProduct" title="导入产品预提" height="300" width="400" icon="icon-add" url="ttProductPolicyAccruedController.do?goDoAccruedForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="importMilk" title="导入奶粉预提" height="300" width="400" icon="icon-add" url="ttProductPolicyAccruedController.do?goDoAccruedForm" funname="addMilk"></t:dgToolBar>--%>
            <t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'ttInitAccrued', gridName:'ttInitAccruedList'})"></t:dgToolBar>
            <t:dgToolBar  title="导出" operationCode="export" icon="icon-dataOut" url="ttInitAccruedController.do?exportXls" funname="excelExport"></t:dgToolBar>
            <%--<t:dgToolBar title="日志" operationCode="log" icon="icon-log" url="ttQuotaAccruedController.do?goTtDirectIncomeLogMain" funname="detail" width="1200"></t:dgToolBar>--%>
        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $(function () {
        $("#ttInitAccruedListtb_r").find("input[name='accruedYearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("#ttInitAccruedListtb_r").find("input[name='actYearMonth']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("#ttInitAccruedListtb_r").find("input[name='beginDate']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

        $("#ttInitAccruedListtb_r").find("input[name='endDate']")
            .addClass("Wdate").css({'height':'20px','width':'100px'})
            .click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });
</script>
