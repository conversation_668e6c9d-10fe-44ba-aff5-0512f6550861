<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
	<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttBudgetBillList"  fitColumns="false" title="客户折扣预算"
                    actionUrl="ttBudgetCustomerController.do?findCustomerBudgetList&year=${year}&quarter=${quarter}&orgCode=${orgCode}&costTypeCode=${costTypeCode}" idField="id" fit="true"
                    queryMode="group" pageSize="20">
            <t:dgCol title="id" field="id"  queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="预算编码" field="budgetCustomerCode" query="true"></t:dgCol>
            <t:dgCol title="客户编号" field="customerCode" query="true"></t:dgCol>
			<t:dgCol title="客户名称" field="customerName" query="true"></t:dgCol>
			<t:dgCol title="期初金额" field="amount"></t:dgCol>
			<t:dgCol title="可用余额" field="balanceAmount"></t:dgCol>
	  		<t:dgCol title="生效状态" field="enableStatus" dictionary="enable_status"></t:dgCol>
	  		<t:dgCol title="停用金额" field="disableAmount"></t:dgCol>
            <t:dgCol title="创建人" field="createName"></t:dgCol>
  		    <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
  		    <t:dgCol title="最近更新人" field="updateName"></t:dgCol>
  		    <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>			  		
	  		
	  		<t:dgToolBar title="录入费用预算" icon="icon-add" url="ttBudgetCustomerController.do?goCustomerBudgetForm" funname="add"></t:dgToolBar>
	  		<t:dgToolBar title="编辑期初金额" icon="icon-edit" onclick="editAmount()"></t:dgToolBar>
	  		<t:dgToolBar title="删除" icon="icon-remove" url="" funname="deleteAmount()"></t:dgToolBar>
	  		<t:dgToolBar title="导入" icon="icon-dataIn" onclick="importDataByXml({impName:'ttBudgetCustomer',gridName:'ttBudgetBillList'})"></t:dgToolBar>
	  		<t:dgToolBar title="导出" icon="icon-dataOut" url="ttBudgetCustomerController.do?exportXls" funname="excelExport"></t:dgToolBar>
	  		<t:dgToolBar title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detailLog" width="1200" height="460"></t:dgToolBar>
	  		<t:dgToolBar title="启用" icon="icon-start" url="" funname="openBudget()"></t:dgToolBar>
	  		<t:dgToolBar title="停用" icon="icon-stop" url="" funname="closeBudget()"></t:dgToolBar>
	  		<t:dgToolBar title="预算使用明细" icon="icon-yusuanshiyongmingxi" url="" funname="budgetBillDetail()"></t:dgToolBar>
        </t:datagrid>

    </div>
</div>


<script type="text/javascript">
$("#ttBudgetBillListtb_r").find("input[name='year']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy'});});
$("input[name='orgId']").combotree({
	url:'tmOrgController.do?getParentOrg'
});
//预算使用明细
function budgetBillDetail(gridname){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelections");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("必须选择一条数据");
		return;
	}
	var orgCode = seletctTarget[0].orgCode;
	var costTypeCode = seletctTarget[0].costTypeCode;
	var year = seletctTarget[0].year;
	var quarter = seletctTarget[0].quarter;
	var customerCode = seletctTarget[0].customerCode;
	
	var url = "ttBudgetDetailCustomerController.do?goTtBudgetCustomerDetailMain&year="+year+"&quarter="+quarter+
			  "&orgCode="+orgCode+"&costTypeCode="+costTypeCode+"&customerCode="+customerCode;
	add('预算使用明细',url,gridname,600,400);
}

//删除
function deleteAmount(){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("选择一条数据");
		return;
	}
	var flag = true;
	var vo = seletctTarget;
	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsDelete&year="+vo.year+"&quarter="+vo.quarter+
	  "&orgCode="+vo.orgCode+"&costTypeCode="+vo.costTypeCode+"&customerCode="+vo.customerCode;
	$.ajax({url:url,type:"post",async:false,success:function(data){
		var d = $.parseJSON(data);
		if(d.success == false){
			tip(d.msg);
			flag = false;
		}
	}});
	if(flag){
		$.messager.confirm('操作提示','确定删除?',function(r){ 
		    if (r){
		    	$.ajax({
		        	type : "POST",
		        	url : "ttBudgetCustomerController.do?deleteBudgetCustomer&ids="+vo.id,
		        	dataType : "json",
		        	async:false,
		        	cache : false,
		        	success : function(data) {
		        		tip(data.msg);
		        		reloadTable();
		        	}
			   });
		    }
		});
	}
}
//编辑
function editAmount(gridname){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("选择一条数据");
		return;
	}
	var vo = seletctTarget;
	
	var flag = true;
	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsEdit&year="+vo.year+"&quarter="+vo.quarter+
			  "&orgCode="+vo.orgCode+"&costTypeCode="+vo.costTypeCode+"&customerCode="+vo.customerCode;
	$.ajax({url:url,type:"post",async:false,success:function(data){
		var d = $.parseJSON(data);
		if(d.success == false){
			tip(d.msg);
			flag = false;
		}
	}});
	
	if(flag == true){
		var openUrl = "ttBudgetCustomerController.do?goBudgetCustomerEditAmountForm&budgetCustomerCode="+vo.budgetCustomerCode;
		createwindow('编辑期初金额',openUrl,500,400);
	}
}
//启用页面
function openBudget(gridname){
	var seleted = $("#ttBudgetBillList").datagrid('getSelected');
	if(seleted == null){
		tip("请选择一条要操作的数据");
		return;
	}
	if(seleted.enableStatus == 0){
		tip("已经处于启用状态,无需再次启用");
		return false;
	}
	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsClose&budgetCustomerCode="+seleted.budgetCustomerCode;
	var flag = false;
	$.ajax({
		url:url,
		type:"post",
		async:false,
		success:function(data){
			if(data == 1){//停用
				flag = true;
			}
		}
	});
	if(flag){
		url = "ttBudgetCustomerController.do?goBudgetCustomerOpenForm&budgetCustomerCode="+seleted.budgetCustomerCode;
		createwindow('启用',url,400,400);
	}else{
		tip("已经处于启用状态,不需要再启用");
	}
}
//停用页面
function closeBudget(gridname){
	var seleted = $("#ttBudgetBillList").datagrid('getSelected');
	if(seleted == null){
		tip("请选择一条要操作的数据");
		return;
	}
	if(seleted.enableStatus == 1){
		tip("已经处停用用状态,无需再次停用");
		return false;
	}
	var url = "ttBudgetCustomerController.do?validateBudgetCustomerIsClose&budgetCustomerCode="+seleted.budgetCustomerCode;
	var flag = false;
	$.ajax({
		url:url,
		type:"post",
		async:false,
		success:function(data){
			if(data == 0){//启用
				flag = true;
			}
		}
	});
	if(flag){
		url = "ttBudgetCustomerController.do?goBudgetCustomerCloseForm&budgetCustomerCode="+seleted.budgetCustomerCode;
		createwindow('停用',url,450,400);
	}else{
		tip("已经处于停用状态,不需要再停用");
	}
}

</script>