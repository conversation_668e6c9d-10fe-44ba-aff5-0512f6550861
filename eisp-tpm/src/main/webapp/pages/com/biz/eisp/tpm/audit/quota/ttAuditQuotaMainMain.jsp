<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div  data-options="region:'center',collapsed:false,split:true,title:'结案申请'"  style="border:0px;height:633px;">
		<t:datagrid name="ttAuditQuotaMainList" title=""
					actionUrl="ttAuditQuotaMainController.do?findTtAuditQuotaMainList"
					idField="id"
					fit="true"
					fitColumns="true"
					pagination="true"
					queryMode="group"
					singleSelect="true">
			<t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
			<t:dgCol field="bpmStatus" title="审批状态" dictionary="bpm_status" query="true" ></t:dgCol>
			<t:dgCol field="billCode" title="结案申请单号" query="true" ></t:dgCol>
			<t:dgCol field="billName" title="结案申请名称" query="true"></t:dgCol>
			<t:dgCol field="actTypeName" title="结案类型" query="true"></t:dgCol>

			<t:dgCol field="createName" title="结案申请人"   ></t:dgCol>
			<t:dgCol field="updateDate" title="结案申请时间" formatter="yyyy-MM-dd hh:mm:ss"  ></t:dgCol>
			<t:dgCol field="remark" title="备注"   ></t:dgCol>


			<t:dgToolBar title="创建结案申请" operationCode="add" icon="icon-add" url="ttAuditQuotaMainController.do?goTtAuditQuotaMainForm" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="ttAuditQuotaMainController.do?goTtAuditQuotaMainForm" funname="auditQuotaMainUpdate"></t:dgToolBar>
			<%--<t:dgToolBar title="附件" operationCode="upload" icon="icon-upload" url="" funname="fileUploadTotal"></t:dgToolBar>--%>
			<t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="ttAuditQuotaMainController.do?deleteTtAuditQuotaMain" funname="deleteALLAuditMainSelect"></t:dgToolBar>
			<t:dgToolBar title="流程日志" operationCode="log" icon="icon-log"  url="ttAuditMainLogController.do?goTtAuditMainLogMain"  funname="detail" width="1200"></t:dgToolBar>
			<t:dgToolBar title="提交审批" operationCode="conventional" icon="icon-ok" onclick="submitLeave()"></t:dgToolBar>
		</t:datagrid>

	</div>
	<div  style="height:350px;" data-options="region:'south',collapsed:false,split:true,title:'结案明细'" >
		<t:datagrid name="ttAuditQuotaList" fit="true" fitColumns="true" singleSelect="true"
					title=""
					queryMode = "group"
					actionUrl="ttAuditQuotaController.do?findTtAuditQuotaList"
					idField="id"
		            autoLoadData="true"
                    onLoadSuccess="loadTotal">

			<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="部门编码"  field="orgCode" hidden = "true"></t:dgCol>
			<t:dgCol title="活动发布要求" field="actDeployRequire" hidden="true"></t:dgCol>
			<t:dgCol title="结案主单主键" field="businessKey" hidden="true"></t:dgCol>

			<t:dgCol title="审批状态"  field="bpmStatus" query="true" dictionary="bpm_status" frozenColumn = "true"></t:dgCol>
			<t:dgCol title="活动类型"  field="actModeCode" dictionary="audit_act_mode_type" frozenColumn = "true"></t:dgCol>
			<t:dgCol title="客户名称"  field="customerName" query="true" frozenColumn = "true"></t:dgCol>
			<t:dgCol title="活动名称"  field="actName" query = "true" frozenColumn = "true"></t:dgCol>
			<t:dgCol title="活动编号"  field="actCode" query = "true"></t:dgCol>
			<t:dgCol title="是否到门店"  field="containTerminal" dictionary="yesorno" query ="true" ></t:dgCol>
			<t:dgCol title="部门名称"  field="orgName" ></t:dgCol>






			<t:dgCol title="产品名称"  field="productName" query="true"></t:dgCol>
			<t:dgCol title="活动细类"  field="costAccountName" query = "true"></t:dgCol>
			<t:dgCol title="活动开始时间" field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
			<t:dgCol title="活动结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>
			<t:dgCol title="活动申请数量"  field="planQuantity"></t:dgCol>
			<t:dgCol title="活动申请金额"  field="planAmount"></t:dgCol>
			<t:dgCol title="超额核销比(%)"  field="overAuditScale" ></t:dgCol>
			<t:dgCol title="申请结案数量 "  field="applyAuditQuantity" editor="{type:'numberbox',options:{precision:1,min:0}}"></t:dgCol>
			<t:dgCol title="申请结案金额"  field="applyAuditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
			<t:dgCol title="归属事业部"  field="businessUnitName"></t:dgCol>
			<t:dgCol title="审核结案数量"  field="auditQuantity"></t:dgCol>
			<t:dgCol title="审核结案金额"  field="auditAmount"></t:dgCol>
			<t:dgCol title="最终结案金额"  field="realAuditAmount" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>


			<t:dgCol title="支付方式"  field="paymentCode" dictionary="payment_type"></t:dgCol>
			<t:dgCol title="货补产品"  field="premiumProductName"></t:dgCol>
			<t:dgCol title="结案状态"  field="auditStatus" editor="{type:'combobox'}" dictionary="audit_bkw_status"></t:dgCol>
			<t:dgCol title="结案申请单号" field="billCode" ></t:dgCol>
			<t:dgCol title="结案明细编码"  field="auditCode" ></t:dgCol>
			<t:dgCol title="附件数量" field="fileNumber" ></t:dgCol>
			<t:dgCol title="备注" field="workFlowRemark"  editor="{type:'text'}"></t:dgCol>


			<t:dgToolBar title="添加费用明细" operationCode="chadd"  url="ttAuditQuotaController.do?goTtQuotaActSelectMain"  funname="goSelectQuotaActAdd"  icon="icon-add"   ></t:dgToolBar>
			<t:dgToolBar title="门店结案金额录入"  operationCode="chaddterminal"  url="ttAuditQuotaController.do?goTtQuotaTerminalSelectMain"  funname="goSelectQuotaTerminalAdd"  icon="icon-add"   ></t:dgToolBar>
			<t:dgToolBar title="查看门店结案金额" operationCode="chlookterminal"  url="ttAuditQuotaController.do?goTtQuotaTerminalSelectedMain"  funname="terminalDetail"  icon="icon-look"  width = "1500" height = "500"></t:dgToolBar>
			<t:dgToolBar title="保存" operationCode="chsave" icon="icon-save" onclick="saveQuotaAuditForm()" ></t:dgToolBar>
			<t:dgToolBar title="移除" operationCode = "chremove" icon="icon-remove" url="ttAuditQuotaController.do?deleteTtQuotaAudit" funname="deleteALLAuditSelect"></t:dgToolBar>
			<t:dgToolBar title="附件" operationCode = "chupload" icon="icon-upload" url="" funname="fileUpload"></t:dgToolBar>
			<t:dgToolBar title="客户导出" operationCode = "chcustdataout" icon="icon-dataOut" url="ttAuditMainExportController.do?exportCustomerXls" funname="excelCustomerExport"></t:dgToolBar>
			<t:dgToolBar title="门店导出" operationCode = "chterminaldataout" icon="icon-dataOut" url="ttAuditMainExportController.do?exportTerminalIncludeImgXls" funname="excelTerminalExport"></t:dgToolBar>
			<t:dgToolBar title="查看客户图片" operationCode = "chlookimg" url=""  funname="detailPicture"  icon="icon-look"  width = "1000" height = "500"></t:dgToolBar>
			<t:dgToolBar title="日志" operationCode = "chlog" url="tmLogController.do?goTmLogDetailMain"  funname="detailLog"  icon="icon-log"  width = "1000" height = "500"></t:dgToolBar>


		</t:datagrid>
	</div>
	<div id="btn_bkw" onclick="reloadBkwTable()"></div>
</div>
<script type="text/javascript">
	//提交审批
    function submitLeave() {
        var rowsData = $('#ttAuditQuotaMainList').datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择提交项目',"error");
            return;
        }
		if(rowsData.length > 1){
			tip("请选择一条数据进行提交","error");
			return;
		}
		if(!(rowsData[0].bpmStatus == "1" || rowsData[0].bpmStatus == "4"||rowsData[0].bpmStatus == "5")){
			tip("处于流程中或者已经审批完成的数据不能再次提交","error");
			return;
		}
		var param = {processKeyType:'process_audit_quota_type'};

        jblSubmitDialog1(rowsData[0].id,rowsData[0].billName,rowsData[0].remark,"com.biz.eisp.tpm.audit.quota.controller.workflow.TtAuditQuotaWorkFlowController",JSON.stringify(param),rowsData[0].actTypeCode);
    }

    //默认不折叠
    var status = '1';
	$(function () {
        $("#ttAuditQuotaList_toolbar_div").parent().append("&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
        $("#ttAuditQuotaList_toolbar_div").remove();
        $("input[name='actTypeName']").combobox({
            url : 'ttAuditQuotaMainController.do?getProcessComb&typeCode=process_audit_quota_type',
            width:'280',
            panelHeight : '300'
        });
        /*$('#system_org_tbaList').layout({
			onCollapse:function () {
			    console.log("status==="+status)
                status = '2';
            },
            onExpand:function(){
                console.log("status==="+status)
                status = '1';
			}
        })*/
        $('#ttAuditQuotaMainList').datagrid({
            onClickRow: function(index,row){
                var auditMainTarget = $("#ttAuditQuotaMainList").datagrid("getSelected");
                $("#ttAuditQuotaListtb_r").find(":input").val("");
                $('#ttAuditQuotaList').datagrid('load',{
                    billMainId: auditMainTarget.id
                });
                $('#system_org_tbaList').layout('expand','south');

            }
        });
        //绑定当行点击事件
        $('#ttAuditQuotaList').datagrid({
            onClickRow: function(index,row){
				if(row.bpmStatus == 1 || row.bpmStatus == 4 ||row.bpmStatus == 5 ){
                    editRow(index,row);
				}
            }
        });
    })
    function loadTotal(){
        var queryParams = $('#ttAuditQuotaList').datagrid('options').queryParams;
        $('#ttAuditQuotaListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttAuditQuotaController.do?getRealAuditAmountByTotal";
        console.log(queryParams);
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("最终结案金额汇总："+d.obj);
            }
        }
        });
    }

    function detailPicture(){
        var auditQuotaList = $("#ttAuditQuotaList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var id = auditQuotaList[0].id;
        var containTerminal = auditQuotaList[0].containTerminal;
        var actModeCode = auditQuotaList[0].actModeCode;
        if(containTerminal == "1"){
            tip("到门店活动，请点击查看门店结案金额查看照片","error");return false;
        }
        if(auditQuotaList[0].actModeCode == 'product_act_type'){
            tip("产品活动没有查看图片","error");return false;
        }

        if(auditQuotaList[0].actModeCode == 'ad_act_type'){
            tip("广告费活动没有查看图片","error");return false;
        }
		var url = "ttAuditQuotaPictureController.do?goActPhotoWall&auditId="+id;
        openwindow("查看图片", url,"ttAuditQuotaList", 1000, 600);
	}
	//主单附件
	function fileUploadTotal() {
        var auditQuotaList = $("#ttAuditQuotaMainList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttAuditAttachemntController.do?goAuditTotalFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
//        openwindow("附件上传", url,"ttAuditQuotaMainList", 600, 400);
        $.dialog({
            title: "附件上传",
            content: "url:"+url,
            lock: true,
            width: "450",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                return true;
            },
            cancelVal: false,
            cancel: function () {
                $("#ttAuditQuotaMainList").datagrid("reload");
            }
        });

    }
	//附件上传
	function fileUpload() {
		var auditQuotaList = $("#ttAuditQuotaList").datagrid("getSelections");
		if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
		if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
		var bpmStatus = auditQuotaList[0].bpmStatus;
		var id = auditQuotaList[0].id;
		var url = "ttAuditAttachemntController.do?goAuditFileUploadForm&id="+id+"&bpmStatus="+bpmStatus;
//        openwindow("附件上传", url,"ttAuditQuotaList", 600, 400);
        $.dialog({
            title: "附件上传",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                return true;
            },
            cancelVal: false,
            cancel: function () {
                $("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }
	//保存行编辑数据
    function saveQuotaAuditForm(){
        var ttBkwAuditMainTarget = $("#ttAuditQuotaMainList").datagrid("getSelections");
        if (!ttBkwAuditMainTarget || ttBkwAuditMainTarget.length == 0) {
            tip('请先选择申请主单，再保存子单');
            return;
        }
        if (ttBkwAuditMainTarget.length > 1) {
            tip('请选择一条记录再保存');
            return;
        }
        if(ttBkwAuditMainTarget[0].bpmStatus == 2 || ttBkwAuditMainTarget[0].bpmStatus == 3){
            tip('该记录正在流程中或者已经审批通过,不能保存',"error");
            return false;
        }
        var billMainId = ttBkwAuditMainTarget[0].id;
        var rows=$("#ttAuditQuotaList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditQuotaList").datagrid("getRowIndex",row);
            $("#ttAuditQuotaList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttAuditQuotaList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttAuditQuotaController.do?saveQuotaAuditByRows",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated),billMainId:billMainId},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"info");
                    $("#ttAuditQuotaList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditQuotaList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditQuotaList").datagrid('getColumnFields',true).concat($("#ttAuditQuotaList").datagrid('getColumnFields'));
        var subStr  = "30";
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var containTerminal = row.containTerminal;
            var actModeCode = row.actModeCode;
			var planAmount = row.planAmount;
            if((fields[i] == "applyAuditQuantity")&&(actModeCode=="product_act_type"||actModeCode=="ad_act_type"||actModeCode=="longterm_act_type")){
                col.editor = null;
            }
            if ((containTerminal== "1")&&(fields[i] == "applyAuditAmount"||fields[i] == "applyAuditQuantity")){
                col.editor = null;
            }

            if (planAmount<0&&fields[i] == "auditStatus"){
                col.editor = null;
			}
        }
        $("#ttAuditQuotaList").datagrid('beginEdit', index);

        var editors=$("#ttAuditQuotaList").datagrid('getEditors',index);

        $.each(editors,function (index1,editor){
            if(editor.type=="combobox"){
                if(editor.field=="auditStatus"){
                    $(editor.target).focus();
                    $(editor.target).combobox('reload',"tmTableConfigController.do?dictCombox&dictCode=audit_bkw_status");
                }
            }
            if(editor.field=="applyAuditAmount"){
                editor.target.bind('change',function () {
                    var str = editor.target.val();
                    editors[index1+1].target.val(str);
                });
			}
			if(editor.field=="realAuditAmount"){
                editor.target.bind('focus',function () {
                    var str = editors[index1-1].target.val();
                    editor.target.val(str);
                });

            }
        });
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }

    //删除主单
    function deleteALLAuditMainSelect(title, url, gname, deleteCallback) {
        gridname = gname;
        var ids = [];
        var rows = $("#" + gname).datagrid('getSelections');
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus == 2 ||rows[i].bpmStatus == 3){
                            tip("费用清单中存在产生了流程的数据,不能删除流程状态为审批中或者审批通过的数据,请检查");
                            return false;
                            break;
						}
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                reloadTable();
                                $('#ttAuditQuotaList').datagrid('load');
//                                var rows = $("#ttAuditQuotaList").datagrid("getRows");
//                                var copyRows = [];
//                                for ( var j= 0; j < rows.length; j++) {
//                                    copyRows.push(rows[j]);
//                                }
//                                for(var n =0;n<copyRows.length;n++){
//                                    var index = $('#ttAuditQuotaList').datagrid('getRowIndex',copyRows[n]);
//                                    $('#ttAuditQuotaList').datagrid('deleteRow',index);
//                                }
                                $("#" + gname).datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要删除的数据");
        }
    }
    //删除子单
    function deleteALLAuditSelect(title, url, gname, deleteCallback) {
        gridname = gname;
        var ids = [];
        var rows = $("#" + gname).datagrid('getSelections');
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定移除该结案明细数据吗？", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                        if(rows[i].bpmStatus == 2 ||rows[i].bpmStatus == 3){
                            tip("费用清单中存在产生了流程的数据,不能删除流程状态为审批中或者审批通过的数据,请检查");
                            return false;
                            break;
                        }
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                reloadTable();
                                $("#" + gname).datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要删除的数据");
        }
    }
    //主单单编辑
    function auditQuotaMainUpdate(title, url, id, width, height){
        var ttAuditQuotaMainTarget = $("#ttAuditQuotaMainList").datagrid("getSelections");
        if (!ttAuditQuotaMainTarget || ttAuditQuotaMainTarget.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (ttAuditQuotaMainTarget.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }

        if(ttAuditQuotaMainTarget[0].bpmStatus == 2 || ttAuditQuotaMainTarget[0].bpmStatus == 3){
            tip('该记录正在流程中或者已经审批通过,不能编辑');
            return;
        }
        update(title, url, id, width, height);
    }
    //选择定额审批通过的活动
    function goSelectQuotaActAdd(){
        var ttAuditQuotaMainTarget = $("#ttAuditQuotaMainList").datagrid(
            "getSelected");
        if (ttAuditQuotaMainTarget == null ||  ttAuditQuotaMainTarget== "") {
            tip("请选择结案申请");
            return false;
        }

        if (ttAuditQuotaMainTarget.bpmStatus == 2
            || ttAuditQuotaMainTarget.bpmStatus == 3) {
            tip("该结案申请单正在审批中或者已经审批完成,不能新增费用结案子单");
            return false;
        }
        $.dialog({
            title: "添加费用明细",
            content: "url:ttAuditQuotaController.do?goTtQuotaActSelectMain&billMainId="+ttAuditQuotaMainTarget.id,
            lock: true,
            width: "1200",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var result = iframe.submitForm();
                $("#ttAuditQuotaList").datagrid("reload");
                return result;
            },
            cancelVal: '关闭',
            cancel: function () {
                $("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }
    //跳转选择门店结案的页面
    function goSelectQuotaTerminalAdd(){
        var ttAuditQuotaTarget = $("#ttAuditQuotaList").datagrid(
            "getSelected");
        if (ttAuditQuotaTarget == null ||  ttAuditQuotaTarget== "") {
            tip("请选择定额费用结案子单");
            return false;
        }

        if (ttAuditQuotaTarget.bpmStatus == 2
            || ttAuditQuotaTarget.bpmStatus == 3) {
            tip("该定额费用结案子单正在审批中或者已经审批完成,不能新增费用结案子单");
            return false;
        }
        var subStr  = "30";
        var containTerminal = ttAuditQuotaTarget.containTerminal;
        if (containTerminal=="0"){
            tip("该定额费用结案子单不能到门店，不能选择门店");
            return false;
        }
        $.dialog({
            title: "添加活动门店",
            content: "url:ttAuditTerminalController.do?goAuditTerminalMain&auditId="+ttAuditQuotaTarget.id,
            lock: true,
            width: "1500",
            height: "500",
            zIndex: 1000,
            parent: windowapi,
           /* ok: function () {
                iframe = this.iframe.contentWindow;
                var result = iframe.submitForm(iframe);
                if(result){
                	tip("操作成功","info");
                }
                $("#ttAuditQuotaList").datagrid("reload");
                return result;
            },*/
            cancelVal: '关闭',
            cancel: function () {
                $("#ttAuditQuotaList").datagrid("reload");
            }
        });
    }
    function terminalDetail(title, url, id, width, height) {

        var ttAuditQuotaTarget = $("#ttAuditQuotaList").datagrid(
            "getSelected");
        if (ttAuditQuotaTarget == null ||  ttAuditQuotaTarget== "") {
            tip("请选择费用结案子单");
            return false;
        }
        var containTerminal = ttAuditQuotaTarget.containTerminal;
        if (containTerminal=="0"){
            tip("该定额费用结案子单不能到门店，不能查看门店");
            return false;
        }

        url += '&load=detail&auditId=' +ttAuditQuotaTarget.id;
        createwindow(title, url, width, height);
    }
	//客户导出
    function excelCustomerExport() {

        var auditMainTarget = $("#ttAuditQuotaMainList").datagrid("getSelected");

        if (auditMainTarget == null ||  auditMainTarget== "") {
            tip("请选择结案申请");
            return false;
        }

        var queryParams = $('#ttAuditQuotaList').datagrid('options').queryParams;
        $('#' + 'ttAuditQuotaList' + 'tb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';
        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#' + 'ttAuditQuotaList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = "ttAuditMainExportController.do?exportCustomerXls";
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.location.href = tagetUrl + encodeURI(fields + params);
    }
	//门店导出
    function excelTerminalExport() {

        var auditMainTarget = $("#ttAuditQuotaList").datagrid("getSelected");

        if (auditMainTarget == null ||  auditMainTarget== "") {
            tip("请选择一条结案子单申请");
            return false;
        }
        var containTerminal = auditMainTarget.containTerminal;

        if (containTerminal == "0"){
            tip("该结案的活动不到门店，不能导出！");
            return false;
        }
        var tagetUrl = "ttAuditMainExportController.do?exportTerminalIncludeImgXls&auditId="+auditMainTarget.id;
        $.dialog.confirm('是否导出图片?', function(){
            var tagetUrl = "ttAuditMainExportController.do?exportTerminalIncludeImgXls&auditId="+auditMainTarget.id;
            //菜单id
            var accessEntry=$("#accessEntry").val();
            if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
                tagetUrl+="&accessEntry="+accessEntry;
            }
            window.location.href = tagetUrl;
        }, function(){
            var tagetUrl = "ttAuditMainExportController.do?exportTerminalXls&auditId="+auditMainTarget.id;
            //菜单id
            var accessEntry=$("#accessEntry").val();
            if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
                tagetUrl+="&accessEntry="+accessEntry;
            }
            window.location.href = tagetUrl;

        });
    }

    function reloadBkwTable(){
        $("#ttAuditQuotaList").datagrid("reload");
        $("#ttAuditQuotaMainList").datagrid("reload");
	}

    function jblSubmitDialog1(bussinessKey,type,detail,path,params,actTypeCode){
        detail = (detail==null || detail=="null")?"无":detail;
        var myOptions = {
            content : "url:jlbTaProcessThemeController.do?goNewTaProcessTheme&businessKey="+bussinessKey+
			"&type="+encodeURIComponent(type)+"&detail="+encodeURIComponent(detail)+
			"&fullPathName="+path+"&params="+encodeURIComponent(params)+"&processKey="+encodeURIComponent(actTypeCode),
            lock : true,
            width : 600,
            height : 350,
            title : "提交",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                iframe = this.iframe.contentWindow;
                return false;
            } ,
            button: [
                {
                    name: "提交",
                    callback: function(){
                        var iframe = this.iframe.contentWindow;
                       var result= iframe.doSubmit();
                       if(result){
                           $("#ttAuditQuotaMainList").datagrid("reload");
                           $("#ttAuditQuotaList").datagrid("reload");
					   }
                        return result;
                    }
                }
            ]
        };
        safeShowDialog1(myOptions);
    }
    function safeShowDialog1(option) {
        var width = option.width;
        var height = option.height;
        var offsetW = window.top.document.body.offsetWidth;
        var offsetH = window.top.document.body.offsetHeight;
        width = (width&&width!='null') ? width : 700;
        height = (height&&height!='null') ? height : 400;
        if (width == "100%" || width > offsetW) {
            width = offsetW;
        }
        if (height == "100%" || height > (offsetH-100)) {
            height = offsetH - 100;
        }
        $.extend(option,{width:width,height:height});
        if (typeof(windowapi) === 'undefined') {
            return $.dialog(option).zindex();
        } else {
            return W.$.dialog($.extend(option, {parent: windowapi})).zindex();
        }
    }
</script>