<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="actTerminalList" fitColumns="false"  pagination="false" title="活动终端范围列表"
                    actionUrl="ttActApplyExcuteWorkFlowController.do?findActTerminalList&flagKey=${flagKey}&businessKey=${businessKey}" idField="id" fit="true" >
            <t:dgCol title="主键" field="id" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" sortable="false" hidden="false" ></t:dgCol>
            <t:dgCol title="执行单号" field="actCode" sortable="false" hidden="false" ></t:dgCol>
            <t:dgCol title="终端编码" field="terminalCode" sortable="false" hidden="false" ></t:dgCol>
            <t:dgCol title="终端名称" field="terminalName" sortable="false" hidden="false" ></t:dgCol>
            <t:dgCol title="经纬度相差距离" field="deviation" sortable="false" hidden="false" ></t:dgCol>
        </t:datagrid>
    </div>
</div>

<script>

</script>
