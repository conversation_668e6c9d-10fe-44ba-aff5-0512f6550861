<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report005List" fitColumns="true" title="产品政策活动追踪"
                    actionUrl="report005Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="基本信息"  field=""  colspan="31"></t:dgCol>
            <t:dgCol title="申请信息"  field=""  colspan="22"></t:dgCol>
            <t:dgCol title="达成信息" field=""   colspan="9"></t:dgCol>
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动是否开启" field="isOpen"  sortable="false" query="true" dictionary="market_yn"></t:dgCol>
            <t:dgCol title="是否计算后返" field="dmsRebateFlag" replace="是_Y,否_N"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="申请日期" field="applyDate"  sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="批复日期" field="expenseClassification" sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"   sortable="false" query="false"></t:dgCol>
            <t:dgCol title="流程编码" field="processCode"   sortable="false"></t:dgCol>
            <t:dgCol title="提交主题" field="submitTheme"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程名称" field="processName"   sortable="false"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策编码" field="policyCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策名称" field="policyName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="随车政策内容" field="carPolicyContent"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返政策内容" field="returnPolicyContent"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="随车预算科目编码" field="carFinancialCode"   sortable="false"></t:dgCol>
            <t:dgCol title="随车预算科目" field="carFinancialName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返预算科目编码" field="returnFinancialCode"   sortable="false"></t:dgCol>
            <t:dgCol title="后返预算科目" field="returnFinancialName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动大类编码" field="returnCostTypeCode"   sortable="false" ></t:dgCol>
            <t:dgCol title="后返活动大类" field="returnCostTypeName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动细类编码" field="returnCostAccountCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动细类" field="returnCostAccountName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="开始时间" field="beginTime"   sortable="false" queryMode="group" query="true" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"   sortable="false" queryMode="group" query="true" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户级别" field="customerlevel"   sortable="false" dictionary="cust_type"></t:dgCol>
            <%--申请信息--%>
            <t:dgCol title="活动品项编码" field="actConditionCode"   sortable="false" ></t:dgCol>
            <t:dgCol title="活动品项" field="actConditionName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="原供价" field="originalPrice"  sortable="false" query="false"></t:dgCol>
            <t:dgCol title="目标销量1" field="targetSales1"  sortable="false" ></t:dgCol>
            <t:dgCol title="目标销售金额1" field="targetSalesAmount1"  sortable="false" ></t:dgCol>
            <t:dgCol title="目标销量2" field="targetSales2"  sortable="false" ></t:dgCol>
            <t:dgCol title="目标销售金额2" field="targetSalesAmount2"  sortable="false" ></t:dgCol>
            <t:dgCol title="关联考核产品" field="associatedPrd"  sortable="false" ></t:dgCol>
            <t:dgCol title="关联考核产品目标量" field="associatedSaleNum"  sortable="false" ></t:dgCol>
            <t:dgCol title="关联考核产品销售额" field="associatedSaleAmo"  sortable="false" ></t:dgCol>
            <t:dgCol title="当期费用（元）" field="periodCharges1"  sortable="false" ></t:dgCol>
            <t:dgCol title="当期费率" field="currentRate1"  sortable="false" ></t:dgCol>
            <t:dgCol title="后返费用（元）" field="afterReturnCharges1"  sortable="false" ></t:dgCol>
            <t:dgCol title="后返费率" field="afterReturnRate1"  sortable="false" ></t:dgCol>
            <t:dgCol title="合计费用" field="sumCost"  sortable="false" ></t:dgCol>
            <t:dgCol title="合计费率" field="sumRate"  sortable="false" ></t:dgCol>
            <t:dgCol title="货补产品" field="supplementProducts1"  sortable="false" ></t:dgCol>
            <t:dgCol title="折合供价" field="discountPrice"  sortable="false" ></t:dgCol>
            <t:dgCol title="终端供价" field="terminalPrice"  sortable="false" ></t:dgCol>
            <t:dgCol title="经销商毛利率" field="dealerRate"  sortable="false" ></t:dgCol>
            <t:dgCol title="终端执行方式" field="terminalExecute"  sortable="false" ></t:dgCol>
            <t:dgCol title="备注" field="remark"  sortable="false" ></t:dgCol>
            <%--达成信息--%>
            <t:dgCol title="是否手工结案" field="whetherLawsuit"  sortable="false" dictionary="yesorno"></t:dgCol>
            <t:dgCol title="活动天数" field="actDays"  sortable="false" ></t:dgCol>
            <t:dgCol title="当前天数" field="presentDays"  sortable="false" ></t:dgCol>
            <t:dgCol title="申请时间段实际销量" field="applySaleVom"  sortable="false" ></t:dgCol>
            <t:dgCol title="申请时间段实际销售额（含税）" field="applySaleAmo"  sortable="false" ></t:dgCol>
            <t:dgCol title="目标销量1达成率" field="targetSaleVolume1"  sortable="false" query="false"></t:dgCol>
            <t:dgCol title="目标销售金额1达成率" field="targetSaleAmo1"  sortable="false" query="false"></t:dgCol>
            <t:dgCol title="目标销量2达成率" field="targetSaleVolume2"  sortable="false" ></t:dgCol>
            <t:dgCol title="目标销售金额2达成率" field="targetSaleAmo2"  sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report005Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report005Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("#report005Listtb_r").find("input[name='applyDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#report005Listtb_r").find("input[name='expenseClassification']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

//        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
//            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report005Listsearch() {
        var orgCode = $("#report005Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report005Listtb_r").find("input[name='yearMonth']").val();

        var queryParams = $("#report005List").datagrid('options').queryParams;
        $("#report005Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report005List").datagrid({url:'report005Controller.do?findReportList', pageNumber:1});
    }

</script>
