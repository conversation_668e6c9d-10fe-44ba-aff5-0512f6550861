<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center">
		<div class="easyui-layout" fit="true">
			<div region="center" style="">
				<t:datagrid name="ttBudgetBillList" title="费用预算管理"  actionUrl="ttBudgetBillController.do?findBudgetPeriodList" 
		  		 idField="year" fit="true"  fitColumns="true" pagination="true" queryMode="group">
			  		<t:dgCol title="年份" field="year" query="true"></t:dgCol>
			  		<t:dgCol title="月份" field="month" query="true"></t:dgCol>
			  		
			  		<%--<t:dgCol title="上上级组织" field="grandfatherOrgName"></t:dgCol>
			  		<t:dgCol title="上级组织" field="parentOrgName"></t:dgCol>--%>
			  		<t:dgCol title="组织名称" field="orgName" ></t:dgCol>
			  		<t:dgCol title="组织编码" field="orgCode" hidden="true"></t:dgCol>

					<t:dgCol title="组织" field="orgNames"  query="true" hidden="true"></t:dgCol>

			  		<t:dgCol title="预算科目" field="accountName" query="true"></t:dgCol>
			  		<t:dgCol title="预算科目编码" field="accountCode" hidden="true" ></t:dgCol>
			  		
			  		
			  		<t:dgCol title="预算金额" field="amount" formatterjs="numExtend"></t:dgCol>
			  		<t:dgCol title="可用余额" field="balance" formatterjs="numExtend"></t:dgCol>
			  		
			  		<t:dgCol field="标识" title="flag" hidden="true"></t:dgCol>
			  		
					<t:dgToolBar operationCode="export" title="导出" icon="icon-dataOut" url="ttBudgetBillController.do?exportXls" funname="excelExport"></t:dgToolBar>
					<t:dgToolBar operationCode="budgetAdd" title="预算追加" icon="icon-yusuan_add" url="" funname="budgetAdd"></t:dgToolBar>
					<t:dgToolBar operationCode="budgetDe" title="预算削减" icon="icon-yusuanxuejian" url="" funname="accountSub"></t:dgToolBar>
					<t:dgToolBar operationCode="feiyongguanli" title="预算调整" icon="icon-yusuantiaozheng_queding" url="" funname="budgetBillAdjust"></t:dgToolBar>
					<%--<t:dgToolBar operationCode="detailView" title="预算使用明细" icon="icon-yusuanshiyongmingxi" url="" funname="budgetBillDetail()"></t:dgToolBar>--%>
					<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="" funname="logDetail" width="1200"></t:dgToolBar>
				</t:datagrid>
			</div>
		</div>
	</div>
	<input type="hidden" id="actId">
</div>
<script type="text/javascript">
    $(function() {
        $("#ttBudgetBillListtb_r").find("input[name='year']").addClass("Wdate").css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy'});
        });
        $("#ttBudgetBillListtb_r").find("input[name='month']").addClass("Wdate").css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
            WdatePicker({dateFmt: 'MM'});
        });
        $("input[name='orgId']").combotree({
            url: 'tmOrgController.do?getParentOrg',
            width: 200
        });


        $("input[name='orgNames']").attr("readonly",true).attr("id","orgNames").attr("style","width:180px").click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='orgCodes' id='orgCodes'/>");

    });

    function openOrgSelect(){
        var orgCode = $('#orgCode').val();
        orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
        var currentOrgCode='${currentOrgCode}';
        var paraArr = {
            textName: 'orgCode,orgName',
            inputTextName: 'orgCodes,orgNames',
            searchType: '1',//查询类型？？
            currentOrgCode:currentOrgCode,
            encapsulationType: 'input',//封装类型--不传或默认
            isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            pageData: {
                orgCode: orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }
    function loadTotal(){
        var queryParams = $('#ttBudgetBillList').datagrid('options').queryParams;
        $('#ttBudgetBillListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttBudgetBillController.do?getTtBudgetByTotal";
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("期初总金额："+d.obj.totalAmount);
                $("#balance").html("可用总余额："+d.obj.balance);
            }
        }
        });
    }

//日志
function logDetail(){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
	if(seletctTarget==null || seletctTarget==""){
		tip("选择一条数据");
		return;
	}
	var vo = seletctTarget;
	var url = 'ttBudgetBillController.do?goBudgetBillLogMain&year='+vo.year+'&month='+
	  vo.month+'&orgCode='+vo.orgCode+'&accountCode='+vo.accountCode;
	openwindow('日志',url,seletctTarget.id,'1200','400');
}

/**
 * 将form表单拼接成json
 */
function getFormJson(iframe,formName){
	var o = {};
	var a = iframe.$("#"+formName).serializeArray();
	$.each(a, function() {
	        if (o[this.name] !== undefined) {
	            if (!o[this.name].push) {
	                o[this.name] = [o[this.name]];
	            }
	            o[this.name].push(this.value || '');
	        } else {
	            o[this.name] = this.value || '';
	        }
	 });
	var actForm = JSON.stringify(o);
	return actForm;
}

//预算调整
function budgetBillAdjust(title, processKey, gname, width, height, params){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
	if(seletctTarget==null || seletctTarget==""){
		tip("选择一条数据");
		return;
	}
	var vo = seletctTarget;
	var org = vo.orgCode;
	if("zz0" == org){
		tip("总公司费用无法调整.");
		return;
	}
	var url = 'ttBudgetBillAdjustController.do?goTtBudgetBillAdjustForm&year='+vo.year+'&month='+vo.month+
			  '&orgCode='+vo.orgCode+'&accountCode='+vo.accountCode+'&amount='+vo.amount;;
	createwindowExt('预算调整',url,'700','450',{
		ok:function(){
			iframe = this.iframe.contentWindow;
			var flag = iframe.validateAmount();
			if(flag == false){return false;}
			//校验两边是否一样
			flag = iframe.validateData();
			if(flag == false){
				tip("调减预算和调增预算不能相同,请重新选择")
				return false;
			}
			var outJson = getFormJson(iframe,"budgetOut");
			var inJson = getFormJson(iframe,"budgetIn");
			var url = "ttBudgetBillAdjustController.do?saveBudgetBalance";
			$.ajax({url:url,type:"post",async:false,data:{
					outJson:outJson,
					inJson :inJson
				},success:function(data){
					var d = $.parseJSON(data);
					if(d.success == true){
						tip(d.msg);
						$("#ttBudgetBillList").datagrid("reload");
					}else{
						flag = false;
						tip(d.msg);
					}
				}
			});
			if(flag == false){return false;}
		}
	}); 
}
//预算追加
function budgetAdd(gridname){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
	if(seletctTarget==null || seletctTarget==""){
		tip("选择一条数据");
		return;
	}
	var vo = seletctTarget;
	var org = vo.orgCode;
	if("zz0" == org){
		tip("总公司费用无法追加");
		return;
	}
	var url = 'ttBudgetBillAddController.do?goBudgetBillAdd&year='+vo.year+'&month='+vo.month+'&orgCode='+vo.orgCode+'&accountCode='+vo.accountCode+'&periodAmount='+vo.amount+'&adjustFlag=10';
	createwindow('预算追加',url,400,400);
	
}
//预算削减
function accountSub(gridname) {
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelected");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("选择一条数据");
		return;
	}
	var vo = seletctTarget;
	var org = vo.orgCode;
	if("zz0" == org){
		tip("总公司费用无法削减.");
		return;
	}
	var url = 'ttBudgetBillSubController.do?goBudgetBillSubForm&year='+vo.year+'&month='+
			  vo.month+'&orgCode='+vo.orgCode+'&accountCode='+vo.accountCode+
			  '&periodAmount='+vo.amount+'&adjustFlag=20';
	createwindow('预算削减',url,550,400);
	
}
//预算使用明细
function budgetBillDetail(gridname){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelections");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("必须选择一条数据");
		return;
	}
	var orgCode = seletctTarget[0].orgCode;
	var costTypeCode = seletctTarget[0].costTypeCode;
	var year = seletctTarget[0].year;
	var quarter = seletctTarget[0].quarter;
	
	var url = "ttBudgetDetailController.do?goTtBudgetDetailMain&year="+year+"&quarter="+quarter+"&orgCode="+orgCode+"&costTypeCode="+costTypeCode;
	add('预算使用明细',url,gridname,600,400);
}

//开放部门
function shareOrg(){
	var seletctTarget =  $("#ttBudgetBillList").datagrid("getSelections");
	var title = "";
	if(seletctTarget==null || seletctTarget==""){
		tip("必须选择一条数据");
		return;
	}
	var orgCode = seletctTarget[0].orgCode;
	var costTypeCode = seletctTarget[0].costTypeCode;
	var year = seletctTarget[0].year;
	
	var url = "ttROrgShareCostTypeController.do?goTtROrgShareCostTypeMain&year="+year+"&orgCode="+orgCode+"&costTypeCode="+costTypeCode;
	
	createwindowExt("开放部门",url,800,500,{
			 button : '',
    	    cancelVal : '关闭',
    	    cancel : function() {
    	        return true;
    	    }
    });
}
</script>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>