<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>经销商活动申请信息维护</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttDealerActApplyController.do?saveDealerActApply"
             refresh="true">

    <input hidden="hidden" id="id" name="id" value="${vo.id}">
    <input hidden="hidden" id="isAreaAct" name="isAreaAct" value="${vo.isAreaAct}">
    <input hidden="hidden" id="actCode" name="actCode" value="${vo.actCode}">
    <input hidden="hidden" id="dealerName" name="dealerName" value="${vo.dealerName}">
    <input hidden="hidden" id="dealerCode" name="dealerCode" value="${vo.dealerCode}">

    <div class="form">
        <label class="Validform_label">活动名称: </label>
        <input name="actName" id="actName"
               datatype="*" class="inputxt" value="${vo.actName}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">活动类型:</label>
        <select name="actType" id="actType" datatype="*">
            <!-- <option value="">--请选择--</option> -->
            <c:forEach items="${costAccounts}" var="costAccount">
                <option value="${costAccount.accountCode}"
                        <c:if test="${vo.actType == costAccount.accountCode}">selected="selected"</c:if>>${costAccount.accountName}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>
    <c:if test="${isAreaAct != 1}">
        <div class="form" hidden="hidden">
            <label class="Validform_label">材料 : </label>
            <input name="material" id="material"
                   datatype="*" class="inputxt" value="${0}"/>
            <span style="color: red;">*</span>
        </div>
    </c:if>


    <div class="form">
        <label class="Validform_label">基金余额 : </label>
        <input name="fundBalance" id="fundBalance"
               readonly="readonly" class="inputxt" value="${vo.fundBalance}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">未报销金额 : </label>
        <input name="otherBalance" id="otherBalance"
                readonly="readonly" class="inputxt" value="${vo.otherBalance}"/>
        <span style="color: red;">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">CRMS待报销积分 : </label>
        <input name="crmsBlance" id="crmsBlance"
                readonly="readonly" class="inputxt" value="${vo.crmsBlance}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">本系统未报销金额 : </label>
        <input name="eblance" id="eblance"
                readonly="readonly" class="inputxt" value="${vo.eblance}"/>
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">授信积分 : </label>
        <input name="applyAmount" id="applyAmount"
               datatype="n" class="inputxt" value="${vo.applyAmount}" />
        <span style="color: red;">*</span>
    </div>





    <div class="form">
        <label class="Validform_label">开始日期 : </label>
        <input name="startDate" id="startDate"
               datatype="*" class="Wdate" readonly="readonly"
               value="<fmt:formatDate value='${vo.startDate}' pattern='yyyy-MM-dd'/>" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: red;">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">结束日期 : </label>
        <input name="endDate" id="endDate" onchange="checkDate()"
               datatype="*" class="Wdate" value="<fmt:formatDate value='${vo.endDate}' pattern='yyyy-MM-dd'/>"
               onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){$('.Wdate').blur();}})"/>
        <span style="color: red;">*</span>
    </div>

</t:formvalid>
</body>
</html>
<script type="text/javascript">
    function checkDate() {
        var startDate = $("#startDate").val();
        var endDate = $("#endDate").val();
        //alert(startDate+'-----'+endDate);
        if (endDate < startDate) {
            tip("结束时间必须大于开始时间");
            $("#endDate").val('');
        }

        var day = DateDiff(endDate, startDate)
        if (day > 365) {
            tip("开始日期至结束日期必须小于或等于1年")
            $("#endDate").val('');
        }
    }


    function DateDiff(d1, d2) {
        var day = 24 * 60 * 60 * 1000;
        try {
            var dateArr = d1.split("-");
            var checkDate = new Date();
            checkDate.setFullYear(dateArr[0], dateArr[1] - 1, dateArr[2]);
            var checkTime = checkDate.getTime();

            var dateArr2 = d2.split("-");
            var checkDate2 = new Date();
            checkDate2.setFullYear(dateArr2[0], dateArr2[1] - 1, dateArr2[2]);
            var checkTime2 = checkDate2.getTime();

            var cha = (checkTime - checkTime2) / day;
            return cha;
        } catch (e) {
            return false;
        }
    }


</script>