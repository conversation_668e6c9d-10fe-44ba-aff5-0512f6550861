<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>财年新增</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">

<t:formvalid formid="formobj" layout="div" dialog="true" action="ttFinancialYearController.do?saveFinancialYear" refresh="true" beforeSubmit="validateBeginAndEnd()">
		<input type="hidden" name="id" id="id" value="${yearVo.id}">
		<div class="form">
            <label class="Validform_label">预算财年:</label> 
			<input type="text" name="year" id="year" value="${yearVo.year}" class="Wdate"
				   onclick="WdatePicker({dateFmt:'yyyy',minDate:'%y'})" readonly="readonly" datatype="*" 
				   errormsg="财年不能重复,且不为空"
				   style="width:150px;">
				   <span style="color: red">*</span>
				   <span class="Validform_checktip" ></span>
        </div>
        
		<div class="form">
            <label class="Validform_label">季度:</label> 
			<select name="quarter" datatype='*' id="quarter">
				<option value="">请选择</option>
				<option value="1" <c:if test="${yearVo.quarter == 1}">selected="selected"</c:if> >1</option>
				<option value="2" <c:if test="${yearVo.quarter == 2}">selected="selected"</c:if> >2</option>
				<option value="3" <c:if test="${yearVo.quarter == 3}">selected="selected"</c:if> >3</option>
				<option value="4" <c:if test="${yearVo.quarter == 4}">selected="selected"</c:if> >4</option>
			</select>
			<span style="color: red">*</span>
		    <span class="Validform_checktip" ></span>
        </div>
        
		<div class="form">
            <label class="Validform_label">月度:</label> 
           	<select name="month" datatype="*" onchange="valiadteYearAndMonth()" id="month"> 
           		<option value="">请选择</option>
           		<option value="01" <c:if test="${yearVo.month == 01}">selected="selected"</c:if> >01</option>
           		<option value="02" <c:if test="${yearVo.month == 02}">selected="selected"</c:if> >02</option>
           		<option value="03" <c:if test="${yearVo.month == 03}">selected="selected"</c:if> >03</option>
           		<option value="04" <c:if test="${yearVo.month == 04}">selected="selected"</c:if> >04</option>
           		<option value="05" <c:if test="${yearVo.month == 05}">selected="selected"</c:if> >05</option>
           		<option value="06" <c:if test="${yearVo.month == 06}">selected="selected"</c:if> >06</option>
           		<option value="07" <c:if test="${yearVo.month == 07}">selected="selected"</c:if> >07</option>
           		<option value="08" <c:if test="${yearVo.month == 08}">selected="selected"</c:if> >08</option>
           		<option value="09" <c:if test="${yearVo.month == 09}">selected="selected"</c:if> >09</option>
           		<option value="10" <c:if test="${yearVo.month == 10}">selected="selected"</c:if> >10</option>
           		<option value="11" <c:if test="${yearVo.month == 11}">selected="selected"</c:if> >11</option>
           		<option value="12" <c:if test="${yearVo.month == 12}">selected="selected"</c:if> >12</option>
           	</select>
           	<span style="color: red">*</span>
	    	<span class="Validform_checktip" id="errorMonth"></span>
        </div>
                        
        <div class="form">
            <label class="Validform_label">费用开始日期:</label> 
			<input type="text" name="beginDate" id="beginDate" value="${yearVo.beginDate}" class="Wdate"
				   ajaxUrl="ttFinancialYearController.do?validateDate&id=${yearVo.id}"
				   onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" 
				   errormsg="财年开始不能在已有财年内,且不能为空" readonly="true" dataType="*"
				   style="width:150px">
				   <span style="color: red">*</span>
				   <span class="Validform_checktip" id="errorBegin"></span>
        </div>
        
        <div class="form">
            <label class="Validform_label">费用结束日期:</label>
            <input type="text" name="endDate" id="endDate" value="${yearVo.endDate}" class="Wdate"
            	   ajaxUrl="ttFinancialYearController.do?validateDate&id=${yearVo.id}"
            	   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')}'})"
            	   errormsg="财年开始不能在已有财年内,且不能为空" readonly="true" dataType="*"
            	   style="width:150px">
            <span style="color: red">*</span>
            <span class="Validform_checktip" id="errorEnd"></span>
        </div>  
</t:formvalid>
</body>
</html>
<script type="text/javascript">
	$(function(){
		var id = $("#id").val();
		if(id != ''){
			var year = $("#year").val();
			var month = $("#month").val();
			//校验财年是否可以编辑
			var url1 = "ttFinancialYearController.do?validateFinanicalYearIsUse&year="+year+"&month="+month;
			$.ajax({url:url1,type:"post",async:false,success:function(data){
				var d = $.parseJSON(data);
				if(d.success == false){
					tip(d.msg);
					$("#year").removeAttr('onclick');
					$("#quarter").attr('disabled','disabled');
					$("#month").attr('disabled','disabled');
					$("#beginDate").removeAttr('onclick');
					$("#endDate").removeAttr('onclick');
				}
			}});
		}
	});
	//校验年+月不能重复
	function valiadteYearAndMonth(){
		var year = $("#year").val();
		var month = $("#month").val();
		var id = $("#id").val();
		if(year == "" || month == ""){
			return false;
		}
		var url = "ttFinancialYearController.do?validateYearAndMonth&year="+year+"&month="+month+"&id="+id;
		$.ajax({url:url,type:"post",async:false,success:function(data){
			var d = $.parseJSON(data);
			if(d.success == false){
				tip(d.msg);
				$("#month").val("");
			}
		}});
	}
	function validateBeginAndEnd(){
		var id = $("#id").val();
		var begin = $("#beginDate").val();
		var end = $("#endDate").val();
		if(begin == "" || end == ""){
			return false;
		}
		var url = "ttFinancialYearController.do?validateBeginAndEnd&beginDate="+begin+"&endDate="+end+"&id="+id;
		var flag = true;
		$.ajax({
			url:url,
			type:"post",
			async:false,
			success:function(data){
				var d = $.parseJSON(data);
				if(d.success == false){
					tip(d.msg);
					flag = false;
					$("#beginDate").val("");
					$("#endDate").val("");
					$("#errorBegin").addClass("Validform_wrong").removeClass("Validform_right").attr("title","验证失败");
					$("#errorEnd").addClass("Validform_wrong").removeClass("Validform_right").attr("title","验证失败");
				}
			}
		});
		if(flag){
			flag = true;
		}
		if(flag){
			$("#quarter").removeAttr('disabled');
			$("#month").removeAttr('disabled');
		}
		return flag;
	}
	
	function validate(){
		var reg = new RegExp("-","g");
		var begin = $("#beginDate").val();
		var end = $("#endDate").val();
		begin = begin.replace(reg,'');
		end = end.replace(reg,'');
		if(begin == ""){
			return false;
		}
		//校验开始时间必须连续
		var year = $("#year").val();
		var flag = true;
		if(year != ""){
			var b = $("#beginDate").val();
			var id = $("#id").val();
			var url = "ttFinancialYearController.do?validateYearMonthIsContinuion&year="+year+"&beginDate="+b+"&id="+id;
			$.ajax({url:url,type:"post",async:false,success:function(data){
				var d = $.parseJSON(data);
				if(d.success == false){
					tip(d.msg);
					$("#errorBegin").addClass("Validform_wrong").removeClass("Validform_right").attr("title","验证失败");
					flag = false;
				}
			}});
		}
		if(end == ""){
			return false;
		}
		if(begin > end){
			tip("结束时间必须大于开始时间,重填");
			$("#beginDate").val("");
			$("#endDate").val("");
			$("#endError").addClass("Validform_wrong");
		}
		return flag;
	}
</script>