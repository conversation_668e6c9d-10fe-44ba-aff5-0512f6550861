<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>产品活动申请</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
<div id="btn_sub" onclick="saveProductAct();"></div>
<div class="easyui-layout" fit="true">
	<div data-options="region:'north'" style="height:50px;padding:10px;">
		客户名称:
		<input id="customerCode" name="customerCode" type="hidden"/>
		<input id="customerName" name="customerName" type="text"/>
		<t:choose hiddenName="customerCode" hiddenid="customerCode"
				  url="tmCommonMdmController.do?goCustomerSearch&singleSelect=true" name="ttCustomerList"
				  icon="icon-search"
				  title="客户列表" textname="customerName"
				  width="400" height="400" fun="selectCustomer"></t:choose>
	</div>
	<div data-options="region:'center'" style="padding:1px;">
		<table id="dg" class="easyui-datagrid" style="height:auto" fit="true"
			   data-options="rownumbers:true,singleSelect: true,toolbar: '#tb',method: 'get',url:'',onClickCell: onClickCell">
			<thead>
			<tr>
				<th data-options="field:'id',hidden:'true'">id</th>
				<th data-options="field:'actCode',hidden:'true'">编码</th>
				<th data-options="field:'yearMonth',width:80,align:'left'">结算年月</th>
				<th data-options="field:'policyCycleName',width:80,align:'left'">结案周期</th>
				<th data-options="field:'customerCode',width:80,align:'left'">客户编码</th>
				<th data-options="field:'customerName',width:120,align:'left'">客户名称</th>
				<th data-options="field:'customerTypeName',width:60,align:'left'">客户级别</th>
				<th data-options="field:'productPolicyName',width:60,align:'left'">政策名称</th>
				<th data-options="field:'productName',width:60,align:'left'">活动品项</th>
				<th data-options="field:'enableRelationProduct',width:60,align:'left',hidden:'true'">是否考核</th>
				<th data-options="field:'policyDesc',width:120,align:'left'">政策内容</th>
				<th data-options="field:'beginDate',width:100,align:'left',editor:{type:'datebox'}">开始时间</th>
				<th data-options="field:'endDate',width:100,align:'left',editor:{type:'datebox',options:{onSelect:endDateSelect}}">结束时间</th>
				<th data-options="field:'oriPrice',width:80,align:'left'">原供价（元）</th>
				<th data-options="field:'dmsCostDiscountPrice',width:80,hidden:'true'">特价费用</th>
				<th data-options="field:'targetQuantity',width:100,align:'left',editor:{type:'numberbox',options:{precision:0,min:0,onChange:calculateTarget1}}">目标销量1（EA）</th>
				<th data-options="field:'targetAmount',width:100,align:'left',editor:{type:'numberbox',options:{precision:2,min:0,onChange:inputAmount}}">目标销售额1（元）</th>
				<th data-options="field:'relationProductName',width:80,align:'left'">关联考核产品</th>
				<th data-options="field:'relationProductQuantity',width:120,align:'left',editor:{type:'numberbox',options:{precision:0,min:0,onChange:relationProductInput}}">关联考核产品销量（EA）</th>
				<th data-options="field:'relationProductAmount',width:120,align:'left',editor:{type:'numberbox',options:{precision:2,min:0,onChange:relationProductInput}}">关联考核产品销售额（元）</th>
				<th data-options="field:'targetQuantity2',width:100,align:'left',editor:{type:'numberbox',options:{precision:0,min:0}}">目标销量2（EA）</th>
				<th data-options="field:'targetAmount2',width:110,align:'left',editor:{type:'numberbox',options:{precision:2,min:0}}">目标销售额2（元）</th>
				<th data-options="field:'targetQuantity3',width:100,align:'left',hidden:'true',editor:{type:'numberbox',options:{precision:0,min:0}}">目标销量3（EA）</th>
				<th data-options="field:'targetAmount3',width:110,align:'left',hidden:'true',editor:{type:'numberbox',options:{precision:2,min:0}}">目标销售额3（元）</th>
				<th data-options="field:'currentPeriodCost',width:100,align:'left'">当期费用（元）</th>
				<th data-options="field:'currentPeriodCostRate',width:100,hidden:'true'">当期费率</th>
				<th data-options="field:'currentPeriodCostRateStr',width:100,align:'left'">当期费率</th>
				<th data-options="field:'laterPeriodCost',width:100,hidden:'true'">后返费用（元）</th>
				<th data-options="field:'laterPeriodCostStr',width:100,align:'left'">后返费用（元）</th>
				<th data-options="field:'laterPeriodCostRate',width:60,hidden:'true'">后返费率</th>
				<th data-options="field:'laterPeriodCostRateStr',width:60,align:'left'">后返费率</th>
				<th data-options="field:'supplyProductName',width:60,align:'left'">货补产品</th>
				<th data-options="field:'rebatePrice',width:80,align:'left',editor:{type:'numberbox',options:{precision:2,min:0,onChange:inputRebatePrice}}">折合供价</th>
				<th data-options="field:'terminalPrice',width:80,align:'left',editor:{type:'numberbox',options:{precision:2,min:0,onChange:inputTerminalPrice}}">终端供价</th>
				<th data-options="field:'distributorGrossProfitRate',hidden:'true',width:100,align:'left'">经销商毛利率</th>
				<th data-options="field:'distributorGrossProfitRateStr',width:100,align:'left'">经销商毛利率</th>
				<th data-options="field:'executionMode',width:100,align:'left',editor:'text'">终端执行方式</th>
				<th data-options="field:'remark',width:100,align:'left',editor:'text'">备注</th>
				<th data-options="field:'productLevelFlag',hidden:'true'">是否产品层级</th>
				<th data-options="field:'tpmCostPaymentCode',hidden:'true'">支付方式</th>
				<th data-options="field:'supplyProductCode',hidden:'true'">货补产品</th>
			</tr>
			</thead>
		</table>

		<div id="tb" style="height:auto">
			<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" onclick="append()">添加政策</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeit()">移除政策</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search',plain:true" onclick="viewPolicy()">政策详情</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-search',plain:true" onclick="viewSales()">销量查看</a>
		</div>
	</div>
</div>
</body>
</html>

<script type="text/javascript">
    var editIndex = undefined;

    function getTempId(){
        return '${tempId}';
	}

    function selectCustomer(obj) {
        var item = $('#dg').datagrid('getRows');
        if (item) {
            for (var i = item.length - 1; i >= 0; i--) {
                var index = $('#dg').datagrid('getRowIndex', item[i]);
                $('#dg').datagrid('deleteRow', index);
            }
        }
    }
    //计算目标销售额1
    function calculateTarget1(newValue,oldValue){
        var row = $("#dg").datagrid("getSelected");
        var dmsCostDiscountPrice=row.dmsCostDiscountPrice;
        var rindex = $("#dg").datagrid('getRowIndex', row);//行索引
        var ed = $("#dg").datagrid('getEditor', {
            index : rindex,
            field : 'targetAmount'
        });//获取编辑行
        var oriPrice = row.oriPrice;
        if(newValue != oldValue){
            if(dmsCostDiscountPrice != null && dmsCostDiscountPrice != "" && dmsCostDiscountPrice != 'undefined'){
                var amount = (dmsCostDiscountPrice * newValue).toFixed(2);
                $(ed.target).numberbox('setValue', amount);
                inputAmount(amount);
            }else if(oriPrice != null && oriPrice != ""){
                var amount = (oriPrice * newValue).toFixed(2);
                $(ed.target).numberbox('setValue', amount);
                inputAmount(amount);
            }
		}
        endEditing();
	}

    function onAfterEdit(index, row, changes) {
        var dmsCostDiscountPrice=row.dmsCostDiscountPrice;
        var oriPrice = row.oriPrice;
        if(dmsCostDiscountPrice != null && dmsCostDiscountPrice != "" && dmsCostDiscountPrice != 'undefined'){
            if(changes.targetQuantity != null
				&& changes.targetQuantity != ""
				&& changes.targetQuantity != 'undefined'){
                row.targetAmount=(dmsCostDiscountPrice * row.targetQuantity).toFixed(2);
			}
        }else if(oriPrice != null && oriPrice != "" && oriPrice != "undefined"){
            if(changes.targetQuantity != null && changes.targetQuantity != "" && changes.targetQuantity != "undefined"){
                row.targetAmount=(oriPrice * row.targetQuantity).toFixed(2);
			}
        }
        $('#dg').datagrid('refreshRow', index);
    }

    function inputAmount(newValue,oldValue) {
        var row = $("#dg").datagrid("getSelected");
        var dmsCostRate = row.currentPeriodCostRate;
        var tpmCostRate = row.laterPeriodCostRate;
        var currentPeriodCost = (dmsCostRate * newValue).toFixed(2);
		var laterPeriodCost = (tpmCostRate * newValue).toFixed(2);
        row.currentPeriodCost = currentPeriodCost;
        row.laterPeriodCost = laterPeriodCost;
        row.laterPeriodCostStr = laterPeriodCost;
        endEditing();
	}

	function relationProductInput(newValue,oldValue) {
        var row = $("#dg").datagrid("getSelected");
        if(row.enableRelationProduct == "N") {

            var rindex = $("#dg").datagrid('getRowIndex', row);//行索引

            var ed = $("#dg").datagrid('getEditor', {
                index : rindex,
                field : 'relationProductAmount'
            });//获取编辑行
            $(ed.target).numberbox('setValue', "");

            var ed = $("#dg").datagrid('getEditor', {
                index : rindex,
                field : 'relationProductQuantity'
            });//获取编辑行
            $(ed.target).numberbox('setValue', "");

            endEditing();
            tip("未启用关联考核产品");
		}
	}

	function inputRebatePrice(newValue, oldValue) {
        var row = $("#dg").datagrid("getSelected");
        var terminalPrice = row.terminalPrice;
        if(terminalPrice != null && terminalPrice != "" && terminalPrice != undefined) {
            var distributorGrossProfitRate = ((terminalPrice - newValue)/terminalPrice).toFixed(4);
            var distributorGrossProfitRateStr = (((terminalPrice - newValue)/terminalPrice) * 100).toFixed(2);
            row.distributorGrossProfitRate = distributorGrossProfitRate;
            row.distributorGrossProfitRateStr = distributorGrossProfitRateStr;
        }
        endEditing();
    }

    function inputTerminalPrice(newValue, oldValue) {
        var row = $("#dg").datagrid("getSelected");
        var rebatePrice = row.rebatePrice;
        if(rebatePrice != null && rebatePrice != "" && rebatePrice != undefined) {
            var distributorGrossProfitRate = ((newValue - rebatePrice)/newValue).toFixed(4);
            var distributorGrossProfitRateStr = (((newValue - rebatePrice)/newValue) * 100).toFixed(2);
            row.distributorGrossProfitRate = distributorGrossProfitRate;
            row.distributorGrossProfitRateStr = distributorGrossProfitRateStr;
		}
        endEditing();
    }

    function endEditing(){
        if (editIndex == undefined){return true}
        if ($('#dg').datagrid('validateRow', editIndex)){
            $('#dg').datagrid('endEdit', editIndex);
            editIndex = undefined;
            return true;
        } else {
            return false;
        }
    }
    function onClickCell(index, field){
        if (editIndex != index){
            if (endEditing()){
                $('#dg').datagrid('selectRow', index)
                    .datagrid('beginEdit', index);
                var ed = $('#dg').datagrid('getEditor', {index:index,field:field});
                if (ed){
                    ($(ed.target).data('textbox') ? $(ed.target).textbox('textbox') : $(ed.target)).focus();
                }
                editIndex = index;
            } else {
                setTimeout(function(){
                    $('#dg').datagrid('selectRow', editIndex);
                },0);
            }
        }
        if(field == "supplyProductName") {
			var row = $("#dg").datagrid("getSelected");
			if(row.tpmCostPaymentCode == 20) {
                openSelectProduct();
			} else {
			    tip("支付方式（货补支付），才能选择货补产品");
			}
		}
    }

    function openSelectProduct(){
        safeShowDialog({
            content : "url:tdProductApiController.do?goTmPremiumProductMain",
            lock : true,
            title : "选择货补产品",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#ttProductList').datagrid('getSelected');
                if (rowsData==null||rowsData==undefined) {
                    iframe.tip('请选择货补产品!');
                    return false;
                }

                var row = $("#dg").datagrid("getSelected");

                $("#productName").val(rowsData.productName);
                $("#productCode").val(rowsData.productCode);
                row.supplyProductCode = rowsData.productCode;
                row.supplyProductName = rowsData.productName;
                endEditing();
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function append(){
        var customerCode = $("#customerCode").val();
        if(customerCode == null || customerCode == "") {
            tip("请选择客户");
            return;
		}

        $.dialog({
            title: "产品政策",
            content: "url:ttProductPolicyApiController.do?goCustomerProductPolicyMain&customerCode=" + customerCode,
            lock: true,
            width: "1000",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var rows = iframe.$("#ttProductPolicyList").datagrid(
                    'getSelections');
                if (endEditing()){
                    appendRows(rows);
                    editIndex = $('#dg').datagrid('getRows').length-1;
                    $('#dg').datagrid('selectRow', editIndex)
                        .datagrid('beginEdit', editIndex);
                }
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    function appendRows(rows) {
        $.each(rows, function (index, item) {
            $('#dg').datagrid('appendRow', {
                id:'',
                actCode:'',
                yearMonth:item.yearMonth,
                policyCycleName:item.policyCycleName,
                policyCycle:item.policyCycle,
                customerCode:item.customerCode,
                customerName:item.customerName,
                customerTypeName:item.customerTypeName,
                customerType:item.customerType,
                productPolicyCode:item.productPolicyCode,
                productPolicyName:item.productPolicyName,
                productName:item.productName,
                enableRelationProduct:item.enableRelationProduct,
                relationProductName:item.relationProductName,
                policyDesc:item.productPolicyDetailDesc,
                beginDate:item.beginDate,
                endDate:item.endDate,
                oriPrice:item.oriPrice,
                dmsCostDiscountPrice:item.dmsCostDiscountPrice,
                currentPeriodCost:item.currentPeriodCost,
                currentPeriodCostRate:item.dmsCostRate,
                currentPeriodCostRateStr:item.dmsCostRateStr,
				laterPeriodCost:item.laterPeriodCost,
                laterPeriodCostStr:item.laterPeriodCost,
				laterPeriodCostRate:item.tpmCostRate,
                laterPeriodCostRateStr:item.tpmCostRateStr,
				status:item.status,
				executionMode:item.executionMode,
				remark:item.remark,
                dmsCostRate:item.dmsCostRate,
                tpmCostRate:item.tpmCostRate,
                productLevelFlag:item.productLevelFlag,
                productCode:item.productCode,
                tpmCostPaymentCode:item.tpmCostPaymentCode,
                relationProductCode:item.relationProductCode,
			});
        });
	}

    $('body').on('keydown','.datagrid-view [field] input',function(e){
        if (e.keyCode == 38){
            console.log("向上");
            switchTo('up');
        }else if (e.keyCode == 40){
            console.log("向下");
            switchTo('down');
        }
    })

    function switchTo(tag){
        var index = editIndex;
        var field = $(":focus").closest('[field]').attr('field') ;
        if(tag == "up" && editIndex > 0){
            index = editIndex - 1;
        }else if(tag == "down" && editIndex < $('#dg').datagrid("getRows").length-1){
            index = editIndex + 1;
        }
        console.log(index);console.log(field);
        onClickCell(index,field);
    }

    function removeit(){
        if (editIndex == undefined){return}
        var row = $("#dg").datagrid("getSelected",editIndex);
        if(row != null ){
            $.messager.confirm("提示信息","确定移除!", function (r) {
                if(r){
                    var thisData = {
                        actCode : row.actCode
                    }
                    var url = "ttProductActController.do?removeTheHasSaveData";
                    var d = ajaxPost(thisData,url);
                    if(d.success){
        $('#dg').datagrid('cancelEdit', editIndex)
            .datagrid('deleteRow', editIndex);
        editIndex = undefined;
                        //reloadMainPage();
    }
                    tip(d.msg);
				}
            });
		}else{
     		tip('请选择一条数据');
		}
    }

    function saveProductAct() {
        endEditing();
        var rows = $('#dg').datagrid('getRows');

        if(rows.length == 0) {
            tip("请填写产品活动申报数据");
            return;
		}

        for(var i = 0; i < rows.length; i++) {
            var row = rows[i];
            if (row.beginDate == undefined || row.beginDate == "") {
                tip("第" + (i + 1) + "行开始时间没有填写");
                return;
            }
            if (row.endDate == undefined || row.endDate == "") {
                tip("第" + (i + 1) + "行结束时间没有填写");
                return;
            }
            if(row.endDate < row.beginDate) {
                tip("第" + (i + 1) + "行开始时间大于结束时间");
                return;
			}
			if(
			    (row.targetQuantity2 != "" || row.targetQuantity2 != null || row.targetAmount2 != "" || row.targetAmount2 != null)
				&& (row.targetQuantity == "" || row.targetQuantity == null || row.targetAmount == "" || row.targetAmount == null)
			) {
                tip("第" + (i + 1) + "行必须填写目标销售1，目标销售额1");
                return;
			}
        }

        $.ajax({
            async: false,
            url: "ttProductActController.do?saveProductAct",
            data: {
                tempId : getTempId(),
                'jsonData': JSON.stringify(rows)
			},
            type: "post",
            dataType : "json",
            success : function(d) {
                var msg = d.msg;
                if (d.success) {
                    $("#dg").datagrid("loadData",{ rows : d.obj });
                    starRefreashPage();
                    reloadMainPage();
                }
                tip(msg);
                /*if (d.success) {
                    $("#dg").datagrid("loadData",{ rows : d.obj });
//                    W.tip(msg,'info');
					W.reloadTable();
//                    windowapi.close();
                }else{
                    tip(msg,'error');
                    return;
                }*/
            },
            error:function(){
            }
        });
    }

    function endDateSelect(date) {
        var row = $("#dg").datagrid("getSelected");
        var index = $("#dg").datagrid("getRowIndex", row);

        $.ajax({
            async: false,
            url: "ttProductActController.do?checkCustomerOrgType",
            data: {'customerCode': row.customerCode},
            type: "post",
            dataType : "json",
            success : function(d) {
                var msg = d.msg;
                if (d.success) {
                    //客户组织是否是奶粉事业部和直营组织,结束时间+两天的年月
					if(msg == "Y"){
                        var curr = date.format("yyyy-MM-dd");
                        row.endDate = curr;
                        var yearMonth = date.format("yyyy-MM");
                        if(Number(yearMonth.replace("-","") < Number((new Date().format("yyyyMM"))))) {
                            yearMonth = new Date().format("yyyy-MM");
						}
                        row.yearMonth = yearMonth;
                        endEditing();
					}else {
                        var currs = new Date(date.setDate(date.getDate()+2));
                        var curr = currs.format("yyyy-MM-dd");
                        row.endDate = curr;
                        var yearMonth = date.format("yyyy-MM");
                        if(Number(yearMonth.replace("-","") < Number((new Date().format("yyyyMM"))))) {
                            yearMonth = new Date().format("yyyy-MM");
                        }
                        row.yearMonth = yearMonth;
                        endEditing();
					}
                }else{
                    tip(msg,'error');
                    return;
                }
            },
            error:function(){
            }
        });

	}
//
//    function beginDateSelect(date) {
//        var row = $("#dg").datagrid("getSelected");
//        var index = $("#dg").datagrid("getRowIndex", row);
//        if(row.policyCycle == 20) {
//            var curr = date.format("yyyy-MM-dd");
//            row.beginDate = curr;
//        	date.setMonth(date.getMonth() + 2);
//            var yearMonth = date.format("yyyy-MM");
//            row.yearMonth = yearMonth;
//            endEditing();
//        }
//    }

    function viewSales() {
        var row = $("#dg").datagrid("getSelected");
        if(row == null || row == "" || row.length == 0) {
            tip("请选择查看数据");
            return;
        }
        view("销量查看", "ttProductPolicyApiController.do?goSalesViewForm", "dg", "400", "250", "customerCode,yearMonth,productPolicyCode,productCode,productLevelFlag")
    }

    function viewPolicy() {
        var row = $("#dg").datagrid("getSelected");
        if(row == null || row == "" || row.length == 0) {
            tip("请选择查看数据");
            return;
        }
        view("政策详情", "ttProductPolicyController.do?goTtProductPolicyViewForm&load=detail", "dg", "1000", "400", "productPolicyCode")
	}

	//刷新
	function refreashPage(){
        $.messager.confirm("提示信息","刷新会清除掉当前页面还没有保存的数据，确定继续!", function (r) {
            if(r){
                starRefreashPage();
			}
        });
	}

    function starRefreashPage(){
        var thisData = {
            tempId : getTempId()
        }
        var url = "ttProductActController.do?findDataByTempIdTorefreachThisPage";
        var d = ajaxPost(thisData,url);
        if(d.success){
            $("#dg").datagrid("loadData",{ rows : d.obj });
        }
        tip(d.msg);
    }

	function reloadMainPage(){
        W.reloadTable();
	}

	function goClose(){
        reloadMainPage();
        close();
	}

    function close(){
        frameElement.api.close();
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }

</script>