<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="tsActBaseList" title="户外广告基础信息"   actionUrl="tsActBaseController.do?findtsActBaseList"
	  		 checkbox="true"  sortName="" sortOrder="asc" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="合同编码" field="contractCode" sortable="false" query="true" width="100" ></t:dgCol>
			<t:dgCol title="合同名称" field="contractName" sortable="false" query="true" width="150" ></t:dgCol>
			<t:dgCol title="合作公司" field="partnerCompany" sortable="false" query="true" width="150" ></t:dgCol>
			<t:dgCol title="活动类型" field="advType" hidden="true" sortable="false" width="100"></t:dgCol>
			<t:dgCol title="活动细类编码" field="accountCode" sortable="false" hidden="true" width="100"></t:dgCol>
			<t:dgCol title="活动细类" field="accountName" sortable="false" query="true" width="150"></t:dgCol>
			<t:dgCol title="合同广告数量" field="contractNum" sortable="false"  hidden="true" width="100"></t:dgCol>
			<t:dgCol title="合同金额(元)" field="contractAmount" sortable="false" hidden="true" width="150" ></t:dgCol>
			<t:dgCol title="开始日期" field="startDate" query="true" sortable="false" queryMode="group"  formatter="yyyy-MM-dd" width="100"></t:dgCol>
			<t:dgCol title="广告有效月数" field="advEffectPeriod" sortable="false"  width="80" ></t:dgCol>
			<t:dgCol title="理论失效日期" sortable="false" field="endDate" width="100" ></t:dgCol>
			<t:dgCol title="地址" field="address" sortable="false" width="200" ></t:dgCol>
			<t:dgCol title="备注" field="remark" sortable="false" width="200" ></t:dgCol>
			<t:dgCol title="状态" field="enableStatus" sortable="false" formatterjs="checkStatus"  width="100"></t:dgCol>
			<t:dgCol title="上刊验收通过日期" field="publicationPassDate" sortable="false"  width="200"></t:dgCol>
			<t:dgOpenOpt width="800" height="400" title="上刊预览" url="tsActBaseController.do?goPhotoInfoUp&id={id}"/>
			<t:dgCol title="上刊备注" field="publicationRemark" sortable="false"  width="200"></t:dgCol>
			<t:dgCol title="实际失效日期" field="invalidRealDate" sortable="false"  width="200"></t:dgCol>

			<t:dgCol title="下刊验收通过日期" field="downPubPassDate" sortable="false" width="200"></t:dgCol>
			<t:dgOpenOpt width="800" height="400" title="下刊预览" url="tsActBaseController.do?goPhotoInfoDown&id={id}"/>
			<t:dgCol title="下刊备注" field="downPubRemark" sortable="false"  width="200"></t:dgCol>
			<t:dgCol title="验收材料" field="opt" width="120"></t:dgCol>
			<t:dgToolBar title="创建" icon="icon-add" operationCode="add" url="tsActBaseController.do?tsActBaseForm&optype=0" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" operationCode="add" url="tsActBaseController.do?tsActBaseForm&optype=1" funname="update"></t:dgToolBar>
			<%--<t:dgToolBar title="编辑" icon="icon-edit"   url="" operationCode="update"  funname="updateInfo"></t:dgToolBar>--%>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteTrainData"></t:dgToolBar>
			<t:dgToolBar operationCode="publication" title="上刊处理"  icon="icon-edit" url="tsActBaseController.do?goTtTrainPublicationForm"  funname="publication"></t:dgToolBar>
			<t:dgToolBar operationCode="downPublication" title="下刊处理"  icon="icon-edit" url="tsActBaseController.do?goTtTrainDownPublicationForm"  funname="downPublication"></t:dgToolBar>
			<t:dgToolBar title="导入"  icon="icon-dataIn" onclick="importDataByXml({impName:'tsActBase', gridName:'tsActBaseList'})"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
			<t:dgToolBar title="查看日志" icon="icon-log" onclick="showLog('tsActBaseList')" ></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

	function updateInfo() {
        var rows = $("#tsActBaseList").datagrid('getSelections');
        if(rows.length==0){
            tip("请选择一条数据");
            return false;
        }

        if(rows.length>1){
            tip("只能选择一条数据");
            return false;
		}
		var row=rows[0];
        var url="";
        var id=row.id;
            $.dialog({
                title: "编辑",
                content: "url:tsActBaseController.do?tsActBaseForm&id="+id,
                lock: true,
                width: "500",
                height: "400",
                zIndex: 10000,
                parent: windowapi,
                ok: function () {
                    iframe = this.iframe.contentWindow;
                    var updateform=iframe.$("#formobj");
                    //updateform.submit();

                    $('#formobj', iframe.document).form('submit', {
                        onSubmit : function() {
                        },
                        success : function(r) {
                            var data =$.parseJSON(r);
                            if(data.success){
                                tip("操作成功");
                                reloadTable();
							}else{
                                tip(data.msg);
                                return false;
							}
                        }
                    });
                   // window.location.reload();
                },
                cancelVal: '关闭',
                cancel: true
            });

    }

    function deleteTrainData() {
        var rows = $("#tsActBaseList").datagrid('getSelections');
        if(rows.length<0){
            tip("请至少选择一条数据")
		}else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="tsActBaseController.do?deleteTsActBase";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
		}

    }
    //启用停用
    function startOrStop(flag){
        var rows = $("#ttAccruedFormulaList").datagrid('getSelections');
        var title="启用";
        if(flag!=0){
            title="停用";
		}
		var url="ttAccruedFormulaController.do?updateTtAccruedFormula";
        var ids=[];
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定"+title+"所选数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(','),
							flag:flag
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                $("#ttAccruedFormulaList").datagrid('reload');
                                $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要"+title+"的数据");
        }
    }
    //导出
    function toExcel(){
        excelExport("tsActBaseController.do?exportXls","tsActBaseList");
    }

    function showLog(datagridName) {
        var rowData = $('#'+datagridName).datagrid("getSelections");
        if (rowData.length==null||rowData.length<1||rowData.length>1) {
            tip("请选择一行数据进行操作");
            return;
        }
        var id = rowData[0].id;
        var url = "url:logController.do?goLogMain&id="+id;
        safeShowDialog({
            content : url,
            lock : true,
            title : "日志",
            width : 1000,
            height : 500,
            cache : false,
            cancelVal : '关闭',
            cancel : true
        });
    }

    function checkStatus(value,row) {
        var now = new Date();

        var year = now.getFullYear();       //年
        var month = now.getMonth() + 1;     //月
        if(month<10){
            month='0'+month;
        }
        var day = now.getDate();            //日
        if(day<10){
            day='0'+day;
        }

        var hh = now.getHours();            //时
        var mm = now.getMinutes();          //分
        var ss = now.getSeconds();           //秒

		var nowTime=year+'-'+month+"-"+day;

		if(nowTime>row.endDate){
		    return '过期';
		}else {
		    return '生效';
		}

        // return year+'-'+month+"-"+day; dictionary="train_base_status"

    }

    function publication(){
        var rowsData = $("#tsActBaseList").datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        var myOptions = {
            content : "url:tsActBaseController.do?goTtTrainPublicationForm&id="+rowsData[0].id,
            lock : true,
            width : 600,
            height : 350,
            title : "上刊处理",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                iframe = this.iframe.contentWindow;
                return false;
            } ,
            button: [
                {
                    name: "提交",
                    callback: function(){
                        var iframe = this.iframe.contentWindow;
                        iframe.doSubmit();
                        return false;
                    }
                }
            ]
        };
        safeShowDialog(myOptions);
    }

    //下刊处理
    function downPublication(){
        var rowsData = $("#tsActBaseList").datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        var myOptions = {
            content : "url:tsActBaseController.do?goTtTrainDownPublicationForm&id="+rowsData[0].id,
            lock : true,
            width : 600,
            height : 350,
            title : "下刊处理",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                iframe = this.iframe.contentWindow;
                return false;
            } ,
            button: [
                {
                    name: "提交",
                    callback: function(){
                        var iframe = this.iframe.contentWindow;
                        iframe.doSubmit();
                        return false;
                    }
                }
            ]
        };
        safeShowDialog(myOptions);
    }

</script>
