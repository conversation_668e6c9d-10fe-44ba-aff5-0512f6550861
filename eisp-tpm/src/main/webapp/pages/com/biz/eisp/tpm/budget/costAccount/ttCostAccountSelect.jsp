<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div id="tmCostAccountMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
	    <t:datagrid name="tmCostAccountList" fitColumns="true" title="活动细类" queryMode = "group" singleSelect="true"
	     idField="id" actionUrl="ttCostAccountController.do?findTtCostAccountByActType&actType=${actType}&actMode=${actMode}">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="活动细类编号" field="accountCode" query="true"></t:dgCol>
	        <t:dgCol title="活动细类名称" field="accountName" query="true"></t:dgCol>
			<t:dgCol title="活动大类编号" field="costTypeCode" query="true"></t:dgCol>
			<t:dgCol title="活动大类名称" field="costTypeName" query="true"></t:dgCol>
			<t:dgCol title="预算科目编号" field="financialAccountCode" query="true"></t:dgCol>
			<t:dgCol title="预算科目名称" field="financialAccountName" query="true"></t:dgCol>
			<t:dgCol title="erp财务科目" field="financialCode" hidden="false"></t:dgCol>
			<t:dgCol title="活动发布要求" field="actDeployRequire" hidden="false" dictionary="act_deploy_require"></t:dgCol>

			<t:dgCol title="支付方式编码" field="paymentModern" hidden="true"></t:dgCol>
			<t:dgCol title="支付方式名称" field="paymentModernName" hidden="false"></t:dgCol>
			<t:dgCol title="核销资料编码" field="auditMaterialModern" hidden="true"></t:dgCol>
			<t:dgCol title="核销资料名称" field="auditMaterialModernName" hidden="false"></t:dgCol>



	    </t:datagrid>
	</div>
</div>
