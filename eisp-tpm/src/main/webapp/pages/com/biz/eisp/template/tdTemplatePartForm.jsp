<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<link rel="stylesheet" href="resources/uploadify/css/uploadify.css" type="text/css">
<script type="text/javascript" src="resources/uploadify/jquery.uploadify-3.1.js"></script>


<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
                 action="tdTemplateController.do?saveTdTemplatePart">
        <input type="hidden" name="id" value="${templatePart.id}">
        <div>
            <div class="form">
                <label class="Validform_label">模板块名称:</label>
                <input class="inputxt" datatype="*" name="tplPartName" value="${templatePart.tplPartName}">
                <span style="color: red">*</span>
            </div>
            <div class="form">
                <label class="Validform_label">模板块标签:</label>
                <input class="inputxt" datatype="*" name="tplPartTag" value="${templatePart.tplPartTag}">
                <span style="color: red">*</span>
            </div>
            <div class="form">
                <label class="Validform_label">替代标签:</label>
                <input class="inputxt" name="replaceTag" value="${templatePart.replaceTag}">
            </div>
            <div class="form">
                <label class="Validform_label">显示顺序:</label>
                <input class="inputxt" name="displaySort" value="${templatePart.displaySort}">
            </div>
                <div class="form">
                <label class="Validform_label">是否显示公式:</label>
                <input type="checkbox" name="isShow" id="isShow" value="${templatePart.isShow}">
                </div>
            <div class="form">
                <label class="Validform_label">所属模板:</label>
                <input class="inputxt" datatype="*" name="tplName" id="tplName" value="${templatePart.tplName}">
                <input type="hidden" name="tplId" id="tplId" value="${templatePart.tplId}">
                <t:choose hiddenName="tplId" hiddenid="id" url="tdTemplateController.do?goTdTemplateMain&type=choose"
                          name="tdTemplateChoose" icon="icon-search"
                          title="模板列表" textname="tplName" inputTextname="tplName" isclear="true" width="400"
                          height="400"></t:choose>
                <span style="color: red">*</span>
            </div>
            <div class="form">
                <label class="Validform_label">备注:</label>
                <textarea name="risk"  rows="5" cols="40">${templatePart.risk}</textarea>
            </div>
            <div class="form">
                <label class="Validform_label">模板内容:</label>
                <textarea id="tplPartXml" datatype="*" name="tplPartXml"  rows="5" cols="40">${templatePart.tplPartXml}</textarea>
                <input type="hidden" id="hiddenHtmlEditorGetId" value="tplPartXml">
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-log"
                   onclick="openHtmlEditor()">html</a>
            </div>

            <%--<div class="form">--%>
                <%--<label class="Validform_label">是否带图片:</label>--%>
                <%--<input type="hidden" name="isPhoto" id="isPhoto" value="${templatePart.isPhoto}">--%>
                <%--<input type="checkbox" id="isPhotoCheckbox">--%>
                <%--<span id="picPath">${templatePart.picPath}</span>--%>
            <%--</div>--%>
            <%--<div class="form" style="display: none;margin-top:10px" id="isPhotoDiv">--%>
                <%--<label class="Validform_label">图片元素:</label>--%>
                    <%--&lt;%&ndash;广告图片id&ndash;%&gt;--%>
                <%--<input type="hidden" name="bannerPicId" id="bannerPicId" value="${templatePart.bannerPicId}">--%>
                <%--<input type="text" name="imgElement" value="${templatePart.imgElement}">--%>
                <%--<input type="file" name="file_upload" id="file_upload"/>--%>
                <%--<div class="uploadify-button " id="startUpload"--%>
                     <%--style="height: 25px; line-height: 25px; width: 120px;">--%>
                    <%--<span class="uploadify-button-text">开始上传</span>--%>
                <%--</div>--%>
                <%--<div class="uploadify-button " id="insertToTpl"--%>
                     <%--style="height: 25px; line-height: 25px; width: 120px;">--%>
                    <%--<span class="uploadify-button-text">添加到模板中</span>--%>
                <%--</div>--%>
                <%--<span id="viewmsg"></span>--%>
                <%--<div id="filediv"></div>--%>
            <%--</div>--%>

        </div>
    </t:formvalid>
</div>



<script>

    var flag = false;
    var serverMsg = "";
    $(function () {

//        $("input[name='tplPartName']").on("blur",function(){
//            $("input[name='tplPartTag']").val($(this).val());
//        });
//        $("input[name='tplPartTag']").on("focus",function(){
//            $(this).val($("input[name='tplPartName']").val());
//        });
//        var isPhoto = $("#isPhoto").val();
//        if (isPhoto && isPhoto != 0) {
//            $("#isPhotoCheckbox").attr("checked", "checked");
//            $("#isPhotoDiv").show();
//            $("#isPhoto").val("1");
//        } else {
//            $("#isPhotoDiv").hide();
//            $("#isPhoto").val("0");
//        }
        var isShow = $("#isShow").val();
        if (isShow!=undefined&&isShow && isShow != 0) {
            $("#isShow").attr("checked", "checked");
            $("#isShow").val("1");
        } else {
            $("#isShow").val("0");
        }
//        $("#isPhotoCheckbox").click(function () {
//            isChecked(this);
//        });
        $("#isShow").click(function () {
            isChecked(this);
        });

        //开始上传
        $("#startUpload").click(function () {
            $("#file_upload").uploadify('upload', '*');
            return flag;
        });

        /**
         * 插入图片到模板
         */
        $("#insertToTpl").click(function () {
            var tempDiv = $("<div class='tempDiv'></div>");
            var imgElemetn = $("input[name='imgElement']").val();
            var tplPartXml = $("#tplPartXml").val();
            tplPartXml = tempDiv.append(tplPartXml);
            var imgObj = tempDiv.find(imgElemetn);
            var picPath = $("#picPath").text();
            $(imgObj[0]).attr("src", picPath);
            document.getElementById("tplPartXml").value = tempDiv.html();
        });


        //图片上传
//        $("#file_upload").uploadify({
//            buttonText: '选择图片',
//            auto: false,
//            progressData: 'speed',
//            multi: false,
//            height: 25,
//            overrideEvents: ['onDialogClose'],
//            fileTypeDesc: '文件格式:',
//            queueID: "filediv",
//            fileTypeExts: "*.jpg;*.png;*.jpeg",
//            fileSizeLimit: '5MB',
//            swf: 'resources/uploadify/uploadify.swf',
//            uploader: 'tdTemplateController.do?uploadPicture',
//            onUploadStart: function (file) {
//                var formData = {};
//                formData['tplId'] = $('#tplId').val();
//                if (formData.tplId) {
//                    $("#file_upload").uploadify("settings", "formData", formData);
//                } else {
//                    tip("请先选择模板");
//                    return;
//                }
//            },
//            onQueueComplete: function (queueData) {
//                tip(serverMsg);
//            },
//            onUploadSuccess: function (file, data, response) {
//                var d = $.parseJSON(data);
//                if (d.success) {
//                    serverMsg = d.msg;
//                    $("#picPath").text(d.attributes.realPath);
//                    $("#bannerPicId").val(d.attributes.bannerPicId);
//                }
//
////                console.log(file.name);
////                console.log(response);
////                console.log(data);
//            },
//            onFallback: function () {
//                tip("您未安装FLASH控件，无法上传图片！请安装FLASH控件后再试");
//            },
//            onSelectError: function (file, errorCode, errorMsg) {
//                switch (errorCode) {
//                    case -100:
//                        tip("上传的文件数量已经超出系统限制的" + $("#file_upload").uploadify('settings', 'queueSizeLimit') + "个文件！");
//                        break;
//                    case -110:
//                        tip("文件 [" + file.name + "] 大小超出系统限制的" + $("#file_upload").uploadify('settings', 'fileSizeLimit') + "大小！");
//                        break;
//                    case -120:
//                        tip("文件 [" + file.name + "] 大小异常！");
//                        break;
//                    case -130:
//                        tip("文件 [" + file.name + "] 类型不正确！");
//                        break;
//                }
//            }
//        });
//
    });

    function isChecked(obj) {
        if ($(obj).attr("checked")) {
//            $("#isPhotoDiv").show();
//            $("#isPhoto").val("1");
            $("#isShow").val("1");
        } else {
//            $("#isPhotoDiv").hide();
//            $("#isPhoto").val("0");
            $("#isShow").val("0");
        }
    }

    function cancel() {
        $("#file_upload").uploadify('cancel', '*');
    }

    function openHtmlEditor() {
        $.dialog({
            title: "html编辑窗口",
            content: "url:tdTemplateController.do?goEditorForm&type=html",
            lock: true,
            width: "500",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var tplPartXml = iframe.$("#htmlContent").val();
                $("#tplPartXml").val(tplPartXml);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

</script>

