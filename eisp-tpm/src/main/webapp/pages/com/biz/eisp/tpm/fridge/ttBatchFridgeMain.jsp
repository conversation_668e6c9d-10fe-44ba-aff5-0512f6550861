<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<style type="text/css">
    .datagrid-toolbar-search form div label{
        width:100px;
    }
</style>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;width:500px;">
            <t:datagrid name="ttActLongtermList" title="冰柜"  actionUrl="ttFridgeMainController.do?findFridgeList"
	  		  idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group" singleSelect="false">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>

            <t:dgCol title="冰柜编号"  field="fridgeCode"  hidden="false" query="true"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="冰柜型号"  field="fridgeModel"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="冰柜规格"  field="fridgeStandard"  hidden="false" query="true" queryMode="single"  ></t:dgCol>
            <t:dgCol title="销售部" field="saleDept" query="true"></t:dgCol>
            <t:dgCol title="组织" field="customerOrgName" query="true"></t:dgCol>

            <t:dgCol title="所属经销商编码"  field="customerCode"  hidden="true"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="所属客户"  field="customerName" query="true"   hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="客户合作性质"  field="nature"   dictionary="cooperative_type" query="true" hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="所属门店"  field="terminalName" query="true"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="冰柜总金额"  field="fridgeWorth"  hidden="false" query="true"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="返款总额"  field="totalAmount"  hidden="false"   queryMode="single"  ></t:dgCol>
            <t:dgCol title="返款/折旧年限"  field="rebateYear"  hidden="false" queryMode="single"  ></t:dgCol>
            <t:dgCol title="累计已返金额"  field="totalRebateAmount" hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="未返金额"  field="isNotRebateAmount" hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="启用状态"  field="enableStatus" dictionary="fridge_status"  hidden="false"  queryMode="single"  ></t:dgCol>

            <t:dgToolBar title="添加" icon="icon-add" onclick="addItem()"></t:dgToolBar>
            <t:dgToolBar title="全部添加" icon="icon-add" onclick="addAllItem()"></t:dgToolBar>
        </t:datagrid>
    </div>
    <div region="east" style="width:450px;padding:1px;">
        <table id="ttActLongtermSelectedList" class="easyui-datagrid" style="height:auto" fit="true"
               data-options="singleSelect: false,rownumbers:true,toolbar: '#tb',method: 'get',url:''">
            <thead>
                <tr>
                    <th data-options="field:'id',width:80,hidden:'true'">ID</th>

                    <th data-options="field:'fridgeCode',width:120,align:'center'">冰柜编号</th>
                    <th data-options="field:'fridgeModel',width:80,align:'center'">冰柜型号</th>
                    <th data-options="field:'fridgeStandard',width:80,align:'center'">冰柜规格</th>
                    <th data-options="field:'saleDept',width:120,align:'center'">销售部</th>
                    <th data-options="field:'customerCode',width:100,align:'center'">所属经销商编码</th>


                    <th data-options="field:'customerName',width:100,align:'center'">所属客户</th>
                    <th data-options="field:'nature',width:100,align:'center'">客户合作性质</th>
                    <th data-options="field:'fridgeWorth',width:100,align:'center'">冰柜总金额</th>
                    <th data-options="field:'totalAmount',width:100,align:'center'">返款总额</th>
                    <th data-options="field:'rebateYear',width:100,align:'center'">返款/折旧年限</th>
                    <th data-options="field:'totalRebateAmount',width:100,align:'center'">累计已返金额</th>
                    <th data-options="field:'isNotRebateAmount',width:100,align:'center'">未返金额</th>

                </tr>
            </thead>
        </table>

        <div id="tb" style="height:auto">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeItem()">移除</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-remove',plain:true" onclick="removeAllItem()">全部移除</a>

            <label>选择客户:</label>
            <input type="hidden" id="customerCode" name="customerCode" value="${vo.customerCode}">
            <input readonly="readonly" type="text" id="customerName" name="customerName" onclick="popCustomer();">
        </div>
    </div>

    <div id="btn_sub" onclick="submitForm()"></div>
</div>
<script type="text/javascript">
    //获取客户
    function popCustomer(){
        popMyClick("customerName,customerCode","customerName,customerCode","tmCommonMdmController.do?goCustomerSearch&singleSelect=true" ,400,400);
    }
    //添加
    function addItem(){
        var seletctTarget =  $("#ttActLongtermList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttActLongtermSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
    }

    //添加全部
    function addAllItem(){
        var name = "ttActLongtermList";	
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }
        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        queryParams.rows = '500';
        $.ajax({
            url:"ttFridgeMainController.do?findFridgeList",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttActLongtermSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
    }

    function removeItem() {
        var checkListTarget =  $("#ttActLongtermSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        for (var i = 0; i < checkListTarget.length; i++) {
            var checkRowIndex = $("#ttActLongtermSelectedList").datagrid("getRowIndex", checkListTarget[i]);
            //移除该数据
            $("#ttActLongtermSelectedList").datagrid("deleteRow",checkRowIndex);
        }
        loadElecteGrid();
    }

    function removeAllItem() {
        var rowsData = $("#ttActLongtermSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttActLongtermSelectedList").datagrid("getRows");
        $.each(rows, function(i, obj) {
            var checkRowIndex = $("#ttActLongtermSelectedList").datagrid("getRowIndex",obj);
            //移除该数据
            $("#ttActLongtermSelectedList").datagrid("deleteRow",checkRowIndex);
        });
        loadElecteGrid();
    }

    //加载待选角色
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttActLongtermSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                exclusiveCodes += "'"+checkedTarget[i].fridgeCode+"'";
            }
        }
        $('#ttActLongtermList').datagrid("reload", {"exclusiveCodes":exclusiveCodes});
    }
    function popMyClick(obj, name, url, width, height) {
        var names = name.split(",");
        var objs = obj.split(",");
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : "选择",
            width : width==null?700:width,
            height : height==null?400:height,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null || selected.length != 1 ) {
                    iframe.$.messager.alert('错误',"选择一条数据","error");
                    return false;
                } else {
                    for ( var i1 = 0; i1 < names.length; i1++) {
                        var str = "";
                        $.each(selected, function(i, n) {
                            if (i == 0){
                                str += n[names[i1]];
                            }else {
                                str += ",";
                                str += n[names[i1]];
                            }
                        });
                        if ($("#" + objs[i1]).length >= 1) {
                            $("#" + objs[i1]).val("");
                            $("#" + objs[i1]).val(str);
                        } else {
                            $("input[name='" + objs[i1] + "']").val("");
                            $("input[name='" + objs[i1] + "']").val(str);
                        }
                    }
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>

