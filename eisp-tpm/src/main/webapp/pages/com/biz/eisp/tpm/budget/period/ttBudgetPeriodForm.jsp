<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>期初费用预算</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true"  action="ttBudgetPeriodController.do?saveTtBudgetPeriod" refresh="true" >
	<input type="hidden" name="id" value="${vo.id }">
	<c:if test="${not empty vo.id }">
		<div class="form">
			<label class="Validform_label">期初费用预算编码: </label>
			<input type="text" name="yearMonth" readonly="readonly" value="${vo.budgetPeriodCode}" class="inputxt">
		    <span style="color: red">*</span>
		</div>
	</c:if>
	<div class="form">
		<label class="Validform_label">年月: </label>
		<input type="text" id="yearMonth" name="yearMonth" value="${vo.yearMonth}" class="Wdate"
				   onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})" readonly="readonly" datatype="*" 
				   errormsg="年月不能为空" style="width:150px;">
				   <span style="color: red">*</span>
	</div>
	
	<div class="form">
		<label class="Validform_label">预算科目: </label>
		<select name="accountCode" dataType="*" id="accountCode">
			<option value="">--请选择--</option>
			<c:forEach items="${accountList}" var="c">
				<option value="${c.accountCode }">${c.accountName }</option>
			</c:forEach>
		</select>
		<span style="color: red;">*</span>
	</div>
	
<%-- 	<div class="form" id="orgForm">
		<label class="Validform_label">部门: </label>
		<input name="orgName" id="orgName" datatype="*" type="hidden" value="${vo.orgName}" />
		<input name="orgId" id="orgCode" style="width: 150px;" class="inputxt" value="${vo.orgCode}"/>
		<span style="color: red;">*</span>
	</div> --%>
	
<%-- 	<div class="form" id="orgForm">
		<label class="Validform_label">部门: </label>
		<input name="orgName" id="orgName" datatype="*" type="hidden" value="${vo.orgName}" />
		<input name="orgId" id="orgCode" style="width: 150px;" class="inputxt" value="${vo.orgCode}"/>
		<span style="color: red;">*</span>
	</div> --%>
	
	<div class="form" id="orgForm">
		<label class="Validform_label">组织: </label>
		<input name="orgName" id="orgName" datatype="*" class="inputxt" value="${vo.orgName}" />
		<input name="orgCode" id="orgCode" type="hidden" class="inputxt" value="${vo.orgCode}"/>
		<span style="color: red;">*</span>
		<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openOrgSelect();"></a>
	</div>	
	
	<div class="form">
		<label class="Validform_label">金额: </label>
		<input name="amount" id="amount" datatype="/^(([1-9]\d*)|\d)(\.\d{1,2})?$/" errormsg="只能输入大于等于0的数字，不超过两位小数" class="inputxt" value="${vo.amount}" />
		<span style="color: red;">*</span>
	</div>

</t:formvalid>
</body>
</html>
<script type="text/javascript">
function openOrgSelect() {
    safeShowDialog({
        content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
        lock : true,
        title : "组织",
        width : 500,
        height : 450,
        left :'85%',
        cache : false,
        ok : function()
        {
            iframe = this.iframe.contentWindow;
            var rowsData = iframe.$('#orgList').datagrid('getSelected');
            if ((rowsData == '' || rowsData == null)) {
                $("#orgCode").val("");
                $("#orgName").val("");
                return true;
            }
            $("#orgCode").val(rowsData.orgCode);
            $("#orgName").val(rowsData.text);
            return true;
        },
        cancelVal : '关闭',
        cancel : true
    });
}
</script>