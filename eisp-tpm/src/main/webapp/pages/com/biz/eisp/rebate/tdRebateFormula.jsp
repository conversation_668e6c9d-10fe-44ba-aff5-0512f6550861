<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>返利公式</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <script src="resources/laytpl/laytpl.js"></script>
    <script src="resources/laytpl/formulacheck.js"></script>
</head>
<body>
<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="beforCheckFomula()"
             action="">
    <div class="line-title" id="formulaConditionContainer">
		返利基准数据:
    	<c:forEach items="${temps}" var="d">
    	   <input type="hidden" name="tag_id" value='${d.tag}'>
    	   <a class="easyui-linkbutton l-btn l-btn-plain" plain="true" href="javascript:;" onclick="addSign('${d.tag}','${d.name}','${d.tagType}')"><span
			class="l-btn-left"><span class="l-btn-text">${d.name}（${d.tag}）</span></span></a>
    	</c:forEach>
    </div>
    <div class="line-title">
    	公式符号:
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('+')">加（+）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('-')">减（-）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('*')">乘（*）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('/')">除（/）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('&gt;')">大于（&gt;）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('&lt;')">小于（&lt;）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('&gt;=')">大于等于（&gt;=）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('&lt;=')">小于等于（&lt;=）</a>
		<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('&&')">并且（&&）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('(')">括号（（）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign(')')">括号（））</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('%')">取余（%）</a>
		<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('INT')">取整（INT）</a>
		<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('||')">或（||）</a>
    </div>
    <div class="line-title">
    	数字:
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('0')">（0）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('1')">（1）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('2')">（2）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('3')">（3）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('4')">（4）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('5')">（5）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('6')">（6）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('7')">（7）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('8')">（8）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('9')">（9）</a>
    	<a class="easyui-linkbutton l-btn" plain="true" href="javascript:;" onclick="addSign('.')">（.）</a>
    </div>
    <div>
        <div class="promotionForm_tem">
            <div>
                <a iconcls="icon-append" class="easyui-linkbutton l-btn" plain="true" href="javascript:;" id="addRebFormula">添加返利公式</a>
				<a iconcls="icon-search" class="easyui-linkbutton l-btn" plain="true" href="javascript:searchRebFormula();" id="searchRebFormula">选择返利条件与公式</a>
            </div>
            <div id="formulaContainer">
            </div>
        </div>
    </div>

</t:formvalid>
</body>

    <style>
	    #content .inputxt,#steps form select{width:250px;}
	    
        #formobj {
            padding: 0 5px;
        }

        #steps form div.form {
            float: left;
            width: 300px;
            min-height:22px;
        }

        #steps form div.form-2 {
            min-width: 700px;
        }

        #steps form div.form-2 textarea {
            width:100%;
        }
		textarea {
			vertical-align:middle;
			margin: 5px 0;
		}
        #steps form div.form .Validform_label {
            width: 85px;
            margin-right: 5px;
            font-weight:normal;
        }
        
        #steps form div.form .Validform_label input[type="radio"],#steps form div.form .Validform_label input[type="checkbox"]{
        	position:absolute;
        	left:3px;
        	top:3px;
        	width:15px;
        	height:15px;
        }

        .formDiv {
            float: left;
        }

        .line-title {
            font-size: 14px;
            padding: 5px;
            /* background:#b4d2ea; */
        }
		
		.form_label{float:left;padding-right: 20px;line-height:26px;}
        .form_label input[type="radio"] {
            float: left;
            margin: 5px 0 0 0px;
            width: 17px;
            height: 17px;
        }

        .promotionForm_tem {
            margin: 10px;
            padding: 5px;
            border: 1px solid #a5aeb6;
            background: #f5f5f5;
            line-height: 30px;
        }

        .promotionForm_tem input {
            margin: 0 10px;
            width: 60px;
            border: 0;
        }
        .promotionArea{border: 1px solid #ddd;background: #fff;min-height: 80px;padding:5px;}
        .promotionArea a{margin-right:5px;}
        .selectArea{background:#ffc6f2 !important;}
        
    </style>

<script src="resources/laytpl/laytpl.js"></script>
<script src="resources/tools/Map.js"></script>
 <script type="text/html" id="formulatpl">
				<div class="back_div">
					<input type="hidden" name="formula_con" readonly="readonly"  style="width:220px;height:150px" data-type="condition" value=""/>
					<input type="hidden" name="formula_val" readonly="readonly" style="width:220px;height:150px" data-type="val" value=""/>
					<input type="hidden" name="formula_con_arr" readonly="readonly"  style="width:220px;height:150px" data-type="condition" value=""/>
					<input type="hidden" name="formula_val_arr" readonly="readonly" style="width:220px;height:150px" data-type="val" value=""/>
					<input type="hidden" name="formula_con_desc_arr" readonly="readonly"  style="width:220px;height:150px" data-type="condition" value=""/>
					<input type="hidden" name="formula_val_desc_arr" readonly="readonly" style="width:220px;height:150px" data-type="val" value=""/>
					返利条件<textarea name="formula_con_desc" readonly="readonly"  style="width:220px;height:150px" data-type="condition" ></textarea>
					返利公式=<textarea name="formula_val_desc" readonly="readonly" style="width:220px;height:150px" data-type="val" ></textarea>
					<a iconcls="icon-remove" class="easyui-linkbutton l-btn l-btn-plain" plain="true" href="javascript:;" style="float:right" onclick="removeFomula(this)"><span class="l-btn-left"><span class="l-btn-text icon-remove l-btn-icon-left">删除</span></span></a>
					<a iconcls="icon-remove" class="easyui-linkbutton l-btn l-btn-plain" plain="true" href="javascript:;" style="float:right" onclick="removeContext(this,'formula_val')"><span class="l-btn-left"><span class="l-btn-text icon-remove l-btn-icon-left">清空返利公式</span></span></a>
					<a iconcls="icon-remove" class="easyui-linkbutton l-btn l-btn-plain" plain="true" href="javascript:;" style="float:right" onclick="removeContext(this,'formula_con')"><span class="l-btn-left"><span class="l-btn-text icon-remove l-btn-icon-left">清空返利条件</span></span></a>
					<a iconcls="icon-back" class="easyui-linkbutton l-btn l-btn-plain" plain="true" href="javascript:;" style="float:right" onclick="removeBack(this)"><span class="l-btn-left"><span class="l-btn-text icon-back l-btn-icon-left">后退</span></span></a>
                </div>
 </script>
<script>
    //编辑用到的js代码
    var fields=new Object();
    $(function () {
		$("#formulaContainer").on("click","textarea",function(){
			$(".selectArea").removeClass("selectArea");
			$(this).addClass("selectArea");
		})
		 //appendTpl();
		$("#addRebFormula").click(function () {
	           appendTpl();
	                   
	        });
		$("#formulaConditionContainer").find("input[name='tag_id']").each(
				function(index, obj) {
					fields[$(obj).val()]=1;
					eval($(obj).val()+"=1");
				});
    });
    function addSign(para,name,tagType){
        var name = name||para;
    	if ($(".selectArea").length == 0) {
    		tip("请选择待填写的返利条件框或返利公式框");
    		return false;
    	}else {
    	    if(tagType == "date_type" || tagType == "date_type_hi") {
                $.dialog({
                    title: "自定义日期",
                    content: "url:ttProductPolicyFormulaController.do?goCustomDateForm",
                    lock: true,
                    width: "450",
                    height: "250",
                    zIndex: 10000,
                    parent: windowapi,
                    ok: function () {
                        iframe = this.iframe.contentWindow;
                        var beginDate = iframe.$("#beginDate").val();
                        var endDate = iframe.$("#endDate").val();
                        if(beginDate == null || beginDate == "") {
                            iframe.tip("请选择开始时间");
                            return false;
						}
                        if(endDate == null || endDate == "") {
                            iframe.tip("请选择结束时间");
                            return false;
                        }
                        if(tagType == "date_type_hi") {
                            var nowDate = new Date().format("yyyy-MM-dd");

                            var nowDate_INT = Number(nowDate.replace("-","").replace("-",""));
                            var endDate_INT = Number(endDate.replace("-","").replace("-",""));

                            if((nowDate_INT < endDate_INT) || (nowDate_INT == endDate_INT)) {
                                iframe.tip("必须选择今天以前的时间");
                                return false;
							}
						}
                        var val = "{" + name + "," + beginDate + "," + endDate + "}";
                        var paraVal = "{" + para + "," + beginDate + "," + endDate + "}";

                        var n = $(".selectArea").attr("name").slice(0,-5);
                        $(".selectArea").siblings("[name='"+n+"']").val( $(".selectArea").siblings("[name='"+n+"']").val() + paraVal);
                        $(".selectArea").val( $(".selectArea").val() + val);
                        $(".selectArea").siblings("[name='"+n+"_arr']").val( $(".selectArea").siblings("[name='"+n+"_arr']").val() + "!!" + paraVal);
                        $(".selectArea").siblings("[name='"+n+"_desc_arr']").val( $(".selectArea").siblings("[name='"+n+"_desc_arr']").val() + "!!" + val);
                        //var obj = $(".selectArea").siblings("[name='"+$(".selectArea").attr("name").slice(0,-5)+"']");
                        //obj.val(obj.val()+paraVal);
                        //$(".selectArea").val($(".selectArea").val()+val);
                    },
                    cancelVal: '关闭',
                    cancel: true
                });
                return;
			}
            var n = $(".selectArea").attr("name").slice(0,-5);
            $(".selectArea").siblings("[name='"+n+"']").val( $(".selectArea").siblings("[name='"+n+"']").val() + para);
            $(".selectArea").val( $(".selectArea").val() + name);
            $(".selectArea").siblings("[name='"+n+"_arr']").val( $(".selectArea").siblings("[name='"+n+"_arr']").val() + "!!" + para);
            $(".selectArea").siblings("[name='"+n+"_desc_arr']").val( $(".selectArea").siblings("[name='"+n+"_desc_arr']").val() + "!!" + name);
    	    //var obj = $(".selectArea").siblings("[name='"+$(".selectArea").attr("name").slice(0,-5)+"']");
	    	//obj.val(obj.val()+para);
            //$(".selectArea").val($(".selectArea").val()+name);
    	}
    }

    function addCustom(para) {
        if ($(".selectArea").length == 0) {
            tip("请选择待填写的返利条件框或返利公式框");
            return false;
        }else {
            $.dialog({
                title: "自定义日期",
                content: "url:ttProductPolicyFormulaController.do?goCustomDateForm",
                lock: true,
                width: "350",
                height: "150",
                zIndex: 10000,
                parent: windowapi,
                ok: function () {
                    iframe = this.iframe.contentWindow;
                    var val = iframe.$("#date").val();
                    $(".selectArea").val($(".selectArea").val() + val);
                },
                cancelVal: '关闭',
                cancel: true
            });
        }
	}

	function searchRebFormula() {
            $.dialog({
                title: "返利条件与公式",
                content: "url:ttProductPolicyFormulaController.do?goTtProductPolicyFormulaMain",
                lock: true,
                width: "650",
                height: "450",
                zIndex: 10000,
                parent: windowapi,
                ok: function () {
                    iframe = this.iframe.contentWindow;
                    var row = iframe.$("#ttProductPolicyFormulaList").datagrid("getSelected");
                    $.ajax({
                        async: false,
                        url: "ttProductPolicyFormulaController.do?findFormulaByPolicyCode",
                        data: {'productPolicyCode': row.productPolicyCode},
                        type: "post",
                        dataType : "json",
                        success : function(d) {
                            var msg = d.msg;
                            if (d.success) {
                               $.each(d.obj,function (k,v) {
                                   appendTpl();
                                   var ele = $("#formulaContainer>div").last();
                                   ele.find("[name='formula_con']").val(v.formulaCon);
                                   ele.find("[name='formula_con_desc']").val(v.formulaConDesc);
                                   ele.find("[name='formula_val']").val(v.formulaVal);
                                   ele.find("[name='formula_val_desc']").val(v.formulaValDesc);
                               })
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                        }
                    });
                },
                cancelVal: '关闭',
                cancel: true
            });
	}

    function removeContext(obj,name){
    	$(obj).parent().find("[name='"+name+"']").val("");
        $(obj).parent().find("[name='"+name+"_arr']").val("");
        $(obj).parent().find("[name='"+name+"_desc']").val("");
        $(obj).parent().find("[name='"+name+"_desc_arr']").val("");
    }
    function appendTpl(type, initDate) {
        var formulatpl=$("#formulatpl").html();
        if(type==1){
        	 laytpl(formulatpl).render(initDate, function (renderHtml) {
                 $("#formulaContainer").append(renderHtml);
              }); 
        }else{
        	 $("#formulaContainer").append(formulatpl);
        }
    }
    /**
     * 公式后退
     * @param obj
     */
    function removeBack(obj) {
        debugger;
        if($(obj).siblings(".selectArea").attr('data-type')=='val'){
            var formula_val_arr = $(obj).siblings("[name='formula_val_arr']").val().split("!!");
            var formula_val_hide = "",formula_val_str = "";
            $.each(formula_val_arr,function (k,v) {
                if(v != "" && k != (formula_val_arr.length-1)){
                    formula_val_hide += "!!"+v;
                    formula_val_str += v;
                }
            })
            var formula_val_desc_arr = $(obj).siblings("[name='formula_val_desc_arr']").val().split("!!");
            var formula_val_desc_hide = "",formula_val_desc_str = "";
            $.each(formula_val_desc_arr,function (k,v) {
                if(v != "" && k != (formula_val_desc_arr.length-1)){
                    formula_val_desc_hide += "!!"+v;
                    formula_val_desc_str += v;
                }
            })
            $(obj).siblings("[name='formula_val']").val(formula_val_str).siblings("[name='formula_val_arr']").val(formula_val_hide).siblings("[name='formula_val_desc']").val(formula_val_desc_str).siblings("[name='formula_val_desc_arr']").val(formula_val_desc_hide);
        }else if($(obj).siblings(".selectArea").attr('data-type')=='condition') {
            var formula_con_arr = $(obj).siblings("[name='formula_con_arr']").val().split("!!");
            var formula_con_hide = "",formula_con_str = "";
            $.each(formula_con_arr,function (k,v) {
                if(v != "" && k != (formula_con_arr.length-1)){
                    formula_con_hide += "!!"+v;
                    formula_con_str += v;
                }
            })
            var formula_con_desc_arr = $(obj).siblings("[name='formula_con_desc_arr']").val().split("!!");
            var formula_con_desc_hide = "",formula_con_desc_str = "";
            $.each(formula_con_desc_arr,function (k,v) {
                if(v != "" && k != (formula_con_desc_arr.length-1)){
                    formula_con_desc_hide += "!!"+v;
                    formula_con_desc_str += v;
                }
            })
            $(obj).siblings("[name='formula_con']").val(formula_con_str).siblings("[name='formula_con_arr']").val(formula_con_hide).siblings("[name='formula_con_desc']").val(formula_con_desc_str).siblings("[name='formula_con_desc_arr']").val(formula_con_desc_hide);
        }
    }
    /**
     * 移除公式
     * @param obj
     */
    function removeFomula(obj) {
//        var rules = $("#formulaContainer").find("div");
//        if (rules.length == 1) {
//            tip("不能删除，至少需要一条公式");
//        } else {
            $(obj).parent().remove();
            //重新组装值
//            buildJson(true);
//        }
    }

    function getFormula_conArr(val) {
		var splitVal = val.split("&&");
		for(i = 0; i < splitVal.length; i++) {
            var splitVal2 = splitVal[i].split("||");
            for(j = 0; j < splitVal2.length; j++) {
                var arr = val.match(/\{[^\}]+\}/g);
			}
		}
	}

    function beforCheckFomula(){
    	var tb=true;
    	 $("#formulaContainer").find("input[name='formula_con']").each(
 				function(index, obj) {
 				try{
 				    debugger;
 				    var val = $(obj).val();
                    var splitVal = val.split("&&");
                    var r = [];
                    for(var i = 0; i < splitVal.length; i++) {
                        var splitVal2 = splitVal[i].split("||");
                        for(var j = 0; j < splitVal2.length; j++) {
                            var arr = splitVal2[j].match(/\{[^\}]+\}/g);
                            if(arr != null) {
                                var matchVal = splitVal2[j];
                                for(var t = 0; t < arr.length; t++) {
                                    matchVal = matchVal.replace(arr[t], "M");
								}
                                r.push(matchVal);
                            } else {
                                r.push(splitVal2[j]);
                            }
                        }
                    }

                    for(i=0; i < r.length; i++) {
                        var eval_val = r[i].replace(/M/g, "1").replace(/INT/g,"");
                        if(new RegExp(">").test(eval_val)
								|| new RegExp("<").test(eval_val)
								|| new RegExp("=").test(eval_val)) {
                            eval(eval_val);
						} else {
                            throw "公式错误";
						}
                    }
 				}catch(err){
                    tip("第"+(index+1)+"行返利条件错误,请正确填写!");
 					tb=false;
 					return false;
 				}
 				});
    	 if(!tb)return tb;
    	 $("#formulaContainer").find("input[name='formula_val']").each(
  				function(index, obj) {
  				    var checkObj = $(obj).val().replace(/INT/g,"");
                    var arr = checkObj.match(/\{[^\}]+\}/g);
                    if(arr != null) {
                        for(var i =0; i < arr.length; i++) {
                            checkObj = checkObj.replace(arr[i], "1");
                        }
                    }
  					tb=fncheck(checkObj, fields);
  					if(!tb){
                        tip("第"+(index+1)+"行返利公式错误,请正确填写!");
  						return false;
  					}
  				});
    	return tb;
    }
</script>

</html>
