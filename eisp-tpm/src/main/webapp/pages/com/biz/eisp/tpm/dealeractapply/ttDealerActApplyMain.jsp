<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="dealerActApplyList" title="经销商活动申请列表"
                    actionUrl="ttDealerActApplyController.do?findDealerActApplyList"
                    checkbox="true" idField="id" fit="true" fitColumns="false" pagination="true" queryMode="group"
                    singleSelect="false">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动编号" field="actCode" query="true" width="150"></t:dgCol>
            <t:dgCol title="活动名称" field="actName" query="true" width="150"></t:dgCol>
            <t:dgCol title="活动类型" field="actType" hidden="true"></t:dgCol>
            <t:dgCol title="活动类型" field="actTypeName" query="true	" width="150"></t:dgCol>
            <t:dgCol title="经销商名称" field="dealerName" width="150"></t:dgCol>
            <%--<t:dgCol title="材料" field="material"   width="200" ></t:dgCol>--%>
            <t:dgCol title="门头数量" field="doorNum"></t:dgCol>
            <t:dgCol title="申请金额"  field="applyAmount" width="100" ></t:dgCol>
            <t:dgCol title="基金余额" field="fundBalance" width="150"></t:dgCol>
            <t:dgCol title="未报销金额" field="otherBalance" width="80" sortable="false" ></t:dgCol>
            <t:dgCol title="CRMS待报销积分" field="crmsBlance" width="80" sortable="false" ></t:dgCol>
            <t:dgCol title="本系统未报销金额" field="eblance" width="80" sortable="false" ></t:dgCol>
            <%--<t:dgCol title="申请金额" field="applyAmount"  width="200"></t:dgCol>--%>
            <t:dgCol title="开始时间" field="startDate" formatter="yyyy-MM-dd" query="true" queryMode="group"
                     width="150"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd" width="150"></t:dgCol>
            <t:dgCol title="流程状态" field="bpmStatus" dictionary="dealer_bpm_status"></t:dgCol>
            <t:dgCol title="活动状态" field="useState" formatterjs="statevalue" width="100"></t:dgCol>
            <t:dgToolBar title="创建" icon="icon-add" operationCode="add"
                         url="ttDealerActApplyController.do?goDealerActApplyForm" funname="add"></t:dgToolBar>
            <t:dgToolBar title="编辑" icon="icon-edit" operationCode="update"
                         url="ttDealerActApplyController.do?goDealerActApplyForm" funname="ownUpdate"></t:dgToolBar>
            <t:dgToolBar title="提交" icon="icon-submit" operationCode="update" url="" funname="submit"></t:dgToolBar>
            <t:dgToolBar title="撤回" icon="icon-process_back" operationCode="update" url=""
                         funname="revoke"></t:dgToolBar>
            <t:dgToolBar operationCode="delete" title="删除" icon="icon-remove" url=""
                         funname="deleteTrainData"></t:dgToolBar>
            <%--<t:dgToolBar title="提交审批" operationCode="dataIn" icon="icon-putIn" url="" ></t:dgToolBar>--%>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
            <t:dgToolBar title="停用" icon="icon-edit" operationCode="update" url="" funname="stopAct"></t:dgToolBar>
            <t:dgToolBar title="查看日志" icon="icon-log" onclick="showLog('dealerActApplyList')"></t:dgToolBar>


        </t:datagrid>
    </div>
    <input type="text">
</div>
<script type="text/javascript">

    function statevalue(value) {
        if(value==1){
            return '生效';
        }
        if(value==0){
            return '停用';
        }

    }

    function createAreaAct() {
        var url = "ttDealerActApplyController.do?goDealerActApplyForm";
        createwindowExt("创建", url, 400, 400, {
            button: [
                {
                    name: "确定",
                    callback: function () {
                        iframe = this.iframe.contentWindow;
                        var submitForm = iframe.$("#formobj");
                        submitForm[0].submit();
                        $('#formobj', iframe.document).form('submit', {
                            onSubmit: function () {
                            },
                            success: function (r) {
                                var data = $.parseJSON(r);
                                tip(data.msg);
//                                reloadTable();
                                $("#dealerActApplyList").datagrid('reload');
                                $("#dealerActApplyList").treegrid('reload');
                            }
                        });
                        return true;
                    }
                }],
            cancelVal: '关闭',
            cancel: function () {
                return true;
            }
        });

    }

    function ownUpdate(title, url, id, width, height) {
        gridname = id;
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        if (rowsData[0].bpmStatus != 1 || rowsData[0].bpmStatus != 6) {

        }else {
            tip("流程中的申请不能编辑");
            return;
        }
        url += '&id=' + rowsData[0].id;
        if (!width || width == "" || width == 'null' || !height || height == "" || height == 'null') {
            createwindowSmall(title, url, width, height);
        }
        else {
            createwindow(title, url, width, height);
        }
    }

    function deleteTrainData() {
        var rows = $("#dealerActApplyList").datagrid('getSelections');
        if (rows.length < 0) {
            tip("请至少选择一条数据")
        } else {
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function (r) {
                if (r) {
                    var ids = '';
                    for (var i = 0; i < rows.length; i++) {
                        if (rows[i].bpmStatus != 1 && rows[i].bpmStatus != 6) {
                            tip("流程中的申请不能删除");
                            return;
                        }
                        var subid = "'" + rows[i].id + "'";
                        ids += subid + ",";
                    }
                    var url = "ttDealerActApplyController.do?deleteDealerActApply";
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {
                            ids: ids
                        },
                        cache: false,
                        success: function (data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg, 'info');
                                // $("#ttAccruedFormulaList").datagrid('reload');
                                // $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                                window.location.reload();
                            } else {
                                tip(msg, 'error');
                                return;
                            }
                        },
                        error: function () {
                            tip("客户端请求错误", 'error');
                            return false;
                        }
                    });
                }
            });
        }

    }

    //启用停用
    function startOrStop(flag) {
        var rows = $("#ttAccruedFormulaList").datagrid('getSelections');
        var title = "启用";
        if (flag != 0) {
            title = "停用";
        }
        var url = "ttAccruedFormulaController.do?updateTtAccruedFormula";
        var ids = [];
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定" + title + "所选数据吗?", function (r) {
                if (r) {
                    for (var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {
                            ids: ids.join(','),
                            flag: flag
                        },
                        cache: false,
                        success: function (data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg, 'info');
                                $("#ttAccruedFormulaList").datagrid('reload');
                                $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                            } else {
                                tip(msg, 'error');
                                return;
                            }
                        },
                        error: function () {
                            tip("客户端请求错误", 'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要" + title + "的数据");
        }
    }

    //导出
    function toExcel() {
        excelExport("ttDealerActApplyController.do?exportXls", "dealerActApplyList");
    }

    function showLog(datagridName) {
        var rowData = $('#' + datagridName).datagrid("getSelections");
        if (rowData.length == null || rowData.length < 1 || rowData.length > 1) {
            tip("请选择一行数据进行操作");
            return;
        }
        var id = rowData[0].id;
        var url = "url:logController.do?goLogMain&id=" + id;
        safeShowDialog({
            content: url,
            lock: true,
            title: "日志",
            width: 1000,
            height: 500,
            cache: false,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function stopAct() {
        var rows = $("#dealerActApplyList").datagrid('getSelections');
        if (rows.length < 1) {
            tip("请至少选择一条数据")
        } else {
            getSafeJq().dialog.confirm("你确定停用所选数据吗?", function (r) {
                if (r) {
                    var ids = [];
                    for (var i = 0; i < rows.length; i++) {
                        var subid = rows[i].id;
                        ids.push(subid);
                    }
                    var url = "ttDealerActApplyController.do?stopActApply";
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {
                            ids: ids.join(',')
                        },
                        cache: false,
                        success: function (data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg, 'info');
//                                 $("#dealerActApplyList").datagrid('reload');
                                // $("#ttAccruedFormulaList").datagrid('unselectAll');
                                window.location.reload();
                            } else {
                                tip(msg, 'error');
                                return;
                            }
                        },
                        error: function () {
                            tip("客户端请求错误", 'error');
                            return false;
                        }
                    });
                }
            });
        }

    }

    function submit() {
        var rows = $("#dealerActApplyList").datagrid('getSelections');
        if (!rows || rows.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rows.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        if (rows[0].bpmStatus != 1) {
            tip("已提交的申请不能再提交");
            return;
        }

        $.ajax({
            url: "ttDealerActApplyController.do?submitDealerActApply",
            data: {
                id: rows[0].id
            },
            success: function (data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    tip(msg, 'info');
                    window.location.reload();
                } else {
                    tip(msg, 'error');
                    return;
                }
            },
            error: function () {
                tip("操作失败", "error");
            }
        });

    }

    function revoke() {
        var rows = $("#dealerActApplyList").datagrid('getSelections');
        if (!rows || rows.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rows.length > 1) {
            tip('请选择一条记录再编辑');
            return;
        }
        if (rows[0].bpmStatus != 2) {
            tip("只有未审批的申请可以撤回");
            return;
        }

        $.ajax({
            url: "ttDealerActApplyController.do?revokeDealerActApply",
            data: {
                id: rows[0].id
            },
            success: function (data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    tip(msg, 'info');
                    window.location.reload();
                } else {
                    tip(msg, 'error');
                    return;
                }
            },
            error: function () {
                tip("操作失败", "error");
            }
        });

    }

</script>
