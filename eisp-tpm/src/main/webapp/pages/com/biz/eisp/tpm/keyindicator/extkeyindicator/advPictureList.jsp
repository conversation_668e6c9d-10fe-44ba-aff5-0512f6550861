<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script src="pages/pic/js/viewer.min.js"></script>
<link rel="stylesheet" href="pages/pic/css/viewer.min.css">
<style>
    * { margin: 0; padding: 0;}
    #jqhtml { width: 1250px; margin: 0 auto; font-size: 0;}
    #jqhtml li { display: inline-block; width: 22%; margin-left: 2%; padding-top: 1%;}
    #jqhtml li img { width: 100%;}

    #video { width: 1250px; margin: 0 auto; font-size: 0;}
    #video li { display: inline-block; width: 22%; margin-left: 2%; padding-top: 1%;}
    #video li img { width: 100%;}

    .btns { position: relative; z-index: 10000; width: 700px; margin: 0 auto; text-align: center;}
    .btns a { display: inline-block; width: 70px; margin-top: 15px; line-height: 26px; font-size: 12px; color: #fff; background-color: #21b384; text-decoration: none;}
    .btns a:hover { background-color: #1fa67a;}
</style>

<body>
<div style="width:100%; border:10px solid white;"></div>
<div style="font-size:18px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;照片</div>
    <ul id="jqhtml">
      <c:forEach items="${list}" var="pic" varStatus="idx">
        <li><img data-original="${pic.imgPath}" src="${pic.imgPath}"
                 style='width: 90%;height:100px;cursor: pointer;controls:controls;'
                 alt="${pic.adCode}"></li>
      </c:forEach>
    </ul>
<div style="width:100%; border:8px solid white;"></div>
<div style="width:100%; border:1px solid darkorange;"></div>
<div style="width:100%; border:8px solid white;"></div>
<div style="font-size:18px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;视频</div>
    <ul id="video">
        <c:forEach items="${videoList}" var="vid" varStatus="idx">
            <li><video style='width: 90%;height:120px;cursor: pointer;controls:controls;'
                       src="${vid.imgPath}" alt="${vid.adCode}" onclick="videoBig(this)"/>${vid.imgPath}</li>
        </c:forEach>
    </ul>
</body>

<script>
    var viewer = new Viewer(document.getElementById('jqhtml'), {
        url: 'data-original'
    });

    function show() {
        viewer.show();
    }

    function videoBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        var url = "tsPictureController.do?showVideo&path="+src;
        var id ="advPictureList";
        var title="视频查看";
        createwindow(title, url, id, 5000, 800);
    }

    function videoBig2(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        $.dialog({
            title: "视频查看",
            content: "url:tsPictureController.do?showVideo&path="+src,
            lock: true,
            width: "680",
            height: "560",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }


    function showBigPic(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $.dialog({
            title: "图片查看",
            content: "url:tsPictureController.do?showBigPic&path="+src,
            lock: true,
            width: "800",
            height: "450",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>