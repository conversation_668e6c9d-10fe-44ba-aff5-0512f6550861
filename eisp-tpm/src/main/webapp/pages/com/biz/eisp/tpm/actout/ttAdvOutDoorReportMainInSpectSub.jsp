<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tsActApplyVoDTList"
                    fitColumns="false" checkbox="false"
                    title="" actionUrl="ttAdvOutDoorReportController.do?getReportDetailsInSpectSub"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20" >
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="主键" field="advid" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="false" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatusStr" hidden="true" query="false" sortable="false" ></t:dgCol>

            <t:dgCol title="当前审批人" field="activityName" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="当前审批人" field="activityUser" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="adCode"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>

            <t:dgCol title="(8-稽查人" field="inspector" hidden="false"  sortable="false" align="center" ></t:dgCol>
            <t:dgCol title="8-指派备注" field="assigncomment" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="8-指派日期)" field="disDate" hidden="false" formatter="yyyy-MM-dd" sortable="false" align="center" ></t:dgCol>
            <t:dgCol title="(9-问题类型" field="rettype" hidden="false"  query="true"   sortable="false" align="center" ></t:dgCol>
            <t:dgCol title="9-核查结果" field="result" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="9-扣回金额" field="amtgbk" hidden="false"  sortable="false" align="center" ></t:dgCol>
            <t:dgCol title="9-处罚金额" field="amtpns" hidden="false"  sortable="false" align="center" ></t:dgCol>
            <t:dgCol title="9-登记日期)" field="crdate" hidden="false" formatter="yyyy-MM-dd" sortable="false" align="center" ></t:dgCol>

            <t:dgCol title="1-是否重复(系统判定)" field="isRepeat" replace="是_1,否_0" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="2-重复原因(经销商)" field="repeatReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="(3-疑似重复" field="isSusRepeat" dictionary="dict_is_suspicious" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="3-疑似重复原因" field="susRepeatReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="3-填写人)" field="innerUser1st" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="(4-疑似造假" field="isSusFraud" dictionary="dict_is_suspicious" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="4-疑似造假原因" field="susFraudReson" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="4-填写人)" field="innerUser2ed" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="天能经销商" field="createName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="区域经理审批" field="manager" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司手机" field="mobilephone" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="actType" hidden="false" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="报销材料" field="materialName" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="尺寸长（米）" field="mlength" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="尺寸宽（米）" field="mwidth" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="数量" field="nums" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="面积" field="mspace" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="单价" field="money" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="入账比例" field="discount" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="打折原因" field="disRemark" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="报销金额" field="realAmount" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" sortable="false" ></t:dgCol>



            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternative" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址3（上刊）" field="constructionAddress" hidden="false"  sortable="false" ></t:dgCol>
            <%--<t:dgCol title="客户编码" field="customerCode"  hidden="false" query="true" sortable="false"></t:dgCol>--%>

            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd" query="false" queryMode="group" sortable="false" ></t:dgCol>
            <t:dgCol title="提交日期" field="commitDate" hidden="false" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
            <t:dgCol title="审核日期" field="approveDate" hidden="false" formatter="yyyy-MM-dd" query="true" queryMode="group" sortable="false"></t:dgCol>
            <%--<t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>--%>

            <%--<t:dgCol title="省"       field="province"  hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="市"       field="city"  hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="区"       field="area"  hidden="false" query="true" sortable="false" ></t:dgCol>--%>
            <%--<t:dgCol title="现价" field="nowPrice" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="单价" field="singlePrice" hidden="false"  sortable="false" ></t:dgCol>

            <t:dgCol title="总价" field="sumPrice" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="折扣" field="discount" hidden="false"  sortable="false" ></t:dgCol>



            <t:dgCol title="折后总价" field="discountPrice" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="创建人" field="createName" hidden="false" query="true" sortable="false" ></t:dgCol>

            <t:dgCol title="创建日期" field="createDate" hidden="false" sortable="false" ></t:dgCol>--%>


            <%--<t:dgCol title="前审审核人" field="priorApprove" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="前审日期" field="priorApproveDate" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="前审状态" field="priorApproveStatus" hidden="false" replace="通过_1,驳回_2" sortable="false" ></t:dgCol>

            <t:dgCol title="后审审核人" field="approve" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="后审状态" field="approveStatus" replace="通过_1,驳回_2" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="可疑（系统）" field="suspiciousSystem" hidden="false" replace="否_0,是_1" sortable="false" ></t:dgCol>
            <t:dgCol title="原因（经销商）" field="customerReason" hidden="false"  sortable="false" ></t:dgCol>

            <t:dgCol title="可疑（人工）" field="suspicious" hidden="false" replace="否_0,是_1" sortable="false" ></t:dgCol>
            <t:dgCol title="原因（后审）" field="reason" hidden="false"  sortable="false" ></t:dgCol>--%>
            <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut" url="ttAdvOutDoorReportController.do?exportExcelInppectSub"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
            <%--<t:dgToolBar title="稽查任务指派" icon="icon-log" url="ttAdvOutDoorReportController.do?goAssignForm" funname="goAssignForm"></t:dgToolBar--%>
            <t:dgToolBar title="稽查结果上传" icon="icon-log" url="ttAdvOutDoorReportController.do?goUploadResult" funname="goUploadResult"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>

    function goAssignForm(title, url,id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一行进行任务指派');
            return;
        }
        var actCode = rowsData[0].actCode;
//        alert(actCode);
        var adCode = rowsData[0].adCode;
//        alert(adCode);
        var idCode = rowsData[0].id;
//        alert(id);
        url = url +"&actCode="+actCode+"&adCode="+adCode;
//        id =id+" "+actCode+" "+adCode;
//        alert(id);
        update(title, url,id, width, height);
//        var thisData = {
//            id: id,
//            actCode: actCode,
//            advCode : adCode
//        }
//
//        var d = ajaxPost(thisData, url);
//        alert(d.msg);
    }
    //ajax请求
    function ajaxPost(json,url){

        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }

    function goUploadResult(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一行进行结果上传');
            return;
        }
        var actCode = rowsData[0].actCode;
        var adCode = rowsData[0].adCode;
        var idCode = rowsData[0].id;
        var advid = rowsData[0].advid;

        url = url +"&actCode="+actCode+"&adsCode="+adsCode+"&advid="+advid;
        update(title, url, id, width, height);
    }

    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('每次只能选择一条查看');
            return;
        }
        update(title, url, id, 930, 500);
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoDTList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function returnNotNullValueValue(obj){
        if(typeof(obj) != 'undefined' && obj != '' && obj != null && obj != 'null' ){
            return obj;
        }
        return '';
    }
    function actCodeColor(value,rows,index){
        var rows = $("#tsActApplyVoDTList").datagrid("getRows");
        var discount = returnNotNullValueValue(rows[index].discount);
        if(discount!=''&&discount!='100%'){
            return "<u  style='color: blue;background: yellow'>"+value+"</u>"
        }else{
            return "<u  style='color: blue'>"+value+"</u>"

        }
    }

    function searchProc(rowIndex,rowData){

        var procinstId = rowData.procinstId;
        var url = "taTaskController.do?goInstanceHandleTabForm&isView=true&isReadFlag=false&processInstanceId="+procinstId;
        openwindow('查看',url,'',1200,800);
    }

</script>
