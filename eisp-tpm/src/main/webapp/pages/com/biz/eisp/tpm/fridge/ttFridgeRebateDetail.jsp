<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttFridgeMainList" fitColumns="true" title=""
					actionUrl="ttFridgeRebateController.do?findFridgeDetailByCode&fridgeId=${fridgeId}"
					queryMode="group" pagination="true" idField="id" fit="true">
			<t:dgCol title="编号" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" width="120"></t:dgCol>
			<t:dgCol title="返利金额" field="rebateAmount"  width="120"></t:dgCol>
			<t:dgCol title="已返利金额" field="applyAmount"  width="120"></t:dgCol>
			<t:dgCol title="返利状态" field="rebateStatus" dictionary="rebate_status"  width="120"></t:dgCol>
		</t:datagrid>
	</div>
</div>
