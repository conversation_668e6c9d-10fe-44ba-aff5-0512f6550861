<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report008List"  fitColumns="false" title="主子户关系"
                    pagination="true" autoLoadData="true" actionUrl="report008Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="主户编码" field="mainResidentsCode" query="true"></t:dgCol>
            <t:dgCol title="主户" field="mainResidentsName" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="子户" field="seedResidentsName" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="子户编码" field="seedResidentsCode" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="SAP第一次发票记账日期" field="sapInvoiceDate" query="true" sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report008Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report008Listtb_r").find("input[name='sapInvoiceDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
        $("#report008ListForm").find("label").eq(4).attr("style","width:150px");
        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

</script>
