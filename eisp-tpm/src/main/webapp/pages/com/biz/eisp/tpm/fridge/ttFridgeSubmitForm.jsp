<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html style="overflow-x: hidden;">
<head>
<title>提交申请</title>
<style>
	#sureFilesDetail{display:table;}
    .sureFilesDetail_a{float:left;padding-right:20px;margin-right:10px;position:relative;}
    .sureFilesDetail_a img{position:absolute;top:7px;right:0;}
</style>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/util/processTheme.js"></script>
</head>
<body>
	<t:formvalid formid="formobj" dialog="true" usePlugin="password" layout="table"  action="#"	tiptype="3">
		<input type="hidden" value="${json}" name="json" id="json">
		<table style="width: 600px;" cellpadding="0" cellspacing="1" class="formtable">
			<tr >
				<td align="right" style="width: 20%;">
					<label class="Validform_label"><span style="color: red">*</span>主题:</label></td>
				<td class="value">
					<input id="name" name="name" type="text" style="width: 50%" class="inputxt" dataType="*"> 
					<span class="Validform_checktip"></span> <label
					class="Validform_label" style="display: none;">主题</label></td>
			</tr>
			<tr>
				<td align="right">
					<label class="Validform_label"> <span style="color: red">*</span>详情:</label>
					<br><!-- <span style="color: red">最多输入1000个字</span> -->
				</td>
				<td class="value">
					<textarea id="detail" name="detail" rows="9" cols="80" onkeyup="value=value.replace(/[\\+]/g,'')" dataType="*" >${type }</textarea>
					<span class="Validform_checktip"></span> <label class="Validform_label"
					style="display: none;">详情</label>
				</td>
			</tr>
	</t:formvalid>
</body>
<script type="text/javascript">
//关闭弹出框页面
function close(){
	frameElement.api.close();
}
//关闭遮罩层
function closePro(){
	window.top.$.messager.progress('close');
}
//打开遮罩
function openPro(){
	window.top.$.messager.progress({
		title:'提示',
		msg:'正在提交中......',
		text:'提交中'
	});
}

//提交操作
function doSubmit(){
//	openPro();
	var name = $('#name').val();
	if (name == '') {
		tip("没有填写主题.");
//		closePro();
		return false;
	}
	var detail = $('#detail').val();
	if (detail == '') {
		tip("没有填写详情.");
//		closePro();
		return false;
	}
	
	//执行通用提交方法
//	var url = 'taProcessThemeController.do?doSubmit';
	<%--var param = {name:name,detail:detail,businessKey:'${businessKey}',businessKeyMain:'${businessKeyMain}',fullPathName:'${fullPathName}',params:'${params}'};--%>
//	closePro();
//	var data = ajaxPost(param,url);//ajax提交
	
//	if(data.success){
//		close();
//		W.top.tip(data.msg);
//		W.reloadTable();
//	}else{
//		tip(data.msg);
//		return false;
//	}
	return true;
}
</script>