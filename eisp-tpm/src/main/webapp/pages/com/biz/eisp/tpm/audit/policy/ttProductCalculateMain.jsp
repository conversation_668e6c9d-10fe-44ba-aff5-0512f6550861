<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttProductCalculateList" checkbox="true" fitColumns="true" title="产品政策计算"
                    singleSelect="false" actionUrl="ttProductCalculateController.do?findTtProductCalculateList" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="状态" field="handleStatus" sortable="false" query="true" replace="已处理_1,未处理_0"></t:dgCol>
            <t:dgCol title="计算类型" field="calculateType" sortable="false" query="true" replace="手工录入_1,系统计算_2"></t:dgCol>
            <t:dgCol title="政策组编码" field="productPolicyGroupCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策组名称" field="productPolicyGroupName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策编号" field="productPolicyCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结算年月" field="yearMonth" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案周期" field="policyCycle" sortable="false" query="true" dictionary="product_policy_cycle"></t:dgCol>
            <t:dgCol title="事业部" field="sybOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户销售部" field="salOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策名称" field="productPolicyName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动品项" field="productName" sortable="false"></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="目标销量1（EA）" field="targetQuantity" sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额1（元）" field="targetAmount" sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品" field="relationProductName" sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品销量（EA）" field="relationProductQuantity" sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品销售额（元）" field="relationProductAmount" sortable="false"></t:dgCol>
            <t:dgCol title="目标销量2（EA）" field="targetQuantity2" sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额2（元）" field="targetAmount2" sortable="false"></t:dgCol>
            <t:dgCol title="实际达成量（EA）" field="actualReachQuantity" sortable="false"></t:dgCol>
            <t:dgCol title="实际达成额（元）" field="actualReachAmount" sortable="false"></t:dgCol>
            <t:dgCol title="实际达成率" field="actualReachRateStr" sortable="false"></t:dgCol>
            <t:dgCol title="申请费用金额（元）" field="laterPeriodCost" sortable="false"></t:dgCol>
            <t:dgCol title="申请费率" field="laterPeriodCostRateStr" sortable="false"></t:dgCol>
            <t:dgCol title="核算结案金额（元）" field="auditAmount" sortable="false"></t:dgCol>
            <t:dgCol title="实际费率" field="actualRateStr" sortable="false"></t:dgCol>
            <t:dgCol title="支付方式" field="paymentName" sortable="false"></t:dgCol>
            <t:dgCol title="货补产品" field="supplyProductName" sortable="false"></t:dgCol>
            <t:dgCol title="是否需要结案" field="hasAudit" dictionary="yesorno" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="创建人" field="createName" sortable="false"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" sortable="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName" sortable="false"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" sortable="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="计算创建人" field="calculateName" sortable="false"></t:dgCol>
            <t:dgCol title="计算时间" field="calculateDate" sortable="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgToolBar title="产品政策计算" operationCode="save" icon="icon-save" url="ttProductCalculateController.do?goDoCalculateForm" funname="add" height="200" width="500"></t:dgToolBar>
            <t:dgToolBar title="手工计算结果录入" operationCode="add" icon="icon-add" url="ttProductCalculateController.do?goManualCalculateForm" funname="add" height="200" width="500"></t:dgToolBar>
            <t:dgToolBar title="手工计算结果导入" operationCode="import" icon="icon-dataIn" onclick="importDataByXml({impName:'ttProductCalculate', gridName:'ttProductCalculateList'})"></t:dgToolBar>
            <t:dgToolBar title="按条件删除" operationCode="deleteAll" icon="icon-remove" url="ttProductCalculateController.do?deleteAllProductCalculate" funname="deleteAllProductCalculate"></t:dgToolBar>
            <t:dgToolBar title="删除" operationCode="delete" icon="icon-remove" url="ttProductCalculateController.do?deleteProductCalculate" funname="deleteALLSelect"></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="ttProductCalculateController.do?exportXls" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="无需结案转上账" operationCode="toaccounting" icon="icon-submit" url="ttProductCalculateController.do?saveToAccounting" funname="toaccounting" height="200" width="500"></t:dgToolBar>
            <t:dgToolBar title="总部转产品政策结案" operationCode="tosystemaudit" icon="icon-submit" url="ttProductCalculateController.do?goToAuditForm" funname="add" height="200" width="500"></t:dgToolBar>
            <t:dgToolBar title="区域转产品政策结案" operationCode="torgaudit" icon="icon-submit" url="ttProductCalculateController.do?saveTorgAudit" funname="torgaudit" height="200" width="500"></t:dgToolBar>
            <t:dgToolBar title="结案" operationCode="start" icon="icon-start" url="ttProductCalculateController.do?startOrStop" funname="startOrStop(1)" height="200" width="500"></t:dgToolBar>
            <t:dgToolBar title="不结案" operationCode="stop" icon="icon-stop" url="ttProductCalculateController.do?startOrStop" funname="startOrStop(0)" height="200" width="500"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    //动态进度条Js
    var interval = null; //定时器
    function dynamicProgress(functionKey) {
        $.messager.progress({interval:0});
        interval = setInterval("getProgressInfo('"+functionKey+"')", 1000); //启动定时器，每1秒执行showImportInfo方法一次
    }

    //显示导入详情
    function getProgressInfo(functionKey){
        var param = {};
        if(functionKey != 'undefined') {
            param.functionKey = functionKey;
        }
        $.ajax({
            async : false,
            cache : false,
            type : 'POST',
            data : param,
            url:"progressController.do?getProgress",// 请求的action路径
            error : function() {// 请求失败处理函数

            },
            success : function(data) {
                var result = $.parseJSON(data);
                if(result.obj != null) {
                    $($.messager.progress("bar")).progressbar('setValue', ((result.obj.current/result.obj.total) * 100).toFixed(2));
                } else {
                    window.clearInterval(interval);
                }
            }
        });
    }

    function closeDynamicProgress() {
        window.clearInterval(interval);
        $.messager.progress("close");
    }

    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='beginDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 结束日期
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
    });

    //删除全部价格申请
    function deleteAllProductCalculate(title, url, name, width, height) {
        gridname = name;
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.dialog.confirm("是否删除查询出来的全部计算数据？", function() {
            $.ajax({
                url:url,
                type:'post',
                data:queryParams,
                cache:false,
                success:function(data) {
                    var d = $.parseJSON(data);
                    var msg = d.msg;
                    tip(msg,"info");
                    reloadTable();
                }
            });
        });
    }


    function torgaudit(title, url, name, width, height) {
        gridname = name;

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.dialog.confirm("是否根据当前查询条件全部区域转产品政策结？", function() {
            $.ajax({
                url:url,
                type:'post',
                data:queryParams,
                cache:false,
                success:function(data) {
                    var d = $.parseJSON(data);
                    var msg = d.msg;
                    tip(msg,"info");
                    reloadTable();
                }
            });
        });
    }
    function toaccounting(title, url, name, width, height) {
        var hasAudit=$("select[name='hasAudit']").val();
        if(hasAudit==undefined||hasAudit==""){
          return tip("无法转上账,是否结案的选项必须为否!");
        }
        if(hasAudit!=null&&"1"==hasAudit){
            return tip("无法转上账,是否结案的选项必须为否!");
        }
        gridname = name;

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.dialog.confirm("是否根据当前查询条件批量无需结案转上账？", function() {
            $.ajax({
                url:url,
                type:'post',
                data:queryParams,
                cache:false,
                success:function(data) {
                    var d = $.parseJSON(data);
                    var msg = d.msg;
                    tip(msg,"info");
                    reloadTable();
                }
            });
        });
    }

    //修改是否结案
    function startOrStop(flag){
        var handleStatus=$("select[name='handleStatus']").val();
        if(handleStatus==undefined||handleStatus==""){
            return tip("无法修改,状态必须为未处理!");
        }
        if(handleStatus!=null&&"1"==handleStatus){
            return tip("无法修改,状态必须为未处理!");
        }
        var rows = $('#ttProductCalculateList').datagrid('getSelections');
        var ids = "";
        for (var i=0;i<1;i++)
        {
            ids = "'"+rows[i].actCode+"'" ;
        }
        for (var i=1;i<rows.length;i++)
        {
           ids = ids +','+"'"+ rows[i].actCode +"'";
        }
        if (!rows || rows.length ==0 ) {
            tip("请选择一条数据操作");
            return;
        } else {
            isUpdate = false;
            $.ajax({
                type : "POST",
                url : "ttProductCalculateController.do?startOrStop",
                data : {
                    "ids" :ids,
                    "flag":flag
                },
                dataType : "json",
                success : function(data) {
                    isUpdate = true;
                    if(data)
                    {
                        tip(data.msg);
                        if (data.success) {
                            $("#ttProductCalculateList").datagrid('reload');
                        }
                    }
                },
                error:function(){
                    isUpdate = true;
                    tip("服务器异常，请稍后再试");
                }
            });
        }
    }
</script>

