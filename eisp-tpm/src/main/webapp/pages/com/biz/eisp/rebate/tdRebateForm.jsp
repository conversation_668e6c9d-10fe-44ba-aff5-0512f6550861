<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>返利信息</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
	<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" beforeSubmit="buildJson()"
		action="rebateController.do?saveTdRebate">
		<div class="line-title">基本属性</div>
		<input type="hidden" name="id" value="${rebVo.id}">
		<input type="hidden" name="rebJson" id="rebJson">
		<div>
			<div class="form">
                <label class="Validform_label">返利名称:</label>
                <input name="rebName" class="inputxt" value="${rebVo.rebName}" datatype="*"/>
                <span style="color: red">*</span>
            </div>
            <div style="clear: both;"></div>
			<div class="form">
				<label class="Validform_label">返利时间:</label> <input name="rebStart"
					class="inputxt Wdate" value="${rebVo.rebStart}" dataType="*"
					style="width: 140px;"
					onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" readonly />
				- <input name="rebEnd" class="inputxt Wdate" dataType="*"
					value="${rebVo.rebEnd}" style="width: 140px;"
					onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" readonly />
				<span style="color: red">*</span>
			</div>
            <div style="clear: both;"></div>
			<div class="form">
				<label class="Validform_label">返利周期:</label>
				<t:dictSelect field="timeType" typeGroupCode="dic_time_type" dataType="*"
					defaultVal="${rebVo.timeType }"></t:dictSelect>
				<span style="color: red">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">支付方式:</label>
				<t:dictSelect field="payType" typeGroupCode="pay_type" dataType="*"
					defaultVal="${rebVo.payType }"></t:dictSelect>
				<span style="color: red">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">数据范围:</label>
                <select name="dtScope">
                    <option value="0" <c:if test="${rebVo.dtScope=='0' ||rebVo.dtScope==null}">selected</c:if>>自身进货</option>
                    <option value="1" <c:if test="${rebVo.dtScope=='1'}">selected</c:if>>所有进货</option>
                </select>
				<span style="color: red">*</span>
			</div>
			<div class="form">
				<label class="Validform_label">返利组:</label>
				<t:dictSelect field="rebGroup" defaultVal="${rebVo.rebGroup }" dataType="*"
							  typeGroupCode="rebate_group"></t:dictSelect>
				<span style="color: red">*</span>
			</div>
            <div style="clear: both;"></div>
            <div class="form form-2">
                <label class="Validform_label">返利描述:</label>
                <textarea rows="4" name="rebNote">${rebVo.rebNote }</textarea>
            </div>
			<div class="form">
				<label class="Validform_label">返利产品:</label> <span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;" onclick="openProduct('product')">添加单品</a>
						<a iconcls="icon-remove" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;" onclick="areaDelete('product')">删除</a>
					</div>
					<div class="promotionArea" id="product"></div>
				</span>
			</div>
			<div class="form">
				<span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;" onclick="openSeries('series')">添加系列</a>
						<a iconcls="icon-remove" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;" onclick="areaDelete('series')">删除</a>
					</div>
					<div class="promotionArea" id="series"></div>
				</span>
			</div>
			<div style="clear: both;"></div>
		</div>
		<div class="line-title">返利公式<c:forEach items="${temps}" var="d">
    	   <a class="easyui-linkbutton l-btn l-btn-plain" plain="true" href="javascript:;" ><span
			class="l-btn-left"><span class="l-btn-text" style="color:#FF6666;">${d.name}（${d.tag}）</span></span></a>
    	</c:forEach></div>
		<div>
			<div class="promotionForm_tem">
				<div>
					<a iconcls="icon-append" class="easyui-linkbutton l-btn"
						plain="true" href="javascript:;" onclick="addFormula('formula')">添加返利公式</a>
				</div>
				<div id="formulaTplContainer"></div>
			</div>
		</div>
		<div class="line-title">返利范围</div>
		<div>
			<div class="form">
				<label class="Validform_label">包含:</label> <span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;" onclick="openOrg('includeOrg')">添加区域</a>
						<a iconcls="icon-remove" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;"
							onclick="areaDelete('includeOrg')">删除</a>
					</div>
					<div class="promotionArea" id="includeOrg"></div>
				</span>
			</div>
			<div class="form">
				<span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;"
							onclick="openCust('includeCust')">添加经销商</a> <a
							iconcls="icon-remove" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;"
							onclick="areaDelete('includeCust')">删除</a>
					</div>
					<div class="promotionArea" id="includeCust"></div>
				</span>
			</div>
			<div style="clear: both;"></div>
		</div>
		<div>
			<div class="form">
				<label class="Validform_label">非包含:</label> <span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;" onclick="openOrg('excludeOrg')">添加区域</a>
						<a iconcls="icon-remove" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;"
							onclick="areaDelete('excludeOrg')">删除</a>
					</div>
					<div class="promotionArea" id="excludeOrg"></div>
				</span>
			</div>
			<div class="form">
				<span>
					<div>
						<a iconcls="icon-append" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;"
							onclick="openCust('excludeCust')">添加经销商</a> <a
							iconcls="icon-remove" class="easyui-linkbutton l-btn"
							plain="true" href="javascript:;"
							onclick="areaDelete('excludeCust')">删除</a>
					</div>
					<div class="promotionArea" id="excludeCust"></div>
				</span>
			</div>
			<div style="clear: both;"></div>
		</div>
	</t:formvalid>
</body>

<style>
#content .inputxt, #steps form select {
	width: 250px;
}

#formobj {
	padding: 0 5px;
}

#steps form div.form {
	float: left;
	width: 300px;
	min-height: 22px;
}

#steps form div.form-2 {
	min-width: 700px;
}

#steps form div.form-2 textarea {
	width: 100%;
}

#steps form div.form .Validform_label {
	width: 85px;
	margin-right: 5px;
	font-weight: normal;
}

#steps form div.form .Validform_label input[type="radio"], #steps form div.form .Validform_label input[type="checkbox"]
	{
	position: absolute;
	left: 3px;
	top: 3px;
	width: 15px;
	height: 15px;
}

.formDiv {
	float: left;
}

.line-title {
	font-size: 14px;
	padding: 5px;
	/* background:#b4d2ea; */
}

.form_label {
	float: left;
	padding-right: 20px;
	line-height: 26px;
}

.form_label input[type="radio"] {
	float: left;
	margin: 5px 0 0 0px;
	width: 17px;
	height: 17px;
}

.promotionForm_tem {
	margin: 10px;
	padding: 5px;
	border: 1px solid #a5aeb6;
	background: #f5f5f5;
	line-height: 30px;
}

.promotionForm_tem input {
	margin: 0 10px;
	width: 60px;
	border: 0;
}

.promotionArea {
	border: 1px solid #ddd;
	background: #fff;
	min-height: 80px;
	padding: 5px;
}

.promotionArea a {
	margin-right: 5px;
}

.selectArea {
	background: #ffc6f2 !important;
}
</style>

<script src="resources/laytpl/laytpl.js"></script>
<script src="resources/tools/Map.js"></script>

<script>
	/** 返利区域 */
	var TdRebAreaVo = function() {
		this.id;
		/**返利id*/
		this.rebId;
		/**组织id*/
		this.orgId;
		/**组织名称**/
		this.orgName;
		/**0非包含，1包含*/
		//结算类型
		this.settlementName;
		this.settlementCode;
		//合同类型
		this.contractName;
		this.contractCode;
		this.protype;
	};
	/** 返利产品 */
	var TdRebProductVo = function() {
		this.id;
		/**返利外键*/
		this.rebId;
		/**返利产品编码*/
		this.rebProduct;
		/**返利产品名称*/
		this.rebProName;
		/**返利产品类型 0整体1单品3系列*/
		this.rebProType;
	};
	/** 返利公式 */
	var TdRebFormulaVo = function() {
		this.id;
		this.rebId;
		this.formulaCon;
		this.formulaVal;
	};
</script>


<script type="text/html" id="areaBlockTpl">
    <a iconcls="icon-ok"
       class="easyui-linkbutton l-btn l-btn-plain"
       plain="true" onclick="selectArea(this)"  data-code="{{d.code}}"  data-name="{{d.name}}" data-type="{{d.type}}">
    <span class="l-btn-left">
    <span class="l-btn-text icon-ok l-btn-icon-left">{{d.name}}</span>
    </span>
    </a>
</script>
<script type="text/html" id="formulaBlockTpl">
    <div>
        返利条件<input type="text" name='formula_con' style="width: 200px" value="{{d.con}}" readonly/>
        返利值=<input type="text" name='formula_val' style="width: 200px" value="{{d.val_}}" readonly/>
        <a iconcls="icon-remove" class="easyui-linkbutton l-btn l-btn-plain"
           plain="true" href="javascript:;" style="float:right" onclick="remove(this)">
            <span class="l-btn-left">
                <span class="l-btn-text icon-remove l-btn-icon-left">删除</span>
            </span>
        </a>
    </div>
</script>

<script type="text/javascript">
    var areaArr=new Array();
    var proArr=new Array();
    var formulaArr=new Array();
	var includeArea = [];
	var excludedArea = [];
	var formulaReb = [];
    //公式
    var formulaMap =new Map();
	//产品
	var productMap = new Map();
	//系列
	var seriesMap = new Map();

	//包含
	// 经销商
	var includeCustMap = new Map();
	//组织
	var includeOrgMap = new Map();

	//非包含
	//经销商
	var excludeCustMap = new Map();
	//组织
	var excludeOrgMap = new Map();

	//公共的map集合定义
	var mapList = {
		includeCustMap : includeCustMap,
		includeOrgMap : includeOrgMap,
		excludeOrgMap : excludeOrgMap,
		excludeCustMap : excludeCustMap,
		productMap : productMap,
		formulaMap :formulaMap,
		seriesMap : seriesMap
	};

	//选择组织
	function openOrg(containerElement) {
		$.dialog({
			title : "组织管理",
			content : "url:tdPromotionController.do?goTdPromotionOrgMain",
			lock : true,
			width : "500",
			height : "400",
			zIndex : 10000,
			parent : windowapi,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rows = iframe.$("#orgList").datagrid('getSelections');
				//                console.log("选中的组织", rows);
				putAreaBlock(containerElement, rows, "id", "orgName","org");
			},
			cancelVal : '关闭',
			cancel : true
		});
	}

	//选择产品
	function openProduct(containerElement) {
		$.dialog({
			title : "产品管理",
			content : "url:tdProductController.do?goTdProductMain&type=choose",
			lock : true,
			width : "500",
			height : "400",
			zIndex : 10000,
			parent : windowapi,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rows = iframe.$("#productListChoose").datagrid(
						'getSelections');
//				                console.log("选中的产品", rows);
				putAreaBlock(containerElement, rows, "id", "fullName","product");
			},
			cancelVal : '关闭',
			cancel : true
		});
	}

	function openSeries(containerElement) {
		$.dialog({
			title : "系列管理",
			content : "url:tdMaterialController.do?tmProductSeriesChoose",
			lock : true,
			width : "500",
			height : "400",
			zIndex : 10000,
			parent : windowapi,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rows = iframe.$("#prdList").datagrid('getSelections');
//				console.log("选中的系列", rows);
				putAreaBlock(containerElement, rows, "id", "text","series");
			},
			cancelVal : '关闭',
			cancel : true
		});
	}

    function putAreaBlock(containerElement, json, code, name, type) {
        var map = mapList[containerElement + "Map"];
        $(json).each(function (e, v) {
            var areaBlockTpl = $("#areaBlockTpl").html();
            if(v[name] || v[code]){
                v.name = v[name];
                v.code = v[code];
                v.type = type;
                //判断是否存在
                var flag = map.containsKey(v.code);
                if (!flag) {
                    map.put(v.code, v.name);
                    laytpl(areaBlockTpl).render(v, function (html) {
                        $("#" + containerElement).append(html);
                    });
                } else {
                    tip("已经选择了" + v.code + "," + v.name);
                }
            }
        });
    }
	/**
	 * 查询经销商
	 */
    function openCust(containerElement) {
        $.dialog({
            title: "经销商管理",
            content: "url:rebateController.do?goCustTypeChoose",
            lock: true,
            width: "500",
            height: "100",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;

                var settlementList = iframe.$("input[name='settlement']:checked");
                var contractList = iframe.$("input[name='contract']:checked");
                var contracJson = [];
                var settlementJson = [];
                $(contractList).each(function (i, o) {
                    var obj = $(o);
                    contracJson.push({name: obj.attr("data-name"), code: "contract"+"_"+obj.val(), type: "contract"});
                });
                $(settlementList).each(function (i, o) {
                    var obj = $(o);
                    settlementJson.push({name: obj.attr("data-name"), code: "settlement"+"_"+obj.val(), type: "settlement"});
                });

                putAreaBlock(containerElement, contracJson, "code", "name","contract");
                putAreaBlock(containerElement, settlementJson, "code", "name","settlement");

            },
            cancelVal: '关闭',
            cancel: true
        });
    }

	function selectArea(obj) {
		$(obj).toggleClass("selectArea");
	}
	function areaDelete(id) {
		var map = mapList[id + "Map"];
		var obj = $("#" + id + " .selectArea");
		obj.each(function(index, object) {
			//var key = object.dataset;
			map.remove($(object).attr("data-code"));
		});
		$("#" + id + " .selectArea").remove();
	}
	//添加公式
    function addFormula(containerElement) {
        createwindowExt('添加返利公式',
            'rebateController.do?goRebateFormula', "900", "400", {
                ok: function () {
                    iframe = this.iframe.contentWindow;//formula_con
                    if (!iframe.beforCheckFomula()) {
                        return false;
                    }
                    var formulaArray = [];
                    iframe.$("body").find("input[name='formula_con']").each(
                        function (index, obj) {
                            formulaArray.push({
                                "con": $(obj).val(),
                                "val_": $(obj).parent().find("[name='formula_val']").val()
                            })
                        });
                    putFormulaBlock(containerElement, formulaArray, "con", "val_");
                }
            });
    }

    function putFormulaBlock(containerElement, json, con, val_) {
        var map = mapList[containerElement + "Map"];
        var formulaBlockTpl = $("#formulaBlockTpl").html();
        $(json).each(function (e, v) {
            v.con = v[con];
            v.val_ = v[val_];
            //判断是否存在
            var flag = map.containsKey(v.con);
            if (!flag) {
                map.put(v.con, v.val_);
                laytpl(formulaBlockTpl).render(v, function (html) {
                    $("#formulaTplContainer").append(html);
                });
            } else {
                tip("已经选择了" + v.con + "," + v.val_);
            }
        });
    }

    /**
     * 移除规则
     * @param obj
     */
    function remove(obj) {
//        if (formulaCheckCount()) {
//            tip("不能删除，至少需要一条公式");
//        } else {
            $(obj).parent().remove();
//        }
    }

    //公式条数检测
    function formulaCheckCount() {
        var rules = $("#formulaTplContainer").find("div");
        if (rules.length >= 1) {
            return true;
        } else {
            return false;
        }
    }

    function buildJson() {
        var tempobj = new Object();
        var areaArr_din = new Array();
        var areaArr_dex = new Array();
        var areaArr_ain = new Array();
        var areaArr_aex = new Array();
        var proArr_pro = new Array();
        var proArr_seri = new Array();
        var formulaArr = new Array();

        if (!formulaCheckCount()) {
            tip("至少添加一条公式");
            return false;
        }
        //公式
        $("#formulaTplContainer").find(
            "input[name='formula_con']").each(
            function (index, obj) {
                var fm = new TdRebFormulaVo();
                fm.rebId = $("#id").val();
                fm.formulaCon = $(obj).val();
                fm.formulaVal = $(obj).parent().find("[name='formula_val']").val();
                formulaArr.push(fm);
            });
        // $("input[name='rebFormulasJson']").val(JSON.stringify(formulaArr));
        //经销商
        $("#includeCust").find(
            "a[data-code]").each(
            function (index, obj) {
                var areain = new TdRebAreaVo();
                if($(obj).attr("data-type") == 'settlement'){
                    areain.settlementCode = $(obj).attr("data-code").split('_')[1];
                    areain.settlementName = $(obj).attr("data-name");
                }else{
                    areain.contractCode = $(obj).attr("data-code").split('_')[1];
                    areain.contractName = $(obj).attr("data-name");
                }
                areain.protype = 1;
                areaArr_din.push(areain);
            });
        $("#excludeCust").find(
            "a[data-code]").each(
            function (index, obj) {
                var areainex = new TdRebAreaVo();
                if($(obj).attr("data-type") == 'settlement'){
                    areainex.settlementCode = $(obj).attr("data-code").split('_')[1];
                    areainex.settlementName = $(obj).attr("data-name");
                }else{
                    areainex.contractCode = $(obj).attr("data-code").split('_')[1];
                    areainex.contractName = $(obj).attr("data-name");
                }
                areainex.protype = 0;
                areaArr_dex.push(areainex);
            });
        $("#includeOrg").find(
            "a[data-code]").each(
            function (index, obj) {
                var areain = new TdRebAreaVo();
                areain.orgId = $(obj).attr("data-code");
                areain.orgName =  $(obj).attr("data-name");
                areain.protype = 1;
                areaArr_ain.push(areain);
            });
        $("#excludeOrg").find(
            "a[data-code]").each(
            function (index, obj) {
                var areainex = new TdRebAreaVo();
                areainex.orgId = $(obj).attr("data-code");
                areainex.orgName =  $(obj).attr("data-name");
                areainex.protype = 0;
                areaArr_aex.push(areainex);
            });

        //产品
        $("#product").find(
            "a[data-code]").each(
            function (index, obj) {
                var prop = new TdRebProductVo();
                prop.rebProduct = $(obj).attr("data-code");
                prop.rebProName = $(obj).attr("data-name");
                prop.rebProType = 1;
                proArr_pro.push(prop);
            });
        $("#series").find(
            "a[data-code]").each(
            function (index, obj) {
                var prs = new TdRebProductVo();
                prs.rebId = $("#id").val();
                prs.rebProduct = $(obj).attr("data-code");
                prs.rebProName = $(obj).attr("data-name");
                prs.rebProType = 2;
                proArr_seri.push(prs);
            });
        // $("input[name='rebateProItemsJson']").val(JSON.stringify(proArr));
        // $("input[name='rebAreaItemsJson']").val(JSON.stringify(areaArr));
        tempobj.rebAreaItemsJson_din = areaArr_din;
        tempobj.rebAreaItemsJson_dex = areaArr_dex;
        tempobj.rebAreaItemsJson_ain = areaArr_ain;
        tempobj.rebAreaItemsJson_aex = areaArr_aex;
        tempobj.rebateProItemsJson_pro = proArr_pro;
        tempobj.rebateProItemsJson_seri = proArr_seri;
        tempobj.rebFormulasJson = formulaArr;
        $("input[name='rebJson']").val(JSON.stringify(tempobj));
//        console.log(tempobj);
//        debugger;
    }
</script>
<script type="text/javascript">
    //编辑返利政策
    $(function () {
        var rebjson =${rebVo.rebJson};
        if (rebjson != undefined && rebjson != null) {

            var din = rebjson.rebAreaItemsJson_din;
            var dex = rebjson.rebAreaItemsJson_dex;
            var ain = rebjson.rebAreaItemsJson_ain;
            var aex = rebjson.rebAreaItemsJson_aex;
            //单品
            var pro = rebjson.rebateProItemsJson_pro;
            var seri = rebjson.rebateProItemsJson_seri;
            var formul = rebjson.rebFormulasJson;
//            console.log("rebjson", rebjson);


            //赋值单品
            putAreaBlock("product", pro, "rebProduct", "rebProName","product");
            //赋值系列
            putAreaBlock("series", seri, "rebProduct", "rebProName","series");
            //包含赋值区域
            putAreaBlock("includeOrg", ain, "orgId", "orgName","org");
            //非包含赋值区域
            putAreaBlock("excludeOrg", aex, "orgId", "orgName","org");
            //包含经销商
            var dinModify = [];
            $(din).each(function (i, o) {
                if (o['settlementCode']) {
                    dinModify.push({
                        "settlementCode": "settlement_" + o.settlementCode,
                        "settlementName": o.settlementName
                    });
                }
                if (o['contractCode']) {
                    dinModify.push({"contractCode": "contract_" + o.contractCode, "contractName": o.contractName});
                }
            })
//            console.log(dinModify);
            putAreaBlock("includeCust", dinModify, "settlementCode", "settlementName","settlement");
            putAreaBlock("includeCust", dinModify, "contractCode", "contractName","contract");
            //非包含经销商

            var dexModify = [];
            $(dex).each(function (i, o) {
                if (o['settlementCode']) {
                    dexModify.push({
                        "settlementCode": "settlement_" + o.settlementCode,
                        "settlementName": o.settlementName
                    });
                }
                if (o['contractCode']) {
                    dexModify.push({"contractCode": "contract_" + o.contractCode, "contractName": o.contractName});
                }
            })
            putAreaBlock("excludeCust", dexModify, "settlementCode", "settlementName","settlement");
            putAreaBlock("excludeCust", dexModify, "contractCode", "contractName","contract");
            //公式
            putFormulaBlock("formula",formul,"formulaCon","formulaVal");
        }
    });
</script>
</html>
