<%--asdasdasdasdasdasdasd--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<div style="clear:both; width: 1280px;height: 80px;">
    <%--<div region="center" style="padding: 1px;">--%>
    <t:datagrid name="suspiciousOpinionsList" title=""  actionUrl="ttActApplyExcuteWorkFlowController.do?findTheSuspiciousOpinionsList&businessKey=${businessKey}&flagKey=${flagKey}&srcType=${srcType}"
                idField="id" fit="true"  fitColumns="false" pagination="false">
        <t:dgCol title="id" field="id" hidden="true" sortable="false"></t:dgCol>
        <t:dgCol title="节点" field="nodeCode" hidden="false" sortable="false"></t:dgCol>
        <t:dgCol title="抬头标识" field="title" hidden="false" sortable="false"></t:dgCol>
        <t:dgCol title="状态" field="isSuspicious" dictionary="dict_is_suspicious" width="100" sortable="false"></t:dgCol>
        <t:dgCol title="意见" field="opinion"  width="300" sortable="false"></t:dgCol>
        <t:dgToolBar title="新增可疑意见" icon="icon-edit" url="ttActApplyExcuteWorkFlowController.do?saveOrUpdateTheSuspOpin" funname="editTheSuspOpin" ></t:dgToolBar>

    </t:datagrid>
    <%--</div>--%>
</div>
<script>

    function getTitle(){
        return '填写意见';
    }

    //填写意见
    function editTheSuspOpin(title, url, gname, callback){
        //获取数据
        var rowData = getTheRowData(gname,getTaskCode());

        var html = returnShowHtml();
        title = getTitle();
        $.dialog({
            title: title,
            content: html,
            lock: true,
            width: "380",
            height: "200",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            init : function(){
                var parentTemp = parent.parent.parent;
//                parentTemp.$('#tempIsSuspicious').val(rowData.isSuspicious);
                parentTemp.$('#tempMsg').val(rowData.opinion);
            },
            ok : function() {
                var parentTemp = parent.parent.parent;
                var isSuspicious = parentTemp.$('#tempIsSuspicious').val();
                 if(!checkIsNotUndefinedAndNullAndNullValue(isSuspicious)){
                 alert('是否可疑不能为空');
                 return false;
                 }
                var opinion = parentTemp.$('#tempMsg').val();
                /*if(!checkIsNotUndefinedAndNullAndNullValue(opinion)){
                 alert('意见不能为空');
                 return false;
                 }*/
                var tempData = {
                    title : title,
                    opinion : opinion,
                    isSuspicious : isSuspicious
                }
                //开始保存
                return starEditTheSuspOpin(gname,rowData,url,tempData);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    function returnShowHtml() {
        var html = "";
        html += '<div class="form"><label class="Validform_label" name="tempIsSuspicious"><span style="color: red;">*</span>是否可疑：</label> ';
        html += '<select id="tempIsSuspicious">';
        html += '<option value="">--请选择--</option>';
        html += '<option value="0">可疑</option>';
        html += '<option value="1">正常</option>';
        html += '</select>';
        html += ' </div>';
        html += '<div class="form" style="margin-top: 10px;"><label class="Validform_label" style="position: relative;left: 0;top: 0;font-size: 15px; float: left; ">意见：</label><textarea id="tempMsg" rows="10" cols="40"></textarea><div>';
        return html;
    }

//    function returnShowHtml() {
//        var html = "";
//        html += '<div class="form"><label class="Validform_label" name="tempIsSuspicious"><span style="color: red;">*</span>是否可疑：</label> ';
//         html += structureRadio();
//        html += ' </div>';
//        html += '<div class="form" style="margin-top: 10px;"><label class="Validform_label" style="position: relative;left: 0;top: 0;font-size: 15px; float: left; ">意见：</label><textarea id="tempMsg" rows="10" cols="40"></textarea><div>';
//        return html;
//    }
    function structureRadio(){
        var selectHtml = '<select id="tempIsSuspicious">';
        selectHtml += '<option value="">--请选择--</option>';
        <c:forEach items="${dictIsSuspiciousList}" var="dictIsSuspicious" >
        selectHtml += '<option value="${dictIsSuspicious.dictCode}">${dictIsSuspicious.dictValue}</option>';
        </c:forEach>
        selectHtml += '</select>';
        return selectHtml;
    }

    //开始
    function starEditTheSuspOpin(gname,rowData,url,tempData){
        var flg = false;
        var thisData = {
            id : rowData.id,
            flagKey : getFlagKey(),
            nodeCode : getTaskCode(),
            businessKey : getBusinessKey(),
            srcType : getSrcType(),
            title : tempData.title,
            opinion : tempData.opinion,
            isSuspicious : tempData.isSuspicious
        }
        var d = ajaxPost(thisData, url);
        if(d.success){
            $("#" + gname).datagrid('reload');
            flg = true;
        }
        newTip(d.msg);
        return flg;
    }

    //获取一条数据
    function getTheRowData(gname,targ){
        var rowData = {}
        var rows = $("#" + gname).datagrid('getRows');
        if(checkIsNotUndefinedAndNullAndNullValue(rows) && rows.length > 0){
            for(var i = 0 ; i < rows.length ; i ++ ){
                var row = rows[i];
                if(targ == row.nodeCode){
                    rowData = row;
                    break;
                }
            }
        }
        return rowData;
    }

</script>