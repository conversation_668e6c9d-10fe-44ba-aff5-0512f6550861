<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttProductActWorkFlowList" checkbox="false" fitColumns="true" title="" onLoadSuccess="loadTotal"
                    actionUrl="ttQuotaActWorkFlowController.do?findTtQuotaActWorkFlowList&phoneSend=1&flagKey=${flagKey}" idField="id" fit="true" queryMode="group" >
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="referBusinessKey" field="referBusinessKey" hidden="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode"></t:dgCol>
            <t:dgCol field="costAccountCode" title="活动细类编码" hidden="true" ></t:dgCol>
            <t:dgCol field="actCode" title="活动编号" query="true" ></t:dgCol>
            <t:dgCol field="actName" title="活动名称"  query="true"></t:dgCol>
            <t:dgCol field="yearMonth" title="活动年月"  ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" sortable="false" query="true"></t:dgCol>
            <t:dgCol field="productName" title="产品品项"></t:dgCol>
            <t:dgCol field="costTypeName" title="活动大类"  ></t:dgCol>
            <t:dgCol field="costAccountName" title="活动细类" ></t:dgCol>
            <t:dgCol field="orgName" title="部门" ></t:dgCol>
            <t:dgCol field="businessUnitName" title="费用归属事业部"></t:dgCol>
            <t:dgCol field="amount" title="活动总金额"></t:dgCol>
            <t:dgCol field="quantity" title="数量"></t:dgCol>
            <t:dgCol field="paymentCode" title="支付方式" dictionary="payment_type"></t:dgCol>
            <t:dgCol field="bpmStatus" title="审批状态" dictionary="bpm_status" hidden="true"></t:dgCol>
            <t:dgCol field="createName" title="发起人" ></t:dgCol>
            <t:dgCol field="createDate" title="发起时间"  formatter="yyyy-MM-dd"  queryMode="group"></t:dgCol>

            <t:dgCol title="操作" field="opt"></t:dgCol>
            <t:dgFunOpt title="门店详情" funname="detailShow"></t:dgFunOpt>
            <t:dgToolBar title="导出" icon="icon-dataOut" url="ttQuotaActWorkFlowController.do?exportXlsQuota&phoneSend=1&type=4&flagKey=${flagKey}" funname="excelExport"></t:dgToolBar>
            <t:dgToolBar title="日志" icon="icon-log" url="" onclick="detailHisShow()"></t:dgToolBar>
            <t:dgToolBar title="查看原活动详情" icon="icon-look" url="" onclick="detailHisInfo()"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
        $("input[name='customerName']").parent().append("&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
    });

    function loadTotal(){
        var queryParams = $('#ttProductActWorkFlowList').datagrid('options').queryParams;
        $('#ttProductActWorkFlowListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttQuotaActWorkFlowController.do?findTtHiActTotalAmount&phoneSend=1&flagKey=${flagKey}";
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("费用总金额："+d.obj);
            }
        }
        });
    }

    function detailHisShow() {
        var select= $("#ttProductActWorkFlowList").datagrid("getSelected");
        var id=select.referBusinessKey;
        createwindowExt(
            '原活动历史',
            'ttQuotaActChangeWorkFlowController.do?goChWorkFlowModelAndView&phoneSend=1&processKey=Q04&businessObjId='+id,
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex:12000,
                width : 900,
                height : 400,
                button : [ {
                    name : '取消',
                    callback : function() {
                    }
                } ]
            });
    }

    //查看原活动详情
    function detailHisInfo() {
        var select= $("#ttProductActWorkFlowList").datagrid("getSelected");
        var actCode=select.actCode;
        var customerCode = select.customerCode;
        createwindowExt(
            '原活动详情',
            'ttQuotaActChangeWorkFlowController.do?goAndViewOldInfo&processKey=Q01&actCode='+actCode+'&customerCode='+customerCode,
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex:12000,
                width : 900,
                height : 400,
                button : [ {
                    name : '取消',
                    callback : function() {
                    }
                } ]
            });
    }

    function detailShow(index) {
        $("#ttProductActWorkFlowList").datagrid("unselectAll");
        $("#ttProductActWorkFlowList").datagrid("selectRow",index);
        var select= $("#ttProductActWorkFlowList").datagrid("getSelected");
        var id=select.id;
        var flagKey='${flagKey}';
        var costAccountCode=select.costAccountCode;
        createwindowExt(
            '门店详情',
            'ttQuotaActWorkFlowController.do?goTtActWorkFlowTerminalMain&phoneSend=1&flagKey='+flagKey+'&id='+id+'&costAccountCode='+costAccountCode,
            "", "", {
                lock : true,
                parent : windowapi,
                zIndex:12000,
                width : 900,
                height : 400,
                button : [ {
                    name : '取消',
                    callback : function() {
                    }
                } ]
            });
    }
</script>

