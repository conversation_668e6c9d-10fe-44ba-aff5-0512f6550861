<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>活动申请</title>
	<t:base type="jquery,easyui,tools,DatePicker,handsontable"></t:base>
</head>
<div id="travelExpensesApp" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<div class="datagrid-wrap panel-body">
			<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
				<div class="datagrid-toolbar-but">
               <span style="float:left;">
                  <a href="#" class="easyui-linkbutton" plain="true" icon="icon-save" onclick="saveAllDataInfo()">保存</a>
               </span>
					<span id="customer_span_temp_info" style="float:right">
               </span>
					<div style="clear:both;float: none;height:0;display: block;"></div>
				</div>
				<div class="datagrid-toolbar-search" id="travelHeadDiv">

					<label><span style="padding-left:5px; color: red">*</span>活动类型</label>
					<select name="actType" id="actType" class="input">
						<!-- <option value="">--请选择--</option> -->
						<c:forEach items="${actTypes}" var="type">
							<option value="${type.actType}" <c:if test="${vo.actType== type.actType}">selected="selected"</c:if>>${type.actTypeName}</option>
						</c:forEach>
					</select>
					<br>
					<hr style="margin-top:5px;" >
					<div style="padding-top: 5px;" >
						<label><span style="padding-left:5px; color: red">*</span>活动明细</label>
						<a href="#" class="easyui-linkbutton" style="float: right;" plain="true" icon="icon-add" onclick="addRows()">选择客户</a>
						<a href="#" class="easyui-linkbutton" style="float: right;" plain="true" icon="icon-remove" onclick="delRow()">删除</a>
						<%--<a href="#" class="easyui-linkbutton" style="float: right;" plain="true" icon="icon-save" onclick="stopEdit()">暂存</a>--%>
					</div>
				</div>
			</div>
			<div  style="width: 978px; height: 400px; overflow: auto;">
				<t:datagrid name="areaCustList" onClick="editRow" autoLoadData="true"  queryMode="group"
							actionUrl="ttAreaActApplyController.do?findAreaActDetailList"  pagination="false"  fitColumns="true" checkbox="false" singleSelect="true">
					<t:dgCol title="经销商编码" field="dealerCode" width="50" sortable="false" ></t:dgCol>
					<t:dgCol title="经销商名称" field="dealerName" width="100" sortable="false" ></t:dgCol>

					<t:dgCol title="授信积分" field="applyAmount" width="50" sortable="false" editor="{type:'numberbox',options:{precision:2}}"></t:dgCol>
					<t:dgCol title="基金余额" field="fundBalance" width="50" sortable="false" ></t:dgCol>
					<t:dgCol title="未报销金额" field="otherBalance" width="50" sortable="false" ></t:dgCol>
					<t:dgCol title="CRMS待报销积分" field="crmsBlance" width="50" sortable="false" ></t:dgCol>
					<t:dgCol title="本系统未报销金额" field="eblance" width="50" sortable="false" ></t:dgCol>
					<t:dgCol title="开始日期" field="startDate" width="50" sortable="false" ></t:dgCol>
					<%--<t:dgCol title="结束日期" field="endDate" width="50" sortable="false" editor="{type:'datebox',options:{required: true}}"></t:dgCol>--%>
					<t:dgCol title="结束日期" field="endDate" width="50" sortable="false" editor="{type:'datebox',options:{required: true}}"></t:dgCol>
				</t:datagrid>
			</div>

		</div>
	</div>
</div>

<script>

    var editRowStatus = false;//是否编辑状态

    function stopEdit() {
        var rows = $("#areaCustList").datagrid("getRows");
        for(var i = 0;i<rows.length;i++){
            $("#areaCustList").datagrid('endEdit', i);
        }
    }
    function editRow(rowIndex,rowData) {
        editRowStatus = true;
        $("#areaCustList").datagrid("beginEdit", rowIndex);
    }



    function addRows(){
        var j=1;
        var targetUrl = 'ttAreaActApplyController.do?goCustomerMain';

        //弹出窗口
        $.dialog({
            content : 'url:' + targetUrl,
            zIndex : 500000,
            title : '客户信息',
            lock : true,
            parent : windowapi,
            width : 800,
            height : 600,
            left : '65%',
            top : '65%',
            opacity : 0.4,
            button : [ {
                name : '确定',
                callback : function() {
                    //回调事件
                    editDateRowStatus=true;
                    iframe = this.iframe.contentWindow;
                    var rowsData = iframe.$('#dealerActApplyList').datagrid('getSelections');
                    for(var i=0;i<rowsData.length;i++){
                        $("#areaCustList").datagrid("insertRow",{index:j,row:{"dealerCode":rowsData[i].dealerCode,"dealerName":rowsData[i].dealerName,"doorNum":'',
								"fundBalance":rowsData[i].fundBalance,
                                "otherBalance":rowsData[i].otherBalance,
                                "crmsBlance":rowsData[i].crmsBlance,
                                "eblance":rowsData[i].eblance,
								'startDate':getNowTime(),'endDate':getEndDate()}});
                        j=j+1;
                    }

                },
                focus : true
            }, {
                name : '取消',
                callback : function() {
                }
            } ]
        });

        var newMonth='';
        var dellink="<a class='easyui-linkbutton pull-right del-row row-button' plain='true' onclick='delTourContRow(this)'icon='icon-remove'></a>";
       // $("#areaCustList").datagrid("insertRow",{index:j,row:{"dealerCode":'test0'+j,"dealerName":'测试经销商'+j,"doorNum":'6',"fundBalance":'10000000'}});


    }

    function getNowTime() {
        var now = new Date();

        var year = now.getFullYear();       //年
        var month = now.getMonth() + 1;     //月
		if(month<10){
		    month='0'+month;
		}
        var day = now.getDate();            //日
        if(day<10){
            day='0'+day;
        }

        var hh = now.getHours();            //时
        var mm = now.getMinutes();          //分
        var ss = now.getSeconds();           //秒

		return year+'-'+month+"-"+day;

    }

    function getEndDate() {
        var now = new Date();

        var year = now.getFullYear()+1;       //年
        var month = now.getMonth() + 1;     //月
        if(month<10){
            month='0'+month;
        }
        var day = now.getDate();            //日
        if(day<10){
            day='0'+day;
        }

        var hh = now.getHours();            //时
        var mm = now.getMinutes();          //分
        var ss = now.getSeconds();           //秒

        return year+'-'+month+"-"+day;

    }

    function delRow(){
        // var checks=$("input[type='checkbox']");
        // var chcked=$('input:checkbox[name="ck"]:checked');
        // alert(chcked.length);
        // $('input:checkbox[name="ck"]:checked').each(function() //multiple checkbox的name
        // {
         //    $(this).parent().parent().parent().parent().parent().remove();
		//
        // });
		// return;




        var seletctTarget =  $("#areaCustList").datagrid("getSelections");
        // alert(seletctTarget.length);
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }

        var selectRows = [];
        for (var i = 0; i < seletctTarget.length; i++) {
            selectRows.push(seletctTarget[i]);
        }
        for(var j =0;j<selectRows.length;j++){
            var rowIndex = $('#areaCustList').datagrid('getRowIndex',selectRows[j]);
            $('#areaCustList').datagrid('deleteRow',rowIndex);
        }


    }



    function serializeJson(form) {
        var serializeObj = {};
        var formArr = form.serializeArray();
        $(formArr).each(function () {
            serializeObj[this.name] = this.value;
        });
        return serializeObj;
    }


    //选择渠道
    function openCust() {
        var targetUrl = 'ttAreaActApplyController.do?goCustomerMain';
        //弹出窗口
        $.dialog({
            content : 'url:' + targetUrl,
            zIndex : 500000,
            title : '客户信息',
            lock : true,
            parent : windowapi,
            width : 600,
            height : 600,
            left : '65%',
            top : '65%',
            opacity : 0.4,
            button : [ {
                name : '确定',
                callback : function() {
                    //回调事件
                    iframe = this.iframe.contentWindow;
                    var rowsData = iframe.$('#dictSelect').datagrid('getSelections');
                    var channelNameData = "";
                    var channelIdData = "";
                    for(var i=0;i<rowsData.length;i++){
                        if(i==0){
                            channelNameData+=rowsData[i].dictValue;
                            channelIdData+=rowsData[i].dictCode;
                        }else{
                            channelNameData+=','+rowsData[i].dictValue;
                            channelIdData+=','+rowsData[i].dictCode;
                        }
                    }
                    $("#channelNameTxt").val(channelNameData);
                    $("#channelName").val(channelNameData);
                    $("#channel").val(channelIdData);
                },
                focus : true
            }, {
                name : '取消',
                callback : function() {
                }
            } ]
        });
    }

    function saveAllDataInfo() {
        var jsonStr;
        var rowsData = $('#areaCustList').datagrid('getRows');

        for(var j=0;j<rowsData.length;j++){
            $("#areaCustList").datagrid("endEdit", j);
		}
        rowsData = $('#areaCustList').datagrid('getRows');

        if(rowsData.length<1){
            tip("请选择经销商信息");
            return false;
		}else{
            for(var j=0;j<rowsData.length;j++){
                var startDate=rowsData[j].startDate;
                var endDate=rowsData[j].endDate;
                var doorNum=rowsData[j].doorNum;
                if(endDate==null||endDate==''){
                    tip("第"+(j+1)+"行请输入结束时间")
					return false;
				}

                //alert(startDate+'-----'+endDate);
                if(endDate<startDate){
                    tip("第"+j+1+"行结束时间必须大于开始时间");
                    return false;
                }
                var day=DateDiff(endDate,startDate)
                if(day>365){
                    tip("第"+j+1+"行开始日期至结束日期必须小于或等于1年")
                   return false;
                }
            }
            jsonStr=JSON.stringify(rowsData);
		}

        var actType=$("#actType").val();
        if(actType==null||actType==''){
            tip("请选择活动类型");
            return false;
        }
        // var startDate=$("#startDate").val();
        // if(startDate==null||startDate==''){
        //     tip("请选择活动开始时间");
        //     return false;
        // }
        // var endDate=$("#endDate").val();
        // if(endDate==null||endDate==''){
        //     tip(" 请选择活动结束时间");
        //     return false;
        // }

        var url="ttAreaActApplyController.do?saveAreaActApply"
        $.ajax({
            type: 'POST',
            url: url,
            data: {jsonArryStr:jsonStr,
				actType:actType
                // startDate:startDate,
				// endDate:endDate
			       },
            dataType: "json",
            success: function(data){
                if(data.success){
                    W.tip(data.msg);
                    W.$("#dealerActApplyList").datagrid("reload");
                    close();
				}else{
                    tip(data.msg);
				}
            },
            error:function () {
                tip("保存失败，请检查数据");
            }
        });

    }

    function DateDiff(d1,d2){
        var day = 24 * 60 * 60 *1000;
        try{
            var dateArr = d1.split("-");
            var checkDate = new Date();
            checkDate.setFullYear(dateArr[0], dateArr[1]-1, dateArr[2]);
            var checkTime = checkDate.getTime();

            var dateArr2 = d2.split("-");
            var checkDate2 = new Date();
            checkDate2.setFullYear(dateArr2[0], dateArr2[1]-1, dateArr2[2]);
            var checkTime2 = checkDate2.getTime();

            var cha = (checkTime - checkTime2)/day;
            return cha;
        }catch(e){
            return false;
        }
    }


    function close(){
        frameElement.api.close();
    }
</script>