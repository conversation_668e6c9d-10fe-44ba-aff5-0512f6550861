<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report030List" fitColumns="false" title="SCI门店活跃度报表"
                    pagination="true" autoLoadData="true" actionUrl="report030Controller.do?findReportList" idField="id" fit="true">
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="终端编码" field="terminalCode" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="SAP编码" field="erpCode" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="终端名称" field="terminalName" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="终端联系人" field="linkman" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="终端联系人电话" field="linkmanPhone" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="省" field="province" query = "true" queryMode="accurate" formType="combobox" formUrl="tmBusinessAreaController.do?getAreaForCombobox&areaLevel=1" sortable="false"></t:dgCol>
            <t:dgCol title="市" field="city" query = "true" formType="combobox" formUrl="tmBusinessAreaController.do?getAreaForCombobox&parentId=2" sortable="false"></t:dgCol>
            <t:dgCol title="区" field="area" query = "true" formType="combobox" formUrl="tmBusinessAreaController.do?getAreaForCombobox&parentId=3" sortable="false"></t:dgCol>
            <t:dgCol title="镇" field="extChar11" sortable="false"></t:dgCol>
            <t:dgCol title="终端地址" field="address" sortable="false"></t:dgCol>
            <t:dgCol title="生效状态" field="enableStatus" sortable="false" dictionary="enable_status"></t:dgCol>
            <t:dgCol title="对接人职位" field="dockPosition" query="true" hidden="true"></t:dgCol>
            <t:dgCol title="对接人姓名" field="dockUserName" query="true"  hidden="true"></t:dgCol>
            <t:dgCol title="上级客户" field="customerName" query="true"  hidden="true"></t:dgCol>
            <t:dgCol title="创建人" field="createName" query = "true" sortable="false"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" query = "true" sortable="false" formatter="yyyy-MM-dd" queryMode="group"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName" sortable="false"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" sortable="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="审批状态" field="approvalStatus" query="true" sortable="false" dictionary="terminal_status"></t:dgCol>
            <t:dgCol title="低温申请费用次数" field="dwActCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="常温申请费用次数" field="cwActCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="奶粉申请费用次数" field="nfActCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="低温检查次数" field="dwCollectionCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="常温检查次数" field="cwCollectionCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="奶粉检查次数" field="nfCollectionCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="低温拜访次数" field="dwVisitCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="常温拜访次数" field="cwVisitCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="奶粉拜访次数" field="nfVisitCountNum" sortable="false"></t:dgCol>
            <t:dgCol title="低温是否拜访" field="dwVisitDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="常温是否拜访" field="cwVisitDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="奶粉是否拜访" field="nfVisitDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="低温申请费用" field="dwActDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="常温申请费用" field="cwActDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="奶粉申请费用" field="nfActDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="低温是否检查" field="dwCollectionDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="常温是否检查" field="cwCollectionDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="奶粉是否检查" field="nfCollectionDataFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="名称是否重复" field="duplTerminalNameFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="联系人重复" field="duplLinkmanFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="电话是否重复" field="duplLinkmanPhoneFlag" replace="是_Y,否_N" hidden="true" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate" sortable="false" hidden="true" query="true"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" sortable="false" hidden="true" query="true"></t:dgCol>
            <t:dgCol title="所属组织" field="orgId" hidden="true" query = "true" formType="combotree" formUrl="tmOrgController.do?getOrgTree" sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report030Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function() {
        $("#orgId").combotree({width: 200});
        $("#orgId").combotree({
            onShowPanel: function () {
                $("#orgId").combotree('reload', "tsExtendsMdmController.do?getOrgForTree");
            }
        });

        $("input[name='beginDate']").addClass("Wdate").css({'height': '20px', 'width': '120px'}).click(function () {WdatePicker({dateFmt: 'yyyy-MM-dd'});});
        $("input[name='endDate']").addClass("Wdate").css({'height': '20px', 'width': '120px'}).click(function () {WdatePicker({dateFmt: 'yyyy-MM-dd'});});
    });

    //编辑时默认加载当前省下属市
    $("#province").combobox({
        onSelect:function(data){
            var name = data.value;
            $("#city").combobox("clear");
            $("#area").combobox("clear");
            $("#city").val("");
            $("#area").val("");
            $('#city').combobox('reload',"tmBusinessAreaController.do?getAreaForCombobox&name="+encodeURI(encodeURI(name)));
        }
    });
    $("#city").combobox({
        onSelect:function(data){
            var name = data.value;
            $("#area").combobox("clear");
            $("#area").val("");
            $('#area').combobox('reload',"tmBusinessAreaController.do?getAreaForCombobox&name="+encodeURI(encodeURI(name)));
        }
    });
</script>
