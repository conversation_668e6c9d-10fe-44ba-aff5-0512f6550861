<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/util/processTheme.js"></script>
<div id="ttActLongtermMain" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="ttActLongtermList" title=""  actionUrl="ttActLongTermWorkFlowController.do?findTtActLongTermWorkFlowList&phoneSend=1&flagKey=${flagKey}"
                    idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动类型" field="actType" replace="长期待摊_0,广告费用或营销费用(到部门)_1"></t:dgCol>

            <t:dgCol title="流程类型" field="actTypeCode" dictionary="ad_long_act_type"></t:dgCol>
            <t:dgCol title="活动编号" field="billCode" align="center"></t:dgCol>
            <t:dgCol title="活动名称" field="billName" align="center"></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeCode" hidden="true"></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountCode" hidden="true"></t:dgCol>
            <t:dgCol title="活动细类名称" field="costAccountName" autocomplete="true"></t:dgCol>

            <c:if test="${flag == '0'}">
                <t:dgCol title="客户名称" field="customerName"  query="true"></t:dgCol>
                <t:dgCol title="所属组织" field="orgName"></t:dgCol>
                <t:dgCol title="部门编码" field="orgCode" query="true" hidden="true"></t:dgCol>
                <t:dgCol title="费用归属事业部" field="businessUnitName"></t:dgCol>
                <t:dgCol title="费用归属事业部编码" field="businessUnitCode" hidden="true" query="true"></t:dgCol>
            </c:if>

            <t:dgCol title="开始时间" field="beginDate" query="true" formatter="yyyy-MM-dd" queryMode="group"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>


            <t:dgCol title="活动总金额" field="amount"></t:dgCol>



            <t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" query="true"></t:dgCol>
            <t:dgCol title="备注" field="remark"></t:dgCol>
            <t:dgCol title="发起人" field="createName" query="true"></t:dgCol>
            <t:dgCol title="发起时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
            <t:dgCol title="操作" field="opt" width="100"></t:dgCol>
            <t:dgFunOpt funname="viewDetail(id,actType)" title="查看详情"></t:dgFunOpt>

            <t:dgToolBar title="导出" icon="icon-dataOut" url="ttActLongTermWorkFlowController.do?export&phoneSend=1&flagKey=${flagKey}" funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
    <input type="text">
</div>

<script type="text/javascript">
    $(function(){


        $("input[name='orgCode']").combotree({
            url: 'tmOrgController.do?getParentOrg&phoneSend=1&pid=${orgId}'
        });
        $("#ttActLongtermListtb_r").find("input[name='beginDate_begin']").prev().html("活动时间");
        $("#ttActLongtermListtb_r").find("input[name='costAccountCode']").combobox({
            url:"ttBudgetApiController.do?findTtAccountListByCostTypeCode&phoneSend=1"
        });
        $("#ttActLongtermListtb_r").find("input[name='businessUnitCode']").combobox({
            url:"tmCommonMdmController.do?findOrgCombox&phoneSend=1"
        });

        $("#ttActLongtermListtb_r").find("input[name='costTypeCode']").combobox({
            url:"ttBudgetApiController.do?findTtCostTypeList&phoneSend=1",
            onSelect:function(data){
                $("#ttActLongtermListtb_r").find("input[name='costAccountCode']").combobox({
                    url:"ttBudgetApiController.do?findTtAccountListByCostTypeCode&phoneSend=1&costTypeCode="+data.value
                });
            }
        });
    });
    //查看详情
    function viewDetail(id,actType){
        safeShowDialog({
            content : "url:ttActLongTermWorkFlowController.do?goTtActDetail&phoneSend=1&id="+id+"&actType="+actType,
            lock : true,
            zIndex:200000,
            title : "详情",
            width : 1000,
            height : 600,
            left :'85%',
            cache : false,
            cancelVal : '关闭',
            cancel : true
        });
    }
    //编辑
    function updateActLongTerm(title, url, gridname, width, height){
        var actLongtermTarget = $("#ttActLongtermList").datagrid("getSelected");
        if(actLongtermTarget == null || actLongtermTarget == ""){
            tip("请选择一条需要编辑的数据");
            return false;
        }
        if(!(actLongtermTarget.bpmStatus == 1 || actLongtermTarget.bpmStatus == 4)){
            tip("该活动已经提交流程无法编辑");
            return false;
        }
        if(actLongtermTarget.actTypeCode == 'A01'){//广告费用
            url = "ttActAdController.do?goTtActAdForm&phoneSend=1";
        }else{
            url = "ttActLongTermController.do?goTtActLongTermForm&phoneSend=1";
        }
        update(title, url, gridname, width, height);
    }

    //删除
    function delActLongterm(){
        var actLongtermTarget = $("#ttActLongtermList").datagrid("getSelected");
        if(actLongtermTarget == null || actLongtermTarget == ""){
            tip("请至少选择一条需要删除的数据");
            return false;
        }
        if(actLongtermTarget.bpmStatus != 1){
            tip("该活动已经产生流程数据无法删除");
            return false;
        }
        getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url : "ttActLongTermController.do?delActLongterm&phoneSend=1",
                    type : 'post',
                    data : {
                        id : actLongtermTarget.id
                    },
                    cache : false,
                    success : function(data) {
                        var d = $.parseJSON(data);
                        var msg = d.msg;
                        if (d.success) {
                            tip(msg);
                            reloadTable();
                        }else{
                            tip(msg);
                            return false;
                        }
                    },
                    error:function(){
                        tip("客户端请求错误");
                        return false;
                    }
                });
            }
        });
    }

    //查看明细
    function previewDetail(){
        var selecTarget = $("#ttActLongtermList").datagrid('getSelections');
        if (selecTarget == "" || selecTarget == null) {
            tip("请选择一条需要查看的活动");
            return false;
        }
        if(selecTarget != null && selecTarget != "" && selecTarget.length > 1){
            tip("请选择一条需要查看的活动");
            return false;
        }
        var url = "";
        var id = selecTarget[0].id;
        var actTypeCode = selecTarget[0].actTypeCode;
        if(actTypeCode == 'A01'){
            url = "ttActAdController.do?goTtActLongTermTab&phoneSend=1&id="+id;
        }else{
            url = "ttActLongTermController.do?goTtActLongTermTab&phoneSend=1&id="+id;
        }
        openwindow("查看明细", url,'',1000, 800);
    }
    //提交工作流
    function submitLeave() {
        var url = "ttActLongTermWorkFlowController.do?goTtActWorkFlowSubmitMain&phoneSend=1";
        $.dialog({
            title: "提交流程",
            content: "url:" + url,
            lock: true,
            width: "1700",
            height: "800",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                $('#btn_sub', this.iframe.contentWindow.document).click();
                return false;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
    function reloadTable(){
        ttActLongtermListsearch();
    }
</script>
