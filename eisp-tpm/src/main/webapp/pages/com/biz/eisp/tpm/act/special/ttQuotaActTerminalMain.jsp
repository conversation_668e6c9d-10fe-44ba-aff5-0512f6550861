<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttQuotaActTerminalMainList" checkbox="false" fitColumns="true"  title="" pagination="false"
                    actionUrl="ttActSpecialWorkFlowController.do?findTtQuotaActTerminalMainList&phoneSend=1&id=${id}" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol field="customerCode" title="客户编号" ></t:dgCol>
            <t:dgCol field="customerName" title="客户名称"  ></t:dgCol>
            <t:dgCol field="yearMonth" title="年月"></t:dgCol>
            <t:dgCol field="terminalCode" title="门店编号"  sortable="false" ></t:dgCol>
            <t:dgCol field="terminalName" title="门店名称"></t:dgCol>
            <t:dgCol field="terminalPoint" title="重点门店"></t:dgCol>
            <t:dgCol field="quantity" title="数量"   ></t:dgCol>
            <t:dgCol field="amount" title="活动总金额"  ></t:dgCol>
            <t:dgCol field="companyAmount" title="公司承担金额"></t:dgCol>
            <t:dgCol field="salesVolume" title="门店销售额"></t:dgCol>
            <t:dgCol field="rateStr" title="费率"></t:dgCol>
            <t:dgCol field="remark" title="备注"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
//        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });
    });
</script>

