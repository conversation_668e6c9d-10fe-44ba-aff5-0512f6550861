<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
	<head>
		<title>终端查看</title>
		<t:base type="jquery,easyui,tools"></t:base>
		<style>
			#steps form div.form {float:left;width:195px !important;min-height:26px;padding-left:115px !important;}
			#steps form div.form .Validform_label {width:115px;margin-right:5px;}
			.formDiv {float:left;}
		</style>
	</head>
	<body>
		<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
			action="tmTerminalController.do?saveTmTerminal">
			<div>
				<!-- 动态表单加载 -->
				<t:formmdm extendTableName="${extendTableName}" optype="${optype}"
					objName="${extendTableName}"></t:formmdm>
				<div style="clear:both;"></div>
			</div>
			<!-- 自定义表单 begin -->		
			<c:choose>  
			   <c:when test="${!empty custPostList}">   
			 	   <!-- 编辑与查看加载对接人职位信息 begin -->
		   			<c:forEach var="items" items="${custPostList}" varStatus="idx" >
		   				
						<div class="formDiv" data-index="${idx.index}" >
							<input type="hidden" name="relationIndex" value="${idx.index}"/>
							<input type="hidden" name="termCustId" value="${items.id}"/>
							<div class="form">
								<label class="Validform_label" name="businessGroupOrgName">
									<c:if test="${optype!=2}">
									<img src='resources/easyui/themes/icons/remove.png' class='formDivRemove' onclick='removePosition(this)' style='padding: 4px;cursor: pointer;' />
									</c:if>
									所属部门${idx.index+1}:
								</label>
								<input id="businessGroupOrgName" name="businessGroupOrgName" readonly="readonly" value="${items.businessGroupOrgName }" class="inputxt"  onClick="chooseOrg(this);"/>
								<input id="businessGroupOrgId" name="businessGroupOrgId" value="${items.businessGroupOrgId }" type="hidden"  /> 
							</div>
							<div class="form">
								<label class="Validform_label" name="positionName">对接人职位${idx.index+1}: </label> 
								<input id="positionName" name="positionName" value="${items.positionName}" class="inputxt" readonly="readonly" onClick="choose(this);"/>
								<input id="positionIds" name="positionIds" value="${items.positionId}" type="hidden" /> 
							</div>
							<div class="form">
								<label class="Validform_label" name="fullName">对接人姓名${idx.index+1}: </label> 
								<input id="fullName" name="fullName" class="inputxt" value="${items.fullName}" readonly="readonly"/> 
							</div>
							<div class="form">
								<label class="Validform_label" name="mobilephone">对接人电话${idx.index+1}: </label>
								<input id="mobilephone" name="mobilephone" readonly="readonly" class="inputxt" value="${items.mobilephone}" />
							</div>
							<div class="form">
								<label class="Validform_label" name="channelName">渠道${idx.index+1}: </label>
								<textarea rows="4" cols="22" id="channelName" name="channelName" readonly="readonly" class="inputxt"  onClick="chooseChannel(this);">${items.channelName }</textarea>
								<input id="channelCode" name="channelCode" value="${items.channelCode }" type="hidden"  /> 
							</div>
							<div class="form">
								<label class="Validform_label" name="productName">产品${idx.index+1}: </label>
								<textarea rows="4" cols="22" id="productName" name="productName" readonly="readonly" class="inputxt"  onClick="chooseProduct(this);">${items.productName }</textarea>
								<input id="productCode" name="productCode" value="${items.productCode }" type="hidden"  /> 
							</div>
							<div class="form">
								<label class="Validform_label" name="businessAreaName">区域${idx.index+1}: </label>
								<textarea rows="4" cols="22" id="businessAreaName" name="businessAreaName" readonly="readonly" class="inputxt"  onClick="chooseBusinessArea(this);">${items.businessAreaName }</textarea>
								<input id="businessAreaCode" name="businessAreaCode" value="${items.businessAreaCode }" type="hidden"  /> 
							</div>
							<div class="form">
								<label class="Validform_label" name="customerName">上级客户${idx.index+1}: </label> 
								<input id="customerId" name="customerId" value="${items.custId}" type="hidden" />
								<input id="customerName" value="${items.customerName}" name="customerName"  class="inputxt" readonly="readonly" onClick="chooseCust(this);"/>
							</div>
							<div class="form">
								<label class="Validform_label" name="bgExtChar1s">备注一${idx.index+1}: </label>
								<input id="bgExtChar1s" value="${items.bgExtChar1}" name="bgExtChar1s"  class="inputxt"/>
							</div>
							<div class="form">
								<label class="Validform_label" name="bgExtChar2s">备注二${idx.index+1}: </label>
								<input id="bgExtChar2s" value="${items.bgExtChar2}" name="bgExtChar2s"  class="inputxt"/>
							</div>
						</div>
					</c:forEach>
					   <!-- 编辑与查看加载对接人职位信息 end -->
		   		</c:when>  
			   <c:otherwise>
			   		<!-- 新增默认对接人职位信息 begin -->
					<div class="formDiv" data-index="${fn:length(custPostList)}" >
					<input type="hidden" name="relationIndex" value="${fn:length(custPostList)}"/>
						<div class="form">
							<label class="Validform_label" name="businessGroupOrgName">
								<c:if test="${optype!=2}">
								<img src='resources/easyui/themes/icons/remove.png' class='formDivRemove' onclick='removePosition(this)' style='padding: 4px;cursor: pointer;' />
								</c:if>
								所属部门1:
							</label>
							<input id="businessGroupOrgName" name="businessGroupOrgName" readonly="readonly" value="" class="inputxt"  onClick="chooseOrg(this);"/>
							<input id="businessGroupOrgId" name="businessGroupOrgId" value="" type="hidden"  /> 
						</div>
						<div class="form">
							<label class="Validform_label" name="positionName">对接人职位1: </label> 
							<input id="positionName" name="positionName" value="" readonly="readonly" class="inputxt" onClick="choose(this);"/>
							<input id="positionIds" name="positionIds" value="" type="hidden" /> 
						</div>
						<div class="form">
							<label class="Validform_label" name="fullName">对接人姓名1: </label>
							<input id="fullName" name="fullName" class="inputxt" readonly="readonly" value="" />
						</div>
						<div class="form">
							<label class="Validform_label" name="mobilephone">对接人电话1: </label>
							<input id="mobilephone" name="mobilephone" readonly="readonly" class="inputxt" value="" />
						</div>
						<div class="form">
							<label class="Validform_label" name="channelName">渠道1: </label>
							<textarea rows="4" cols="22" id="channelName" name="channelName" readonly="readonly" value="" class="inputxt"  onClick="chooseChannel(this);"></textarea>
							<input id="channelCode" name="channelCode" value="" type="hidden"  /> 
						</div>
						<div class="form">
							<label class="Validform_label" name="productName">产品1: </label>
							<textarea rows="4" cols="22" id="productName" name="productName" readonly="readonly" value="" class="inputxt"  onClick="chooseProduct(this);"></textarea>
							<input id="productCode" name="productCode" value="" type="hidden"  /> 
						</div>
						<div class="form">
							<label class="Validform_label" name="businessAreaName">区域1: </label>
							<textarea rows="4" cols="22" id="businessAreaName" name="businessAreaName" readonly="readonly" value="" class="inputxt"  onClick="chooseBusinessArea(this);"></textarea>
							<input id="businessAreaCode" name="businessAreaCode" value="" type="hidden"  /> 
						</div>
						<div class="form">
							<label class="Validform_label" name="customerName">上级客户1: </label> 
							<input id="customerName" name="customerName" readonly="readonly" class="inputxt" onClick="chooseCust(this);" />
							<input id="customerId" name="customerId" value="" type="hidden" />
						</div>
						<div class="form">
							<label class="Validform_label" name="bgExtChar1s">备注一: </label>
							<input id="bgExtChar1s" value="" name="bgExtChar1s"  class="inputxt"/>
						</div>
						<div class="form">
							<label class="Validform_label" name="bgExtChar2s">备注二: </label>
							<input id="bgExtChar2s" value="" name="bgExtChar2s"  class="inputxt"/>
						</div>
					</div>
					<!-- 新增默认对接人职位信息 end -->
				</c:otherwise>  
			</c:choose>  
			<!-- 自定义表单 end-->		
		</t:formvalid>
		<c:if test="${not empty includeJsp}">
			<jsp:include page="${includeJsp}" flush="true"></jsp:include>
		</c:if>
	</body>
<script type="text/javascript">
	$(document).ready(function(){
		//编辑时默认加载当前省下属市
		var province=	$('#province').val();
		if(province!=null&&province!=''){
			$('#cb_city').combobox('reload',"tmBusinessAreaController.do?getAreaForCombobox&name="+encodeURI(encodeURI(province)));
		}
		//编辑时默认加载当前市下属区
		var city=$('#city').val();
		if(city!=null&&city!=''){
			$('#cb_area').combobox('reload',"tmBusinessAreaController.do?getAreaForCombobox&name="+encodeURI(encodeURI(city)));
		}
		//省onchange事件 加载市下拉列表 
		$('#cb_province').combobox({
			onSelect: function(record){
			 	$("#province").val(record.value);
				var url="tmBusinessAreaController.do?getAreaForCombobox&name="+encodeURI(encodeURI(record.text));
				$("#cb_city").combobox("clear");
				$("#cb_area").combobox("clear");
				$("#cb_city").combobox('reload',url);
				$("#city").val("");
				$("#area").val("");
			}
		});
		//市onchange事件 加载区下拉列表 
		$('#cb_city').combobox({
			onSelect: function(record){
				$("#city").val(record.value);
				var url="tmBusinessAreaController.do?getAreaForCombobox&name="+encodeURI(encodeURI(record.text));
				$("#cb_area").combobox("clear");
				$("#cb_area").combobox('reload',url);
				$("#area").val("");
			}
		});
		if('${optype}' != '2'){
			$("label[name='businessGroupOrgName']").eq($(".formDiv").length-1).find("img").remove();
			$("label[name='businessGroupOrgName']").eq($(".formDiv").length-1).prepend("<img src='resources/easyui/themes/icons/add.png' class='formDivAdd' onclick='addPosition()' style='padding: 4px;cursor: pointer;' /><img src='resources/easyui/themes/icons/remove.png' class='formDivRemove' onclick='removePosition(this)' style='padding: 4px;cursor: pointer;' />");
		}
		
	 });
	
	//清除业务组
	function clearAll(_this) {
	}
	
	//选择所属部门
	function chooseOrg(_this) {
		var myIndex = $(_this).closest(".formDiv").attr("data-index");
		var url = "tmOrgController.do?goOrgSelectMain";
		createwindowExt('组织列表',url,400,500,{
			ok:function(){
				iframe = this.iframe.contentWindow;
				var data = iframe.$("#orgList").datagrid("getSelected");
				$("[data-index='"+myIndex+"']").find("input[name='businessGroupOrgId']").val(data.id);
				$("[data-index='"+myIndex+"']").find("input[name='businessGroupOrgName']").val(data.orgName);
			}
		});
	}
	
	//选择对接人职位弹出框
	function choose(_this) {
		var myIndex = $(_this).closest(".formDiv").attr("data-index");
		var targetUrl = 'tmPositionController.do?goTmPositionQuerySelectMain';
		safeShowDialog({
			content : 'url:' + targetUrl,
			title : '职位选择',
			lock : true,
			width : 600,
			height : 450,
			button : [ {
				name : '确定',
				callback : 	function () {
					iframe = this.iframe.contentWindow;
				
					var positionName = iframe.gettmPositionListSelections('positionName');
					if(positionName==null||positionName==''){
						$.messager.alert('提示','未选择任何职位信息');    
						return;
					}
					
					var flag=false;
					$("input[name='positionName']").each(function(i, o) {
						var pname=$(o).val();
						if(pname==positionName){
							$.messager.alert('提示','职位'+positionName+'已存在');    
							flag=true ;
							return;
						}
					});
					if(flag){
						return;
					}
					var obj = new Object();
					obj.positionName = positionName;
					var fullName = iframe.gettmPositionListSelections('fullName');
					obj.fullName = fullName;
					var mobilephone = iframe.gettmPositionListSelections('mobilephone');
					obj.mobilephone = mobilephone;
					var id = iframe.gettmPositionListSelections('id');
					obj.positionIds = id;
					callback(obj,myIndex);
				},
				focus : true
			}, {
				name : '关闭',
				callback : function() {
				}
			} ]
		});
	}

	//回调信息
	function callback(obj,myIndex) {
		$("[data-index='"+myIndex+"']").find("input[name='positionName']").val(obj.positionName);
		$("[data-index='"+myIndex+"']").find("input[name='positionIds']").val(obj.positionIds);
		$("[data-index='"+myIndex+"']").find("input[name='fullName']").val(obj.fullName);
		$("[data-index='"+myIndex+"']").find("input[name='mobilephone']").val(obj.mobilephone);
	}

	//弹出窗口选择上级客户
	function chooseCust(_this) {
		var myIndex = $(_this).closest(".formDiv").attr("data-index");
		var targetUrl = 'tmTerminalController.do?goTerminalCust';
		var id=$("#id").val();
		if(id!=null&&id!=""){
			targetUrl+="&terminalId="+id;
		}
		safeShowDialog({
			content : 'url:' + targetUrl,
			title : '客户选择',
			lock : true,
			width : 600,
			height : 450,
			button : [ {
				name : '确定',
				callback : function (){
					iframe = this.iframe.contentWindow;
					var customerName = iframe.getcustListSelections('customerName');
					var customerId = iframe.getcustListSelections('id');
					var cnlength = $("input[name='customerName']").length;
					var cilength = $("input[name='customerId']").length;
					/* 
					var flag=false;
					var oldselected=$("[data-index='"+myIndex+"']").find("input[name='businessGroupOrgId']").val();
					$(".formDiv").each(function(i, o) {
						var selected = $(o).find("input[name='businessGroupOrgId']").val();
						var id = $(o).find("input[name='customerId']").val();
						if (selected == oldselected && id == customerId){
							$.messager.alert('提示','同一个业务组只能包含一个上级客户');
							flag=true ;
							return false;
						}
					});
					
					if(flag){
						return false;
					}
					 */
					$("[data-index='"+myIndex+"']").find("input[name='customerName']").val(customerName);
					$("[data-index='"+myIndex+"']").find("input[name='customerId']").val(customerId);
				},
				focus : true
			}, {
				name : '关闭',
				callback : function() {
				}
			} ]
		});
	}
	
	var numAll = $('.formDiv').length;
	//html追加
	function addPosition() {
		numAll++;
		$(".formDiv").eq(0).clone().insertAfter($(".formDiv").eq($(".formDiv").length-1));
		var numCurr = $(".formDiv").length;
		
		//清空新添加的表单值
		$("label[name='businessGroupOrgName']").eq(numCurr-1).html("所属部门" +numAll+ ":");
		$("input[name='businessGroupOrgName']").eq(numCurr-1).val("");
		$("input[name='businessGroupOrgId']").eq(numCurr-1).val("");
		$("label[name='positionName']").eq(numCurr-1).html("对接人职位" +numAll+ ":");
		$("input[name='positionName']").eq(numCurr-1).val("");
		$("input[name='positionIds']").eq(numCurr-1).val("");
		$("label[name='fullName']").eq(numCurr-1).html("对接人姓名" +numAll+ ":");
		$("input[name='fullName']").eq(numCurr-1).val("");
		$("label[name='mobilephone']").eq(numCurr-1).html("对接人电话" +numAll+ ":");
		$("input[name='mobilephone']").eq(numCurr-1).val("");
		$("label[name='channelName']").eq(numCurr-1).html("渠道" +numAll+ ":");
		$("textarea[name='channelName']").eq(numCurr-1).val("");
		$("input[name='channelCode']").eq(numCurr-1).val("");
		$("label[name='productName']").eq(numCurr-1).html("产品" +numAll+ ":");
		$("textarea[name='productName']").eq(numCurr-1).val("");
		$("input[name='productCode']").eq(numCurr-1).val("");
		$("label[name='businessAreaName']").eq(numCurr-1).html("区域" +numAll+ ":");
		$("textarea[name='businessAreaName']").eq(numCurr-1).val("");
		$("input[name='businessAreaCode']").eq(numCurr-1).val("");
		$("label[name='customerName']").eq(numCurr-1).html("上级客户" +numAll+ ":");
		$("input[name='customerName']").eq(numCurr-1).val("");
		$("input[name='customerId']").eq(numCurr-1).val("");
        $("label[name='bgExtChar1s']").eq(numCurr-1).html("备注一" +numAll+ ":");
        $("input[name='bgExtChar1s']").eq(numCurr-1).val("");
        $("label[name='bgExtChar2s']").eq(numCurr-1).html("备注二" +numAll+ ":");
        $("input[name='bgExtChar2s']").eq(numCurr-1).val("");
		
		//序号、图标 处理
		$(".formDiv").eq(numCurr-1).attr("data-index",numAll-1);
		$("input[name='relationIndex']").eq(numCurr-1).val(numAll);
		$("input[name='termCustId']").eq(numCurr-1).val("");
		$("label[name='businessGroupOrgName'] .formDivAdd").remove();
		if('${optype}' != '2'){
			$("label[name='businessGroupOrgName']").eq($(".formDiv").length-1).find("img").remove();
			$("label[name='businessGroupOrgName']").eq($(".formDiv").length-1).prepend("<img src='resources/easyui/themes/icons/add.png' class='formDivAdd' onclick='addPosition()' style='padding: 4px;cursor: pointer;' /><img src='resources/easyui/themes/icons/remove.png' class='formDivRemove' onclick='removePosition(this)' style='padding: 4px;cursor: pointer;' />");
		}
	}

	//选择客户经理职位弹出框
	function chooseChannel(_this) {
		var myIndex = $(_this).closest(".formDiv").attr("data-index");
		var targetUrl = 'tmDictDataController.do?goDictDataGridByTypeMain&dictTypeCode=channel';
		safeShowDialog({
			content : 'url:' + targetUrl,
			title : '渠道选择',
			lock : true,
			width : 600,
			height : 450,
			button : [ {
				name : '确定',
				callback : function() {
					//回调事件
					iframe = this.iframe.contentWindow;
					var rowsData = iframe.$('#channeldictList').datagrid('getSelections');
	    			if ((rowsData == '' || rowsData == null) ) {
	    				$.messager.alert('提示',"请选择数据");
	    				return false;
	    			} 
	    			var dictCodes = "";
	    			var dictNames = "";
	    			for(var i = 0; i < rowsData.length; i++) {
	    				dictCodes += rowsData[i].dictCode + ";";
	    				dictNames += rowsData[i].dictValue + ";";
	    			}
	    			$("[data-index='"+myIndex+"']").find("textarea[name='channelName']").val(dictNames.substring(0, dictNames.length - 1));
	    			$("[data-index='"+myIndex+"']").find("input[name='channelCode']").val(dictCodes.substring(0, dictCodes.length - 1));
				},
				focus : true
			}, {
				name : '关闭',
				callback : function() {
				}
			} ]
		});
	}
	
	//选择客户经理职位弹出框
	function chooseProduct(_this) {
		var myIndex = $(_this).closest(".formDiv").attr("data-index");
		var targetUrl = 'tmProductController.do?goTmProductSelectMain';
		safeShowDialog({
			content : 'url:' + targetUrl,
			title : '产品选择',
			lock : true,
			width : 600,
			height : 450,
			button : [ {
				name : '确定',
				callback : function() {
					//回调事件
					iframe = this.iframe.contentWindow;
					var rowsData = iframe.$('#proList').datagrid('getSelections');
	    			if ((rowsData == '' || rowsData == null) ) {
	    				$.messager.alert('提示',"请选择数据");
	    				return false;
	    			} 
	    			var productCodes = "";
	    			var productNames = "";
	    			for(var i = 0; i < rowsData.length; i++) {
	    				productCodes += rowsData[i].productCode + ";";
	    				productNames += rowsData[i].text + ";";
	    			}
	    			$("[data-index='"+myIndex+"']").find("textarea[name='productName']").val(productNames.substring(0, productNames.length - 1));
	    			$("[data-index='"+myIndex+"']").find("input[name='productCode']").val(productCodes.substring(0, productCodes.length - 1));
				},
				focus : true
			}, {
				name : '关闭',
				callback : function() {
				}
			} ]
		});
	}
	
	//选择客户经理职位弹出框
	function chooseBusinessArea(_this) {
		var myIndex = $(_this).closest(".formDiv").attr("data-index");
		var targetUrl = 'tmBusinessAreaController.do?goTmBusinessAreaSelectMain';
		safeShowDialog({
			content : 'url:' + targetUrl,
			title : '业务区域选择',
			lock : true,
			width : 600,
			height : 450,
			button : [ {
				name : '确定',
				callback : function() {
					//回调事件
					iframe = this.iframe.contentWindow;
					var rowsData = iframe.$('#businessAreaList').datagrid('getSelections');
	    			if ((rowsData == '' || rowsData == null) ) {
	    				$.messager.alert('提示',"请选择数据");
	    				return false;
	    			} 
	    			var businessAreaCodes = "";
	    			var businessAreaNames = "";
	    			for(var i = 0; i < rowsData.length; i++) {
	    				businessAreaCodes += rowsData[i].businessAreaCode + ";";
	    				businessAreaNames += rowsData[i].text + ";";
	    			}
	    			$("[data-index='"+myIndex+"']").find("textarea[name='businessAreaName']").val(businessAreaNames.substring(0, businessAreaNames.length - 1));
	    			$("[data-index='"+myIndex+"']").find("input[name='businessAreaCode']").val(businessAreaCodes.substring(0, businessAreaCodes.length - 1));
				},
				focus : true
			}, {
				name : '关闭',
				callback : function() {
				}
			} ]
		});
	}
	
	//删除单行数据
	function removePosition(_this){
		$(_this).closest(".formDiv").remove();
	}
</script>
</html>
