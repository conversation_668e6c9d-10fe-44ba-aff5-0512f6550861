<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true" id="tdTemplateList">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tdTemplateDataGrid"  fitColumns="false" title="模板列表"
                    actionUrl="tdTemplateController.do?findTdTemplateList" idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="操作" field="opt"></t:dgCol>
            <t:dgCol title="状态" field="status" query="single" width="50" replace="有效_1,失效_0"></t:dgCol>
            <t:dgCol title="模板名称" field="tplName" width="120"></t:dgCol>
            <t:dgCol title="模板类型" field="tplType" width="120"></t:dgCol>
            <t:dgCol title="模板路径" field="tplPath" width="120"></t:dgCol>
            <t:dgCol title="备注" field="risk" width="300"></t:dgCol>
            <t:dgFunOpt title="查询模块" funname="expendTemplatePart(id)"></t:dgFunOpt>
            <t:dgToolBar title="新增" icon="icon-add" url="tdTemplateController.do?goTdTemplateForm" funname="add"></t:dgToolBar>
            <t:dgToolBar title="修改" icon="icon-edit" url="tdTemplateController.do?goTdTemplateForm" funname="update"></t:dgToolBar>
            <t:dgToolBar title="预览" icon="icon-edit" onclick="preview();"></t:dgToolBar>
            <t:dgToolBar title="生成" icon="icon-edit" url="tdTemplateController.do?goTdTemplateForm" funname="update"></t:dgToolBar>
        </t:datagrid>

    </div>

    <div data-options="region:'east',
		title:'模块列表',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
         style="width: 800px; overflow: hidden;">
        <div class="easyui-panel" style="padding: 0;" fit="true" border="false" id="tdTemplatePartList"></div>
    </div>
</div>
<script>
    var li_east = 0;

    function expendTemplatePart(id){
        if(li_east == 0){
            $('#tdTemplateList').layout('expand','east');
        }
        $('#tdTemplatePartList').panel("refresh", "tdTemplateController.do?goTdTemplatePartMain&id="+id);
    }

    function preview() {
        var id = gettdTemplateDataGridSelected("id");
        if (id) {
            window.open('tdTemplateController.do?previewPage&id=' + id, '_blank');
        } else {
            alert("请选择一条数据");
        }
    }
</script>


