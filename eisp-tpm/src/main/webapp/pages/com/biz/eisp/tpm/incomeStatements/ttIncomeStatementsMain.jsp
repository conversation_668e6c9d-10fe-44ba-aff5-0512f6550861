<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="TtIncomeStatementsList" fitColumns="false" title="收入报表"
                    actionUrl="ttIncomeStatementsController.do?findIncomeStatementsList" idField="id" fit="true">

            <t:dgCol title="开票凭证" field="billNum" query="true" width="150"></t:dgCol>
            <t:dgCol title="开票凭证类型" field="billType" query="true" width="150"></t:dgCol>
            <t:dgCol title="项目类型" field="itemCateg" query="true" width="150"></t:dgCol>

            <t:dgCol title="归属事业部" field="bicZcSyb"  width="150" query="true"></t:dgCol>
            <t:dgCol title="归属事业部名称" field="sybName"  width="150"></t:dgCol>
            <t:dgCol title="售达方编码" field="soldTo" query="true" width="150"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" query="true" width="150"></t:dgCol>
            <t:dgCol title="所属组织" field="orgName" query="true" width="150"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName" query="true" width="150"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" query="true" width="150"></t:dgCol>
            <t:dgCol title="成本中心" field="costcenter" query="true" width="150"></t:dgCol>
            <t:dgCol title="SAP部门编码" field="bicZcBmbh" query="true" width="150"></t:dgCol>
            <t:dgCol title="开票记帐日期" field="billDateFor" hidden="true" query="true" queryMode="group" formatter="yyyyMMdd" width="150"></t:dgCol>
            <t:dgCol title="开票记帐日期" field="billDate" query="false" width="150"></t:dgCol>
            <t:dgCol title="销售订单创建日期" field="bicZcErdat1For"  hidden="true" query="true" queryMode="group" formatter="yyyyMMdd" width="150"></t:dgCol>
            <t:dgCol title="销售订单创建日期" field="bicZcErdat1" query="false"  width="150"></t:dgCol>
            <t:dgCol title="客户采购订单编号" field="bicZcBstnk" width="150"></t:dgCol>
            <t:dgCol title="物料号" field="material" query="true" width="150"></t:dgCol>
            <t:dgCol title="物料描述" field="productName" query="true" width="150"></t:dgCol>
            <t:dgCol title="物料组" field="matlGroup" query="true" width="150"></t:dgCol>
            <t:dgCol title="开票基本单位" field="baseUom"  width="150"></t:dgCol>
            <t:dgCol title="开票基本数量" field="billQty"  width="150"></t:dgCol>
            <t:dgCol title="税收金额" field="taxAmount" width="150"></t:dgCol>
            <t:dgCol title="行项目销售成本" field="cost"  width="150"></t:dgCol>
            <t:dgCol title="行项目净价" field="netvalInv"  width="150"></t:dgCol>

            <t:dgCol title="货补折扣" field="bicZkKzwi3"  width="150"></t:dgCol>
            <t:dgCol title="折价金额" field="bicZkKzwi4" width="150"></t:dgCol>
            <t:dgCol title="费用折扣" field="bicZkKzwi5" width="150"></t:dgCol>
            <t:dgCol title="政策折扣" field="bicZkKzwi6" width="150"></t:dgCol>
            <t:dgCol title="原始金额" field="bicZkKzwi2" width="150"></t:dgCol>
            <t:dgCol title="搭赠折扣" field="bicZkTldz" width="150"></t:dgCol>
            <t:dgCol title="降价折扣" field="bicZkKzwi7" width="150"></t:dgCol>
            <t:dgCol title="实际销售收入(不含税)" field="bicZkXssr1" width="150"></t:dgCol>
            <t:dgCol title="实际销售收入" field="bicZkXssr" width="150"></t:dgCol>
            <%--<t:dgCol title="总值" field="grossVal"  width="150"></t:dgCol>--%>
            <%--<t:dgCol title="客户编码" field="bicZcustomer"  width="150"></t:dgCol>--%>
            <%--<t:dgCol title="开票数量单位" field="salesUnit" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="开票数量" field="invQty" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="行项目总金额" field="subtotal_1" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="销售区域" field="salesOff" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="销售组织" field="salesorg" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="分销渠道" field="distrChan" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="销售部" field="salesDist" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="销售片区" field="salesGrp" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="开票创建日期" field="createdon" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="订单号" field="docNumber" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgCol title="送达方" field="shipTo" query="true" width="150"></t:dgCol>--%>
            <%--<t:dgToolBar operationCode="add" title="新增" height="500" width="800" icon="icon-add" url="costTypePositionController.do?goCostTypePositionForm" funname="add"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="edit" title="编辑" height="500" width="800" icon="icon-edit" url="costTypePositionController.do?goCostTypePositionForm" funname="update"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="del" title="刪除"  height="500" width="800" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>--%>
            <%--<t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'costTypePositionImport', gridName:'CostTypePositionList'})"></t:dgToolBar>--%>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttIncomeStatementsController.do?exportXls"  onclick="excelExport1()"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    //年月格式化
    $("#TtIncomeStatementsListtb_r").find("input[name='billDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
    $("#TtIncomeStatementsListtb_r").find("input[name='bicZcErdat1']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
    $("#TtIncomeStatementsListtb_r").find("input[name='billDateFor_begin']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
    $("#TtIncomeStatementsListtb_r").find("input[name='billDateFor_end']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
    $("#TtIncomeStatementsListtb_r").find("input[name='bicZcErdat1For_begin']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
    $("#TtIncomeStatementsListtb_r").find("input[name='bicZcErdat1For_end']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});



//
//    $(function () {
//        $("input[name='billDate']").removeAttr("onfocus");
//        $("input[name='bicZcErdat1']").removeAttr("onfocus");
//
//        $("input[name='billDate']").addClass("Wdate").css({
//            'height': '20px',
//            'width': '150px'
//        }).click(function () {
//            WdatePicker({dateFmt: 'yyyyMMdd'});
//        });
//
//        $("input[name='bicZcErdat1']").addClass("Wdate").css({
//            'height': '20px',
//            'width': '150px'
//        }).click(function () {
//            WdatePicker({dateFmt: 'yyyyMMdd'});
//        });
//    });
//
//    $("#dmsNoticeListtb_r").find("input[name='createDate']").addClass("Wdate")
//        .css({
//            'height': '20px',
//            'width': '100px'
//        }).click(function () {
//        WdatePicker({
//            dateFmt: 'yyyy-MM-dd'
//        });
//    });
//
//    $("#dmsNoticeListtb_r").find("input[name='periodStart']").addClass("Wdate")
//        .css({
//            'height': '20px',
//            'width': '100px'
//        }).click(function () {
//        WdatePicker({
//            dateFmt: 'yyyy-MM-dd'
//        });
//    });
//
//    $("#dmsNoticeListtb_r").find("input[name='periodEnd']").addClass("Wdate")
//        .css({
//            'height': '20px',
//            'width': '100px'
//        }).click(function () {
//        WdatePicker({
//            dateFmt: 'yyyy-MM-dd'
//        });
//    });

    function excelExport1() {
        var url="ttIncomeStatementsController.do?exportXls";
        var queryParams = $('#TtIncomeStatementsList' ).datagrid('options').queryParams;
        $('#TtIncomeStatementsListtb_r').find('*').each(function() {
            queryParams[$(this).attr('name')] = $(this).val();
        });
        var params = '&';

        var billDateFor_begin=$("input[name='billDateFor_begin']").val();
        var billDateFor_end=$("input[name='billDateFor_end']").val();
        var grandOrgName=$("input[name='grandOrgName']").val();
//        if(grandOrgName==null||grandOrgName==''){
//            tip("由于数据量较大，上上级组织必填");
//            return;
//        }

        if(billDateFor_end==null||billDateFor_end==''){
            tip("由于数据量较大，开票记帐日期开始结束日期必填");
            return;
        }

        if(billDateFor_begin==null||billDateFor_begin==''){
            tip("由于数据量较大，开票记帐日期开始结束日期必填");
            return;
        }
        if( billDateFor_begin.substring(0,6)!=billDateFor_end.substring(0,6)){
            tip("由于数据量较大，开票记帐日期开始结束日期不能跨月");
            return;
        }


        $.each(queryParams, function(key, val) {
            params += '&' + key + '=' + val;
        });
        var fields = '&field=';
        $.each($('#TtIncomeStatementsList').datagrid('options').columns[0], function(i, val) {
            if (val.field != 'opt') {
                fields += val.field + ',';
            }
        });
        var tagetUrl = url;
        //菜单id
        var accessEntry=$("#accessEntry").val();
        if(accessEntry!=null&&accessEntry!=""&&accessEntry!=undefined){
            tagetUrl+="&accessEntry="+accessEntry;
        }
        window.open( url + encodeURI(fields + params) ) ;
    }

    //删除
    function deleteData() {
        /*选择数据中的一行*/
        var seletctTarget = $("#TtIncomeStatementsList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if (seletctTarget.length == 0) {
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示', '确定删除?', function (r) {
            //如果r等于true就执行下面的操作
            if (r) {
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type: "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url: "costTypePositionController.do?deleteCostTypePosition&id="
                    + seletctTarget[0].id,
                    //是否是同步还是异步
                    async: true,
                    //提示消息
                    success: function (data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#TtIncomeStatementsList").datagrid("reload");
                    }
                });
            }
        });
    }
</script>
