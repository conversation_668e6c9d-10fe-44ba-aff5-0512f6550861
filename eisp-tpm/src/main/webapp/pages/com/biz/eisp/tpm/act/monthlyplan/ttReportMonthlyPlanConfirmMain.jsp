<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}

	.qf{/* 区分--qufen-qf-- =.=!! */}

	.ownTabs li {
		margin: -4px 4px 0 0;
		height: 25px;
		float : left;
	}
	.ownTabs li a.tabs-inner {
		height: 25px;
		line-height: 39px;
		background: #6c94c2;
		border-color: #6c94c2;
		color: #fff;
		transition: .4s;
		filter:none;
	}

	.ownTabs li.tabs-selected a.tabs-inner,.ownTabs li:hover a.tabs-inner {
		background: #ececec;
		color:#000000;
		filter:none;
	}
	.ownTabs li.tabs-selected a.tabs-close,.ownTabs li:hover a.tabs-close {
		display: block;
	}
	.ownTabs li.tabs-selected a.tabs-inner {
		font-weight: bold;
		outline: none;
	}

	.ownTabs li a.tabs-inner {
		display: inline-block;
		text-decoration: none;
		margin: 0;
		padding: 0 10px;
		height: 25px;
		line-height: 25px;
		text-align: center;
		white-space: nowrap;
		border-width: 1px;
		border-style: solid;
		-moz-border-radius: 5px 5px 0 0;
		-webkit-border-radius: 5px 5px 0 0;
		border-radius: 5px 5px 0 0;
	}
	ul li {
		list-style: none;
	}
	ul {
		display: block;
		list-style-type: disc;
		-webkit-margin-before: 0em;
		-webkit-margin-after: 0em;
		-webkit-margin-start: 0px;
		-webkit-margin-end: 0px;
		-webkit-padding-start: 0px;
	}
</style>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'north',title:'月度计划${departName}确认'" style="margin: 3px 0px 1px 1px;height:118px;background-color: EEEEEE;" >
		<div class="qf panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" id="sqPass" name="appMonthlyPlan" icon="icon-check_pass" onClick="approveMonthlyPlan(${pass},1)">通过</a>
							<c:if test="${isSaleDepart == 1}">
								<a href="#" class="easyui-linkbutton" plain="true" id="xsbPass" name="appMonthlyPlanBySale" icon="icon-check_pass" onClick="approveMonthlyPlan(${pass},2)">销售部直接通过</a>
							</c:if>
							<a href="#" class="easyui-linkbutton" plain="true" id="sqRejected" name="updateMonthlyPlan" icon="icon-check_back" onClick="approveMonthlyPlan(${rejecte},0)">驳回</a>
							<c:if test="${isSaleDepart == 1}">
								<a href="#" class="easyui-linkbutton" plain="true" id="xsbRejected" name="updateMonthlyPlanBySale" icon="icon-check_back" onClick="approveMonthlyPlan(${rejecte},4)">销售部直接驳回</a>
							</c:if>
							<a href="#" id="outAsQy" class="easyui-linkbutton" plain="true" name="outAsQy" icon="icon-dataOut" onClick="ownExcelExport('ttMonthlyPlanConfirmController.do?exportXlsQy')">按区域别导出</a>
							<a href="#" id="outAsProduct" class="easyui-linkbutton" plain="true" name="outAsProduct" icon="icon-dataOut" onClick="ownExcelExport('ttMonthlyPlanConfirmController.do?exportXlsProduct')">按产品别导出</a>
							<a href="#" id="outAsDetail" class="easyui-linkbutton" plain="true" name="outAsDetail" icon="icon-dataOut" onClick="ownExcelExport('ttMonthlyPlanController.do?exportXls')">导出明细</a>
							<a href="#" id="sqRemarkA" class="easyui-linkbutton" plain="true" name="sqRemarkA" icon="icon-edit" onClick="writeSqRemark()">编写备注</a>
							<div style="color:red;float: right;margin-top: 5px;margin-left: 50px;">
								<label><b>销售额汇总:</b>&nbsp;</label>
								<span id="showTotalAmount" >***</span>
								&nbsp;&nbsp;元&nbsp;
							</div>
						</span>
						<span style="float:right">
							<a href="#" class="easyui-linkbutton" iconcls="icon-search" onclick="ttMonthlyPlanGatherListSearchFunction()">查询</a>
							<a href="#" class="easyui-linkbutton" iconcls="icon-reload" onclick="searchReset()">重置</a>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search" style=" height: 49px;">
		            	<form>
		            		<div>
		            			<label>计划年份</label>
		            			<input type="text" name="year" id="year" class="Wdate"
								onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){chooseYearAndMonthSearch(); }})" readonly="readonly" />
		            			<span style="color:red;">*</span>
		            		</div>
		            		<div>
		            			<label>计划月份</label>
		            			<input type="text" name="month" id="month" class="Wdate"
								onclick="WdatePicker({dateFmt:'MM',onpicked:function(){ chooseYearAndMonthSearch(); }})" readonly="readonly" />
		            			<span style="color:red;">*</span>
		            		</div>
		            		<%--<div>
		            			<label>客户编码</label>
		            			<input type="text" name="customerCode" id="customerCode" onkeyup="value=value.replace(/[^a-z^A-Z^0-9]/g,'')"  />
		            		</div>
		            		<div>
		            			<label>客户名称</label>
		            			<input type="text" name="customerName" id="customerName" />
		            		</div>--%>
							<div>
								<label title="产品编号">产品编号</label>
								<input style="width: 100px" type="text" name="productCode" id="productCode" class="inuptxt" >
							</div>
							<div>
								<label title="产品名称">产品名称</label>
								<input style="width: 100px" type="text" name="productName" id="productName" class="inuptxt" >
							</div>
		            	</form>
						<div class="top_tip" style="margin-top: 10px;background-color: EEEEEE;" >
							<ul class="ownTabs" id="ulTemp">
								<input type="hidden" value="ttMonthlyPlanGatherList" id="tabs-sel" hidden="hidden" />
								<li  class="tabs-selected" data-id="ttMonthlyPlanGatherList"><a class="tabs-inner" href="javascript:void(0)"  ><span class="tabs-title">区域别汇总</span><span class="tabs-icon"></span></a></li>
								<li  data-id="ttMonthlyPlanProductList" id="priceGuide"><a class="tabs-inner" href="javascript:void(0)" ><span class="tabs-title">产品别汇总</span><span class="tabs-icon"></span></a></li>
							</ul>
						</div>
					</div>
				</div>
				<div class="datagrid-view">
				</div>
			</div>
		</div>
	</div>

	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttMonthlyPlanGatherList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
	       autoLoadData="false" actionUrl="ttMonthlyPlanConfirmController.do?findTtMonthlyPlanGatherList&isSaleDepart=${isSaleDepart }" onClick="clickMonthlyPlanFun" onLoadSuccess="monthlyPlanGatheronLoadSuccessFun">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" ></t:dgCol>
         	<t:dgCol title="销售部" field="salesDepartName" sortable="false" ></t:dgCol>
         	<t:dgCol title="区域编码" field="orgCode" hidden="true" frozenColumn="true" sortable="false" ></t:dgCol>
          	<t:dgCol title="区域" field="orgName" sortable="false" ></t:dgCol>
          	<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="环比（元）" field="sequential" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
          	<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" formatterjs="numExtend" ></t:dgCol>
	        <t:dgCol title="差异额" field="differenceAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
	        <t:dgCol title="状态" field="bpmStatus" dictionary="monthlyPlan_Bpm_status" sortable="false" ></t:dgCol>
			<t:dgCol title="省区备注" field="sqRemark" sortable="false" ></t:dgCol>
	    </t:datagrid>
		<t:datagrid name="ttMonthlyPlanProductList" fitColumns="true" title="" queryMode = "group" idField="id" pagination="true"
					autoLoadData="false" actionUrl="ttMonthlyPlanConfirmController.do?findTtMonthlyPlanProductList&isSaleDepart=${isSaleDepart }" onLoadSuccess="monthlyPlanProductonLoadSuccessFun">
			<t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" ></t:dgCol>
			<t:dgCol title="区域" field="orgName" sortable="false" ></t:dgCol>
			<t:dgCol title="产品编号" field="productCode" sortable="false" ></t:dgCol>
			<t:dgCol title="产品名称" field="productName" sortable="false" ></t:dgCol>
			<t:dgCol title="计划销量（EA）" field="planSales" sortable="false" ></t:dgCol>
			<t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false" ></t:dgCol>
			<t:dgCol title="吨位" field="tonnage" sortable="false" ></t:dgCol>
			<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="环比（元）" field="sequential" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol title="差异额" field="differenceAmount" sortable="false" formatterjs="numExtend" ></t:dgCol>
		</t:datagrid>
	</div>
	<div id="ttMonthlyPlanGatherListDiv" data-options="region:'east',
	title:'明细',
	collapsed:true,
	split:true,
	border:false,
	onExpand : function(){
		li_east = 1;
	},
	onCollapse : function() {
	    li_east = 0;
	}"
	 style="padding:1px;width:700px;">
		<t:datagrid name="ttMonthlyPlanList" fitColumns="true" queryMode = "group" idField="id" singleSelect="false"
		pagination="true" autoLoadData="false" actionUrl="ttMonthlyPlanConfirmController.do?findTtMonthlyPlanList"  >
	     	<t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
	      	<t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" ></t:dgCol>
	  		<t:dgCol title="区域" field="orgName" sortable="false" ></t:dgCol>
	  		<t:dgCol title="客户编码" field="customerCode" sortable="false" ></t:dgCol>
	        <t:dgCol title="客户名称" field="customerName" sortable="false" ></t:dgCol>
	  		<t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" ></t:dgCol>
			<t:dgCol title="提报备注" field="remark" sortable="false" ></t:dgCol>
			<t:dgCol title="操作" field="opt" sortable="false" ></t:dgCol>
	  		<t:dgFunOpt title="详情"  funname="operationDetail(customerCode,yearMonth)" ></t:dgFunOpt>
	    </t:datagrid>
	</div>
</div>
<%@include file="/pages/com/biz/eisp/tpm/common/operation.jsp"%>
<script type="text/javascript">
    var searchSingle = 0;
    var chooseUseSearchType = 2;
    var pageListMenu = new pageListMenuList();

    $(document).ready(function(){
        //放入datagrid tital
//		initGetUlDivOutInDatagridTitle();
        initInputValue();
        initSomePropertyEvent();
//        $('.tabs-selected').click();
        addInputEvent();
        searchReset();
    });
    function defaultYear(){
        return '${year}'
    }
    function defaultMonth(){
        return '${month}'
    }
    function initInputValue(){
        $("#year").val(defaultYear());
        $("#month").val(defaultMonth());
    }
    function isSaleDepartNum(){
		return '${isSaleDepart }';
	}

    //放入datagrid tital
    function initGetUlDivOutInDatagridTitle(){
        $('div[class="panel datagrid"] div[class="panel-header"] div[class="panel-title"]').html($('#ulDiv').html());
    }

    //给input标签添加时间
    function addInputEvent(){
        //去除所有回车监控
        $('#ttReportMonthlyPlanMain .datagrid-toolbar-search input').removeAttr("onkeypress").removeAttr("onkeydown");
//        onkeypress="EnterPress(event)" onkeydown="EnterPress(event)
        //添加回车监控
        $('#ttReportMonthlyPlanMain .datagrid-toolbar-search input[readonly != "readonly" ][disabled != "disabled"] ').attr("onkeypress","EnterPress(event)").attr("onkeydown","EnterPress(event)");
//        $('#ttReportMonthlyPlanMain .datagrid-toolbar-search select').change(function(){EnterPress(event)});
    }
    //回车监控
    function EnterPress(e) {
        var e = e || window.event;
        if(e.keyCode == 13) {
            ttMonthlyPlanGatherListSearchFunction();
        }
    }
    //初始绑定点击事件
    function initSomePropertyEvent(){
        $("#ulTemp li").click(function() {
            $(".tabs-selected").removeClass("tabs-selected");
            $(this).addClass("tabs-selected");
			/*var obj = $('#' + $(this).attr("data-id")).parents('div[class="panel datagrid"]');
			 $('div[class="panel datagrid"]').hide();
			 obj.show();*/
            $("#tabs-sel").val($(this).attr("data-id"));
            changeDatagridShow();
            changeSearchParamsShow();
        });
    }
    //改变显示
    function changeDatagridShow(){
        var obj = $('#' + returnTabsSelectedVal()).parents('div[class="panel datagrid"]');
        $('div[class="panel datagrid"]').hide();
        obj.show();
    }

    function returnTabsSelectedVal(){
        return $("#tabs-sel").val();
    }

    function pageListMenuList(){
        this.oneList = "ttMonthlyPlanGatherList";
        this.twoList = "ttMonthlyPlanProductList";
    }

    function changeSearchParamsShow() {
        var key = returnTabsSelectedVal();
//        orgCode,customerCode,customerName,productCode,productName,sqlbuilder,year,month,productCode,productName
//        orgCode,customerCode,customerName,productCode,productName
        var showStr = "";
        var hideStr = "";
        switch (key){
            case pageListMenu.oneList:
                showStr = "orgCode";
                hideStr = "productCode,productName";
                break;
            case pageListMenu.twoList:
                hideStr = "orgCode";
                showStr = "productCode,productName";
                $('#ttReportMonthlyPlanMain').layout('collapse','east');
                break;
            default : tip("?????---咋回事？"); return false;
        }
        changeHideOrShow(hideStr,showStr);
        reloadTtMonthlyPlanListDatagridChooseDoFun(key);
    }
    //选择处理方法
    function reloadTtMonthlyPlanListDatagridChooseDoFun(key) {
        if(chooseUseSearchType == 1){
            reloadTtMonthlyPlanListDatagrid(key);
        }else if(chooseUseSearchType == 2){
            reloadTtMonthlyPlanListDatagridTwo(key);
        }else{
            tip("选取失败");
        }
    }
    var nTemp = 0;
    function reloadTtMonthlyPlanListDatagridTwo(grid){
        if (searchSingle == 0 && nTemp != 1){
            nTemp = 1;
            ttMonthlyPlanGatherListSearchFunction();
        }else{
            nTemp = 0;
            searchSingle = 0;
            getAllTotalAmount(grid);
        }
    }

    function reloadTtMonthlyPlanListDatagrid(grid){
        if (searchSingle == 0 && nTemp != 1){
            nTemp = 1;
            $('#' + grid).datagrid("reload");
        }else{
            nTemp = 0;
            searchSingle = 0;
            getAllTotalAmount(grid);
        }
	}
    //汇总销售额
    function getAllTotalAmount(grid){
        var thisDatas = $('#' + grid).datagrid('options').queryParams
        var url = "ttMonthlyPlanController.do?getAllTotalAmount";
        var d = ajaxPost(thisDatas,url)
        if (d.success){
            $('#showTotalAmount').html(accounting.formatMoney(d.obj,""));
            return true;
        }
        tip(d.msg);
        return false;
    }
    function changeHideOrShow(hide,show) {
        var hides = hide.split(",");
        for (var i in hides){
            $('#' + hides[i]).parent('div').hide();
            $('#' + hides[i]).val('');
        }
        var shows = show.split(",");
        for (var i in shows){
            $('#' + shows[i]).parent('div').show();
        }
    }

/*-------------------------添加备注star----------------------------*/

    function writeSqRemark(){
        var rowsDatas = $('#ttMonthlyPlanGatherList').datagrid('getSelections');
		if(rowsDatas == null ){
		    tip('请选择一条数据');
		    return ;
		}
		if(rowsDatas.length != 1){
            tip('请选择一条数据');
            return ;
		}
		var thisData = rowsDatas[0];
        var myOptions = {
            content : "url:ttMonthlyPlanController.do?goWriteRemark",
            lock : true,
            width : 600,
            height : 270,
            title : "填写备注",
            opacity : 0.3,
            cache : true,
            async: false,
            init : function() {
                var iframeTe = this.iframe.contentWindow;
                //传递参数
                iframeTe.setParams(thisData,thisData.sqRemark,"sqRemark",backWriteRemarkInPage);
                return false;
            } ,
            ok : function() {
                var iframe = this.iframe.contentWindow;
                iframe.saveRemark();
                return false;
            },
            cancelVal : '关闭',
            cancel : true
        };
        safeShowDialog(myOptions);
	}

	function backWriteRemarkInPage(thisPageData,data,remarkFild){
        var thisDataobj = new Object();
        thisDataobj[remarkFild] = data[remarkFild];
        var grObj = $('#ttMonthlyPlanGatherList');
        var rowNum = grObj.datagrid('getRowIndex',thisPageData);
        grObj.datagrid('updateRow',{index:rowNum , row:thisDataobj });
	}

/*-------------------------添加备注end----------------------------*/

	var li_east = 0;
	function clickMonthlyPlanFun(rowIndex,rowData) {
        if(li_east == 0){
            $('#ttReportMonthlyPlanMain').layout('expand','east');
        }
		var orgName = rowData.orgName;
		var yearMonth = rowData.yearMonth;
		var queryParams = $('#ttMonthlyPlanList').datagrid('options').queryParams;
        queryParams.orgName = orgName;
        queryParams.yearMonth = yearMonth;
		$("#ttMonthlyPlanList").datagrid({url:"ttMonthlyPlanConfirmController.do?findTtMonthlyPlanList"});
	}
	//通过/驳回
	function approveMonthlyPlan(n,chooseNum){
		var year = $('#year').val();
		if (year == '') {
			tip("填写年份");
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			tip("填写月份");
			return ;
		}
		var dataTemp = {
				bpmStatus : n,
				isSaleDepart : isSaleDepartNum(),
				year:year,
				month:month,
				chooseNum : chooseNum
			}
		if('1' == chooseNum){
			approveMonthlyPlanPass(n,'通过所有项目',dataTemp);
		}else if('0' == chooseNum){
			approveMonthlyPlanRejecte(n,'驳回已选项目',dataTemp);
		}else if('2' == chooseNum){
            approveMonthlyPlanPass(n,'通过所有项目',dataTemp);
        }else if('4' == chooseNum){
            approveMonthlyPlanRejecte(n,'驳回已选项目',dataTemp);
        }else {
			tip(" ???????  -.-!! ");
		}
	}
	//确认
	function approveMonthlyPlanPass(n,message,dataTemp){
		starApproveMonthlyPlan(message,dataTemp);
	}
	//驳回
	function approveMonthlyPlanRejecte(n,message,dataTemp){
		
		var rowsData = $('#ttMonthlyPlanGatherList').datagrid('getSelections');
		if (rowsData.length == 0) {
			approveMonthlyPlanPass(n,"驳回所有项目",dataTemp);
			/* getSafeJq().dialog.confirm("未选择指定项目，会驳回所有项目，是否继续？", function(r) {
	            if (r) {
	            	approveMonthlyPlanPass(n,"驳回所有项目",dataTemp)
	            }
	        }); */
			return ;
		}
		/* if (rowsData.length != 1) {
			tip("请选择项目");
			return ;
		} */
		var orgCodes = [];
		for ( var i = 0; i < rowsData.length; i++) {
			orgCodes.push(rowsData[i].orgCode);
        }
		/*var data = {
			bpmStatus : n,
			isSaleDepart : '\\${isSaleDepart }',
			year : dataTemp.year,
			month : dataTemp.month,
			orgCode : orgCodes.join(',')
		}*/
        var data = dataTemp;
        data["orgCode"] = orgCodes.join(',');
		starApproveMonthlyPlan(message,data);
	}
	function starApproveMonthlyPlan(message,data){
        var tips = returnConfirmTips(data);
		url = "ttMonthlyPlanConfirmController.do?approveMonthlyPlan";
		ajaxRequest({tips:tips + '确定' + message + '?',url:url,params:data,callbackfun:function(data){
	    	var d = $.parseJSON(data);
            if(d.success == true) {
            	ttMonthlyPlanGatherListSearchFunction();
            } else {
            	tip(d.msg);
            }
	    }});
	}
    //返回提示信息
    function returnConfirmTips(data){
        var bpmStatus = '${pass}';
        if(bpmStatus != data.bpmStatus){//判定只有通过时才进行检查
            return '';
        }
        var showMsg = checkHowManyCityNoMonthlyPlan(data);
        if(checkIsNotUndefinedAndNullAndNullValue(showMsg)){
            return showMsg + "，";
        }
        return '';
    }

    //检查还有多少城市或区域没有创建月度计划
    function checkHowManyCityNoMonthlyPlan(data){
        var url = "ttMonthlyPlanConfirmController.do?checkHowManyCityNoMonthlyPlan";
        var d = ajaxPost(data,url);
        if (d.success){
            return d.obj;
        }
        return '';
    }
	//打开明细---行级
	function rowLeveloperationDetail(index,rowData){
		/* var rowsData = rowData.rows;
		//$('#ttMonthlyPlanGatherList').datagrid('getSelections');
		if (rowsData.length != 1) {
			tip("请选择一条项目");
			return ;
		} */
		var data = rowData;//rowsData[0];
		var customerCode = data.customerCode;
		var yearMonth = data.yearMonth;
		createwindowExt("查看明细","ttMonthlyPlanConfirmController.do?gottReportMonthlyPlanDetail&customerCode=" + customerCode 
				+ "&yearMonth=" + yearMonth,"1000","650",{
	    	 button : [
	 	            {name : "关闭",
		            callback : function() {
		            	return true;
	            }}
	   	 ] });
	}
    //打开明细--列级
    function operationDetail(customerCode,yearMonth,index){
        var customerCode = customerCode;
        var yearMonth = yearMonth;
        createwindowExt("查看明细","ttMonthlyPlanConfirmController.do?gottReportMonthlyPlanDetail&customerCode=" + customerCode
            + "&yearMonth=" + yearMonth,"1000","650",{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ] });
    }
	function chooseYearAndMonthSearch(){
		var year = $('#year').val();
		if (year == '') {
			return ;
		}
		var month = $('#month').val();
		if (month == '') {
			return ;
		}
		ttMonthlyPlanGatherListSearchFunction();
	}

    //查询
    function ttMonthlyPlanGatherListSearchFunction(){
        var year = $('#year').val();
        if (year == '') {
            tip("填写年份");
            return ;
        }
        var month = $('#month').val();
        if (month == '') {
            tip("填写月份");
            return ;
        }
        var searchData = {
            year : year,
            month : month,
        }
        searchSingle = 1;
        //选择查询方式
        if (chooseUseSearchType == 1){
            searchAllList(searchData);
        }else if(chooseUseSearchType == 2){
            chooseSearchSingleList(searchData);
        }else{
            tip("查询失败，请不要乱动全局变量，谢谢！");
            return ;
        }
    }

    function chooseSearchSingleList(searchData){
        var key = returnTabsSelectedVal();
        switch (key){
            case pageListMenu.oneList://查询销售部汇总
                ttMonthlyPlanGatherListSearchStar(searchData);
                break;
            case pageListMenu.twoList://查询产品汇总
                ttMonthlyPlanProductListSearchStar(searchData);
                break;
            default : tip("?????---咋回事？"); return ;
        }
    }

    /**
     * 同事查询所有列表数据
     */
    function searchAllList(searchData){
        //查询销售部汇总
        ttMonthlyPlanGatherListSearchStar(searchData);
        //查询产品汇总
        ttMonthlyPlanProductListSearchStar(searchData);
    }

    //查询区域别汇总
    function ttMonthlyPlanGatherListSearchStar(searchData){
        searchData.orgCode = $('#orgCode').val();
        var url = "ttMonthlyPlanConfirmController.do?findTtMonthlyPlanGatherList&isSaleDepart=" + isSaleDepartNum();
        sharingSearch("ttMonthlyPlanGatherList",url,searchData)
    }
    //查询产品别汇总
    function ttMonthlyPlanProductListSearchStar(searchData){
        searchData.productCode = $('#productCode').val()
        searchData.productName = $('#productName').val();
        var url = "ttMonthlyPlanConfirmController.do?findTtMonthlyPlanProductList&isSaleDepart=" + isSaleDepartNum();
        sharingSearch("ttMonthlyPlanProductList",url,searchData)
    }
    //共用查询
    function sharingSearch(grid,url,searchData) {
        var grObj = $('#' + grid );
        var queryParams = grObj.datagrid('options').queryParams;
        encapsulationSearchParameters(queryParams,searchData);
        grObj.datagrid({url:url ,queryParams:queryParams});
    }
    //访问成功回调
    function monthlyPlanGatheronLoadSuccessFun(rowData){
//        setTimeout("onLoadSuccessFun('ttMonthlyPlanGatherList')",0);
        syncTemp("onLoadSuccessFun('ttMonthlyPlanGatherList')");
    }
    //访问成功回调
    function monthlyPlanProductonLoadSuccessFun(rowData){
//        setTimeout("onLoadSuccessFun('ttMonthlyPlanProductList')",0);
        syncTemp("onLoadSuccessFun('ttMonthlyPlanProductList')");
    }

    //目标执行数
    var targ = 0;
    function syncTemp(fun){
        targ ++ ;
        setTimeout(fun,80);
    }

    //伪同步锁
    var lockStr = '';
    function checklock(name){
        if(lockStr == ''){
            lockStr = name
            return true;
        }else{
            setTimeout("onLoadSuccessFun(" + name + ")",200);
        }
        return false;
    }
    var iTemp = 0;

    function onLoadSuccessFun(name){
        if(checklock(name)){
            iTemp ++;
            if (iTemp == targ){
//            changeDatagridShow();
                if(chooseUseSearchType == 2){
                    searchSingle = 1;
                }
                $('.tabs-selected').click();
                iTemp = 0;
                targ = 0;
            }
            lockStr = '';
        }
    }
    function encapsulationSearchParameters(queryParams,searchData) {
        for(key in searchData){
            queryParams[key] = searchData[key];
        }
    }
    //重置
    function searchReset(){
        initInputValue();
        setTimeout("ttMonthlyPlanGatherListSearchFunction()",0);
        setAllParmsNull();
    }
    function setAllParmsNull(){
        $('div[class="qf panel datagrid"] form input[class!="Wdate"]').val('');
    }
    //导出
    function ownExcelExport(url) {
        var year = $('#year').val();
        if (year == '') {
            tip("填写年份");
            return;
        }
        var month = $('#month').val();
        if (month == '') {
            tip("填写月份");
            return;
        }
        var productCode = returnNotNullValueValue($('#productCode').val());
        var productCode = returnNotNullValueValue($('#productName').val());
        var thisData = {
            year : year,
            month : month,
            productCode : productCode,
            productCode : productCode
        }

        url = url + changeDataToUrlData(thisData);
        window.open(url);
    }
    //返回不为‘null’的数据--避免传到后台为 undefined
    function returnNotNullValueValue(obj){
        if(typeof(obj) != 'undefined' && obj != '' && obj != null && obj != 'null' ){
            return obj;
        }
        return '';
    }
    //将obj转换为urlData
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }
</script>
