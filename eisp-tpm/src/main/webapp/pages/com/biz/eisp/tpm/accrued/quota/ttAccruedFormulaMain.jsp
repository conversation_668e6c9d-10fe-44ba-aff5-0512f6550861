<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttAccruedFormulaList" title="定额预提公式配置"  actionUrl="ttAccruedFormulaController.do?findTtAccruedFormulaList" 
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="预提年月" field="yearMonth"  query="true" ></t:dgCol>
			<t:dgCol title="活动细类编码" field="costAccountCode" query="true" ></t:dgCol>
			<t:dgCol title="活动细类名称" field="costAccountName" query="true" ></t:dgCol>
			<t:dgCol title="条件公式" field="formulaCon"  ></t:dgCol>
			<t:dgCol title="结果公式" field="formulaRel" ></t:dgCol>
			<t:dgCol title="启用状态" dictionary="enable_status" field="enableStatus" query="true" ></t:dgCol>
			<t:dgCol title="创建人" field="createName" hidden="true"></t:dgCol>
			<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" ></t:dgCol>
			<t:dgCol field="updateName" title="最近更新人" ></t:dgCol>
			<t:dgCol field="updateDate" title="最近更新时间"  formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
			<t:dgToolBar title="新增" icon="icon-add" operationCode="add" url="ttAccruedFormulaController.do?goTtAccruedFormulaForm&optype=0" funname="add"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" operationCode="update" url="ttAccruedFormulaController.do?goTtAccruedFormulaForm&optype=1" funname="update"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url="ttAccruedFormulaController.do?deleteTtAccruedFormula"  funname="deleteALLSelect"></t:dgToolBar>
			<t:dgToolBar operationCode="start" title="启用" icon="icon-start"   onclick="startOrStop(0)"></t:dgToolBar>
			<t:dgToolBar operationCode="stop" title="停用" icon="icon-stop" onclick="startOrStop(1)"></t:dgToolBar>
			<t:dgToolBar title="日志" icon="icon-log" operationCode="log" url="tmLogController.do?goTmLogDetailMain" funname="detail" width="1200"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">
    //启用停用
    function startOrStop(flag){
        var rows = $("#ttAccruedFormulaList").datagrid('getSelections');
        var title="启用";
        if(flag!=0){
            title="停用";
		}
		var url="ttAccruedFormulaController.do?updateTtAccruedFormula";
        var ids=[];
        if (rows.length > 0) {
            getSafeJq().dialog.confirm("你确定"+title+"所选数据吗?", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            ids : ids.join(','),
							flag:flag
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                $("#ttAccruedFormulaList").datagrid('reload');
                                $("#ttAccruedFormulaList").datagrid('unselectAll');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        } else {
            tip("请选择需要"+title+"的数据");
        }
    }
    //导出
    function toExcel(){
            excelExport("ttAccruedFormulaController.do?exportXlsCalculationList","ttAccruedFormulaList");
    }
</script>
