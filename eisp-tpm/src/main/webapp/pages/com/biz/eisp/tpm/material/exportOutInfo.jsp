<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<style>
</style>
<div class="easyui-layout" fit="true">
    <div region="center" style="width:400px;">
        <t:formvalid formid="formobj" layout="div" dialog="true" action=""  refresh="true">
            <div class="form">
                <label class="Validform_label">待选导出内容: </label>
                <input name="checkbox" type="checkbox" value="1" />照片
                <input name="checkbox" type="checkbox" value="2" />可申报客户、排除客户、可申报组织、排除组织
            </div>
        </t:formvalid>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
function getCheckedInfo(){
    var type=0;
    var chk_value =[];
    $('input[name="checkbox"]:checked').each(function(){
       var value=$(this).val();
        chk_value.push(value);
    });
    if(chk_value.length==2){
        type=3;
    }
    if(chk_value.length==1){
        type=chk_value[0];
    }
    return type;
}
</script>