<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>活动大类</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttCostTypeController.do?editRolePosition" refresh="true">
 	<input id="id" name="id" type="hidden" value="${ttCostTypeVo.id }"/>


    <div class="form">
        <label class="Validform_label">角色编码: </label>
        <input name="actRoleCode" value="${ttCostTypeVo.actRoleCode}" class="inputxt"></input>
    </div>

    <div class="form">
        <label class="Validform_label">职位编码: </label>
        <input name="actPositionCode" value="${ttCostTypeVo.actPositionCode}" class="inputxt"></input>
    </div>


</t:formvalid>
</body>
<script type="text/javascript">
	$(function(){
		var has = $("#hasOrgShare").val();	
		if(has == '1'){
			$("#orgShareForm").show();
			$("#orgShareCodes").attr("datatype","*");
		}else{
			$("#orgShareCodes").removeAttr("datatype");
		}
	});
	function validateShareOrg(){
		var has = $("#hasOrgShare").val();
		if(has == '0'){
			return true;
		}
		var orgShare = $("#orgShareCodes").val();
		if(orgShare == ""){
			return false;
			$("#errorShare").addClass("Validform_wrong").attr("title","请填写信息");
		}else{
			$("#errorShare").addClass("Validform_right").removeAttr("title");
		}
	}

	function showDiv(){
		var isOrNo = $("#hasOrgShare").val();
		if(isOrNo == "1"){
			$("#orgShareNames").val("");
			$("#orgShareCodes").val("");
			$("#orgShareForm").show();
			$("#orgShareCodes").attr("datatype","*");
		}else{
			$("#orgShareCodes").removeAttr("datatype");
			$("#orgShareForm").hide();
		}
	}
	
	function openCostAccountSelect(){
		/* popSelectClick("costAccountCode", "costAccountName","accountName",
				"ttCostTypeController.do?goSelectCostAccountMain&costTypeId=${costType.id}&accessEntry="+accessEntry,
						"ttCostAccountSelectedList",'undefined',1000,450); */
 		safeShowDialog({
			content:"url:ttCostTypeController.do?goSelectCostAccountMain&costTypeId=${costType.id}&accessEntry="+accessEntry,
			lock:true,
			title:'选择',
			width:1000,
			height:450,
			left:'85%',
			cache:false,
			ok : function() {
				iframe = this.iframe.contentWindow;
				var rowsData = iframe.$('#ttCostAccountSelectedList').datagrid('getRows');
				if ((rowsData == '' || rowsData == null) && validFunction == null) {
					tip("请至少选择一条数据");
					return false;
				} else {
					$("#costAccountCode").val(JSON.stringify(rowsData));
					var str1 = "";
					var str2 = "";
					$.each(rowsData, function(i, n) {
						if (i == 0){
							str1 += n.accountName;
							str2 += n.accountCode;
						}else {
							str1 += ",";
							str2 += ",";
							str1 += n.accountName;
							str2 += n.accountCode;
						}
					});
					$("#costAccountName").val(str1);
					$("#costAccountCode").val(str2);
					$("#errorAccount").removeClass("Validform_wrong").addClass("Validform_right");
					return true;
				}
			},
			cancelVal : '关闭',
			cancel : true
		});
	}
	//可选部门弹窗
	function openOrgSelect(type){
		var costTypeCode = '${costType.costTypeCode }';
		var orgCodes = $("#orgCodes").val();
        var orgCode = $('#orgCode').val();
        var paraArr = {
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型--不传或默认
            isCouldRemove:true,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            callBackFun : saveLog,
            pageData:{
                orgCode : orgCodes
            }
        }
        openChooseOrgSelect(paraArr);
/*
		var url = "tmCommonMdmController.do?gotmOrgSelectMain";
		createwindowExt('选择',url,1000,500,{
			ok:function(){
				var codes = [];
				var names = [];
				iframe = this.iframe.contentWindow;
				var data = iframe.$("#tmHasSelectedOrg").datagrid("getRows");
				var length = data.length;
				for(var i = 0;i<length;i++){
					var code = data[i].orgCode;
					var name = data[i].orgName;
					codes.push(code);
					names.push(name);
				}
				$("#orgNames").val("");
				$("#orgCodes").val("");
				$("#orgNames").val(names.join(','));
				$("#orgCodes").val(codes.join(','));
				$("#errorOrg").removeClass("Validform_wrong").addClass("Validform_right");
			}
		});*/
	}
	function saveLog(data){
	    var orgNames = [];
	    var orgCodes = [];
        $.each(data,function(){
            var obj = $(this);
            var orgName = obj[0].orgName;
            var orgCode = obj[0].orgCode;
            orgNames.push(orgName);
            orgCodes.push(orgCode);
        });
        $("#orgNames").val(orgNames.join(","));
        $("#orgCodes").val(orgCodes.join(","));
    }
</script>
</html>



