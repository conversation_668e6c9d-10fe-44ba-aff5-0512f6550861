<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report004List" fitColumns="false" title="预提费用分析"
                    pagination="false" autoLoadData="false" actionUrl="report004Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="主户编码" field="mainResidentsCode" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="主户" field="mainResidentsName" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="子户" field="seedResidentsName" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="子户编码" field="seedResidentsCode" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="SAP第一次发票记账日期" field="sapInvoiceDate" query="true" queryMode="group" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report004Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report004Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report004Listsearch() {
        var orgCode = $("#report004Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report004Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report004List").datagrid('options').queryParams;
        $("#report004Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report004List").datagrid({url:'report004Controller.do?findReportList'});
    }

</script>
