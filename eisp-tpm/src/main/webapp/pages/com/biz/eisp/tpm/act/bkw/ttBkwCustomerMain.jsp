<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="ttBkwCustomerMain" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttBkwCustomerMainList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
	      autoLoadData="true" actionUrl="ttBkwCustomerController.do?findTtBkwCustomerList" >
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
	        <t:dgCol title="客户编码" field="custCode"  query="true"  width="100" ></t:dgCol>
	        <t:dgCol title="ERP编码" field="erpCode" query="true"  width="100" ></t:dgCol>
	        <t:dgCol title="客户名称" field="custName" query="true"  width="100" ></t:dgCol>
	        <t:dgCol title="渠道类型" field="manaChannel" dictionary="mana_channel"  width="100" ></t:dgCol>
	  		<t:dgCol title="客户联系人" field="custContact"  query="true"  width="100" ></t:dgCol>
	  		<t:dgCol title="客户联系方式" field="custContactPhone"  width="100" ></t:dgCol>
			<t:dgCol title="所属部门" field="orgId" hidden="true" query="true"  width="100" ></t:dgCol>
          	<t:dgCol title="所属部门" field="orgName"  width="100" ></t:dgCol>
          	<t:dgCol title="对接人职位" field="positionName"   width="100" ></t:dgCol>
          	<t:dgCol title="对接人姓名" field="fullName"  width="100" ></t:dgCol>
			<%--<t:dgCol title="合作状态" field="cooperative"   width="140" ></t:dgCol>--%>
			<t:dgCol title="启用状态" field="enableStatus" dictionary="enable_status" width="140" ></t:dgCol>
          	<t:dgCol title="创建人" field="createName"  width="100" ></t:dgCol>
          	<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" width="140" ></t:dgCol>
          	
          	<t:dgToolBar title="新增"  operationCode="add" icon="icon-add" url="ttBkwCustomerController.do?goTtBkwCustomerForm" funname="add" height="450" width="450"></t:dgToolBar>
	    	<t:dgToolBar title="编辑"  operationCode="edit" icon="icon-edit" url="ttBkwCustomerController.do?goTtBkwCustomerForm" funname="update" height="450" width="450"></t:dgToolBar>
	    	<t:dgToolBar title="停用"  operationCode="stop" icon="icon-remove" url="" funname="deleteALLSelect()" ></t:dgToolBar>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
$(document).ready(function(){
	$("input[name='orgId']").combotree({
	    url: 'tmOrgController.do?getParentOrg',
	    width:200
	});  
	
});

//批量删除
function deleteALLSelect(){
	 var rowsData = $('#ttBkwCustomerMainList').datagrid('getSelections');
		if( rowsData.length > 0){
			 var ids = "";
		    	for(var i = 0;i<rowsData.length;i++){
		    		if(i==0){
		    			ids = rowsData[i].id;
		    		}else{
		    			ids +=","+rowsData[i].id;
		    		}
		    	} 
			 $.ajax({
		            url : 'ttBkwCustomerController.do?deleteAllList',
		            type : 'post',
		            dataType:"json",
		            async: false,
		            data : {
		            	"ids" :ids 
		            },
		            cache : false, 
		            success : function(data) {
		            	tip(data.msg);
		            	ttBkwCustomerMainListsearch();
		            },
		            error : function(){
						tip('服务器繁忙，请稍后再试');
					}
			 });       
				
		}else{
	 	$.messager.alert('提示','至少选择一条');
	     	return;
	}
}
</script>