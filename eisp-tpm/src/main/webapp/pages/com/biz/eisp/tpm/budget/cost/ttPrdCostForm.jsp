<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>产品成本管理编辑</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <%--开始日期与结束日期校验js--%>

</head>
<body>
<t:formvalid formid="formobj" dialog="true" layout="div"  action="ttPrdCostController.do?saveTtPrdCost">
    <input id="id" name="id" type="hidden" value="${ttPrdCostVo.id}">
    <div class="form">
        <label class="Validform_label">年月：</label> <input name="yearMonth" class="Wdate"
      onclick="WdatePicker({dateFmt:'yyyy-MM',onpicked:function(){$('.Wdate').blur();}})"
      dataType="*" class="inputxt"
      value="${ttPrdCostVo.yearMonth}">
        <span style="color: red">*</span>
    </div>

    <div class="form">
        <label class="Validform_label" name="productName">产品名称: </label>
        <input datatype="*" id="productName" name="productName" value="${ttPrdCostVo.productName}" class="inputxt"
               readonly="readonly"/>
        <input id="productCode" name="productCode" value="${ttPrdCostVo.productCode}" type="hidden"/>
        <span style="color: red">*</span>
        <a href="#" class="easyui-linkbutton" plain="true" name="choose" icon="icon-search"
           onClick="openProductSelect()"></a>
        <span class="Validform_checktip" id="errorPro"></span>
    </div>

    <div class="form">
        <label class="Validform_label">产品成本：</label> <input name="costAmount"
           onkeyup="clearNoNum(this)"
    dataType="*" class="inputxt"
    value="${ttPrdCostVo.costAmount}">
        <span style="color: red">*</span>
        <span>元</span>

    </div>

    <div class="form">
        <label class="Validform_label">单位：</label>
        <select name="unit" dataType="*" value="${ttPrdCostVo.unit}">
            <option value="">--请选择--</option>
            <c:forEach items="${costunit}" var="c">
                <option value="${c.dictCode}"
                        <c:if test="${ttPrdCostVo.unit == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">成本类型： </label>
        <select name="costTypeCode" dataType="*" value="${ttPrdCostVo.costTypeCode}">
            <option value="">--请选择--</option>
            <c:forEach items="${costtypes}" var="c">
                <option value="${c.dictCode}"
                        <c:if test="${ttPrdCostVo.costTypeCode == c.dictCode}">selected='selected'</c:if>>${c.dictValue}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>
</t:formvalid>
</body>

<script type="text/javascript">
    //只能输入数字，或者保留两位小数
    function clearNoNum(obj){
        obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }

    //弹出选择产品
    function openProductSelect() {
        safeShowDialog({
            content: "url:tdProductApiController.do?goTdProductMain",
            lock: true,
            title: "选择产品",
            width: 500,
            height: 450,
            left: '85%',
            cache: false,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#productList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#productName").val("");
                    $("#productCode").val("");
                    return true;
                }
                $("#productName").val(rowsData.fullName);
                $("#productCode").val(rowsData.sn);
                return true;
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>
</html>
