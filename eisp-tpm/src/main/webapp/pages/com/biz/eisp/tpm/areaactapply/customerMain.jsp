<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="dealerActApplyList" title="经销商列表"  actionUrl="ttAreaActApplyController.do?findCustomerList"
                    checkbox="true"  fit="true" idField="id"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
            <t:dgCol title="经销商名称" field="dealerName" query="true"  width="200"></t:dgCol>
            <t:dgCol title="经销商编码" field="dealerCode" query="true"   width="100" ></t:dgCol>
            <t:dgCol title="地区" field="orgName"   width="100" ></t:dgCol>
            <t:dgCol title="基金余额" field="fundBalance" width="80" sortable="false" ></t:dgCol>
            <t:dgCol title="未报销金额" field="otherBalance" width="80" sortable="false" ></t:dgCol>
            <t:dgCol title="CRMS待报销积分" field="crmsBlance" width="80" sortable="false" ></t:dgCol>
            <t:dgCol title="本系统未报销金额" field="eblance" width="80" sortable="false" ></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">


</script>
