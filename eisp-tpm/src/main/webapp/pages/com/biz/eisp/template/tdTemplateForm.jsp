<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
                 action="tdTemplateController.do?saveTdTemplate">
        <input type="hidden" name="id" value="${template.id}">
        <div>
            <div class="form">
                <label class="Validform_label">模板名称:</label>
                <input class="inputxt" name="tplName" value="${template.tplName}">
            </div>
            <div class="form">
                <label class="Validform_label">模板类型:</label>
                <t:dictSelect field="tplType" typeGroupCode="tpl_type"></t:dictSelect>
            </div>

            <div class="form">
                <label class="Validform_label">模板路径:</label>
                <input class="inputxt" name="tplPath" value="${template.tplPath}">
            </div>
            <div class="form">
                <label class="Validform_label">备注:</label>
                <textarea name="risk" rows="5" cols="40">${template.risk}</textarea>
            </div>
        </div>
    </t:formvalid>
</div>
<script>
    $(function(){
        //赋值模板类型值
        var tplType = "${template.tplType}";
        $("select[name='tplType']").val(tplType);
    });
</script>

