<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<html>
<head>
    <title>附件上传</title>
</head>
<body style="overflow-y: hidden" scroll="no">

<t:formvalid formid="formobj" layout="div" dialog="true" action="" refresh="true">

    <fieldset class="step">

        <div class="form">
            <label class="Validform_label">核销材料:</label>

            <textarea rows="5" cols="22" disabled="disabled" style="resize: none;">${auditMaterialName}</textarea>

            <span class="Validform_checktip"></span>
        </div>

        <div class="form">
            <label class="Validform_label">备注: </label>
            <textarea rows="5" cols="22" name="attachmentRemark" id="attachmentRemark" style="resize:none;">${attachmentRemark}</textarea>
            <span class="Validform_checktip"></span>
        </div>

        <c:if test="${isReadOnly eq true}">
            <div class="form">
                <label class="Validform_label">附件类型: </label>
                <c:if test="${auditMaterialHead != null && fn:length(auditMaterialHead) > 0 }">
                    <select id="auditMaterialType" onchange="setUploadUrl();">
                        <option value="">请选择</option>
                        <c:forEach items="${auditMaterialHead }" var="material">
                            <option value="${material.auditMaterialCode }">${material.auditMaterialName }</option>
                        </c:forEach>
                    </select>
                </c:if>
                <span>
					<input type="hidden" id="attachmentType" />
		         	<t:uploadH5 name="file_upload" buttonText="选择文件" onUploadSuccess="sureUpload" dialog="false" callback=""
                                uploader="ttBkwAuditAttachemntController.do?saveFiles&businessKey=${businessKey}"
                                extend="*.*" id="file_upload" formData="{attachmentType:'${attachmentType}'}"></t:uploadH5>

		         	<a href="javascript:;" class="easyui-linkbutton" plain="true" id="btn_sub" iconCls="icon-upload" onclick="upload()">确定上传</a>
		         	<div></div>
		         	<div id="filediv" ></div>
				</span>
            </div>
        </c:if>
        <div class="form">
            <div id="sureFilesDetail">
                <c:if test="${auditMaterialDetail != null && fn:length(auditMaterialDetail) > 0 }">
                    <c:forEach items="${auditMaterialDetail}" var="material">
                        <div id="${material.auditMaterialCode }div"><span>${material.auditMaterialName}：</span></div>
                    </c:forEach>
                </c:if>
            </div>
        </div>
    </fieldset>
</t:formvalid>
</body>
</html>
<script type="text/javascript">
    //处理选择的类型
    function setUploadUrl(){
        var type = $("#auditMaterialType").find("option:selected").val();
        $("#attachmentType").val(type);
    }
    //上传
    function upload() {
        var attachmentType = $("#attachmentType").val();
        $('#file_upload').data('uploadifive').settings.formData = {"attachmentType": attachmentType};
        $("#file_upload").uploadifive("upload");
        return false;
    }

    //上传返回后的处理
    function sureUpload(d, file, response) {
        //失败处理
        if (typeof(d) != "undefined" && !d.success) {
            tip(d.msg);
            return false;
        }
        //成功处理
        var businessKey = '${businessKey}';
        var isReadOnly = '${isReadOnly}';
        $.ajax({
            url : "ttBkwAuditAttachemntController.do?findBkwAuditMainAttachment&optype=${optype}",
            data : {
                businessKey : businessKey,
                attachmentRemark : $("#attachmentRemark").val()
            },
            method : "post",
            success : function(data) {
                var str = "";
                var d = $.parseJSON(data);
                if (d.success) {
                    var rows = d.obj;
                    //清空document
                    $("#sureFilesDetail").find("div div").html("");
                    for (var i = 0; i < rows.length; i++) {
                        str = '<div class="sureFilesDetail_a">'+
                            '<a targe="_blank" href="taAttachmentController.do?viewFile&fileid='+ rows[i].id + '"class="easyui-linkbutton l-btn l-btn-plain" ' +
                            'plain="true" iconcls="icon-download">' +
                            '<span class="l-btn-left">' +
                            '<span class="l-btn-text icon-download l-btn-icon-left">'
                            + rows[i].attachmentTitle + '.' + rows[i].extend
                            +'</span>' +
                            '</span>' +
                            '</a>';
                        if(isReadOnly != 'false') {
                            str += '<img onclick="deleteFile(\''+ rows[i].id + '\')" src="resources/Validform/images/error.png" />';
                        }
                        str += '</div>';
                        $("#"+rows[i].attachmentType+"div").append(str);
                    }
                }
                if (str == "")
                    $("#sureFiles").hide();
                else
                    $("#sureFiles").show();
            }
        });
    }
    //删除附件
    function deleteFile(id) {
        var isReadOnly = '${isReadOnly}';
        if (isReadOnly == "true") {
            $.ajax({
                url : "ttBkwAuditAttachemntController.do?delObjFile",
                data : "fileKey=" + id,
                method : "post",
                success : function(data) {
                    var d = JSON.parse(data);
                    tip(d.msg);
                    sureUpload();
                }
            })
        }else{
            tip("该核销申请单在流程中或者审批完成不能删除附件");
            return false;
        }
    }
    $(function() {
        sureUpload();
    });
</script>
