<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="top_panel" fit="true" style="height: 25px;">
    <span style="font-size: 18px;color: mediumvioletred;font-family: 微软雅黑;font-weight: bold">
        &nbsp;注意：尺寸只能往小调整，如果需要往大调，请撤回门头重新提交设计稿！</span>

</div>
<div id="buttom_pannel" style="clear:both; width: 100%;height: 670px;">
        <t:datagrid name="tsActApplyVoDTList"
                    fitColumns="false" checkbox="false"
                    title="可调整尺寸的门头清单（排除经销商未审批通过的，以及公司审批通过的门头。）" actionUrl="ttAdvOutDoorReportController.do?getAdvList"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="材料主键" field="advid" hidden="true" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="false" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatusStr" hidden="false" query="false" sortable="false" ></t:dgCol>

            <t:dgCol title="当前审批人" field="activityName" sortable="false" ></t:dgCol>
            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="重复制作" field="isRepeat" replace="是_1,否_0" hidden="true" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="adCode"  hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="改过尺寸?" field="isModify"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>

            <t:dgCol title="报销材料" field="materialName" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="长（米）" field="mlength" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="高（米）" field="mwidth" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="数量" field="nums" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="面积" field="mspace" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternative" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="天能经销商" field="createName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd" query="true" queryMode="group" sortable="false" ></t:dgCol>
            <t:dgCol title="提交日期" field="commitDate" hidden="false" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司手机" field="advCode" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="actType" hidden="false" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
            <t:dgToolBar title="修改门头尺寸" icon="icon-bcbedit" url="ttAdvOutDoorReportController.do?goAdvSizeView" funname="goAdvSizeView"></t:dgToolBar>
            <t:dgToolBar title="删除封边" icon="icon-bcbdel" url="ttAdvOutDoorReportController.do?delFB" funname="delFB"></t:dgToolBar>

        </t:datagrid>
</div>
<script>

    function delFB(){

        var gridObj = $('#tsActApplyVoDTList');

        var rowsData = gridObj.datagrid('getSelections');
        if(rowsData.length==null||rowsData.length<1){
            tip("请选择要删除的广告封边");
            return ;
        }
        if (rowsData.length>1){
            tip("每次只能处理一条数据，请不要选择多行！");
            return ;
        }
        var ids = []
        for(var i = 0;i<rowsData.length;i++){
            ids.push(rowsData[i].advid);
        }
        var materialName = rowsData[0].materialName;
        if(materialName == null || materialName.indexOf('封边') == -1){
            tip("只允许删除封边数据!");
            return ;
        }
        $.dialog.confirm('亲，没有后悔药哦，您确定要删除封边吗？', function(r) {
            var thisData = {
                ids : ids.join(','),
                status : status
            }
            var url = "ttAdvOutDoorReportController.do?delFB";
            var d = ajaxPost(thisData,url);
            if(d.success){
                gridObj.datagrid("reload");
            }
            tip(d.msg);
        });
    }

    $(function () {
        $("a").each(function () {
            if($(this).hasClass("easyui-linkbutton l-btn l-btn-plain")){
                $(this).removeClass();
                $(this).addClass("easyui-linkbutton l-btn");
            }
        });
    });

    function goAdvSizeView(title, url, id, width, height) {

        var row = $('#' + id).datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        url = url + "&advid=" +row[0].advid;
        update(title, url, id, 1000, 428);
    }

    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('每次只能选择一条记录进行修改');
            return;
        }
        update(title, url, id, 930, 500);
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoDTList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function returnNotNullValueValue(obj){
        if(typeof(obj) != 'undefined' && obj != '' && obj != null && obj != 'null' ){
            return obj;
        }
        return '';
    }
    function actCodeColor(value,rows,index){
        var rows = $("#tsActApplyVoDTList").datagrid("getRows");
        var discount = returnNotNullValueValue(rows[index].discount);
        if(discount!=''&&discount!='100%'){
            return "<u  style='color: blue;background: yellow'>"+value+"</u>"
        }else{
            return "<u  style='color: blue'>"+value+"</u>"

        }
    }

    function searchProc(rowIndex,rowData){

        var procinstId = rowData.procinstId;
        var url = "taTaskController.do?goInstanceHandleTabForm&isView=true&isReadFlag=false&processInstanceId="+procinstId;
        openwindow('查看',url,'',1200,800);
    }

</script>
