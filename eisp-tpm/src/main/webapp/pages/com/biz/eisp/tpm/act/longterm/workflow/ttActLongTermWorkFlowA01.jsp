<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/util/processTheme.js"></script>
<div id="ttActLongtermMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttActLongtermList" title=""  actionUrl="ttActLongTermWorkFlowController.do?findTtActLongTermWorkFlowA01&flagKey=${flagKey}"
	  		  idField="id" fit="true"  fitColumns="false" pagination="false" queryMode="group" singleSelect="false">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
	  		<t:dgCol title="组织" field="orgName"></t:dgCol>
	  		<t:dgCol title="年月" field="yearMonth"></t:dgCol>
	  		<t:dgCol title="预算科目" field="accountName"></t:dgCol>
	  		<t:dgCol title="期初预算金额" field="periodAmount" formatterjs="numExtend"></t:dgCol>
	  		<t:dgCol title="费用额" field="costAmount" formatterjs="numExtend"></t:dgCol>
	  		<t:dgCol title="累计费用" field="totalCostAmount" formatterjs="numExtend"></t:dgCol>
	  		<t:dgCol title="结余费用" field="balanceAmount" formatterjs="numExtend"></t:dgCol>
		</t:datagrid>
	</div>
</div>




