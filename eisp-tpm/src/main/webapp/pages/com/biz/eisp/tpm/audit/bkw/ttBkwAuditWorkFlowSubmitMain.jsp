<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="border:0px;">
		<t:datagrid name="ttBkwAuditMainSelectList" title="未选择宝旺库结案申请"
					actionUrl="ttBkwAuditMainController.do?findTtBkwAuditMainSelectVoList"
					idField="id"
					fit="true"
					fitColumns="true"
					pagination="true"
					queryMode="group"
					singleSelect="false">
			<t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
			<t:dgCol field="billCode" title="结案申请单号" query="true"  ></t:dgCol>
			<t:dgCol field="billName" title="结案申请名称" query="true"  ></t:dgCol>

			<t:dgCol field="createName" title="结案申请人"   ></t:dgCol>
			<t:dgCol field="updateDate" title="结案申请时间" formatter="yyyy-MM-dd hh:mm:ss"  ></t:dgCol>

			<t:dgToolBar title="添加" icon="icon-add" onclick="addItem()"></t:dgToolBar>
			<t:dgToolBar title="全部添加" icon="icon-add" onclick="addAllItem()"></t:dgToolBar>

		</t:datagrid>

	</div>
	<div region="east" style="width:550px;padding:1px;">
		<t:datagrid name="ttBkwAuditMainSelectedList" title="已选择宝旺库结案申请"
					actionUrl=""
					idField="id"
					fit="true"
					fitColumns="false"
					pagination="false"
					queryMode="group"
					singleSelect="false"
					autoLoadData="false" >
			<t:dgCol field="id" title="主键" hidden="true"></t:dgCol>
			<t:dgCol field="billCode" title="结案申请单号" width="80" ></t:dgCol>
			<t:dgCol field="billName" title="结案申请名称"  width="200"  ></t:dgCol>

			<t:dgCol field="createName" title="结案申请人"   width="200"  ></t:dgCol>
			<t:dgCol field="updateDate" title="结案申请时间" formatter="yyyy-MM-dd hh:mm:ss"  width="150"  ></t:dgCol>

			<t:dgToolBar title="删除" icon="icon-remove" onclick="removeItem()()"></t:dgToolBar>
			<t:dgToolBar title="全部删除" icon="icon-remove" onclick="removeAllItem()"></t:dgToolBar>

		</t:datagrid>
	</div>
	<div id="btn_sub" onclick="submitForm()"></div>
</div>
<script type="text/javascript">

    function getLocalTime(nS) {
        return new Date(parseInt(nS) * 1000).toLocaleString().substr(0,17);
    }
    //添加
    function addItem(){
        var seletctTarget =  $("#ttBkwAuditMainSelectList").datagrid("getSelections");
        if(seletctTarget==null || seletctTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        //添加
        for (var i = 0; i < seletctTarget.length; i++) {
            var rows = seletctTarget[i];
            $("#ttBkwAuditMainSelectedList").datagrid("insertRow",{row:rows});
        }
        loadElecteGrid();
    }

    //添加全部
    function addAllItem(){
        var name = "ttBkwAuditMainSelectList";
        var rowsData = $('#' + name).datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var queryParams = $('#' + name).datagrid('options').queryParams;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.ajax({
            url:"ttBkwAuditMainController.do?findTtBkwAuditMainSelectVoList",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        $("#ttBkwAuditMainSelectedList").datagrid("insertRow",{row:row});
                    }
                    loadElecteGrid();
                } else {
                    tip(msg, "error");
                }
            }
        });
    }

    function removeItem() {
        var checkListTarget =  $("#ttBkwAuditMainSelectedList").datagrid("getSelections");
        if(checkListTarget==null || checkListTarget==""){
            tip("请至少选择一条数据");
            return false;
        }
        var copyRows = [];
        for ( var j= 0; j < checkListTarget.length; j++) {
            copyRows.push(checkListTarget[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttBkwAuditMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttBkwAuditMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
    }

    function removeAllItem() {
        var rowsData = $("#ttBkwAuditMainSelectedList").datagrid('getData');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }

        var rows = $("#ttBkwAuditMainSelectedList").datagrid("getRows");
        var copyRows = [];
        for ( var j= 0; j < rows.length; j++) {
            copyRows.push(rows[j]);
        }
        for(var i =0;i<copyRows.length;i++){
            var index = $('#ttBkwAuditMainSelectedList').datagrid('getRowIndex',copyRows[i]);
            $('#ttBkwAuditMainSelectedList').datagrid('deleteRow',index);
        }
        loadElecteGrid();
    }

    //加载待选
    function loadElecteGrid(){
        //加载待选列表
        var exclusiveCodes = "";
        var checkedTarget = $("#ttBkwAuditMainSelectedList").datagrid("getRows");
        if(checkedTarget != null && checkedTarget != ""){
            for(var i = 0;i<checkedTarget.length;i++){
                if(exclusiveCodes != ""){
                    exclusiveCodes += ",";
                }
                debugger;
                exclusiveCodes += "'"+checkedTarget[i].id+"'";
            }
        }
        var actType=$("select[name='actType']").val();
        $('#ttBkwAuditMainSelectList').datagrid("reload", {"exclusiveCodes":exclusiveCodes});
    }

    function submitForm() {
        var rows = $("#ttBkwAuditMainSelectedList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }

        var codes = [];
        for ( var i = 0; i < rows.length; i++) {
            codes.push(rows[i].id);
        }
        var params = {processKeyType:'process_audit_bkw_type'};
        jblSubmitDialog(codes.join(","),"","","com.biz.eisp.tpm.audit.bkw.controller.workflow.TtBkwAuditWorkFlowController",JSON.stringify(params))
    }
</script>