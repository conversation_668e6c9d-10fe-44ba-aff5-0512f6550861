<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
	#ttSplitCostCustomerList thead td{
		text-align: left;
	}
</style>
<div id="ttDirectPresentPoilcyApp" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<div class="datagrid-wrap panel-body">
			<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
				<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<c:if test="${optype == 0 }">
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-add" onclick="addPresentPoil()">添加政策</a>
								<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="removeSelectPresentPoil()">移除产品</a>
							</c:if>
						</span>
						<span id="customer_span_temp_info" style="float:right">
						</span>
					<div style="clear:both;float: none;height:0;display: block;"></div>
				</div>
				<div class="datagrid-toolbar-search" id="productGlobSearchDiv">
					<c:if test="${optype == 0 }">
						<label>开始时间</label>
						<input type="text" id="tempBeginDate" class="Wdate"
							   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'tempEndDate\') }',onpicked:function(){ changeBeginDate() } } )" readonly="readonly" />
						<label>结束时间</label>
						<input type="text" id="tempEndDate" class="Wdate"
							   onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'tempBeginDate\')}',onpicked:function(){ changeEndDate() } } )" readonly="readonly" />
						<label>正品计量单位</label>
						<%--<input type="text" id="unitTemp" />--%>
					</c:if>
					<t:dictSelect id="unitTemp" field="unitTemp" typeGroupCode="dic_mea_unit"  />
				</div>
			</div>
			<div class="datagrid-view" style="height: 100%; overflow: auto;">
				<table class="actTable" id="ttSplitCostCustomerList">
					<thead>
					<tr>
						<td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
						<td>序号</td>
						<td>签呈编号</td>
						<td>开始时间</td>
						<td>结束时间</td>
						<td>客户名称</td>
						<td>客户编码</td>
						<td>门店SAP编码</td>
						<td>门店名称</td>
						<td>正品编码</td>
						<td>正品名称</td>
						<td>正品数量</td>
						<td>正品计量单位</td>
						<td>赠品编码</td>
						<td>赠品名称</td>
						<td>赠品数量</td>
						<td>赠品计量单位</td>
					</tr>
					</thead>
					<tbody></tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">
    var li_east = 0;
    //datagrid行号
    var rowIndexTemp;
    //datagrid行数据
    var rowDataTemp;
    //全局编号
    var gloNum = 1;
    //是否不展示选择框
    var checkboxIsNotneedShow = true;
    var selectHtml;
    //存储已选择的客户Map
    var map = new HashKey();
    $(document).ready(function(){
        initSelectHtml();
        if(getOptype() == '1'){
            initRowDataTemp();
            queryDirPresentPoliMain();
            removeSelectBox();
        }else if(getOptype() == '2'){
            initRowDataTemp();
            queryDirPresentPoliMain();
            changeToDisable();
            removeSelectBox();
        }else{
        }
        addEventToSomLabel();
    });

    function getOptype(){
		return '${optype}';
	}

    function initRowDataTemp(){
        rowDataTemp = new Object();
        rowDataTemp.id = '${ttDirectPresentPoliMainVo.id}';
        rowIndexTemp = 0;
    }
	//移除下拉选择框
    function removeSelectBox(){
        $('#unitTemp').remove();
	}

    //改变展示
    function changeToDisable(){
        $(':input').attr("disabled",true);
        $('select').attr("disabled",true);
        checkAndChangeCheckBox();
//		$('.actTable tbody td[id ^= "presentPrdName_"]').attr('onclick','');
        $('.actTable tbody td').attr('onclick','');
    }

    //检查选择框处理
    function checkAndChangeCheckBox(){
        if(checkboxIsNotneedShow){
            $(':checkbox').parent().hide();
        }else{
            $(':checkbox').parent().show();
            $(':checkbox').removeAttr("disabled");
        }
    }

    //初始化选择框html
    function initSelectHtml(){
        selectHtml = $('#unitTemp').html();
	}

    //初始化绑定事件
    function addEventToSomLabel(){
        $('#unitTemp').change(function(){
            unitTempChanged();
		});
	}

    //清空查询数据
    function initSearchDivsNode(){
        $('#productGlobSearchDiv :input').val('');
    }
    //---------------------直营搭赠产品处理区star-------------------------------//
    //查询政策数据
    function queryDirPresentPoliMain(){
        clearActTableTbody();
        var thisData = {
            ids : rowDataTemp.id
        }
        var url = "ttDirectPresentPoliMainController.do?findHasTtDirectPresentPoliMainList";
        $.ajax({
            async : false,
            cache : false,
            data : thisData,
            type : 'POST',
            url : url,// 请求的action路径
            error : function() {// 请求失败处理函数
            },
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    starWriteInTemplate(d.obj,'1');
                }else{
                    tip(d.msg);
                }
            }
        });
    }
    //添加产品
    function addPresentPoil(){
        var rowTr = $(".actTable tbody tr");
        if(rowTr != null && rowTr.length > 0){
            getSafeJq().dialog.confirm("<span style='color: red; '>注意：重新选择搭赠数据会清空现有的数据哦，确定继续？</span>", function() {
				starToChoosePresentPoil();
            })
		}else{
            starToChoosePresentPoil();
		}
    }

    function starToChoosePresentPoil(){
        var paraArr = {
            title : '选择搭赠数据',
            selectType : 'getSelections',
            grId : 'chooseDirectPresentMainList', //datagrid 对应的id
            url : 'ttDirectPresentApiController.do?goChooseDirectPresentList',
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型
            callBackFun : distinguishResource
        }
        openChooseSelect(paraArr);
	}

    function distinguishResource(datas){
		var dateTemp = reconstructingData(datas);
        starWriteInTemplate(dateTemp,'0');
    }

    //后台查询并返回封装数据
    function reconstructingData(datast){
		var dataTemp = '';

		var ids = [];
        for ( var i = 0; i < datast.length; i++) {
            ids.push(datast[i].id);
        }

        var thisData = {
            ids : ids.join(","),
        }

        //访问后台--读取并构造数据
        url = "ttDirectPresentPoliMainController.do?reconstructingDataByParams";
        $.ajax({
            async : false,
            cache : false,
            data : thisData,
            type : 'POST',
            url : url,// 请求的action路径
            success : function(data) {
                window.top.$.messager.progress('close');
                var d = $.parseJSON(data);
                if (d.success) {
                    dataTemp = d.obj
                }else{
                    tip(d.msg);
                }
            },
            error : function() {
                window.top.$.messager.progress('close');
                tip('服务器繁忙，请稍后再试');
            }
        });
		return dataTemp;
	}

    //写入产品列表
    function starWriteInTemplate(datas,srcType){
        var trStrs = "";
        for (var i = 0; i < datas.length; i++) {
            var data = datas[i];
            var obj = new Object();

            if (srcType != '1') {
                data.id = '';
            }
            obj.id = returnNotUndefinedData(data.id);
            obj.index = gloNum;
            obj.num = returnNotUndefinedData(data.num);

            obj.yearMonth = returnNotUndefinedData(data.yearMonth);
            obj.beginDate = returnNotUndefinedData(data.beginDate);
            obj.srcBeginDate = returnNotUndefinedData(data.srcBeginDate);
            obj.endDate = returnNotUndefinedData(data.endDate);
            obj.srcEndDate = returnNotUndefinedData(data.srcEndDate);

            obj.orgCode = returnNotUndefinedData(data.orgCode);
            obj.orgName = returnNotUndefinedData(data.orgName);
            obj.orgSapcode = returnNotUndefinedData(data.orgSapcode);
            obj.orgCostCenter = returnNotUndefinedData(data.orgCostCenter);//
            obj.businessUnitCode = returnNotUndefinedData(data.businessUnitCode);
            obj.businessUnitName = returnNotUndefinedData(data.businessUnitName);

            obj.customerCode = returnNotUndefinedData(data.customerCode);
            obj.customerName = returnNotUndefinedData(data.customerName);
            obj.terminalSapcode = returnNotUndefinedData(data.terminalSapcode);
            obj.terminalName = returnNotUndefinedData(data.terminalName);//
            obj.productCode = returnNotUndefinedData(data.productCode);
            obj.productName = returnNotUndefinedData(data.productName);//
            obj.productQuantity = returnNotNullAmountData(data.productQuantity);
            obj.productUnit = returnNotUndefinedData(data.productUnit);

            obj.presentCode = returnNotUndefinedData(data.presentCode);
            obj.presentName = returnNotUndefinedData(data.presentName);//
            obj.presentQuantity = returnNotNullAmountData(data.presentQuantity);
            obj.presentUnit = returnNotUndefinedData(data.presentUnit);

            obj.dpId = returnNotUndefinedData(data.dpId);

            //构造组成行数据
            trStrs += structureRowData(obj);
            gloNum ++;
        }
        if (trStrs != '') {
            clearActTableTbody();
            $(".actTable tbody").append(trStrs);
            changeSelectValue();
        }
    }

    var htmlConnect = '_';
    var inputConnect = '_input_';
    var selectConnect = '_select_';
    //构造组成行数据
    function structureRowData(o){
        var indexTd = returnIndex(o.index,htmlConnect);
        var indexInput = returnIndex(o.index,inputConnect);
        var indexSelect = returnIndex(o.index,selectConnect);
        var str = ' <tr id="tr' + indexTd + '"> ';
        str += '<td class="datagrid-td-rownumber" ><input type="checkbox" id="actTableId' + indexInput + '" name="actTableId" value="' + o.id + '"/></td>';
        str += '<td id="index' + indexTd + '" >' + o.index + '</td>';//序号

        str += '<td id="num' + '' + indexTd + '" >' + o.num + '</td>';//签呈编号

        str += '<td id="yearMonth' + '' + indexTd + '" style="display:none" >' + o.yearMonth + '</td>';//年月

        str += '<td id="beginDate' + indexTd + '" class="Wdate">';
        str += '<input type="text" ' + 'style="width:90px;"' + ' id="beginDate' + indexInput + '" name="beginDate' + indexTd + '" value="' + o.beginDate + '" class="Wdate" onclick="WdatePicker({dateFmt:\'yyyy-MM-dd\',maxDate:\'#F{$dp.$D(\\\'endDate' + indexInput + '\\\')}\' ,onpicked:function(){$(\'.Wdate\').blur();}})" readonly="readonly" />';
        str += '</td>';//开始时间

        str += '<td id="srcBeginDate' + indexTd + '" style="display:none" >' + o.srcBeginDate + '</td>';//原开始时间

        str += '<td id="endDate' + indexTd + '" class="Wdate">';
        str += '<input type="text" ' + 'style="width:90px;"' + ' id="endDate' + indexInput + '" name="endDate' + indexTd + '" value="' + o.endDate + '" class="Wdate" onclick="WdatePicker({dateFmt:\'yyyy-MM-dd\',minDate:\'#F{$dp.$D(\\\'beginDate' + indexInput + '\\\')}\' ,onpicked:function(){$(\'.Wdate\').blur();}})" readonly="readonly" />';
        str += '</td>';//结束时间

        str += '<td id="srcEndDate' + indexTd + '" style="display:none" >' + o.srcEndDate + '</td>';//原结束时间

        str += '<td id="orgCode' + indexTd + '" style="display:none" >' + o.orgCode + '</td>';//所属组织编码
        str += '<td id="orgName' + indexTd + '" style="display:none" >' + o.orgName + '</td>';//所属组织名称
        str += '<td id="orgSapcode' + indexTd + '" style="display:none" >' + o.orgSapcode + '</td>';//组织sap编码
        str += '<td id="orgCostCenter' + indexTd + '" style="display:none" >' + o.orgCostCenter + '</td>';//成本中心
        str += '<td id="businessUnitCode' + indexTd + '" style="display:none" >' + o.businessUnitCode + '</td>';//所属事业部编码
        str += '<td id="businessUnitName' + indexTd + '" style="display:none" >' + o.businessUnitName + '</td>';//所属事业部名称

        str += '<td id="customerCode' + indexTd + '">' + o.customerCode + '</td>';//客户编号
        str += '<td id="customerName' + indexTd + '">' + o.customerName + '</td>';//客户名称
        str += '<td id="terminalSapcode' + indexTd + '">' + o.terminalSapcode + '</td>';//门店sap编码
        str += '<td id="terminalName' + indexTd + '">' + o.terminalName + '</td>';//门店名称

        str += '<td id="productCode' + indexTd + '">' + o.productCode + '</td>';//正品编号
        str += '<td id="productName' + indexTd + '">' + o.productName + '</td>';//正品名称
        str += '<td id="productQuantity' + indexTd + '">' + o.productQuantity + '</td>';//正品数量

        str += '<td id="productUnit' + indexTd + '">';
//        str += '<input ' + 'style="width:70px;"' + ' id="productUnit' + indexInput + '" name="productUnit' + indexTd + '" value="' + o.productUnit + '" onkeydown="keyCode(event,false)" onkeyup="verifyInput(this,false)" onblur="productUnitTheNewChoice(this)"/>';
        str += ' <select ' + 'style="width:70px;"' + ' id="productUnit' + indexSelect + '" name="productUnit' + indexTd + '" dfValue="' + o.productUnit + '" onchange="productUnitTheNewChoice(this)">' + selectHtml + '</select> '
		str += '</td>';//正品计量单位  selectHtml

        str += '<td id="presentCode' + indexTd + '">' + o.presentCode + '</td>';//赠品编码
        str += '<td id="presentName' + indexTd + '">' + o.presentName + '</td>';//赠品名称
        str += '<td id="presentQuantity' + indexTd + '">' + o.presentQuantity + '</td>';//赠品数量
        str += '<td id="presentUnit' + indexTd + '">' + o.presentUnit + '</td>';//赠品计量单位

        str += '<td id="dpId' + indexTd + '" style="display:none" >' + o.dpId + '</td>';//搭赠明细id

        str += ' </tr> ';
        return str;
    }

    function changeSelectValue(){
        var trDatas = $(".actTable tbody tr select ");
        trDatas.each(function(){
            var obj = $(this);
            obj.val(obj.attr("dfValue"))
//            productUnitTheNewChoice(this)
        });
	}

    function returnIndex(index,connect){
		return connect + index;
	}
    /**
     * 监听Tab按键
     */
    function keyCode(event,isDouble) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target,isDouble);
        }
    }
    /**
     * 页面输入验证
     */
    function verifyInput(obj,isDouble) {
        if (isDouble == false) {
            verifyNotDouble(obj);
        }else{
            verifyDouble(obj);
        }
    }

    //检查数字
    function verifyNotDouble(obj){
        obj.value = obj.value.replace(/[^\d]/g,""); //清除"数字"以外的字符
    }
    //检查数字+小数
    function verifyDouble(obj){
        obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }

    //----------------------------时间处理Star-----------------------------
    //批量修改开始时间
    function changeBeginDate() {
		starChangeBeginDate();
    }
    function starChangeBeginDate(){
        var beginDate = $('#tempBeginDate').val();
        if(beginDate != ''){
            getSelectInputJsObj("beginDate" + inputConnect,true).val(beginDate);
        }
    }
    //批量修改结束时间
    function changeEndDate() {
		starChangeEndDate();
    }
    //批量修改结束时间
    function starChangeEndDate() {
        var endDate = $('#tempEndDate').val();
        if(endDate != ''){
            getSelectInputJsObj("endDate" + inputConnect ,true).val(endDate);
        }
    }

    //批量修改计量单位
    function unitTempChanged(){
        starUnitTempChanged();
	}
	//批量修改计量单位
	function starUnitTempChanged(){
        var unitTemp = $('#unitTemp').val();
        if(unitTemp != ''){
            getSelectSelectJsObj("productUnit" + selectConnect ,true).val(unitTemp);
            getSelectTdJsObj("presentUnit" + htmlConnect,true).html(unitTemp);
        }
	}


    //----------------------------时间处理end-----------------------------

	function productUnitTheNewChoice(obj){
        var name = $(obj).attr("name");
        var names = name.split("_");
        var indexTempt = names[names.length-1];
        subsequentProcessing(indexTempt);
	}

	function subsequentProcessing(indexTemp){
		//产品计量单位
        var productUnit = $("#productUnit" + selectConnect + indexTemp ).val();
        $("#presentUnit" + htmlConnect + indexTemp ).html(productUnit);
	}

    //移除选中的政策维护数据
    function removeSelectPresentPoil(){
        var trDatas = $(".actTable tbody :checked[id ^= 'actTableId_'] ");
        if (checkIsNotUndefinedAndNull()) {
            tip("请选择要移除的项目");
            return ;
        }
        getSafeJq().dialog.confirm("确定要移除选中的数据？", function() {
            trDatas.each(function(){
                var obj = $(this);
                if (obj.attr("checked") == "checked") {
                    var num = readLineNumber(obj,"id");
                    $("#tr_" + num).remove();
                }
            });
        })
    }

    var isCloseSave = 0;
    //关闭检查保存
    function saveDirectPresentByClose(){
        getSafeJq().dialog.confirm("是否保存当前数据?", function(r) {
                if (r) {
                    isCloseSave = 1;
                    openProgress();
                    setTimeout("encapsulationAndStarPageDiectPresent()",0);
                }
            },
            function(r){
                close();
            });
    }
    //保存
    function saveDiectPresent(){
        getSafeJq().dialog.confirm("是否保存当前数据?", function(r) {
            if (r) {
                openProgress();
                setTimeout("encapsulationAndStarPageDiectPresent()",0);
            }
        });
    }

    function encapsulationAndStarPageDiectPresent(){
        var falg = false;
        var isContinue = true;
        var objList = [];
        var mapTemp = new HashKey();
        var errorMapTemp = new HashKey();
        $(".actTable tbody tr").each(function(i,o){
            falg = true;
            var num = readLineNumber($(o),"id");
            var obj = new Object();

            obj.id = returnRowInputData("actTableId",num);

            obj.pnum = returnRowHtmlData("index",num);

            obj.num = returnRowHtmlData("num",num);
            obj.yearMonth = returnRowHtmlData("yearMonth",num);

            obj.beginDate = returnRowInputData("beginDate",num);
            obj.endDate = returnRowInputData("endDate",num);

            obj.srcBeginDate = returnRowHtmlData("srcBeginDate",num);
            obj.srcEndDate = returnRowHtmlData("srcEndDate",num);

            if(!checkYearMonth(obj.beginDate,obj.srcBeginDate,obj.endDate,obj.srcEndDate,num)){
                isContinue = false;
                return false;
            }

            obj.orgCode = returnRowHtmlData("orgCode",num);
            obj.orgName = returnRowHtmlData("orgName",num);
            obj.orgSapcode = returnRowHtmlData("orgSapcode",num);
            obj.orgCostCenter = returnRowHtmlData("orgCostCenter",num);
            obj.businessUnitCode = returnRowHtmlData("businessUnitCode",num);
            obj.businessUnitName = returnRowHtmlData("businessUnitName",num);

            obj.customerCode = returnRowHtmlData("customerCode",num);
            obj.customerName = returnRowHtmlData("customerName",num);
            obj.terminalSapcode = returnRowHtmlData("terminalSapcode",num);
            obj.terminalName = returnRowHtmlData("terminalName",num);

            obj.productCode = returnRowHtmlData("productCode",num);
            obj.productName = returnRowHtmlData("productName",num);
            obj.productQuantity = returnRowHtmlData("productQuantity",num);
            obj.productUnit = returnRowSelectData("productUnit",num);

            obj.presentCode = returnRowHtmlData("presentCode",num);
            obj.presentName = returnRowHtmlData("presentName",num);
            obj.presentQuantity = returnRowHtmlData("presentQuantity",num);
            obj.presentUnit = returnRowHtmlData("presentUnit",num);

            obj.dpId = returnRowHtmlData("dpId",num);
            /*if(!checkIsRepeat(mapTemp,obj,errorMapTemp)){
                //暂无处理逻辑
            }*/
            objList.push(obj);
        });
        if (!falg) {
            tip("至少一条政策维护数据");
            return false;
        }
        if(isContinue){
            var keyStrs = errorMapTemp.returnKey();
            if(keyStrs.length > 0){
                return starSaveDirectPresent(objList);
                /*var errorMsg = '';
                for(var keyNum in keyStrs){
                    var keyStr = keyStrs[keyNum];
                    errorMsg += "组别" + (Number(keyNum) + 1 )+ ":-->序号：" + errorMapTemp.get(keyStr) + "，重复；";
                }
                if(errorMsg != ''){
                    errorMsg = '按规则：’同一客户、同一品项、同一活动细类、同一时间段‘,检测有重复数据->按重复数据分组：' + errorMsg;
                    tipThisDataIsCouldToContinue(objList,errorMsg);
                }*/
            }else{
                return starSaveDirectPresent(objList);
            }
        }
        return false;
    }

    function tipThisDataIsCouldToContinue(objList,errorMsg){
        getSafeJq().dialog.confirm(errorMsg + ",是否继续？", function(r) {
            if (r) {
                setTimeout(function(){ starSaveDirectPresent(objList);},0);
            }
        });
    }
    function checkIsRepeat(mapTemp,obj,errorMapTemp){
        var filedTemp = "costTypeCode,beginDate,endDate,productCode";
        var resultKsy = connectParams(obj,'_',filedTemp);
        var numTemp = mapTemp.get(resultKsy);
        if(checkIsNotUndefinedAndNull(numTemp)){
            var errorMsg = errorMapTemp.get(numTemp);
            if(checkIsNotUndefinedAndNull(errorMsg)){
                errorMsg += "," + obj.pnum
            }else{
                errorMsg += numTemp + "," + obj.pnum;
            }
            errorMapTemp.set(numTemp,errorMsg);
            return false;
        }else{
            mapTemp.set(resultKsy,obj.pnum);
        }
        return true;
    }
    //连接参数
    function connectParams(obj,connect,filedStrs){
        var strTemp = '';
        var fileds = filedStrs.split(",");
        for (var filedNum in fileds) {
            var filed = fileds[filedNum]
            if(strTemp != ''){
                strTemp += connect;
            }
            strTemp += obj[filed];
        }
        return strTemp;
    }
    function checkDataIsNotNull(value,errorMsg){
        if(!checkIsNotUndefinedAndNull(value)){
            tip(errorMsg);
            return false;
        }
        return true;
    }
    //检查年月
    function checkYearMonth(beginDate,srcBeginDate,endDate,srcEndDate,num){
        if (beginDate == '') {
            tip("请填写开始时间,序号：" + num);
            return false;
        }
        if (endDate == '') {
            tip("请填写结束时间,序号：" + num);
            return false;
        }
        if(beginDate > endDate){
            tip("开始时间必须小于结束时间，序号：" + num);
            return false;
        }
        if(checkBeginDateAndEndDate(beginDate,endDate,num)){
            var dateStr = beginDate;
            if(!checkTheYearMonthIsOk(dateStr,srcBeginDate,srcEndDate,num,"开始时间")){
                return false;
            }
            dateStr = endDate;
            if(!checkTheYearMonthIsOk(dateStr,srcBeginDate,srcEndDate,num,"结束时间")){
                return false;
            }
            return true;
        }
        return false;
    }
    function checkTheYearMonthIsOk(dateStr,srcBeginDate,srcEndDate,num,msg){
        if(!(srcBeginDate <= dateStr && dateStr <= srcEndDate)){
            tip( msg + "应当在原开始时间(" + srcBeginDate + ")和原结束时间(" + srcEndDate + ")之间,序号：" + num + "," + msg );
            return false;
		}
		return true;
    }
    //检查开始时间和结束时间是否处于同一个年月
    function checkBeginDateAndEndDate(beginDateStr,endDateStr,num){
        //将yyyy-MM-dd截取字符串为yyyy-MM
        var beginDateTemp = changeDateToyyyMM(beginDateStr);
        var endDateTemp = changeDateToyyyMM(endDateStr);
        if (beginDateTemp != endDateTemp) {
            tip("开始时间和结束时间必须为同一个月,序号：" + num);
            return false;
        }
        return true;
    }
    //抽成方法便于维护，共用
    function changeDateToyyyMM(dateStr){
        //将yyyy-MM-dd截取字符串为yyyy-MM
        return dateStr.substr(0, 7);//从第1个开始到之后的七个（包含第一个）
    }
    //开始保存政策维护
    function starSaveDirectPresent(list){
        var thisData = {
            info:JSON.stringify({poliMainVoList:list}),
        }
        url = "ttDirectPresentPoliMainController.do?saveOrUpdateSelectedTtDirectPresentPoliMainList";
        var flag = false;
        $.ajax({
            async : false,
            cache : false,
            data : thisData,
            type : 'POST',
            url : url,// 请求的action路径
            error : function() {// 请求失败处理函数
            },
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
//                    queryDirPresentPoliMain();
                    //主页面刷新
					headPagerefresh();
                    W.tip(d.msg);
                    close();
                    /*if(isCloseSave == 1){
                        W.tip(d.msg);
                        close();
                    }*/
                }
                tip(d.msg);
                flag = d.success;
            },
            complete : function(){
                closeProgress();
            }
        });
        return flag;
    }
    //主页面刷新
    function headPagerefresh(){
        W.$('#ttDirectresentPoliMainList').datagrid("reload");
    }

	//检查是否大于0
    function checkNumberIsNotZero(value,msg,num){
        if(value <= 0){
            var errorMsg = msg + "必须大于0," + returnErrorNumMsg(num)
            tip(errorMsg);
            return false;
        }
        return true;
    }
    //错误行
    function returnErrorNumMsg(num){
        if (checkIsNotUndefinedAndNull(num)) {
            return "序号：" + num;
        }
    }

    //返回行内input数据
    function returnRowInputData(id,num){
//		return returnNotUndefinedData($('#' + id + '_input_' + num).val());
        return $('#' + id + inputConnect + num).val();
    }

    //返回行内selct数据
    function returnRowSelectData(id,num){
        return $('#' + id + selectConnect + num).val();
    }

    //返回行内html数据
    function returnRowHtmlData(id,num){
//		return returnNotNullAmountData($('#' + id + '_' + num).html());
        return $('#' + id + htmlConnect + num).html();
    }

    //返回行编号
    function readLineNumber(obj,property){
        var strTemp = obj.attr(property);
        var strTemps = strTemp.split("_");
        return strTemps[strTemps.length-1];
    }
    function clearActTableTbody(){
        $(".actTable tbody").html("");
        gloNum = 1;
    }
    //获取选择的数据对象
    function getSelectTdJsObj(id,isSelect) {
        if(isSelect == true){
            return $(":checked").parent().nextAll('td[id^=' + id + ']');
        }
        return $("td[id^=" + id + "]" );
    }

    function getSelectInputJsObj(id,isSelect){
        if(isSelect == true){
            return $(":checked").parent().nextAll('td').find('input[id^=' + id + ']');
        }
        return $("input[id^=" + id + "]" );
    }

    function getSelectSelectJsObj(id,isSelect){
        if(isSelect == true){
            return $(":checked").parent().nextAll('td').find('select[id^=' + id + ']');
        }
        return $("select[id^=" + id + "]" );
    }

    //---------------------直营搭赠产品处理区end-------------------------------//

    //--------------------高精度计算函数star------------------------------//
    /**
     * 高精度加法函数
     */
    function add(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
	/*
	 * 高精减法函数
	 */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();

        try {
            m += s1.split(".")[1].length;
        }
        catch (e) {

        }
        try {
            m += s2.split(".")[1].length;
        }
        catch (e) {

        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;

        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch(e) {
            r2 = 0;
        }

        // 动态求出哪一个位数最多，再得出10的n次幂
        return Math.pow(10, Math.max(r1, r2));
    }

    //--------------------高精度计算函数end------------------------------//

    //返回不为undefined的数据
    function returnNotUndefinedData(value){
        return (typeof(value) != 'undefined' && value != null && $.trim(value ).length != 0) ? value : '';
    }

    //金额字段返回不为undefined及不为空的数据
    function returnNotNullAmountData(value){
        return (typeof(value) != 'undefined' && value != null && value != 'null' && $.trim(value ).length != 0 ) ? value : '';
    }

    //将obj转换为urlData
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }
    //不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //置空返回---勿删有用的
    function returnUndefined(){
        var aaa;
        return aaa;
    }
    function openProgress(){
        //window.top.$.messager.progress('open');
    }
    function closeProgress(){
        //window.top.$.messager.progress('close');
    }
    function close(){
        frameElement.api.close();
    }
    //处理键值对
    function HashKey(){
        var data = {};
        this.set = function(key,value){   //set方法
            data[key] = value;
        };
        this.unset = function(key){     //unset方法
            delete data[key];
        };
        this.get = function(key){     //get方法
            return data[key] || "";
        }
        this.returnKey = function(){  //返回所有的key
            var arrTemp = [];
            for(name in data){
                arrTemp.push(name);
            }
            return arrTemp;
        }
    }
</script>