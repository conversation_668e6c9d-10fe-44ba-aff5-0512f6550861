<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_ttAuditMaterialList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
	    <t:datagrid name="ttAuditMaterialList" fitColumns="true" title="核销资料管理" 
	   				 actionUrl="ttAuditMaterialController.do?findTtAuditMaterialMain" idField="id" queryMode = "group">
			 
			<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
  		    <t:dgCol title="核销资料编号" field="materialCode" query="true" width="200"></t:dgCol>
	        <t:dgCol title="核销资料名称" field="materialName" query="true" width="200"></t:dgCol>
	  		<t:dgCol title="创建人" field="createName" width="120"></t:dgCol>
	  		<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss" width="120"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName" width="120"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss" width="120"></t:dgCol>	      
	        <t:dgToolBar title="新增" operationCode="add" icon="icon-add" url="ttAuditMaterialController.do?goTtAuditMaterialForm" funname="add"></t:dgToolBar>
	        <t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="ttAuditMaterialController.do?goTtAuditMaterialForm" funname="update"></t:dgToolBar>
	        <t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="ttAuditMaterialController.do?delTtAuditMaterial" funname="deleteALLSelect"></t:dgToolBar>
	        <t:dgToolBar title="日志" operationCode="log" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detailLog" width="1200" height="460"></t:dgToolBar>
	    </t:datagrid>
	</div>
</div> 
