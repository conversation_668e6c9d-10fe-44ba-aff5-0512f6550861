<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
    <div data-options="region:'center'" style="padding:1px;">
        <t:datagrid name="ttMonthlyPlanGatherList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
                    actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanGatherEditList&orgCode=${ttMonthlyPlanEditVo.orgCode}&year=${ttMonthlyPlanEditVo.year}&month=${ttMonthlyPlanEditVo.month}&yearMonth=${ttMonthlyPlanEditVo.yearMonth}"
                    onClick="clickMonthlyPlanFun">
            <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" hidden="false" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="组织名称" field="orgName" hidden="false" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  query="true" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"  query="true" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="计划年份" field="year" query="false" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="计划月份" field="month"  query="false" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="挑战版收入预算(元)" field="tiaoZhanRevenueBudget" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="计划总额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="是否可编辑" field="editableStatus" replace="可编辑_1,不可编辑_0" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="状态" field="bpmStatus" dictionary="monthlyPlan_Bpm_status" sortable="false" width="100" ></t:dgCol>
        </t:datagrid>
    </div>
    <div data-options="region:'east',
	title:'',
	collapsed:true,
	split:true,
	border:false"
         style="padding:1px;width:700px;">
        <t:datagrid name="ttMonthlyPlanList" fitColumns="false" queryMode = "group" idField="id" singleSelect="false"
                    pagination="true" autoLoadData="false" actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findTtMonthlyPlanList">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="产品编号" field="productCode" query="true" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="产品名称" field="productName"  query="true" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="单价（元）" field="price" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="计划销量（EA）" field="planSales" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="计划销额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="吨位" field="tonnage" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="发起人" field="createName" sortable="false" width="100" ></t:dgCol>
        </t:datagrid>
    </div>
</div>

<script type="text/javascript">
    var li_east = 0;
    $(document).ready(function(){
    });
    function clickMonthlyPlanFun(rowIndex,rowData) {
        if(li_east == 0){
            $('#ttReportMonthlyPlanMain').layout('expand','east');
            li_east ++;
        }
        var customerCode = rowData.customerCode;
        var year = rowData.year;
        var month = rowData.month;
        var queryParams = $('#ttMonthlyPlanList').datagrid('options').queryParams;
        queryParams.customerCode = customerCode;
        queryParams.year = year;
        queryParams.month = month;
        $("#ttMonthlyPlanList").datagrid({url:"ttMonthlyPlanController.do?findTtMonthlyPlanList"});
    }
</script>