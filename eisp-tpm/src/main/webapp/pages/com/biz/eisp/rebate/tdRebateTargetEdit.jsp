<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>返利目标管理</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body >
<div>
	<t:formvalid formid="formobj" layout="div" beforeSubmit="fetchTableValues();" dialog="true" refresh="true"
				 action="tdRebateTargetController.do?saveTdRebateTarget">
		<input id="id" name="id" type="hidden" value="${tdRebateTargetVo.id}">

		<div class="form">
			<label class="Validform_label">非直供编码: </label>
			<input name="disbCode" class="inputxt" value="${tdRebateTargetVo.disbCode}" datatype="s2-30">

			<t:choose
					hiddenName="disbCode" hiddenid="kunnr"
					url="tdDistributorSalesController.do?goTdDistributorChoose" name="tdDistributorList"
					icon="icon-search"
					title="客户编码" textname="name" inputTextname="name" isclear="true"
					width="400" height="400">
			</t:choose>
			<span style="color: red">*</span>
		</div>

		<div class="form">
			<label class="Validform_label">非直供名称: </label>
			<input name="disbName" class="inputxt" id="name" value="${tdRebateTargetVo.disbName}" datatype="*2-30">
			<span style="color: red">*</span>
		</div>

		<div class="form">
			<label class="Validform_label">上级客户编码: </label>
			<input name="kunnr" class="inputxt"   id="kunnrInput" value="${tdRebateTargetVo.kunnr}" datatype="s2-30">
			<span style="color: red">*</span>

			<t:choose
					hiddenName="kunnrInput" hiddenid="customerCode"
					url="tmCustomerController.do?goTpmCustMain" name="customerList"
					icon="icon-search"
					title="客户编码" textname="customerName" inputTextname="customerName" isclear="true"
					width="400" height="400">
			</t:choose>
		</div>


		<div class="form">
			<label class="Validform_label">上级客户名称: </label>
			<input name="kunnrName" id="customerName" class="inputxt" readonly="readonly" datatype="*"  value="${tdRebateTargetVo.kunnrName}" >
			<span style="color: red">*</span>

		</div>


		<div class="form">
			<label class="Validform_label">目标对象编码: </label>
			<input name="targetObject" class="inputxt"   id="targetObject" value="${tdRebateTargetVo.targetObject}" datatype="s2-30">
			<span style="color: red">*</span>
		</div>

		<div class="form">
			<label class="Validform_label">目标对象: </label>
			<input name="targetObjectName" id="text" class="inputxt" readonly="readonly" datatype="*"  value="${tdRebateTargetVo.targetObjectName}" >
			<t:choose
					hiddenName="targetObject" hiddenid="productCode"
					url="tdMaterialController.do?tmProductSeriesChoose" name="prdList"
					icon="icon-search"
					title="系列" textname="text" inputTextname="text" isclear="true"
					width="400" height="400">
			</t:choose>
			<span style="color: red">*</span>

		</div>

		<div class="form">
			<label class="Validform_label">目标单位: </label>
			<select name="targetUnit">
				<option value=0
						<c:if test="${tdRebateTargetVo.targetUnit ==0}">selected</c:if>
				>箱
				</option>
				<option value=1
						<c:if test="${tdRebateTargetVo.targetUnit >=1}">selected</c:if>
				>万元
				</option>
			</select>
			<span style="color: red">*</span>
		</div>


		<div class="form">
			<label class="Validform_label">目标值: </label>
			<input name="targetValue" class="inputxt"   id="targetValue" value="${tdRebateTargetVo.targetValue}" datatype="n2-30">
			<span style="color: red">*</span>
		</div>


		<div class="form" id="startDateDiv">
			<label class="Validform_label">开始日期:</label>
			<input onclick="checkDateStart('endDateInput','ymd')"
				   class="inputxt Wdate" readonly="readonly"
				   name="startDate" id="startDateInput" value="${tdRebateTargetVo.startDate}" datatype="*"/>
			<span style="color: red">*</span>
		</div>

		<div class="form" id="endDateDiv">
			<label class="Validform_label">结束日期:</label>
			<input onclick="checkDateEnd('startDateInput','ymd')"
				   class="inputxt Wdate" readonly="readonly"
				   name="endDate" id="endDateInput" value="${tdRebateTargetVo.endDate}" datatype="*"/>
			<span style="color: red">*</span>
		</div>


</t:formvalid>

</div>

</body>


<!--开始日期与结束日期校验js-->
<script src="resources/shop/js/common.js"></script>





</html>