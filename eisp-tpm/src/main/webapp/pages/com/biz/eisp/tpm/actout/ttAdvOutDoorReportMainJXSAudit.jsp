<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<title>经销商审批</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="top_panel" fit="true" style="height: 70px;">
<!--div id = "showReferenceData" -->
    <table>
        <tr><td>&nbsp;&nbsp;&nbsp;&nbsp;</td><td>当前可用积分</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>授信积分</td>
            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td align="center">广告基金账户</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td align="center">广告门头未报销</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td align="center">本批次可报销</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td align="center">CRMS待报销</td>
            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td rowspan="4">
                <!--button class="but" type="button" id="but_login" style="cursor:pointer;"
                        onclick="checkUserName()" >智能审批</button-->
                <a iconcls="icon-see" class="easyui-linkbutton l-btn" href="#" onclick="AIAudit()">&nbsp;&nbsp;智能审批</a>
            </td><td></td>
        </tr>
        <tr><td>&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td rowspan="3" style="font-size: 25px;background-color: #c0a16b;color: white;font-weight: bold" align="center" id="balance"></td>
            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td style="color: #0000cc;font-size: 20px;font-weight: bold"  id="creditScore"></td>
            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td></td><td style="font-size: 20px;font-weight: bold;color: #0000cc" align="center" id="cashBalance"></td><td></td>
            <td style="font-size: 20px;font-weight: bold;color: crimson" align="center" id="otherBalance"></td>
            <td></td><td  style="font-size: 20px;font-weight: bold" align="center" id="avaBalance"></td>
            <td></td><td  style="font-size: 20px;font-weight: bold;color:crimson" align="center" id="crmsScore"></td>
            <td></td><td></td>
        </tr>
        <tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>
        <tr><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
            <td colspan="12" style="font-size: 14px;color:dimgrey">
                计算公式：&nbsp;&nbsp;当前可用积分 = 授信积分 + 广告基金账户 - 广告门头未报销 - CRMS待报销</td><td></td><td></td></tr>
    </table>


<!--/div--></div>
<div id="buttom_pannel" style="clear:both; width: 100%;height: 640px;">

        <t:datagrid name="tsActApplyVoDTList"
                    fitColumns="false" checkbox="true"
                    title="" actionUrl="ttAdvOutDoorReportController.do?getReportDetails"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">

            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="false" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatusStr" hidden="true" query="false" sortable="false" ></t:dgCol>

            <t:dgCol title="当前审批人" field="activityName" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="当前审批人" field="activityUser" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="重复制作" field="isRepeat" replace="是_1,否_0" hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="门头广告编号" field="adCode"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="广告公司手机" field="mobilephone" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="actType" hidden="false" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="报销材料" field="materialName" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="尺寸长（米）" field="mlength" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="尺寸宽（米）" field="mwidth" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="数量" field="nums" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="面积" field="mspace" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="单价" field="money" hidden="false"  sortable="false" ></t:dgCol>
            <%--<t:dgCol title="入账比例" field="discount" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="打折原因" field="disRemark" hidden="false"  sortable="false" ></t:dgCol>--%>
            <t:dgCol title="报销金额" field="realAmount" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" sortable="false" ></t:dgCol>



            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternative" hidden="false"  sortable="false" ></t:dgCol>
            <%--<t:dgCol title="广告发布地址3（上刊）" field="constructionAddress" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  hidden="false" query="true" sortable="false"></t:dgCol>--%>
            <t:dgCol title="经销商姓名" field="createName" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd" query="false" queryMode="group" sortable="false" ></t:dgCol>
            <t:dgCol title="提交日期" field="commitDate" hidden="false" formatter="yyyy-MM-dd" sortable="false" ></t:dgCol>
            <%--<t:dgCol title="审核日期" field="approveDate" hidden="false" formatter="yyyy-MM-dd"  sortable="false" ></t:dgCol>
            <t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>--%>

            <%--<t:dgCol title="省"       field="province"  hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="市"       field="city"  hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="区"       field="area"  hidden="false" query="true" sortable="false" ></t:dgCol>--%>
            <%--<t:dgCol title="现价" field="nowPrice" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="单价" field="singlePrice" hidden="false"  sortable="false" ></t:dgCol>

            <t:dgCol title="总价" field="sumPrice" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="折扣" field="discount" hidden="false"  sortable="false" ></t:dgCol>



            <t:dgCol title="折后总价" field="discountPrice" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="创建人" field="createName" hidden="false" query="true" sortable="false" ></t:dgCol>

            <t:dgCol title="创建日期" field="createDate" hidden="false" sortable="false" ></t:dgCol>--%>


            <%--<t:dgCol title="前审审核人" field="priorApprove" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="前审日期" field="priorApproveDate" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="前审状态" field="priorApproveStatus" hidden="false" replace="通过_1,驳回_2" sortable="false" ></t:dgCol>

            <t:dgCol title="后审审核人" field="approve" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="后审状态" field="approveStatus" replace="通过_1,驳回_2" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="可疑（系统）" field="suspiciousSystem" hidden="false" replace="否_0,是_1" sortable="false" ></t:dgCol>
            <t:dgCol title="原因（经销商）" field="customerReason" hidden="false"  sortable="false" ></t:dgCol>

            <t:dgCol title="可疑（人工）" field="suspicious" hidden="false" replace="否_0,是_1" sortable="false" ></t:dgCol>
            <t:dgCol title="原因（后审）" field="reason" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut" url="ttAdvOutDoorReportController.do?exportExcel"  funname="excelExport"></t:dgToolBar>--%>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频(新)" icon="icon-bcbgrid" url="tsPictureController.do?goPicList" funname="goPostPicture"></t:dgToolBar>

        </t:datagrid>
</div>
<script>
    function goPostPicture(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 1250, 700);
    }

    $(function () {
        getShowReferenceData();
    });

    function getShowReferenceData(){
        var url = "ttAdvOutDoorReportController.do?getJXSScoreInfo";
        var d = ajaxPost('',url);

        if(d.success){
            var objs = d.obj;
            for(var obj in objs){
//                alert(obj + '    ' + objs[obj]);
                $('#'+obj).html(objs[obj]);
            }
//            var html = returnShowReferenceDataHtml(d.obj);
//            $('#showReferenceData').html(html)
        }
        //$("a").removeClass();
        //$("a").addClass("easyui-linkbutton l-btn");
        $("a").each(function () {
            if($(this).hasClass("easyui-linkbutton l-btn l-btn-plain")){
                $(this).removeClass();
                $(this).addClass("easyui-linkbutton l-btn");
            }
        });
    }

    function returnShowReferenceDataHtml(objs) {
        var html = "";
        for(var obj in objs){
            if(html != ''){
                html += "\t|\t"
            }
            html += '<label>' + obj + ':<span style="font-size: 15px;color: red;">' + objs[obj] + '</span></label>';
        }
        return html;
    }
    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('每次只能选择一条查看');
            return;
        }
        update(title, url, id, 930, 500);
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoDTList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function returnNotNullValueValue(obj){
        if(typeof(obj) != 'undefined' && obj != '' && obj != null && obj != 'null' ){
            return obj;
        }
        return '';
    }
    function actCodeColor(value,rows,index){
        var rows = $("#tsActApplyVoDTList").datagrid("getRows");
        var discount = returnNotNullValueValue(rows[index].discount);
        if(discount!=''&&discount!='100%'){
            return "<u  style='color: blue;background: yellow'>"+value+"</u>"
        }else{
            return "<u  style='color: blue'>"+value+"</u>"

        }
    }

    function searchProc(rowIndex,rowData){

        var procinstId = rowData.procinstId;
        var url = "taTaskController.do?goInstanceHandleTabForm&isView=true&isReadFlag=false&processInstanceId="+procinstId;
        openwindow('查看',url,'',1200,800);
    }

</script>
<style type="text/css">
    .but{
        width: 120px ;
        min-height: 28px ;
        display: block ;
        background-color: #1b1b1b ;
        border: 1px solid #3762bc ;
        color: #fff ;
        padding: 9px 14px ;
        font-size: 20px ;
        line-height: normal ;
        border-radius: 5px ;
        margin: 0 ;
        font-family:微软雅黑 ;
        font-weight: bold;

    }
</style>