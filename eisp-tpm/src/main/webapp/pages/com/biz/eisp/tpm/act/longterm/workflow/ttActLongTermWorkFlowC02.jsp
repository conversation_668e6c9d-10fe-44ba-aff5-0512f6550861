<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center" style="padding:1px;">
		<t:datagrid name="ttProductActWorkFlowList" checkbox="false" fitColumns="true"  title="" pagination="false"
					actionUrl="ttActLongTermWorkFlowController.do?findTtActLongTermWorkFlowC02&flagKey=${flagKey}" idField="id" fit="true" queryMode="group">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol field="businessUnitName" title="归属事业部"  ></t:dgCol>
			<t:dgCol field="orgName" title="组织" ></t:dgCol>
			<t:dgCol field="yearMonth" title="年月"  ></t:dgCol>
			<t:dgCol field="finacialName" title="预算科目" ></t:dgCol>
			<t:dgCol field="applyAmount" title="本次申请金额" formatterjs="numExtend" ></t:dgCol>
			<t:dgCol field="applyRateStr" title="本次申请率"  ></t:dgCol>
			<t:dgCol field="accumulativeAmount" title="当月累计申请额" formatterjs="numExtend" sortable="false" ></t:dgCol>
			<t:dgCol field="accumulativeRateStr" title="当月累计申请率"></t:dgCol>
			<t:dgCol  field="yearBudget"  title="当月预算金额" formatterjs="numExtend" sortable="false" ></t:dgCol>

			<t:dgCol field="yearBudgetRate" hidden="true" title="年度预算率"></t:dgCol>
			<t:dgCol field="overBudget" title="超额预算"></t:dgCol>
			<t:dgCol field="overBudgetRate" title="超额预算率"></t:dgCol>
			<t:dgCol field="totalBudget" title="年度预算总额" formatterjs="numExtend"></t:dgCol>
			<t:dgCol field="accumulativeInfactAmount" title="年度累计实际金额" formatterjs="numExtend"></t:dgCol>
			<t:dgCol field="balanceAmount" title="可用余额" formatterjs="numExtend"></t:dgCol>
		</t:datagrid>
	</div>
</div>

