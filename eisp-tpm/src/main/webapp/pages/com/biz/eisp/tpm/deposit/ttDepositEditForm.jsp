<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <title>批量修改押金</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttDepositController.do?updateTtDeposition"
             refresh="true" beforeSubmit="load">
    <!-- id -->
    <input name="id" type="hidden" value="${depositValue.id}"/>

    <div class="form">
        <label class="Validform_label">产品系列: </label>
        <t:comboBox id="productCode" name="productCode" url="ttDepositController.do?findOrgCombox"
                    defaultVal="${depositValue.productCode}" required="true" width="150"></t:comboBox>
        <span style="color: red;">*</span>
    </div>


    <div class="form">
        <label class="Validform_label">协议金额: </label>
        <input onkeyup="clearNoNum(this)"  name="protocolAmount" id="protocolAmount" datatype="*" value="${depositValue.protocolAmount}" class="inputxt" onblur="load()"/>
        <span style="color: red;">*</span>
        <span>元</span>
    </div>

    <div class="form">
        <label class="Validform_label">原押金金额: </label>
        <input onkeyup="clearNoNum(this)"  value="${depositValue.depositAmount}" id="depositAmount" datatype="*" name="depositAmount" class="inputxt" onblur="load()"/>
        <span style="color: red;">*</span>
        <span>元</span>
        <span class="Validform_checktip" id="channelError"></span>

    </div>

    <div class="form">
        <label class="Validform_label">修改后押金金额: </label>
        <input  onkeyup="clearNoNum(this)"  name="newDepositionAmount" id="editdepositAmount" datatype="*" class="inputxt" onchange="load()"/>
        <span style="color: red;">*</span>
        <span>元</span>
    </div>
</t:formvalid>
</body>
</html>


<script type="text/javascript">

    //只能输入数字，或者保留两位小数
    function clearNoNum(obj){
        obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字而不是
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }

	$(document).ready(function () {
		$("#cbproductCode").combobox({
			onChange:function(newValue, oldValue){
				load();
			}
		});
	});
    function load() {
        var productCode = $("#productCode").val();
        var protocolAmount = $("#protocolAmount").val();
        var depositAmount = $("#depositAmount").val();
        if (productCode != '' && protocolAmount != '' && depositAmount != '') {
            var url = "ttDepositController.do?validateTtDeposition&productCode="+productCode+
                "&protocolAmount="+protocolAmount+"&depositAmount="+depositAmount;
            $.ajax({
                url: url,
                type: "post",
                success: function (data) {
                    var d = $.parseJSON(data);
                    if (!d.success) {
                        tip(d.msg);
                        $("#channelError").addClass("Validform_wrong");
                        $("#channelError").attr("title","");
                        return false;
                    }
                }
            });
        }
    }

</script>