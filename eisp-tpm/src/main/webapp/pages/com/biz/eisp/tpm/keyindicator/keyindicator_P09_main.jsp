<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="trainADBaseList" title="高铁普列广告核销列表"  actionUrl="ttTrainAuditWorkFlowController.do?findKeyindicatorBPM009Main&flagKey=${flagKey}"
                    idField="id" fit="true"  fitColumns="false"  queryMode="group" singleSelect="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="状态" field="bpmStatus" dictionary="bpm_status" hidden="true" width="80" ></t:dgCol>
            <t:dgCol title="标题" field="title"  width="200"></t:dgCol>
            <t:dgCol title="核销单号" field="auditCode"   width="200"></t:dgCol>
            <t:dgCol title="经办人" field="operator" hidden="true"  width="200" ></t:dgCol>
            <t:dgCol title="申请人" field="createName"  ></t:dgCol>
            <t:dgCol title="申请部门"  field="orgName" width="200" ></t:dgCol>
            <t:dgCol title="所属单位" field="unitName" width="200"></t:dgCol>
            <t:dgCol title="核报日期" field="createDate" formatter="yyyy-MM-dd"  width="200"></t:dgCol>
            <t:dgCol title="费用开始日期" field="costStartDate"  formatter="yyyy-MM-dd"  width="200"></t:dgCol>
            <t:dgCol title="费用截止日期" field="costEndDate"  formatter="yyyy-MM-dd"  width="200"></t:dgCol>
            <t:dgCol title="核销申请金额" field="auditAmount"  width="200"></t:dgCol>
            <t:dgCol title="瑕疵率" field="flawChance"  width="200"></t:dgCol>
            <t:dgCol title="实际核销金额" field="factAmount" hidden="true"  width="200"></t:dgCol>
            <t:dgCol title="备注" field="remark"  width="200" hidden="true"></t:dgCol>
            <t:dgToolBar title="创建" icon="icon-add" operationCode="add" url="" funname="createAudit"></t:dgToolBar>
            <t:dgToolBar title="发起核销" icon="icon-edit"  operationCode="update" url=""  funname="submit_act"></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
            <t:dgToolBar title="编辑" icon="icon-edit"  operationCode="update" url=""  funname="updateInfo"></t:dgToolBar>
            <t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteAudit"></t:dgToolBar>

        </t:datagrid>
    </div>
    <input type="text">
</div>