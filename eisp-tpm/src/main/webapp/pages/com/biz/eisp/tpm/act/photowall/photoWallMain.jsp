<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    img{ width:80px; height:80px}
</style>
<div id="system_photoWallList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="photoWallList" title="照片墙管理"  actionUrl="photoWallController.do?findPhotoWallList"
                    idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group" singleSelect="true">
            <t:dgCol title="id"  field="id"  hidden="true"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="活动编号"  field="actCode" query="true" hidden="false" queryMode="single"></t:dgCol>
            <t:dgCol title="活动名称"  field="actName" query="true" hidden="false" queryMode="single"></t:dgCol>
            <t:dgCol title="活动大类"  field="actTypeName" query="true" hidden="false" queryMode="single"></t:dgCol>
            <t:dgCol title="销售部"  field="saleOrg" query="true" hidden="false" queryMode="single"></t:dgCol>
            <t:dgCol title="客户编号"  field="customerCode" query="true" hidden="false" queryMode="single"></t:dgCol>
            <t:dgCol title="客户名称"  field="customerName" query="true" hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="门店编号"  field="terinalCode" query="true" hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="门店名称"  field="terminalName" query="true"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="活动细类"  field="costAccountName"  query="true"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="活动期间"  field="actPeriod"  query="false"  hidden="false"  queryMode="single"  ></t:dgCol>
            <t:dgCol title="年月"  field="yearMonth" query="true"  hidden="false"  queryMode="single" ></t:dgCol>
            <t:dgCol field="createDate" title="采集时间" query="true" hidden="true" formatter="yyyy-MM-dd" queryMode="group"></t:dgCol>
            <t:dgCol title="离线队列照片"  field="isLosePic" query="${flag}" dictionary="market_yn"  hidden="true"  queryMode="single" ></t:dgCol>
            <t:dgCol title="操作" field="opt"></t:dgCol>
            <t:dgFunOpt title="照片详情" funname="delPhoto(id,terinalCode,costAccountName)" />
            <t:dgToolBar title="导出" icon="icon-dataOut" url="photoWallController.do?exportXls" funname="toExcel"></t:dgToolBar>
            <t:dgToolBar title="选择每种类型第一张照片" icon="icon-ok" url="" funname="updateStatus"></t:dgToolBar>

        </t:datagrid>
    </div>
</div>
<script type="text/javascript">

    $(function(){
        //日期格式查询条件
        $('#photoWallListForm').find("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); }).attr("readonly",true);
    });

    //删除照片
    function delPhoto(id,terinalCode,costAccountName){
        safeShowDialog({
            content : "url:photoWallController.do?goPhotoHandlePage&id="+id+"&terinalCode="+terinalCode+"&costAccountName="+costAccountName,
            lock : true,
            title : "照片详情",
            width : 900,
            height : 650,
            left :'50%',
            cache : true,
            cancel:true,
            button:[{name:"保存",callback:function(){
                var flag = false;
                iframe = this.iframe.contentWindow;
                flag=iframe.submit();
                if(flag){
                    tip("保存成功","info");
                    $("#photoWallList").datagrid("reload");
                }
                return flag;
            }}],
            cancelVal:'关闭'
        });
    }

    function toExcel(){
        var title = '是否导出图片？';
         $.dialog.confirm(title, function(){
         excelExport("photoWallController.do?exportXls&flag=1","photoWallList");
         }, function(){
         excelExport("photoWallController.do?exportXls&flag=0","photoWallList");
         });
    }

    function updateStatus(){
        $.ajax({
            async : false,
            cache : false,
            type : 'POST',
            url : 'photoWallController.do?updateStatus',// 请求的action路径
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                    tip("操作成功","info");
                }else{
                    tip(d.msg,"error");
                }
            }
        });
    }

</script>