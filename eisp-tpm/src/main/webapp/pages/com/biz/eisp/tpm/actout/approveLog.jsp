<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    .classTable{border:1px solid #93b3d3;width:99%;height:auto;margin:0 auto;}
    .classTd{border-left:1px solid #93b3d3;border-bottom:1px solid #93b3d3;}
    .classTd_left{border-left:1px solid #93b3d3;}
    .classTd_right{border-right:1px solid #93b3d3;}
    .classTh{border-bottom:1px solid #93b3d3;word-break:break-all}
    td{padding-left: 5px;height:30px;}
    th{background-color: #ddd;width:143px;height:30px;}
    .classKeyIndicators{height:30px;line-height:30px;border-top:1px solid #ddd;border-bottom:1px solid #ddd;background-color:#ddebf7;text-align:center;font-weight:bold}
    ._display{display:none;}
    .bolded{
        color: #345;
        /* font-weight: bold; */
        padding-left: 5px;
    }
    .formtable {
        margin-bottom: 10px;width:100%;
    }
    .formtable td{
        height: 30px;
        border-collapse: collapse;
        background-color: #e8f0ff;
    }
    .formtable td.section {
        text-align: center;
        color: #345;
        font-size: 16px;
        font-weight: bold;
        height: 33px;
    }
    .formtable td.value{
        background-color: #fff;
    }
    .formtable td.approval {
        width: 20%;
        text-align: center;
    }
    .formtable td.approval div, .formtable td.approval hr {
        margin: 10px;
    }
    .data-table{
        line-height: 30px;
        text-align: center;
        width: 98%;
        margin: 10px 1%;
        background: #c8c8c8;
        border-collapse: collapse;
    }
    .data-table td {
        border: solid 1px #c8c8c8;
        border-collapse: collapse;
        border-spacing: 1px;
    }
    .data-table thead td{
        background: #ddd;
    }
    .data-table tbody td{
        background: #fff;
        word-break: break-all;
    }
    .centerdivBtn{
        text-align: center;
        line-height: 35px;
        margin: 10px 20px;
        background: #ccc;
        cursor: pointer;
    }
    .centerdivBtn:hover{
        background:#bbb;
    }
    .formtable .download td {
        padding: 0 10px;
    }
</style>

<body>
<div style="width:100%; border:10px solid white;"></div>
<table class="classTable"  cellpadding="0" cellspacing="0">
    <tr>
        <th class="classTd_right classTh" style="width:5%">序号</th>
        <th class="classTd_right classTh" style="width:12%">审批时间</th>
        <th class="classTd_right classTh" style="width:15%">审批人角色/岗位</th>
        <th class="classTd_right classTh" style="width:8%">审批人账号</th>
        <th class="classTd_right classTh" style="width:8%">审批人姓名</th>
        <th class="classTd_right classTh" style="width:6%">审批类型</th>
        <th class="classTd_right classTh" style="width:45%">审批意见</th>
    </tr>
    <c:forEach items="${log }" var="lg" varStatus="vs">
        <tr>
            <td style="text-align:center;" class="classTh">${vs.index+1 }</td>
            <td style="text-align:center;" class="classTh">${lg.createDate }</td>
            <td style="text-align:center;" class="cx classTh">${lg.roleName }</td>
            <td style="text-align:center;" class="cx classTh">${lg.account }</td>
            <td style="text-align:center;" class="cx classTh">${lg.name }</td>
            <td  <c:if test="${lg.type eq '驳回'}"> style="font-size: 18px;color:red;font-weight: bold !important;" </c:if> class="classTh">${lg.type }</td>
            <td <c:if test="${lg.content ne '同意' && lg.content ne '发起流程'}"> style="font-size: 18px;color:red;font-weight: bold !important;" </c:if>  class="classTh"><pre style="white-space: pre-wrap;">${lg.content }</pre></td>
        </tr>
    </c:forEach>
</table>
<div style="width:100%; border:8px solid white;"></div>

</body>

<script>

</script>