<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<script>
function getBusinessKey(){
    return '${businessKey}';
}

function getFlagKey(){
    return '${flagKey}';
}

function getTaskCode(){
    return '${taskCode}';
}

function getSrcType(){
    return '${srcType}';
}

/*===================共用=====================*/
/**
 * 通用ajax post 方法
 * @param json 传入参数json
 * @param url  调用url
 */
function ajaxPost(json,url){
    var json;
    $.ajax({
        url:url,
        data:json,
        dataType:'json',
        async:false,
        success:function(data){
            json = data;
        },
        error:function(error){
            json = {"sucess":false,"msg":"服务器错误!"};
        }
    });
    return json;
}

//检查是否为未定义或为空不为undefined
function checkIsNotUndefinedAndNull(value){
    return (typeof(value) != 'undefined' && $.trim(value).length > 0)
}
//检查是否为未定义或为空不为undefined和不为空值（'null'）
function checkIsNotUndefinedAndNullAndNullValue(value){
    return (checkIsNotUndefinedAndNull(value) && value != 'null');
}

//将obj转换为urlData
function changeDataToUrlData(data){
    var urlData = '';
    if(typeof(data) != 'undefined' && data != '' && data != null){
        for (var name in data){
            urlData += '&' + name + '=' + data[name];
        }
    }
    return urlData;
}

//--------------------高精度计算函数star------------------------------//
/**
 * 高精度加法函数
 */
function add(summand1, summand2){
    var power = getMaxPowerForTen(summand1, summand2);
    return (multiply(summand1, power) + multiply(summand2, power)) / power;
}
/*
 * 高精减法函数
 */
function subtract(minuend, subtrahend) {
    var power = getMaxPowerForTen(minuend, subtrahend);
    return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
}
/**
 * 高精乘法函数
 */
function multiply(multiplier, multiplicand) {
    var m=0;
    var s1 = multiplier.toString();
    var s2 = multiplicand.toString();

    try {
        m += s1.split(".")[1].length;
    }
    catch (e) {

    }
    try {
        m += s2.split(".")[1].length;
    }
    catch (e) {

    }
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
}
/**
 * 获取最大次幂
 */
function getMaxPowerForTen(arg1, arg2) {
    var r1 = 0;
    var r2 = 0;

    try {
        r1 = arg1.toString().split(".")[1].length;
    }
    catch(e) {
        r1 = 0;
    }
    try {
        r2 = arg2.toString().split(".")[1].length;
    }
    catch(e) {
        r2 = 0;
    }

    // 动态求出哪一个位数最多，再得出10的n次幂
    return Math.pow(10, Math.max(r1, r2));
}

//--------------------高精度计算函数end------------------------------//

</script>