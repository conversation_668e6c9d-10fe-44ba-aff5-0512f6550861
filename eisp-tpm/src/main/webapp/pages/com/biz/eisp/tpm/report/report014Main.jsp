<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report014List" fitColumns="false" title="市场投入费用明细"
                    pagination="false" autoLoadData="false" actionUrl="report014Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="事业部" field="xx"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="大区" field="xx"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="销售部" field="xx"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="区域" field="xx"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="城市" field="xx"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户" field="xx"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="本期销售收入" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="随车搭赠费用" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="免费订单费用" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="广宣费" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="礼品/赠品" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="主题促销" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="通路返利" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="铺货及调换货补贴" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="商超陈列" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="通路陈列" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="长促工资" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="消费者推广" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="本期实际支付费用合集" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="本期实际支付费用率" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="本年累计销售收入" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="本年累计市场投入费用" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="本年累计费用率" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="上月实际销售收入" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="上月实际支付费用" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="上月实际费用率" field="xx"  sortable="false" ></t:dgCol>
            <t:dgCol title="费用率环比差异" field="xx"  sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report014Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report014Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report014Listsearch() {
        var orgCode = $("#report014Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report014Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report014List").datagrid('options').queryParams;
        $("#report014Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report014List").datagrid({url:'report014Controller.do?findReportList'});
    }

</script>
