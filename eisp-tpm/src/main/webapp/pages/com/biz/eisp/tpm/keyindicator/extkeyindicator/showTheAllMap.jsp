<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <style type="text/css">
        body, html{width: 100%;height: 100%;margin:0;font-family:"微软雅黑";}
        #allmap{height:100%;width:100%;}
        #r-result{width:100%; font-size:14px;}
    </style>
    <script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=nCfhrj7MYj7jGxyR6GvpYYGkVWrmhivq"></script>
    <title>城市名定位</title>
</head>
<body>
<div id="allmap"></div>
</body>
</html>
<script type="text/javascript">
    // 百度地图API功能
    var map = new BMap.Map("allmap");
    map.centerAndZoom(new BMap.Point(116.331398,39.897445),11);
    map.enableScrollWheelZoom(true);

    $(function () {
        setTimeout("theLocation()",0)
    });

    //经度
    function getLongitude(){
        return '${tsActApplyVo.longitudeSg}'
    }

    //纬度
    function getLatitude(){
        return '${tsActApplyVo.latitudeSg}'
    }

    // 用经纬度设置地图中心点
    function theLocation(){
        debugger;
        var longitude = getLongitude();
        var latitudeSg = getLatitude();
        if(longitude != "" && latitudeSg != ""){
            map.clearOverlays();
            var new_point = new BMap.Point(longitude,latitudeSg);
            var marker = new BMap.Marker(new_point);  // 创建标注
            map.addOverlay(marker);              // 将标注添加到地图中
            map.panTo(new_point);
        }
    }
</script>
