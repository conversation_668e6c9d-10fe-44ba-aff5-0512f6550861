<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report017List" fitColumns="false" title="月度费用核销明细"
                    pagination="true" autoLoadData="false" actionUrl="report017Controller.do?findReportList" idField="id" fit="true" pageSize="30" >
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案明细编码" field="auditCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上账编号" field="verificationCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上账年月" field="yearMonth" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" width="100"  sortable="false" ></t:dgCol>
            <t:dgCol title="组织" field="orgName" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="成本中心编码" field="costCenterCode" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="成本中心" field="costCenterName" width="120"  sortable="false"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="预算科目编码" field="financialCode" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="预算科目" field="financialName" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类编码" field="accountCode" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类" field="accountName" width="150"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="应收款客户SAP编码" field="customerSapCode" width="120"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="应收款客户SAP名称" field="customerSapName" width="120"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="发票号码" field="invoiceNumber" width="100"  sortable="false" query="true"></t:dgCol>
            <%--<t:dgCol title="发票日期" field="invoiceDate" width="100" sortable="false" query="true"></t:dgCol>--%>
            <t:dgCol title="SAP财务科目编码" field="sapFinancialCode" width="100"  sortable="false" ></t:dgCol>
            <t:dgCol title="SAP财务科目" field="sapFinancialName" width="100"  sortable="false" ></t:dgCol>
            <t:dgCol title="本次费用额" field="currentCost" width="100"  sortable="false"></t:dgCol>
            <t:dgCol title="税码" field="taxCode" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="发票税额" field="invoiceAmount" width="100"  sortable="false"></t:dgCol>
            <t:dgCol title="进转税额" field="turnIntoAmount" width="100"  sortable="false"></t:dgCol>
            <t:dgCol title="本次结案上账额" field="upAccount" width="100"  sortable="false"></t:dgCol>
            <t:dgCol title="上账金额" field="accountingAmount" width="100"  sortable="false"></t:dgCol>
            <t:dgCol title="支付方式" field="paymentMode" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="SAP凭证号" field="sapVoucherNumber" width="100"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="入账会计" field="accountingName" width="100"  sortable="false" query="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report017Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report017Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:120px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report017Listsearch() {
        var orgCode = $("#report017Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report017Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report017List").datagrid('options').queryParams;
        $("#report017Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report017List").datagrid({url:'report017Controller.do?findReportList'});
    }

</script>
