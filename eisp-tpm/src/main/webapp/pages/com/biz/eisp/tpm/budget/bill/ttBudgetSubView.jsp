<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>预算削减</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<input type="hidden" value="${budgetVo.id}" id="id">
<t:formvalid formid="budgetIncrease" layout="div" dialog="true" action="ttBudgetBillSubController.do?saveBudgetSubAmount" refresh="true" beforeSubmit="checkAmount()">
		<input type="hidden" value="${billVo.adjustFlag}" id="addOrSub" name="adjustFlag">
		<div class="form">
			<label class="Validform_label">年度:</label>
			<input name="year" class="inputxt" style="width: 150px" value="${billVo.year}" disabled="disabled">
		</div>
		<div class="form">
			<label class="Validform_label">季度:</label>
			<input name="quarter" class="inputxt" style="width: 150px" value="${billVo.quarter}" disabled="disabled">
		</div>		
		<div class="form">
			<label class="Validform_label" name="custName">部门: </label> 
			<input class="inputxt" value="${billVo.orgName}" name="orgName" disabled="disabled"/>
			<input type="hidden" name="orgCode" value="${billVo.orgCode}">
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">费用类型: </label> 
			<input class="inputxt" value="${billVo.costTypeName}" name="costTypeName" disabled="disabled"/>
			<input type="hidden" name="costTypeCode" value="${billVo.costTypeCode}">
		</div>

		<div class="form">
			<label class="Validform_label" name="areaName">期初金额: </label> 
			<input name="periodAmount" class="inputxt" value="${billVo.periodAmount}" disabled="disabled"/>
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">可用余额: </label> 
			<input id="accountName" class="inputxt" value="${billVo.beforeAdjustBalance}" disabled="disabled"/>
		</div>
		
		<div class="form">
			<label class="Validform_label">削减金额</label> 
			<input type="text" class="inputxt" disabled="disabled" value="${billVo.adjustAmount}"/>
			<span style="color: red">*</span>
		</div>
		
		<div class="form">
			<label class="Validform_label" name="areaName">备注: </label> 
			<textarea style="width:150px;height:100px;resize:none;" disabled="disabled">${billVo.remark}</textarea>
		</div>		
</t:formvalid>
</body>
</html>