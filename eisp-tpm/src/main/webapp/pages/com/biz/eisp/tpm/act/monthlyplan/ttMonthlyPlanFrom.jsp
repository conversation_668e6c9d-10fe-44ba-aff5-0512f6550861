<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
	.searchbox{display: inline-table;}
</style>

<div id="ttActBusinessPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'north',title:'省区月度规划报表'" style="padding:1px;height:85px;" >
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div id="ttActTempleteListtb_r" class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							
						</span>
						<span style="float:right">
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search" style=" height: 40px;">
						<div>
							<span style="color: red;">*</span>
	            			<label>计划年份:</label>
	            			<input type="text" name="year" id="year" class="Wdate"
							onclick="WdatePicker({dateFmt:'yyyy',onpicked:function(){ changeListYearAndMonth(); } } )" readonly="readonly" />
	            			<span style="color: red;">*</span>
	            			<label>计划月份:</label>
	            			<input type="text" name="month" id="month" class="Wdate"
							onclick="WdatePicker({dateFmt:'MM',onpicked:function(){ changeListYearAndMonth(); } } )" readonly="readonly" />
		            		<span style="color: red;">*</span>
		            		<label>选择客户:</label>
		            		<input type="hidden" readonly="readonly" id="customerCode">
		            		<input type="hidden" readonly="readonly" id="orgCode">
		            		<input type="hidden" readonly="readonly" id="orgName">
		            		<input type="hidden" readonly="readonly" id="orgId" value="${orgId }">
					 		<!-- <input class="easyui-combobox" name="cbcustomer" id="cbcustomer" panelHeight="auto" style="width:150px;"
					 		data-options=" url:'ttActBusinessPlanController.do?findCustomerByPosId',multiple:false, editable:false,valueField:'value',textField:'text'" />
					  		<span style="color: red;">*</span> -->
		            		<!-- <a href="#" class="easyui-linkbutton" plain="true" name="chooseCustomer" icon="icon-add" onClick="chooseCustomer()">选择客户</a> -->
	            			<c:if test="${isUpdate == 1 }">
	            				<input type="text" readonly="readonly" id="customerName" disabled="disabled">
	            			</c:if>
	            			<c:if test="${isUpdate != 1 }">
	            				<b:search hiddenFields="customerName,customerCode,orgCode,orgName" inputTextName="customerName,customerCode,orgCode,orgName"
										  value="" dialogUrl="tmCommonMdmController.do?findCustomerBySearchList" url="tmCommonMdmController.do?goCustomerSearch"
										  inputTextId="customerName"  name="ttCustomerList" parameter="orgId" type="3" title="选择客户"
										  needQuery="false" berforeSearchFun="berforeCheckFun" fun="callBack"  ></b:search>
	            			</c:if>
							<label>备注:</label>
							<textarea title="remark" id="remark" name="remark" id="" cols="30" rows="3" style="position: absolute;" ></textarea>
							<label style="float: right;"><b>销售额汇总:</b>&nbsp;<span id="totalAmountSpanShow" style="color: red;">***</span>&nbsp;&nbsp;元</label>
							<div id = "remarkLengthShow" style="background-color: #f4f4f4; width: 65px;height: 20px;position: absolute;left: 847px;top: 6px;">
								<span id="lengthNum">0</span>/<span id="maxLengthNum">0</span>
							</div>
						</div>
					</div>
				</div>
				<div class="datagrid-view">
				</div>
			</div>
		</div>
	</div>
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttAddMonthlyPlanList" fitColumns="false" title="客户可购清单列表" queryMode = "group"
	     idField="id" pagination="false" autoLoadData="false" actionUrl="ttMonthlyPlanController.do?findTtAddMonthlyPlanList" onLoadSuccess="editRow">
	        <t:dgCol title="主键" sortable="false" hidden="true"  field="id" width="120" ></t:dgCol>
	        <%-- <t:dgCol title="组织编号" field="orgCode" hidden="true" width="120" ></t:dgCol>
	        <t:dgCol title="组织名称" field="orgName" hidden="true" width="120" ></t:dgCol> --%>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
	        <t:dgCol title="产品编号" field="productCode" sortable="false" width="100" ></t:dgCol>
	  		<t:dgCol title="产品名称" field="productName" sortable="false" width="200" ></t:dgCol>
			<t:dgCol title="原供价（元）" field="originalPrice" sortable="false" width="120" ></t:dgCol>
	  		<t:dgCol title="单价（元）" field="price" sortable="false" width="120" editor="{type:'numberbox',options:{min:0,precision:2}}" ></t:dgCol>
	  		<t:dgCol title="本品销量（EA）" field="planSales" sortable="false" editor="{type:'numberbox',options:{min:0}}" width="120"  ></t:dgCol>
	  		<t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false" editor="{type:'numberbox',options:{min:0}}" width="120" ></t:dgCol>
	  		<t:dgCol title="本品销售额（元）" field="totalPlanSales" sortable="false" editor="{type:'numberbox',options:{min:0,precision:2}}" width="120" ></t:dgCol>
			<t:dgCol title="价格差异" field="priceDifference" sortable="false" editor="{type:'numberbox',options:{precision:2}}" width="120" ></t:dgCol>
			<t:dgCol title="备注" field="remark" sortable="false" frozenColumn="true" hidden="true" width="200" ></t:dgCol>
		</t:datagrid>
	</div>
</div>

<script type="text/javascript">
//定义全局参数集 -- 提示产品信息使用用
var productNopriceObj = {};
$(document).ready(function(){
    $('div[class="datagrid-toolbar-search"] input[name="customerName"]').attr("readonly","readonly");
	if('1' == '${isUpdate}' ){
		var customerCode = '${ttIncomePlanVo.customerCode}';
		var customerName = '${ttIncomePlanVo.customerName}';
		var orgCode = '${ttIncomePlanVo.orgCode}';
		var orgName = '${ttIncomePlanVo.orgName}';
		var year = '${ttIncomePlanVo.year}';
		var month = '${ttIncomePlanVo.month}';
		if(customerCode != '' && customerName != '' && orgCode != '' && orgName != '' && year != '' && month != ''){
			$('#customerCode').val(customerCode);
			$('#customerName').val(customerName);
			$('#orgCode').val(orgCode);
			$('#orgName').val(orgName);
			$('#year').val(year);
			$('#month').val(month);
			$('#year').attr("disabled",true);
			$('#month').attr("disabled",true);
			//查询一次
			setTimeout('callBack("")',200);
		}else{
			tip("数据传递失败");
		}
	}
    initRemarkEvent();
});

function initProductNopriceObj(){
    productNopriceObj.flag = false;
    productNopriceObj.errorMsg = '';
}

function returnDoingNum(){
    return '${ttIncomePlanVo.doingNum}'
}

function initRemarkEvent(){
    $('#remarkLengthShow').hide();
    var obj = $('#remark');
    obj.focus(function(){
        $('#maxLengthNum').html(returnMaxLengthNum());
		$('#remarkLengthShow').show();
	});
    obj.blur(function(){
        checkRemarkIsOk(this);
        $('#remarkLengthShow').hide();
	});
    obj.bind('input propertychange',function(){
        checkRemarkIsOk(this)
	});
}
function returnMaxLengthNum(){
    return '${maxLength}';
}

function checkRemarkIsOk(obj){
    var thisObj = $(obj);
    var length = thisObj.val().length;
    $('#lengthNum').html(length);
    var maxLength = returnMaxLengthNum();
    if(length > maxLength){
        $('#remarkLengthShow').css("color","red");
        thisObj.addClass("inputxt Validform_error");
    }else{
        $('#remarkLengthShow').css("color","");
        thisObj.removeClass("inputxt Validform_error");
    }
}

function changeListYearAndMonth(){
    var year  = $("#year").val();
    if(year == null || year == "" || $.trim(year).length == 0) {
        return ;
    }
    var month= $("#month").val();
    if( month == null || month == "" || $.trim(month).length == 0 ) {
        return ;
    }
    var gridObj = $('#ttAddMonthlyPlanList');
    var rowDatas = gridObj.datagrid("getRows");
    if(rowDatas.length <= 0){
		return ;
	}
    getSafeJq().dialog.confirm("改变年月会重新调取数据覆盖当前数据是否继续?", function(r) {
        callBack("");
    });
//	reloadDatagridData(rowDatas);
//    changeDataGridearMonth(gridObj,rowDatas,year,month);
}
function reloadDatagridData(){

}
//改变datagrid--yearmonth的值
function changeDataGridearMonth(gridObj,rowDatas,year,month){
    var yearMonth = year + "-" + month;
    for (var i = 0; i <=  rowDatas.length ; i ++ ) {
        var data = rowDatas[i];
        data.yearMonth = yearMonth;
        gridObj.datagrid('updateRow',{index: i,row: data});
    }
}

function berforeCheckFun(value,name){
	var year  = $("#year").val();
	if(year == null || year == "" || $.trim(year).length == 0) {
		tip('请先选年');
		return false;
	}
	var month= $("#month").val();
	if( month == null || month == "" || $.trim(month).length == 0 ) {
		tip('请先选月');
		return false;
	}
	return true;
}

function callBack(data){
	var customerCode = $('#customerCode').val();
	var customerName = $('#customerName').val();
	var year = $('#year').val();
	var month = $('#month').val();
	var orgCode = $('#orgCode').val();
	var orgName = $('#orgName').val();
	var thisData = {
        customerCode : customerCode
	}
    //获取错误信息
	setTimeout(function(){getErrorMsg(thisData);},10);

	var queryParams = $('#ttAddMonthlyPlanList').datagrid('options').queryParams;
    queryParams.customerCode = customerCode;
    queryParams.customerName = customerName;
    queryParams.year = year;
    queryParams.month = month;
    queryParams.orgCode = orgCode;
    queryParams.orgName = orgName;
	$('#ttAddMonthlyPlanList').datagrid({url:"ttMonthlyPlanController.do?findTtAddMonthlyPlanList"});
}

function getErrorMsg(thisData){
    $.ajax({
        async : true,
        cache : false,
        data : thisData,
        type : 'POST',
        url : "ttMonthlyPlanController.do?getErrorMsg",// 请求的action路径
        error : function() {// 请求失败处理函数
        },
        success : function(data) {
            var d = $.parseJSON(data);
            if (d.success) {
//                contxtTip(d.msg);
                productNopriceObj.errorMsg = d.msg;
            }
            productNopriceObj.flag = true;
        }
    });
}
function contxtTip(msg){
    var myOptions = {
        content : msg,
        lock : true,
        width : 500,
        height : 200,
        title : "错误提示",
        opacity : 0.3,
        cache : true,
        async: false,
        cancelVal : '确定',
        cancel : true
    };
    safeShowDialog(myOptions);
    initProductNopriceObj();
}

function editRow(rowData) {
    strWritTotalAmountSpanShow("***");
	var rows = rowData.rows;
	if(!(rows != null && rows.length > 0)){
		return ;
	}
    setRemark(rows[0].remark);
	var gridObj = $("#ttAddMonthlyPlanList");
	$.each(rows,function (index,row){
        gridObj.datagrid('endEdit', index);
        gridObj.datagrid('beginEdit', index);
		var originalPrice = row.originalPrice;
		var priceEd = getEditorObj(gridObj,index,"price")
		var planSalesED = getEditorObj(gridObj,index,"planSales");
        var premiumQuantityED = getEditorObj(gridObj,index,"premiumQuantity");
		var totalPlanSalesED = getEditorObj(gridObj,index,"totalPlanSales");
        var priceDifferenceED = getEditorObj(gridObj,index,"priceDifference");
        changeEditorIsDoNotEdit(totalPlanSalesED);
        changeEditorIsDoNotEdit(priceDifferenceED);
        //是否继续
        var isContinue = true;
		//检查有无价格
        if(originalPrice == '' || originalPrice == null){
            changeEditorIsDoNotEdit(priceEd);
            changeEditorIsDoNotEdit(planSalesED);
            changeEditorIsDoNotEdit(premiumQuantityED);
            isContinue = false;
		}
		if(isContinue){
            //单价
            priceEd.target.bind('blur', function () {
                var price = returnNotNullNumber(getEditorValue(gridObj,index,"price"));
                var planSales = returnNotNullNumber(getEditorValue(gridObj,index,"planSales"));
                //重新计算销售额 = 单价 * 计划销量
                var total = (parseFloat(price) * parseFloat(planSales)).toFixed(2);
                $(totalPlanSalesED.target).numberbox('setValue',total);

                //重新计算价格差异 = 单价 - 原供价
                var originalPriceTemp = returnNotNullNumber(originalPrice);
                var priceDifference = subtract(price,originalPriceTemp).toFixed(2);
                $(priceDifferenceED.target).numberbox('setValue',priceDifference);
                writTotalAmountSpanShow();
            });
            //销量
            planSalesED.target.bind('blur', function () {
                var price = returnNotNullNumber(getEditorValue(gridObj,index,"price"));
                var planSales = returnNotNullNumber(getEditorValue(gridObj,index,"planSales"));
                //重新计算销售额 = 单价 * 计划销量
                var total = (parseFloat(price) * parseFloat(planSales)).toFixed(2);
                $(totalPlanSalesED.target).numberbox('setValue',total);
                writTotalAmountSpanShow();
            });
		}
	});
    writTotalAmountSpanShow();
    //开始展示错误信息
    setTimeout("starShowProductErrorMsg()",100);
}
function starShowProductErrorMsg(){
	if(productNopriceObj.flag == true){
	    var errorMsg = productNopriceObj.errorMsg;
		if(checkIsNotUndefinedAndNullAndNullValue(errorMsg)){
			contxtTip(errorMsg);
		}
	}else{//?????
	    setTimeout("starShowProductErrorMsg()",500);
	}
}

//写入备注
function setRemark(remark){
	$('#remark').val(remark);
}

function getRemark(){
    return $('#remark').val();
}

//汇总金额
function writTotalAmountSpanShow(){
    var obj = $('#ttAddMonthlyPlanList');
    var rowDatas = obj.datagrid("getRows");
    var totalAmountNum = 0;
	$.each(rowDatas,function (index,row) {
        var totalAmount = getEditorValue(obj,index,'totalPlanSales');
        totalAmountNum = add(totalAmountNum,totalAmount);
    });
    strWritTotalAmountSpanShow(totalAmountNum);
}

function strWritTotalAmountSpanShow(value){
    $('#totalAmountSpanShow').html(value);
}


function returnNotNullNumber(value){
	return value == '' ? 0 : value;
}
function changeEditorIsDoNotEdit(ed){
    ed.target.attr('readonly', 'readonly').attr('disabled',true).attr('border','0').css("border-style","none");
}
function binding(ed){

}
function addKeyUpEvent(){
	 $('input[class="datagrid-editable-input"]').each(function(){
	 	 $(this).keyup(function(){
	 		$(this).val($(this).val().replace(/[^\d^.]/g,''));
		 });
	 	 $(this).blur(function(){
	 		if ($(this).val() == '') {
	 			return ;
			}
	 		$(this).val(Number(changeNum($(this).val())));
	 	 });
	 	 $(this).attr("maxlength",'9');
	});
}
//处理两个小数点
function changeNum(num){
	  var amountTemp = 0;
	  var array = num.split(".");
	  if (array.length > 1) {
		  if (array[1].length == 1) {
			  amountTemp = parseFloat(num).toFixed(1)
		  }else if(array[1].length > 1){
			  amountTemp = parseFloat(num).toFixed(2)
		  }
	  }else{
		  amountTemp = parseFloat(num);
	  }
	  return amountTemp;
}

function saveMonthlyPlanData() {
	var obj = $('#ttAddMonthlyPlanList');
	var rows = obj.datagrid('getRows');
	var list = [];
	var falg = false;
	var customerCode = $('#customerCode').val();
	if (!checkIsOk(customerCode,"客户")) {
		return ;
	}
	var customerName = $('#customerName').val();
	if (!checkIsOk(customerName,"客户")) {
		return ;
	}
	var year = $('#year').val();
	if (!checkIsOk(year,"年份")) {
		return ;
	}
	var month = $('#month').val();
	if (!checkIsOk(month,"月份")) {
		return ;
	}
	var orgCode = $('#orgCode').val();
	if ($.trim(orgCode ).length == 0) {
	    tip("客户组织为空，请联系管理员");
		return ;
	}
	var orgName = $('#orgName').val();
	if ($.trim(orgName ).length == 0) {
        tip("客户组织为空，请联系管理员");
		return ;
	}

	var remark = getRemark();
	var maxLength = returnMaxLengthNum();
    if (remark.length > maxLength) {
        tip("备注字数(包含标点、空格、换行等所有字符)不能超过" + maxLength );
        return ;
    }

	var falgTemp = true;
	for (var i = 0; i < rows.length; i++) {
		var index = obj.datagrid('getRowIndex', rows[i]);
        // 获取输入框编辑器的值---销售信息
		var priceTemp = getEditorValue(obj,index,'price');
        var planSalesTemp = getEditorValue(obj,index,'planSales');
        var premiumQuantity = getEditorValue(obj,index,'premiumQuantity');
//		obj.datagrid('endEdit', index);
		if (planSalesTemp != '' || premiumQuantity != '') {
			var pnum = Number(index) + 1;
			var numStr = "第" + pnum + "行，";
			if (!checkIsOk(priceTemp,numStr + "单价",'')
				|| !checkIsOk(planSalesTemp,numStr + "计划销量",'')) {
				falgTemp = false;
			}
			if (falgTemp) {
				var objTemp = {};
				objTemp.pnum = pnum;
				objTemp.year = year;
				objTemp.month = month;
				objTemp.customerCode = customerCode;
				objTemp.customerName = customerName;
				objTemp.orgCode = orgCode;
				objTemp.orgName = orgName;
				objTemp.yearMonth = rows[i].yearMonth;
				objTemp.productCode = rows[i].productCode;
				objTemp.productName = rows[i].productName;
				objTemp.originalPrice = rows[i].originalPrice;
                objTemp.price = priceTemp;
				objTemp.planSales = planSalesTemp;
				objTemp.premiumQuantity = premiumQuantity != '' ? premiumQuantity : 0 ;
				objTemp.totalPlanSales = getEditorValue(obj,index,'totalPlanSales');
				list.push(objTemp);
				falg = true;
			}
		}
//		obj.datagrid('beginEdit', index);
		if (!falgTemp) {
			return ;
		}
	}
	if (!falg) {
		tip("至少填写一条计划销量、赠品量不为空的数据");
		return;
	}
	startSaveAmount(list,obj);
}
// 获取输入框编辑器对象
function getEditorObj(gridObj,index,clomnName){
    // 获取输入框编辑器对象
   return gridObj.datagrid('getEditor', {index:index,field:clomnName});
}
//返回数据
function returnEditorValueByEditorObj(ed){
    if(checkIsNotUndefinedAndNull(ed)){
        return $(ed.target).val();
    }else{
        return '';
    }
}
//获取编辑器的值
function getEditorValue(gridObj,index,clomnName){
	// 获取输入框编辑器的值
    var ed = getEditorObj(gridObj,index,clomnName);
    return returnEditorValueByEditorObj(ed);
}
//检查是否为未定义或为空不为undefined
function checkIsNotUndefinedAndNull(value){
    return (typeof(value) != 'undefined' && $.trim(value).length > 0)
}
//检查是否为未定义或为空不为undefined和不为空值（'null'）
function checkIsNotUndefinedAndNullAndNullValue(value){
    return (checkIsNotUndefinedAndNull(value) && value != 'null');
}
//检查是否合格--数字类型--空不管
function checkIsOk(objValue,message,reg){
	if($.trim(objValue ).length != 0){
        if (typeof(reg) != 'undefined' && reg != '' && !reg.test(objValue)) {
			tip(message + "格式不正确");
			return false;
		}
    }else{
        tip(message + "不能为空");
        return false;
    }
	return true;
}

function startSaveAmount(list,obj){
	var year = $('#year').val();
	var month = $('#month').val();
 	var thisdata = {
		info : JSON.stringify({monthlyPlanList:list}),
        orgCode : $('#orgCode').val(),
        orgName : $('#orgName').val(),
		customerCode : $('#customerCode').val(),
	 	customerName : $('#customerName').val(),
        remark : getRemark(),
	 	year : year,
		month : month,
		doingNum : returnDoingNum()
	}
	url = "ttMonthlyPlanController.do?saveMonthlyPlan";
    ajaxRequest({tips:'确定保存吗 ?',url:url,params:thisdata,callbackfun:function(data){
    	var d = $.parseJSON(data);
        if(d.success == true) {
           	W.setVlaue('year',year);
            W.setVlaue('month',month);
        	W.ttMonthlyPlanGatherListSearchFunction();
        	W.tip(d.msg);
        	window.top.$.messager.progress('close');
        	close();
        } else {
        	tip(d.msg);
        }
    }});
}
//--------------------高精度计算函数star------------------------------//
/**
 * 高精度加法函数
 */
function add(summand1, summand2){
    var power = getMaxPowerForTen(summand1, summand2);
    return (multiply(summand1, power) + multiply(summand2, power)) / power;
}
/*
 * 高精减法函数
 */
function subtract(minuend, subtrahend) {
    var power = getMaxPowerForTen(minuend, subtrahend);
    return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
}
/**
 * 高精乘法函数
 */
function multiply(multiplier, multiplicand) {
    var m=0;
    var s1 = multiplier.toString();
    var s2 = multiplicand.toString();

    try {
        m += s1.split(".")[1].length;
    }
    catch (e) {

    }
    try {
        m += s2.split(".")[1].length;
    }
    catch (e) {

    }
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
}
/**
 * 获取最大次幂
 */
function getMaxPowerForTen(arg1, arg2) {
    var r1 = 0;
    var r2 = 0;

    try {
        r1 = arg1.toString().split(".")[1].length;
    }
    catch(e) {
        r1 = 0;
    }
    try {
        r2 = arg2.toString().split(".")[1].length;
    }
    catch(e) {
        r2 = 0;
    }

    // 动态求出哪一个位数最多，再得出10的n次幂
    return Math.pow(10, Math.max(r1, r2));
}

//--------------------高精度计算函数end------------------------------//
function close(){
	frameElement.api.close();
}
</script>