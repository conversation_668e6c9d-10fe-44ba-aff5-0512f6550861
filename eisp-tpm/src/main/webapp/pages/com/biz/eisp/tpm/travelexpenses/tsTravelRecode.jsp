<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tsTravelList"
                    fitColumns="false" checkbox="true"
                    title="出行记录" actionUrl="tsTravelRecordController.do?tsTravelRecodeList"
                    idField="id" fit="true" queryMode="group" singleSelect="true" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="申请人账号" field="userName" hidden="true"></t:dgCol>
            <t:dgCol title="申请人" field="applyName" hidden="false" query="true"></t:dgCol>
            <t:dgCol title="申请人岗位" field="positionName" hidden="false" query="true"></t:dgCol>
            <t:dgCol title="申请日期" field="applyDate" hidden="false"></t:dgCol>
            <t:dgCol title="出行目的地" field="travelDestination" hidden="false" query="true"></t:dgCol>
            <t:dgCol title="交通工具" field="travelTransport" hidden="false"></t:dgCol>
            <t:dgCol title="是否住宿" field="isLive" hidden="false" dictionary="yesorno"></t:dgCol>
            <t:dgCol title="开始时间" field="startDate" hidden="false"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" hidden="false"></t:dgCol>
            <t:dgCol title="出行原因" field="travelReason" hidden="false"></t:dgCol>
            <t:dgCol title="审批人姓名" field="auditUserName"></t:dgCol>
            <t:dgCol title="审批意见" field="auditComment"></t:dgCol>

            <t:dgCol title="日期" field="queryDate" query="true" queryMode="group" formatter="yyyy-MM-dd"
                     hidden="true"></t:dgCol>
           <%-- <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut" url="ttActOutUploadController.do?exportExcel"  funname="excelExport"></t:dgToolBar>--%>
            <t:dgToolBar title="查看" icon="icon-log" onclick="findDetailById()"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>

<script>
    function findDetailById() {
        var rows=$("#tsTravelList").datagrid('getSelections');

        if (rows.length==null||rows.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (rows.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "tsTravelRecordController.do?goTsTravelDetail" +
            "&id="+rows[0].id+"&userName="+rows[0].userName+"&startDate="+rows[0].startDate+"&endDate="+rows[0].endDate;
        $.dialog({
            title: "出行明细",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "550",
            zIndex: 10000,
            parent: windowapi,
            cache : true,
            max: false,
            min: false,
            cancelVal: '关闭',
            resize: true
        });
    }
</script>
