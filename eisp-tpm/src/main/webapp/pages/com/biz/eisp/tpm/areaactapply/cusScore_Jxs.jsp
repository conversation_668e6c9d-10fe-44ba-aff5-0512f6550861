<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <span style="font-size: 20px;color: red">注意：1、经销商审批通过一个门头，可做门头数量就自动随之减少一个。<br/>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、每月根据经销商下单情况动态调整（当月27日 ~ 次月5日）
     <br/>&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3、可用积分 = 授信积分 + SAP基金余额 - SAP未报销 - CRMS待报销 - 本系统未报销
    </span><br/><br/>
    <div region="center" style="padding: 1px;">
        <t:datagrid name="dealerScoreList" title="经销商可做天能、汇源门头查询（不含汽车门头）"  actionUrl="ttAreaActApplyController.do?findCusScoreList_Sub&adsType=1"
                    checkbox="false"  fit="true" idField="id"  fitColumns="false" pagination="true" queryMode="group"
                    singleSelect="false" >
             <span style="font-size: 20px;color: red">注意：1、经销商审批通过一个门头，可做门头数量就自动随之减少一个。<br/>&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、每月根据经销商下单情况动态调整可做门头数（当月27日 ~ 次月5日）
             <br/>&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3、可用积分 = 授信积分 + SAP基金余额 - SAP未报销 - CRMS待报销 - 本系统未报销 - 经销商未审批
             </span><br/><br/>
            <t:dgCol title="经销商名称" field="dealerName" query="false"  ></t:dgCol>
            <t:dgCol title="经销商编码" field="dealerCode" query="false"    ></t:dgCol>
            <t:dgCol title="本批次未报销(SAP)" field="wbxWrz"   width="120" ></t:dgCol>
            <t:dgCol title="经销商未审批" field="wEblanStr" width="130" sortable="false" ></t:dgCol>
            <t:dgCol title="可用积分" field="avaIncWAuit" width="115" sortable="false" ></t:dgCol>
            <%--t:dgCol title="可用积分" field="available"   width="80" ></t:dgCol--%>
            <t:dgCol title="(1) 授信积分" field="applyAmount"   width="85" ></t:dgCol>
            <t:dgCol title="(1) 更新日期" field="crDate"   width="85" ></t:dgCol>
            <t:dgCol title="(1) 截止日期" field="enDate"   width="85" ></t:dgCol>
            <t:dgCol title="(2) SAP基金余额" field="fundBalance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(3) SAP未报销" field="otherBalance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(4) CRMS待报销" field="crmsBlance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(5) 本系统未报销" field="eblance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="可做门头数(最少)" field="doorNumMin2" width="115" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="可做门头数(最大)" field="doorNumMax2" width="115" sortable="false" align="center" ></t:dgCol>

        </t:datagrid>

    </div>
</div>
<script type="text/javascript">

</script>
