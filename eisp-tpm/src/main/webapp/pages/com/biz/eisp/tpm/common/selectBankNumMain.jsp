<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<c:set var="bankTransfer" value="10"></c:set>
<c:set var="employeeMat" value="20"></c:set>
<c:set var="strikingLoan" value="50"></c:set>

<div class="easyui-layout" fit="true">
	<div region="north" style="height: 20px;width: 200px;">
		<c:if test="${paymentCode==bankTransfer }">
			<input type="radio" name="paymentCode"  value="1" onchange="changePay();" checked/>客户
			<input type="radio" name="paymentCode"  value="2" onchange="changePay();"/>供应商
		</c:if>
		<c:if test="${paymentCode==employeeMat }">
				<input type="radio" name="paymentCode"  value="3" onchange="changePay();" checked/>内部员工
				<input type="radio" name="paymentCode"  value="4" onchange="changePay();" />外部员工
		</c:if>		
		<c:if test="${paymentCode==strikingLoan }">
			<input type="radio" name="paymentCode"  value="1" onchange="changePay();" checked/>客户
			<input type="radio" name="paymentCode"  value="2" onchange="changePay();"/>供应商
			<input type="radio" name="paymentCode"  value="3" onchange="changePay();"/>内部员工
			<input type="radio" name="paymentCode"  value="4" onchange="changePay();"/>外部员工
		</c:if>
	</div>
	<div region="center" style="padding: 1px;">
		<t:datagrid name="bankList" actionUrl="ttBankNumController.do?findBankNumInfo" idField="bankn" fit="true" fitColumns="true"  queryMode="group">
		    <t:dgCol field="bkont" hidden="true" title="银行控制码"></t:dgCol>
		    <t:dgCol field="bankl" title="银行编号" hidden="true"></t:dgCol>
 		    <t:dgCol field="kionh" title="账户持有人" width="120" query="true" ></t:dgCol> 
  		    <t:dgCol field="banka" title="银行名称" width="120" query="true"></t:dgCol>
		    <t:dgCol field="bankn" title="银行账号" width="120"></t:dgCol>
		    <t:dgCol field="banks" title="银行国家" width="120"></t:dgCol>
		    <t:dgCol field="bvtyp" title="银行标识" width="120"></t:dgCol>
		</t:datagrid>
	</div>	
</div>

<script type="text/javascript">	
	$(function(){	  
		 //改变支付对象,客户/经销商
		 changePay() 
	});	
	
	//改变支付对象,客户/经销商 函数
	function changePay() {
	    var item = $("input[name='paymentCode']:checked").val();
	    var ownerCode = '${ownerCode}';
	    var userName = '${userName}';
	    $('#bankList').datagrid({
	    	queryParams: {
	    		type: Number(item),
	    		ownerCode : ownerCode,
	    		userName : userName
	    	}
	    });
	}
</script>

