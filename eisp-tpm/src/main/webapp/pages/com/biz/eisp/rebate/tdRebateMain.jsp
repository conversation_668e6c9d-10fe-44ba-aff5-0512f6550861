<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="dtRebateList" checkbox="true" fitColumns="false" title="返利管理"
                    actionUrl="rebateController.do?findTdRebateList" idField="id" fit="true" queryMode="group" pageSize="30">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" ></t:dgCol>
            <t:dgCol title="返利名称" field="rebName" query="true" queryMode="single" ></t:dgCol>
            <t:dgCol title="返利描述" field="rebNote" queryMode="single"  width="70"></t:dgCol>
            <t:dgCol title="返利周期" field="timeType" queryMode="single"  dictionary="dic_time_type" query="true" ></t:dgCol>
            <t:dgCol title="返利组" field="rebGroup"  queryMode="single" query="true" dictionary="rebate_group"  ></t:dgCol>
            <t:dgCol title="支付方式" field="payType"  queryMode="single" dictionary="pay_type" width="70"></t:dgCol>
            <t:dgCol title="返利开始时间" field="rebStart" queryMode="group" query="true" formatter="yyyy-MM-dd"   ></t:dgCol>
            <t:dgCol title="返利结束时间" field="rebEnd" queryMode="single" query="false" formatter="yyyy-MM-dd"   ></t:dgCol>
            <t:dgToolBar title="新增" operationCode="add" icon="icon-add" url="rebateController.do?goRebateForm" funname="add" height="450" width="900"></t:dgToolBar>
            <t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="rebateController.do?goRebateForm" funname="update" height="450" width="900"></t:dgToolBar>
            <t:dgToolBar title="查看" operationCode="search" icon="icon-search" url="rebateController.do?goOrderForm&load=detail" funname="detail" height="450" width="900"></t:dgToolBar>
            <t:dgToolBar title="停用" operationCode="stop" icon="icon-stop" url="rebateController.do?goOrderForm&load=detail" funname="detail" height="450" width="600"></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="cgDynamicController.do?goModifyDynamicHead&action=add" funname="add" height="450" width="1200"></t:dgToolBar>
        </t:datagrid>

    </div>
</div>


