<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>期初费用预算</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttBudgetCustomerController.do?saveBudgetCustomerAmount" 
refresh="true" beforeSubmit="validateAmount()" callback="refresh">
	<input type="hidden" name="id" value="${vo.id}">
	
	<input id="max" value="${maxAmount}" type="hidden"/>
	
	<div class="form">
		<label class="Validform_label">期初金额: </label>
		<input name="amount" id="amount" datatype="/^(([1-9]\d*)|\d)(\.\d{1,2})?$/" errormsg="只能输入数字，带两位小数" 
			   class="inputxt" value="${vo.amount}" onblur="validateAmount()"/>
		<span style="color: red;">*</span>
		<span class="Validform_checktip" id="error">可编辑最大金额:${maxAmount},最小金额:0</span>
	</div>

</t:formvalid>
</body>
</html>
<script type="text/javascript">
function validateAmount(){
	var max = $("#max").val();
	var amount = $("#amount").val();
	if(amount == ""){
		return false;
	}
	if(parseFloat(amount) > parseFloat(max)){
		tip("编辑金额不能大于最大金额");
		$("#error").addClass("Validform_wrong");
		$("#amount").val("");
		return false;
	}
}
</script>