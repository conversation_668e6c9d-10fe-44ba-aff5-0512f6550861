<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid checkbox="true" singleSelect="false" name="ttDepositMainList" title="奶粉押金管理" actionUrl="ttDepositController.do?findTtDepositMainList"
                    idField="id" fit="true" fitColumns="false" pagination="true" queryMode="group">
            <%--<t:dgCol field="finacialYear" title="预算财年" query="true"></t:dgCol>--%>
            <t:dgCol field="customerName" title="客户名称" query="true"></t:dgCol>
            <t:dgCol field="productName" title="产品系列" query="true"></t:dgCol>
            <t:dgCol field="enableStatus" dictionary="enable_status"  query="true" title="启动状态"></t:dgCol>
            <t:dgCol field="beginDate"  title="任务开始时间" query="true" queryMode="group"  formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol field="endDate" title="任务结束时间"></t:dgCol>
            <t:dgCol field="protocolAmount" title="协议金额(元)"></t:dgCol>
            <t:dgCol field="depositAmount" title="押金金额(元)"></t:dgCol>
            <t:dgCol field="paymentCode" title="支付方式" dictionary="dms_payment_Code" ></t:dgCol>
            <t:dgCol field="productRebateStandard" title="产品返利标准(%)"></t:dgCol>
            <t:dgCol field="createName" title="创建人"></t:dgCol>
            <t:dgCol title="创建时间"  field="createDate" formatter="yyyy-MM-dd HH:mm:ss"  width="150"></t:dgCol>
            <t:dgCol field="updateName" title="最近更新人"></t:dgCol>
            <t:dgCol field="updateDate" formatter="yyyy-MM-dd HH:mm:ss" title="最近更新时间"></t:dgCol>
            
            <t:dgToolBar operationCode="add" title="录入押金" height="500" width="800" icon="icon-add" url="ttDepositController.do?goTtDepositForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑"  height="500" width="800" icon="icon-edit" url="ttDepositController.do?goTtDepositForm"  funname="update"></t:dgToolBar>
            <t:dgToolBar operationCode="bulkEditing" title="批量修改押金" icon="icon-edit" url="" funname="updateAll"></t:dgToolBar>
            <t:dgToolBar operationCode="view" title="查看"  height="500" width="800" icon="icon-look" url="ttDepositController.do?goTtDepositViewForm" funname="detail" ></t:dgToolBar>
            <t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="ttDepositController.do?deleteTtDepositMain"  funname="deleteALLSelect"></t:dgToolBar>
            <t:dgToolBar operationCode="start" title="启用" icon="icon-start"    onclick="startOrStop(0)"></t:dgToolBar>
            <t:dgToolBar operationCode="stop" title="停用" icon="icon-stop" onclick="startOrStop(1)"></t:dgToolBar>
            <t:dgToolBar operationCode="datain" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'ttDepositImport', gridName:'ttDepositMainList'})"></t:dgToolBar>
            <t:dgToolBar operationCode="out" title="导出" icon="icon-dataOut" url="ttDepositController.do?exportXls"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttDepositController.do?goTtDepositLogMain" funname="detail" width="1200"></t:dgToolBar>
        </t:datagrid>
    </div>
    <input type="text">
</div>
<script type="text/javascript">

// $("#ttDepositMainListtb_r").find("input[name='beginDate']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

	function updateAll(){
		var url = "ttDepositController.do?goTtDeposiEdittForm";
		createwindow("批量修改押金",url,500,400);
	}
    //启用停用
    function startOrStop(flag){
        var seleted = $("#ttDepositMainList").datagrid('getSelected');
        if(seleted == null){
            tip("请选择一条要操作的数据");
            return;
        }
        var tipmMsg = "";
        if(flag == 0){
            tipmMsg = "确定要启用该数据吗?"
            if(seleted.enableStatus == flag){
                tip("已经处于启用状态,无需再次启用");
                return false;
            }
        }else{
            if(seleted.enableStatus == flag){
                tip("已经处于停用状态,无需再次停用");
                return false;
            }
            tipmMsg = "确定要停用该数据吗?"
        }
        $.messager.confirm('操作提示',tipmMsg,function(r){
            if (r){
                $.ajax({
                    type : "POST",
                    url : "ttDepositController.do?isEnableStatus",
                    data : {
                        "id" : seleted.id,
                        "enableStatus": flag
                    },
                    dataType : "json",
                    success : function(data) {
                        tip(data.msg);
                        $("#ttDepositMainList").datagrid('reload');
                    },
                    error:function(){
                        tip("服务器异常，请稍后再试");
                    }
                });
            }
        });
    }

    //日期格式化
    $(function () {
        $("input[name='createDate_begin']").removeAttr("onfocus");
        $("input[name='createDate_end']").removeAttr("onfocus");

        $("input[name='createDate_begin']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
        $("input[name='createDate_end']").addClass("Wdate").css({
            'height': '20px',
            'width': '150px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy-MM-dd'});
        });
    });

</script>
