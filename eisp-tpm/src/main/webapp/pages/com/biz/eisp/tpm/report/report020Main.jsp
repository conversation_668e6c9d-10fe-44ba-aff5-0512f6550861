<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report020List" fitColumns="true" title="定额费用台账明细"
                    pagination="true" autoLoadData="true" actionUrl="report020Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="审批状态" field="approvalStatus" sortable="false" replace="审批通过_3,关闭_6" dictionary="bpm_status" query="true"></t:dgCol>
            <t:dgCol title="活动类型" field="actType"  sortable="false" query="true" replace="定额活动_定额活动,长期待摊费用_长期待摊费用,广告费申请_广告费申请"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程提交日期" field="flowDate"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程编号" field="processCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="提交主题" field="submitTheme"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="业务费用归类" field="expenseClassification" sortable="false" dictionary="business_cost_type" query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="费用归属事业部" field="costSyb"  sortable="false"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  query="true" sortable="false"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"  sortable="false" query="true"></t:dgCol>

            <t:dgCol title="活动总单编号" field="billCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动总单名称" field="billName"  sortable="false"></t:dgCol>
            <t:dgCol title="活动编号" field="actCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动名称" field="actName"  sortable="false"></t:dgCol>

            <t:dgCol title="预算科目编码" field="financialCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="预算科目名称" field="financialName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动大类编码" field="costTypeCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动大类名称" field="costTypeName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类编码" field="costAccountCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类名称" field="costAccountName"  sortable="false" query="true"></t:dgCol>

            <t:dgCol title="开始时间" field="beginTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品层级编码" field="productHierarchyCode"  sortable="false" ></t:dgCol>
            <t:dgCol title="产品层级名称" field="productHierarchyName"  sortable="false" query="true"></t:dgCol>
            <%--申请信息--%>
            <t:dgCol title="数量" field="num"  sortable="false" ></t:dgCol>
            <t:dgCol title="费用申请额" field="costApply"  sortable="false" ></t:dgCol>
            <t:dgCol title="申请支付方式" field="applyPayment"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="申请货补品项" field="applyNewArrival"  sortable="false"></t:dgCol>
            <%--结案信息--%>
            <t:dgCol title="结案时间" field="closingTime" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案流程号" field="closingProcessCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案总单号" field="closingTotalNum"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案子单号" field="closingSonNumber"  sortable="false"></t:dgCol>
            <t:dgCol title="数量" field="quantity"  sortable="false"></t:dgCol>
            <t:dgCol title="结案金额" field="closingAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="结案状态" field="closingStatus"  sortable="false" dictionary="audit_bkw_status"></t:dgCol>
            <t:dgCol title="支付方式" field="paymentMode"  sortable="false" dictionary="payment_type" query="true"></t:dgCol>
            <%--上账信息--%>
            <t:dgCol title="上账月份" field="upAccountMonth"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上账编码" field="upAccountCode"  sortable="false"></t:dgCol>
            <t:dgCol title="上账金额" field="upAccount"  sortable="false"></t:dgCol>
            <t:dgCol title="上账凭证号" field="upAccountVoucher"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report020Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report020Listtb_r").find("input[name='billingDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyyMMdd'});});
        $("#report020Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});


        $($("#report020ListForm").find("div")[3].children[0]).html("流程提交年月");

        $("#report020Listtb_r").find("input[name='flowDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("#report020Listtb_r").find("input[name='beginTime']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#report020Listtb_r").find("input[name='endTime']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#report020Listtb_r").find("input[name='closingTime']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("#report020Listtb_r").find("input[name='upAccountMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});


//        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
//            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report020Listsearch() {
        var orgCode = $("#report020Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report020Listtb_r").find("input[name='yearMonth']").val();



        var queryParams = $("#report020List").datagrid('options').queryParams;
        $("#report020Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report020List").datagrid({url:'report020Controller.do?findReportList'});
    }

</script>
