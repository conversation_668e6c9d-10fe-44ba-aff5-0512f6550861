<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttBudgetDocumentList" title="预算记账"  actionUrl="ttBudgetDocumentController.do?findTtBudgetDocumentList" 
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">
			<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="编码" field="code" query="true" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="公司代码" field="bukrs" query="true" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="凭证类型" field="blart" query="true" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="记账日期" field="budatStr"  query="true" sortable="false"  hidden="false"  ></t:dgCol>
			<%--<t:dgCol title="凭证创建人" field="usnam" query="true" sortable="false"  hidden="false"  ></t:dgCol>--%>
			<t:dgCol title="凭证抬头文本" field="bktxt" query="true" sortable="false"  hidden="false"  ></t:dgCol>
			<%--<t:dgCol title="参考凭证编号" field="xblnr" query="true" sortable="false"  hidden="false"  ></t:dgCol>--%>
			<t:dgCol title="货币码" field="waers" dictionary="dict_currency_type" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="凭证状态" field="bpmStatus" replace="待过账_1,已过账_3,已冲销_4" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="会计凭证号" field="acDocNo"  sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="冲销凭证号" field="sapBudgetCode"  sortable="false"  hidden="false"  ></t:dgCol>

			<t:dgCol title="创建人" field="createName" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="创建时间" field="createDate" sortable="false"  hidden="false" formatter="yyyy-MM-dd hh:mm:ss"  ></t:dgCol>
			<t:dgCol title="最近更新人" field="updateName" sortable="false"  hidden="false"  ></t:dgCol>
			<t:dgCol title="最近更新时间" field="updateDate" sortable="false"  hidden="false" formatter="yyyy-MM-dd hh:mm:ss"  ></t:dgCol>
			<t:dgToolBar title="新增" icon="icon-add" operationCode="add" url="ttBudgetDocumentController.do?goTtBudgetDocumentForm&optype=0" width="800" height="450" funname="addBudgetDocument"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" operationCode="edit" url="ttBudgetDocumentController.do?goTtBudgetDocumentForm&optype=1" width="800" height="400" funname="updateBudgetDocument"></t:dgToolBar>
			<t:dgToolBar title="删除" icon="icon-remove" operationCode="delete" url="ttBudgetDocumentController.do?deleteTtbudgetDoc"  funname="deleteTtbudgetDoc"></t:dgToolBar>
			<t:dgToolBar title="过账" icon="icon-edit" operationCode="sendToSap" url="ttBudgetDocumentController.do?sendToTheSap" funname="sendToSap"></t:dgToolBar>
			<t:dgToolBar title="导入" icon="icon-dataIn" operationCode="input" onclick="importDataByXml({impName:'ttBudgetDocument', gridName:'ttIncomeTargetList1'})"></t:dgToolBar>
			<t:dgToolBar title="同步成本中心" icon="icon-edit" operationCode="syncSap" url="ttBudgetDocumentController.do?syncCostCenter" funname="syncCostCenter"></t:dgToolBar>
			<t:dgToolBar title="查看" icon="icon-edit" url="ttBudgetDocumentController.do?goTtBudgetDocumentForm&optype=2" width="800" height="400" funname="detail"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

	$(function(){
        $("#ttBudgetDocumentListtb_r").find("input[name='budatStr']").addClass("Wdate")
            .css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
	});

    //政策申请
    function addBudgetDocument(title, url, grid, width, height){
        gridname = grid;
        openWindOwn(title, url,width, height);
    }

    //修改
    function updateBudgetDocument(title, url, grid, width, height){
        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var rowData = rowDatas[0];
		if (rowData.bpmStatus == 2){
            tip("处理中的数据不可编辑");
            return ;
        }else if (rowData.bpmStatus == 3){
            tip("已过账的数据不可编辑");
            return ;
        }else if(rowData.bpmStatus == 4){
            tip("已冲销的数据不可编辑");
            return ;
		}
        var id = rowData.id;
//        if (checkThisDataCouldToUpdate(id)){
            url += "&id=" + id;
            openWindOwn(title, url,width, height);
//        }
    }

    function deleteTtbudgetDoc(){
        var url = "ttBudgetDocumentController.do?deleteTtbudgetDoc";
        var rowDatas = $('#ttBudgetDocumentList').datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }

        if(rowDatas[0].bpmStatus=='3'){
            tip("已过账的数据不能删除！");
            return ;
        }
        if(rowDatas[0].bpmStatus=='4'){
            tip("已冲销的数据不能删除！");
            return ;
        }
        var thisData = {
            id : rowDatas[0].id
        }
        var d = ajaxPost(thisData,url);
        if(d.success){
            $('#' + grid).datagrid("reload");
        }
        tip(d.msg);
	}

	function syncCostCenter(){
        var url = "ttBudgetDocumentController.do?syncCostCenter";
        var thisData = {
        }
        var d = ajaxPost(thisData,url);
        if(d.success){
            $('#ttBudgetDocumentList').datagrid("reload");
        }
        tip(d.msg);
	}

    //过账
    function sendToSap(title, url, grid, width, height){

        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        if(rowDatas[0].bpmStatus=='3'){
            tip("该凭证已过账！");
            return ;
		}
        if(rowDatas[0].bpmStatus=='4'){
            tip("该凭证已冲销！");
            return ;
        }
        var thisData = {
			id : rowDatas[0].id
		}

        var d = ajaxPost(thisData,url);
        if(d.success){
            $('#' + grid).datagrid("reload");
		}
        tip(d.msg);
    }


    //-------------------------共用----------------------
    function openWindOwn(title, url,width, height){
        width = width == '' ? '1000': width;
        height = height == '' ? '700': height;
		createwindowExt(title,url,width, height,{
			button : [
				{name : "关闭",
					callback : function() {
                        $('#ttBudgetDocumentList').datagrid('reload');
						return true;
					}}
			]
		});
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }
</script>
