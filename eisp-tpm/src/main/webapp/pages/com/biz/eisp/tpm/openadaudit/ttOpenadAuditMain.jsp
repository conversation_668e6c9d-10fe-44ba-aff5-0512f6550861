<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="openadList" title="户外广告核销列表"  actionUrl="ttOpenadAuditController.do?findopenAdAuditList"
	  		checkbox="false"  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="true">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="状态" field="bpmStatus"  dictionary="bpm_status"  query="true" width="80" ></t:dgCol>
			<t:dgCol title="活动单号" field="actCode"  query="true"  width="200"></t:dgCol>
			<t:dgCol title="标题" field="title"   width="200"></t:dgCol>
			<t:dgCol title="所属公司" field="unitName" query="true"  width="200"></t:dgCol>
			<t:dgCol title="核销单号" field="auditCode"   width="200"></t:dgCol>
			<t:dgCol title="核报日期" field="createDate" formatter="yyyy-MM-dd"  width="200"></t:dgCol>
			<t:dgCol title="费用开始日期" field="startDate" hidden="true" width="200"></t:dgCol>
			<t:dgCol title="费用截止日期" field="endDate" hidden="true"  width="200"></t:dgCol>
			<t:dgCol title="核销申请金额" field="auditAmount"  width="200"></t:dgCol>
			<t:dgCol title="瑕疵率" field="flawChance" hidden="true" width="200"></t:dgCol>
			<t:dgCol title="hdlx" field="accountName" hidden="true" width="200"></t:dgCol>
			<t:dgCol title="备注" field="remark" hidden="true" width="200"></t:dgCol>
			<t:dgCol title="最近更新人" field="updateName"  width="200"></t:dgCol>
			<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd"  width="200"></t:dgCol>
			<t:dgToolBar title="发起核销" icon="icon-add" operationCode="" url="" onclick="createAudit()"></t:dgToolBar>
			<t:dgToolBar title="提交" icon="icon-edit"  operationCode="update" url=""  funname="submit_act"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
			<t:dgToolBar title="查看" icon="icon-edit"  operationCode="update" url=""  funname="showAudit"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteAudit"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit" url="" operationCode="update" funname="updateInfo"></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

    // function openstatus(value) {
		// if(value==2222){
		//     return '未核销';
		// }else {
		//     return value;
		// }
    // }

    function updateInfo() {
        // debugger;
        var applyIds='';
        var rows = $("#openadList").datagrid('getSelections');
        var actCode=rows[0].actCode;
        var auditCode=rows[0].auditCode;
        var accountName=rows[0].accountName;



        if(rows!=null&&rows.length>0){
            var actCode0=rows[0].actCode;
            for(var i=0;i<rows.length;i++){
                if(actCode0!=rows[i].actCode){
                    tip('请选择同一活动单号的数据');
                    return false;
                }
                if(rows[i].bpmStatus==122){
                    tip("未发起核销，无法编辑");
                    return false;
                }
                if(rows[i].bpmStatus==1||rows[i].bpmStatus==4){
                    ;
                }else{
                    tip('只能选择待提交或驳回的数据！');
                    return false;
                }
            }

            for(var j=0;j<rows.length;j++){
                applyIds+=rows[j].id+",";
            }
        }else{
            tip('请至少选择一条数据!');
            return false;
        }


        gridname="openadList";
        var url = "ttOpenadAuditController.do?updateForm&actCode="+actCode+"&applyIds="+applyIds+"&auditCode="+auditCode+"&accountName="+accountName;

        createwindowExt("编辑",url,1200,1200, {
            button:[
                {
                    name : "确定",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var submitForm=iframe.$("#submitForm");

                        var unitName=submitForm[0].children.unitName.value;
                        var title=submitForm[0].children.title.value;
                        var auditAmount=submitForm[0].children.auditAmount.value;
                        var adType=submitForm[0].children.adType.value;


                        if(isNull(title)){
                            tip("请输入标题");
                            return false;
                        }



                        if(isNull(auditAmount)){
                            tip("请输入核销金额");
                            return false;
                        }else {
                            var reg=/^\d+(\.\d+)?$/;
                            if(reg.test(auditAmount)){
                                if(parseFloat(auditAmount)<0){
                                    tip("核销金额应为大于0的正数");
                                    return false;
                                }
                            }else{
                                tip("核销金额应为大于0的正数");
                                return false;
                            }
                        }



                        //submitForm.submit();
                        $('#submitForm', iframe.document).form('submit', {
                            onSubmit : function() {
                            },
                            success : function(r) {
                                var data =$.parseJSON(r);
                                tip(data.msg);
                                reloadTable();

                            }
                        });
                        return true;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }



    //导出
    function toExcel(){
        excelExport("ttOpenadAuditController.do?exportXls","openadList");
    }


    function createAudit(){
        // var actCode=$("input[name='actCode']").val();
        // var unitName=$("input[name='unitName']").val();
        var applyIds='';
        var rows = $("#openadList").datagrid('getSelections');
        var actCode=rows[0].actCode;
        var unitName=rows[0].unitName;
        var accountName=rows[0].accountName;
        if(rows!=null&&rows.length>0){
            var actCode0=rows[0].actCode;
            for(var i=0;i<rows.length;i++){
                if(actCode0!=rows[i].actCode){
                    tip('请选择同一活动单号的数据');
                    return false;
				}
                if(rows[i].bpmStatus==122){
                    ;
                }else{
                    tip('请选择未发起核销的数据');
                    return false;
				}
			}

            for(var j=0;j<rows.length;j++){
                applyIds+=rows[j].id+",";
            }
		}else{
            tip('请至少选择一条数据!');
            return false;
		}


        gridname="openadList";
        // var url = "ttOpenadAuditController.do?openadForm&applyIds="+applyIds;
        var url = "ttOpenadAuditController.do?openadForm&actCode="+actCode+"&applyIds="+applyIds+"&unitName="+unitName+"&accountName="+accountName;

        createwindowExt("创建",url,1000,600, {
            button:[
                {
                    name : "确定",
                    callback : function() {
                        iframe = this.iframe.contentWindow;
                        var submitForm=iframe.$("#submitForm");

                       var unitName=submitForm[0].children.unitName.value;
                        var title=submitForm[0].children.title.value;
                        var auditAmount=submitForm[0].children.auditAmount.value;
                        var adType=submitForm[0].children.adType.value;


                        if(isNull(title)){
                            tip("请输入标题");
                            return false;
                        }



                            if(isNull(auditAmount)){
                                tip("请输入核销金额");
                                return false;
                            }else {
                                var reg=/^\d+(\.\d+)?$/;
                                if(reg.test(auditAmount)){
                                    if(parseFloat(auditAmount)<0){
                                        tip("核销金额应为大于0的正数");
                                        return false;
                                    }
                                }else{
                                    tip("核销金额应为大于0的正数");
                                    return false;
                                }
						}



                        //submitForm.submit();
                        $('#submitForm', iframe.document).form('submit', {
                            onSubmit : function() {
                            },
                            success : function(r) {
                                var data =$.parseJSON(r);
                                tip(data.msg);
                                reloadTable();

                            }
                        });
                        return true;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }




    //提交工作流
    function submit_act(){
        var rows = $("#openadList").datagrid('getSelections');
        if(rows.length>1){
            tip("只能选择一条数据!");
            return false;
        }else if(rows==null||rows.length==0){
            tip("必须选择一条数据!");
            return false;
        }else{
            var auditCode=rows[0].auditCode;
            var bpmStatus=rows[0].bpmStatus;
            //alert(bpmStatus);
            if(bpmStatus!=1){
                tip("请选择待提交的数据");
                return false;
            }
            if(auditCode!=null&&auditCode!=""){
                var params = {processKeyType:'act_bpm_type'};
                customSubmitDialog(auditCode,"","","com.biz.eisp.tpm.openadaudit.controller.TtOpenadAuditWorkFlowController",JSON.stringify(params));

            }else{
                tip("核销单号不能为空!");
            }

        }

    }

    function deleteAudit() {
        var rows = $("#openadList").datagrid('getSelections');
        if(rows.length<1){
            tip("请至少选择一条数据")
        }else {
            for ( var m = 0; m < rows.length; m++) {

                if(rows[m].bpmStatus==122){
                    tip("未发起核销，无法删除");
                    return false;
                }

                if(rows[m].bpmStatus!=1){
                    if(rows[m].bpmStatus!=4){
                        tip("流程中的数据不能删除");
                        return false;
                    }



                }
            }
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="ttOpenadAuditController.do?deleteAudit";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            id : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
        }

    }


    function isNull(obj) {
        if(obj == ""|| obj == null||obj == undefined){
            return true;
        }
    }


    function showAudit(){

        var applyIds='';
        var rows = $("#openadList").datagrid('getSelections');

        if(rows==null||rows.length>1){
          tip("请选择一条记录查看")
        }

        if(rows[0].bpmStatus==122){
            tip("未发起核销");
            return false;

		}


        gridname="openadList";

        var url = "ttOpenadAuditController.do?showOpenadForm&id="+rows[0].id;

        createwindowExt("查看",url,1000,600, {

            button:[{
                name:'关闭',
                cancel : function() {
                    return true;
                }

            }]
        });
    }

</script>
