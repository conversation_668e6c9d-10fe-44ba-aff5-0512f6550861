<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/util/processTheme.js"></script>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<style type="text/css">
</style>
<div id="ttActLongtermMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttActLongtermList" title="部门费用&长期待摊"  actionUrl="ttActLongTermController.do?findTtActLongTermList"
	  		  idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>

			<t:dgCol title="审批状态" field="bpmStatus" dictionary="bpm_status" query="true"></t:dgCol>
			
			<t:dgCol title="活动类型" field="actType" replace="长期待摊_0,广告费用&营销费用(到部门)_1" query="true"></t:dgCol>

			<t:dgCol title="流程类型" field="actTypeCode" dictionary="ad_long_act_type" query="true"></t:dgCol>

			<t:dgCol title="活动类型" field="actModeCode" hidden="true"></t:dgCol>


			<t:dgCol title="活动编号" field="billCode" align="center" query="true"></t:dgCol>
			<t:dgCol title="活动名称" field="billName" align="center" query="true"></t:dgCol>
			<%--<t:dgCol title="活动大类" field="costTypeCode" query="true" hidden="true"></t:dgCol>--%>
			<t:dgCol title="活动大类" field="costTypeName" query="true"></t:dgCol>
			<%--<t:dgCol title="活动细类" field="costAccountCode" query="true" hidden="true"></t:dgCol>--%>
			<t:dgCol title="活动细类" field="costAccountName" query="true"></t:dgCol>
			<t:dgCol title="客户名称" field="customerName"  query="true"></t:dgCol>
			<t:dgCol title="开始时间" field="beginDate" query="true" formatter="yyyy-MM-dd" queryMode="group"></t:dgCol>
			<t:dgCol title="结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>
			<t:dgCol title="所属组织" field="orgName" query="true"></t:dgCol>
			<t:dgCol title="所属组织" field="orgCode" hidden="true"></t:dgCol>
			<t:dgCol title="活动总金额" field="amount"></t:dgCol>
			<t:dgCol title="费用归属事业部" field="businessUnitName"></t:dgCol>
			<t:dgCol title="费用归属事业部" field="businessUnitCode" query="true" hidden="true"></t:dgCol>
			<t:dgCol title="支付方式" field="paymentCode" dictionary="payment_type" query="true"></t:dgCol>
	  		<t:dgCol title="文本描述" field="remark"></t:dgCol>
	  		<t:dgCol title="发起人" field="createName" query="true"></t:dgCol>
	  		<t:dgCol title="发起时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>

			<t:dgToolBar title="长期待摊费用申请" operationCode="addLong" icon="icon-add" url="ttActLongTermController.do?goTtActLongTermForm" funname="add"  width="800" height="650"></t:dgToolBar>
			<t:dgToolBar title="广告费用&营销费用(到部门)" operationCode="addAd" icon="icon-add" url="ttActAdController.do?goTtActAdForm" funname="add"  width="1000" height="650"></t:dgToolBar>
			
			<t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="ttActLongTermController.do?goTtActLongTermForm" funname="updateActLongTerm"  width="800" height="650"></t:dgToolBar>
			
			<t:dgToolBar title="查看明细" operationCode="view" icon="icon-preview" url="" funname="previewDetail" width="800" height="650"></t:dgToolBar>
			
			<t:dgToolBar title="删除" operationCode="remove" icon="icon-remove" url="" funname="delActLongterm" ></t:dgToolBar>
			<t:dgToolBar title="关闭"  operationCode="close" icon="icon-stop"  onclick="closeTtActLongTerm()"></t:dgToolBar>
			<t:dgToolBar title="流程日志" operationCode="log" icon="icon-log" url="" funname="workflowLog"></t:dgToolBar>
			
			<t:dgToolBar title="提交申请" operationCode="submit" icon="icon-ok" url="" funname="submitLeave"></t:dgToolBar>


			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="ttActLongTermController.do?exportXls" funname="excelExport"></t:dgToolBar>
			<t:dgToolBar title="导出明细" operationCode="dataDetailOut" icon="icon-dataOut" url="ttActLongTermController.do?exportDetailXls" funname="excelExport"></t:dgToolBar>

			<t:dgToolBar title="导出分摊明细" operationCode="dataOutDetail" icon="icon-dataOut" url="ttActLongTermController.do?exportXlsDetail" funname="excelExport"></t:dgToolBar>
			<t:dgToolBar title="导出产品明细" operationCode="dataOutProduct" icon="icon-dataOut" url="ttActLongTermController.do?exportXlsProduct" funname="excelExport"></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>

<script type="text/javascript">
	$(function(){
        $("#ttActLongtermListForm").find("label").eq(8).addClass("width","120px");

		<%--$("input[name='orgCode']").combotree({--%>
			<%--url: 'tmOrgController.do?getParentOrg&pid=${orgId}',--%>
			<%--width:180--%>
		<%--});--%>
		$("#ttActLongtermListtb_r").find("input[name='beginDate_begin']").prev().html("活动时间");
        $("#ttActLongtermListtb_r").find("input[name='businessUnitCode']").combobox({url:"tmCommonMdmController.do?findOrgCombox"});
        $("input[name='orgName']").attr("readonly",true).attr("id","orgName").attr("style","width:180px").click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='orgCodes' id='orgCodes'/>");
	});
    function openOrgSelect(){
        var orgCode ='${orgCode}';
        var currentOrgCode='${currentOrgCode}';
        orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
        var paraArr = {
            textName: 'orgCode,orgName',
            inputTextName: 'orgCodes,orgName',
            searchType: '1',//查询类型？？
            encapsulationType: 'input',//封装类型--不传或默认
            isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            currentOrgCode:currentOrgCode,
            couldNull : true,
            pageData: {
                orgCode: orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }
	//编辑
	function updateActLongTerm(title, url, gridname, width, height){
		var actLongtermTarget = $("#ttActLongtermList").datagrid("getSelected");
		if(actLongtermTarget == null || actLongtermTarget == ""){
			tip("请选择一条需要编辑的数据");
			return false;
		}
		if(!(actLongtermTarget.bpmStatus == 1 || actLongtermTarget.bpmStatus == 4 || actLongtermTarget.bpmStatus == 5)){
            tip("当前流程状态不允许编辑");
			return false;
		}
		if(actLongtermTarget.actType == '1'){//广告费用
			url = "ttActAdController.do?goTtActAdForm";
		}else{
			url = "ttActLongTermController.do?goTtActLongTermForm";			
		}
		update(title, url, gridname, width, height);
	}
	
	//删除
	function delActLongterm(){
		var actLongtermTarget = $("#ttActLongtermList").datagrid("getSelected");
		if(actLongtermTarget == null || actLongtermTarget == ""){
			tip("请至少选择一条需要删除的数据");
			return false;
		}
		if(actLongtermTarget.bpmStatus == 2 || actLongtermTarget.bpmStatus == 3 || actLongtermTarget.bpmStatus == 6){
			tip("当前流程状态不能删除");
			return false;
		}
	 	getSafeJq().dialog.confirm("你确定永久删除该数据吗?", function(r) {
            if (r) {
                $.ajax({
                    url : "ttActLongTermController.do?delActLongterm",
                    type : 'post',
                    data : {
                        id : actLongtermTarget.id
                    },
                    cache : false,
                	success : function(data) {
                        var d = $.parseJSON(data);
                        var msg = d.msg;
                        if (d.success) {
                            tip(msg);
                            reloadTable();
                        }else{
            				tip(msg);
            				return false;
            			}
                    }
                });
            }
        });
	}
	
	//查看明细
	function previewDetail(){
		var selecTarget = $("#ttActLongtermList").datagrid('getSelections');
	    if (selecTarget == "" || selecTarget == null) {
	    	tip("请选择一条需要查看的活动");
			return false;
	    }
		if(selecTarget != null && selecTarget != "" && selecTarget.length > 1){
			tip("请选择一条需要查看的活动");
			return false;
		}
		var url = "";
		var id = selecTarget[0].id;
		var actTypeCode = selecTarget[0].actTypeCode;
        if(selecTarget[0].actType == '1'){//广告费用
			url = "ttActAdController.do?goTtActLongTermTab&id="+id;
		}else{
		 	url = "ttActLongTermController.do?goTtActLongTermTab&id="+id;
		}
        openwindow("查看明细", url,'',1000, 800);
	}
	//提交工作流
	function submitLeave() {
		var url = "ttActLongTermWorkFlowController.do?goTtActWorkFlowSubmitMain";
        $.dialog({
            title: "提交流程",
            content: "url:" + url,
            lock: true,
            width: "950",
            height: "500",
//            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                $('#btn_sub', this.iframe.contentWindow.document).click();
                return false;
            },
            cancelVal: '关闭',
            cancel: true
        });
	}
	function reloadTable(){ttActLongtermListsearch();}
    function workflowLog(){
        var select = $("#ttActLongtermList").datagrid('getSelections');
        if(select==null || select==""){
            tip("请至少选择一条数据");
            return false;
        }
        var billCode = select[0].billCode;
        var url = "ttActWorkFlowController.do?goTtLongtermWorkflowLogList&billCode="+billCode;
        openwindow('查看',url,'ttActQuotaList',1000,600);
    }

    //关闭活动
    function closeTtActLongTerm() {
        var seleted = $("#ttActLongtermList").datagrid('getSelected');
        if(seleted == null||seleted == ""){
            tip("请选择一条要操作的数据");
            return;
        }

        if(seleted.bpmStatus != 3){
            tip("当前流程状态不允许关闭!");
            return;
        }
        if(seleted.bpmStatus == 6){
            tip("当前流程已关闭!");
            return;
        }
        var tipmMsg = "是否确认关闭此活动，关闭后无法再次开启！";
        $.messager.confirm('操作提示',tipmMsg,function(r){
            if (r){
                $.ajax({
                    type : "POST",
                    url : "ttActLongTermController.do?closeActLongterm",
                    data : {
                        "id" : seleted.id
                    },
                    dataType : "json",
                    success : function(data) {
                        tip(data.msg);
                        $("#ttActLongtermList").datagrid('reload');
                    },
                    error:function(){
                        tip("服务器异常，请稍后再试");
                    }
                });
            }
        });
    }

</script>
