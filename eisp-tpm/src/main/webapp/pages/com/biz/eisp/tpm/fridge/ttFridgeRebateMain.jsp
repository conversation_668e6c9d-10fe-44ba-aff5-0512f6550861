<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttFridgeMainList" fitColumns="true" title="冰柜台账及返利管理"
					actionUrl="ttFridgeRebateController.do?findTtFridgeRebateList"
					queryMode="group" pagination="true" idField="customerCode" fit="true">
			<t:dgCol title="id" field="id" hidden="true" ></t:dgCol>
			<t:dgCol title="客户编号" field="customerCode" query="true"></t:dgCol>
			<t:dgCol title="客户名称" field="customerNames" hidden="true" query="true" ></t:dgCol>
			<t:dgCol title="客户名称" field="customerName" query="false" ></t:dgCol>
			<t:dgCol title="年月" field="yearMonth" formatter="yyyy-MM" queryMode="group" query="true"></t:dgCol>
			<t:dgCol title="购买类型" field="purchaseType" query="true" dictionary="purchase_type"></t:dgCol>
			<t:dgCol title="申请返利总额" field="totalAmount"></t:dgCol>
			<t:dgCol title="状态" field="rebateStatus" query="true" dictionary="rebate_status"></t:dgCol>

			<t:dgToolBar operationCode="add" title="批量提交返利申请" icon="icon-add" url="ttFridgeRebateController.do?goTtFridgetBatchMain" funname="openSubit"></t:dgToolBar>
			<%--<t:dgToolBar title="查看" icon="icon-look" url="" funname="viewDetail()"></t:dgToolBar>--%>

			<t:dgToolBar operationCode="detai" title="返利明细" icon="icon-look" url="" funname="detailData()"></t:dgToolBar>

			<t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttFridgeRebateController.do?exportXls" funname="excelExport"></t:dgToolBar>
			<t:dgToolBar title="导入" operationCode="import" icon="icon-add" url=""
						 onclick="importDataByXmlDMS({gridName:'ttFridgeMainList'});"
						 height="450" width="900"></t:dgToolBar>

			
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
$(function(){
	$("#ttFridgeMainListtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
    $("input[name='customerNames']").attr("readonly",true).attr("id","customerNames").click(function(){addCustomers();}).parent().append("<input type='hidden' name='customerCodes' id='customerCodes'/>");
});
function detailData(){
    var selected = $("#ttFridgeMainList").datagrid('getSelected');
    if(selected == null){
        tip("请选择一条要操作的数据");
        return;
    }
    var url = "ttFridgeRebateController.do?goFridgeLedgerAndRebateDetail&customerCode="+
				selected.customerCode+"&purchaseType="+selected.purchaseType+"&yearMonth="+selected.yearMonth;
    openwindow('返利明细',url,null,800,400);
}

/*function viewDetail(){
	var selected = $("#ttFridgeMainList").datagrid('getSelected');
	if(selected == null){
		tip("请选择一条要操作的数据");
		return;
	}
	selected.customerCode;
	selected.yearMonth;
	selected.purchaseType;
	var url = "ttFridgeRebateController.do?viewFridgeDetail&customerCode="+selected.customerCode+"&yearMonth="+selected.yearMonth+"&purchaseType="+selected.purchaseType;
	openwindow('详情',url,null,800,400);
}*/
//可选部门弹窗
function openSubit(){
 	safeShowDialog({
		content : "url:ttFridgeRebateController.do?goTtFridgetBatchMain",
		lock : true,
		title : "审批进度",
		width : 1200,
		height : 800,
		left :'50%',
		cache : true,
		cancel:true,
		button:[{name:"确定",callback:function(){
			var flag = false;
			iframe = this.iframe.contentWindow;
			iframe.aaa();
			$("#ttFridgeMainList").datagrid("reload");
			return flag;
		}}],cancelVal:'关闭'
	});
}
function importDataByXmlDMS(cfg) {
    gridname=cfg.gridName;
    $.dialog({
        content:"url:ttFridgeRebateImportController.do?importCg",
        lock : true,
        title: "冰柜台账及返利导入",
        max:false,
        width:660,
        height:400,
        button: [
            {
                name: "确定",
                callback: function(){
                    iframe = this.iframe.contentWindow;
                    iframe.upload();
                    return false;
                }
            },

            {
                name: "取消",
                callback: function(){
                    this.close();
                }
            }
        ]
    }).zindex();
}

function addCustomers () {
    var paraArr = {
        textName: 'customerCode,customerName',
        inputTextName: 'customerCodes,customerNames',
        searchType:'1',//查询类型？？
        encapsulationType:'input',//封装类型
        pageData:{
            customerCodes : customerCodes
        }
    }
    openChooseCustomerSelect(paraArr);
}

//-----------选择组织end---------------//
//-----------选择客户star--------------//
function openChooseCustomerSelect(dataArr){
    createwindowExt("添加客户","tmCommonMdmController.do?goSelectCustomerMain&exclusiveCodes=" + dataArr.pageData.customerCodes,1000,600,{
        ok : function() {
            iframe = this.iframe.contentWindow;
            var rowsData = iframe.$('#ttCustomerSeletedList').datagrid('getRows');
            if (rowsData == '' || rowsData == null) {
                iframe.tip("请添加已选客户");
                return false;
            }
            //返回赋值
            return returnAssign(iframe,rowsData,dataArr);
        },
        cancelVal : '关闭',
        cancel : true
    });
    safeShowDialog(myOptions);
}
//-----------选择客户end---------------//
//-----------选择产品star--------------//
</script>