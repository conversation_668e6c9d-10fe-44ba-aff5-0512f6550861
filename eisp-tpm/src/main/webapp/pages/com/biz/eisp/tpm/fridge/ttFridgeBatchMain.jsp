<%@ taglib prefix="t" uri="/base-tags" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<body>   
<div class="easyui-layout" fit="true">
	<div region="west" style="width:500px;" split="true" >
		<t:datagrid name="ttFridgeMainList" fitColumns="true" title="冰柜台账及返利"
					actionUrl="ttFridgeRebateController.do?findTtFridgeBatchList" 
					queryMode="group"  idField="id" fit="true" singleSelect="false">
					
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="冰柜编号" field="fridgeCode"></t:dgCol>
			<t:dgCol title="购买类型" field="purchaseType" query="true" dictionary="purchase_type"></t:dgCol>
			<t:dgCol title="购买类型名称" field="purchaseTypeName" hidden="true"></t:dgCol>
			<t:dgCol title="所属经销商" field="customerName" query="true"></t:dgCol>
			<t:dgCol title="所属经销商" field="customerCode" hidden="true"></t:dgCol>
			<t:dgCol title="返利年月" field="yearMonth" query="true"></t:dgCol>
			<t:dgCol title="本月返利金额" field="rebateAmount"></t:dgCol>
			<t:dgCol title="状态" field="rebateStatus" query="true" replace="未返利_0,部分返利_1,全部返利_2"></t:dgCol>
			
			<t:dgToolBar title="添加" icon="icon-add" url="" funname="addData"></t:dgToolBar>
			<t:dgToolBar title="全部添加" icon="icon-add" onclick="addAllItem()"></t:dgToolBar>
			
		</t:datagrid>		
	</div>
	<div region="center" style="" >
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="removeUseApply();">删除</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onclick="removeAllUserApply();">全部删除</a>
						</span>
						<span style="float:right">
							支付方式:
							<select id="payment" name="payment" onchange="changeAllPayment();">
								<option value=''>--请选择--</option>
								<c:forEach items="${paymentList}" var="pay">
									<option value='${pay.value }' >${pay.text }</option>
								</c:forEach>
							</select>

							活动细类:
							<input type="inputxt" name="costAccountName" id="costAccountName" readonly="readonly">
							<input type="hidden" name="costAccountCode" id="costAccountCode">
							<input type="hidden" name="costTypeCode" id="costTypeCode">
							<input type="hidden" name="costTypeName" id="costTypeName">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openCostAccountSelect();"></a>
							<%--<select name="costAccountCode" id="costAccountCode">
								<option value="-1">--请选择--</option>
								<c:forEach items="${cost}" var="c">
									<option value="${c.accountCode}">${c.accountName}</option>
								</c:forEach>
							</select>--%>
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">
					</div>
				</div>
				<div class="datagrid-view">
					<table class="actTable useApplyDetail" id="table1">
						<thead>
							<tr>
								<td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
								<td>冰柜编号</td>
								<td>购买类型</td>
								<td>所属经销商</td>
								<td>返利年月</td>
								<td>申请返利金额(元)</td>
								<td>支付方式</td>
								<td>状态</td>
							</tr>
						</thead>
						<tbody>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
</body>
<script type="text/javascript">
    $(function(){
        $("#ttFridgeMainListtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        //双击添加
        $('#ttFridgeMainList').datagrid({
            //双击事件
            onDblClickRow :function(rowIndex,rowData){
                doubleClickAdd(rowIndex,rowData);
                doubleClickMove();
            }
        });
        //双击移除
		doubleClickMove();
    });
    //双击添加
    function doubleClickAdd(rowIndex,rowData){
        var selectRows = [];
        var content = "";
		var row = rowData;
		var bpmS = '';
		if(row.rebateStatus == "0"){
			bpmS = '未返利';
		}else if(row.rebateStatus == "1"){
			bpmS = '部分返利';
		}
		content+="<tr id='useApplyTr'>"+
			"<td>" +
			"<input type='checkbox' name='actTableId'/>" +
			"<input type='hidden' value='"+row.id+"' name='id' id='id'/>" +
			"</td>"+
			"<td name='fridgeCode'>"+row.fridgeCode+"</td>"+
			"<td>"+row.purchaseTypeName+
			"<input type='hidden' value='"+row.purchaseTypeName+"' name='purchaseTypeName'>"+
			"<input type='hidden' value='"+row.purchaseType+"' name='purchaseType'>" +
			"</td>"+
			"<td name='customerName'>"+row.customerName+"<input type='hidden' value='"+row.customerCode+"' name='customerCode'></td>"+
			"<td name='yearMonth'>"+row.yearMonth+"</td>"+
			"<td>"+
			"<input type='inputxt' onblur='validateAmount(this)' name='amount' id='amount' width='120px' value='"+row.rebateAmount+"'>"+
			"<input type='hidden' name='oldAmount' value='"+row.rebateAmount+"'>" +
			"</td>"+

			"<td>"+createPayment()+"</td>"+
			"<td>"+bpmS+
			"<input type='hidden' value='"+row.rebateStatus+"' name='rebateStatus'>"+
			"</td>"+
			"</tr>";
		selectRows.push(row);
        $(".useApplyDetail").find("tbody").append(content);
		$('#ttFridgeMainList').datagrid('deleteRow',rowIndex);
	}
	//绑定双击移除事件
	function doubleClickMove(){
        var rows=document.getElementById("table1").rows;
        if(rows.length>0){
            for(var i=1;i<rows.length;i++){
                (function(i){
                    var temp=rows[i].cells[0].childNodes[1].value;
                    var obj=rows[i];
                    obj.ondblclick=function(){
                        var datas = $(this);
                        var id = datas.find("input[name='id']").val();
                        var fridgeCode = datas.find("td[name='fridgeCode']").text();//冰柜编码
                        var purchaseType = datas.find("input[name='purchaseType']").val();//购买类型
                        var purchaseTypeName = datas.find("input[name='purchaseTypeName']").val();
                        var customerName = datas.find("td[name='customerName']").text();//客户名称
                        var yearMonth = datas.find("td[name='yearMonth']").text();//年月
                        var rebateAmount = datas.find("input[name='amount']").val();//返利金额
                        var bpmStatus = datas.find("input[name='rebateStatus']").val();//状态
                        var rows = [];
                        rows.id = id;
                        rows.fridgeCode = fridgeCode;
                        rows.purchaseType = purchaseType;
                        rows.purchaseTypeName = purchaseTypeName;
                        rows.customerName = customerName;
                        rows.yearMonth = yearMonth;
                        rows.rebateAmount = rebateAmount;
                        rows.rebateStatus = bpmStatus;
                        $("#ttFridgeMainList").datagrid("insertRow",{row:rows});
                        $(this).remove();
                    };
                })(i)
            }
        }
    }

    //列表支付方式随动
    function changeAllPayment(){
        var paymentCode = $("#payment").val();

        $(".actTable tbody tr").each(function(trindex, tritem){//遍历每一行
            $(tritem).find("select[name=payment] option").prop("selected", false);
            if (paymentCode == "--请选择--") {
                $(tritem).find("select[name=payment] option[value='']").prop("selected", true);
                return true;
            }
            var obj = $(tritem).find("select[name=payment] option[value='"+paymentCode+"']");
            if (obj.val() == "" || obj.val() == undefined) {
                $(tritem).find("select[name=payment] option[value='']").prop("selected", true);
                return true;
            }
            obj.prop("selected", true);
        });

    }

	function removeUseApply(){
		//验证
		var trTarget = $(".actTable").find("tbody tr#useApplyTr.active");
		if(trTarget.length == 0){
			tip("请先选择一条需要删除数据");
			return false;
		}
		//删除
		$(".actTable").find("tbody tr#useApplyTr.active").each(function(){
			var datas = $(this);
			var id = datas.find("input[name='id']").val();
			var fridgeCode = datas.find("td[name='fridgeCode']").text();//冰柜编码
			var purchaseType = datas.find("input[name='purchaseType']").val();//购买类型
			var purchaseTypeName = datas.find("input[name='purchaseTypeName']").val();
			var customerName = datas.find("td[name='customerName']").text();//客户名称
			var yearMonth = datas.find("td[name='yearMonth']").text();//年月
			var rebateAmount = datas.find("input[name='amount']").val();//返利金额
			var bpmStatus = datas.find("input[name='rebateStatus']").val();//状态
			var rows = [];
			rows.id = id;
			rows.fridgeCode = fridgeCode;
			rows.purchaseType = purchaseType;
			rows.purchaseTypeName = purchaseTypeName;
			rows.customerName = customerName;
			rows.yearMonth = yearMonth;
			rows.rebateAmount = rebateAmount;
			rows.rebateStatus = bpmStatus;
			$("#ttFridgeMainList").datagrid("insertRow",{row:rows});
			$(this).remove();
		});
		$(".actTable").find("thead").find("input").attr("checked",false);
	}

	//全部删除
    function removeAllUserApply() {
	    debugger;
        //验证
        var trTarget = $(".actTable").find("tbody tr#useApplyTr");
        if(trTarget.length == 0){
            tip("没有数据");
            return false;
        }
        //删除
        $(".actTable").find("tbody tr#useApplyTr").each(function(){
            var datas = $(this);
            var id = datas.find("input[name='id']").val();
            var fridgeCode = datas.find("td[name='fridgeCode']").text();//冰柜编码
            var purchaseType = datas.find("input[name='purchaseType']").val();//购买类型
            var purchaseTypeName = datas.find("input[name='purchaseTypeName']").val();
            var customerName = datas.find("td[name='customerName']").text();//客户名称
            var yearMonth = datas.find("td[name='yearMonth']").text();//年月
            var rebateAmount = datas.find("input[name='amount']").val();//返利金额
            var bpmStatus = datas.find("input[name='rebateStatus']").val();//状态
            var rows = [];
            rows.id = id;
            rows.fridgeCode = fridgeCode;
            rows.purchaseType = purchaseType;
            rows.purchaseTypeName = purchaseTypeName;
            rows.customerName = customerName;
            rows.yearMonth = yearMonth;
            rows.rebateAmount = rebateAmount;
            rows.rebateStatus = bpmStatus;
            $("#ttFridgeMainList").datagrid("insertRow",{row:rows});
            $(this).remove();
        });
        $(".actTable").find("thead").find("input").attr("checked",false);
    }


    function addData(){
		var seletctTarget =  $("#ttFridgeMainList").datagrid("getSelections");
		if(seletctTarget.length == 0){
			tip("请至少选择一条数据");
			return false;
		}
		var selectRows = [];
		var content = "";
		for(var i = 0 ;i<seletctTarget.length;i++){
			var row = seletctTarget[i];
			var bpmS = '';
			if(row.rebateStatus == "0"){
				bpmS = '未返利';
			}else if(row.rebateStatus == "1"){
				bpmS = '部分返利';
			}
			content+="<tr id='useApplyTr'>"+
						"<td>" +
							"<input type='checkbox' name='actTableId'/>" +
							"<input type='hidden' value='"+row.id+"' name='id' id='id'/>" +
						"</td>"+
						"<td name='fridgeCode'>"+row.fridgeCode+"</td>"+
						"<td>"+row.purchaseTypeName+
							"<input type='hidden' value='"+row.purchaseTypeName+"' name='purchaseTypeName'>"+
							"<input type='hidden' value='"+row.purchaseType+"' name='purchaseType'>" +
						"</td>"+
						"<td name='customerName'>"+row.customerName+"<input type='hidden' value='"+row.customerCode+"' name='customerCode'></td>"+
						"<td name='yearMonth'>"+row.yearMonth+"</td>"+
						"<td>"+
                            "<input type='inputxt' onblur='validateAmount(this)' name='amount' id='amount' width='120px' value='"+row.rebateAmount+"'>"+
                            "<input type='hidden' name='oldAmount' value='"+row.rebateAmount+"'>" +
                        "</td>"+

						"<td>"+createPayment()+"</td>"+
						"<td>"+bpmS+
						"<input type='hidden' value='"+row.rebateStatus+"' name='rebateStatus'>"+
						"</td>"+
					 "</tr>";
			selectRows.push(row);
		}
		$(".useApplyDetail").find("tbody").append(content);
		for(var j =0;j<selectRows.length;j++){
		    var rowIndex = $('#ttFridgeMainList').datagrid('getRowIndex',selectRows[j]);
		    $('#ttFridgeMainList').datagrid('deleteRow',rowIndex);
		}
	}
	
	function addAllItem() {
        var name = "ttFridgeMainList";
        var rowsData = $('#' + name).datagrid('getRows');
        if (rowsData.total == 0) {
            tip('没有数据');
            return;
        }
        var queryParams = $('#' + name).datagrid('options').queryParams;
        queryParams.rows = 30;
        $('#' + name + 'tb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});

        $.ajax({
            url:"ttFridgeRebateController.do?findTtFridgeBatchList",
            type:'post',
            data:queryParams,
            cache:false,
            success:function(data) {
                debugger;
                var d = $.parseJSON(data);
                var msg = d.msg;
                if (d.success) {
                    var rows = d.rows;
                    var selectRows = [];
                    var content = "";
                    //添加
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        var bpmS = '';
                        if(row.rebateStatus == "0"){
                            bpmS = '未返利';
                        }else if(row.rebateStatus == "1"){
                            bpmS = '部分返利';
                        }
                        content+="<tr id='useApplyTr'>"+
                            "<td>" +
                            "<input type='checkbox' name='actTableId'/>" +
                            "<input type='hidden' value='"+row.id+"' name='id' id='id'/>" +
                            "</td>"+
                            "<td name='fridgeCode'>"+row.fridgeCode+"</td>"+
                            "<td>"+row.purchaseTypeName+
                            "<input type='hidden' value='"+row.purchaseTypeName+"' name='purchaseTypeName'>"+
                            "<input type='hidden' value='"+row.purchaseType+"' name='purchaseType'>" +
                            "</td>"+
                            "<td name='customerName'>"+row.customerName+"<input type='hidden' value='"+row.customerCode+"' name='customerCode'></td>"+
                            "<td name='yearMonth'>"+row.yearMonth+"</td>"+
                            "<td>"+
                            "<input type='inputxt' onblur='validateAmount(this)' name='amount' id='amount' width='120px' value='"+row.rebateAmount+"'>"+
                            "<input type='hidden' name='oldAmount' value='"+row.rebateAmount+"'>" +
                            "</td>"+

                            "<td>"+createPayment()+"</td>"+
                            "<td>"+bpmS+
                            "<input type='hidden' value='"+row.rebateStatus+"' name='rebateStatus'>"+
                            "</td>"+
                            "</tr>";
                        selectRows.push(row);
                    }
                    $(".useApplyDetail").find("tbody").append(content);
                    for(var j =0;j<selectRows.length;j++){
                        var rowIndex = $('#ttFridgeMainList').datagrid('getRowIndex',selectRows[j].id);
                        $('#ttFridgeMainList').datagrid('deleteRow',rowIndex);
                    }

                } else {
                    tip(msg, "error");
                }
            }
        });


    }

	function validateAmount(obj){
		var reg = /^(([1-9]\d*)|\d)(\.\d{1,2})?$/;
		var val = obj.value;
		if(!reg.test(val)){
			obj.value = "";
		}
	} 
	function createPayment(){
		var paymentCode = '${payment}';
		var content = "<select id='payment' name='payment'>";
			content += "<option value='0'>--请选择--</option>";
		inventoryLocation = JSON.parse(paymentCode);
		if(inventoryLocation.length > 0){
			$.each(inventoryLocation,function(i,v){
				content += "<option value='"+v.dictCode+"'>"+v.dictValue+"</option>";
			});
		}
		content += "</select>";
		return content;
	}
	function validateData(){
	    var cost = $("#costAccountCode").val();
	    if(cost == ""){
            tip("活动细类必选");
            return false;
        }
		var fa = true;
	    var num = [];
        var flag = true;
        var i = 0;
        $(".actTable").find("tbody tr#useApplyTr").each(function(){
            i++;
            var datas = $(this);
            var id = datas.find("input[name='id']").val();
            var payment = datas.find("select[name='payment']").val();
            //需要填写的钱
            var amount = datas.find("input[name='amount']").val();
            var oldAmount = datas.find("input[name='oldAmount']").val();
            if(amount > oldAmount){
                num.push(i);
                fa = false;
            }
            if(payment == "0"){
                flag = false;
            }
        });
        if(!fa){
            tip("第"+num.join(",")+"行填写的金额大于本月返利金额,请检查");
            return false;
		}
        if(!flag){
            tip("请填写金额或者选择支付方式","error");
            return false;
        }
        if(i == 0){
            tip("请勾选要提交的数据","error");
            return false;
        }
        return true;
	}
	function aaa(){
        var result = validateData();
        if(!result){
            return false;
        }
        var json = [];
        var i = 0;
        var flag = true;
        $(".actTable").find("tbody tr#useApplyTr").each(function(){
            i++;
            var datas = $(this);
            var id = datas.find("input[name='id']").val();
            var rebateAmount = datas.find("input[name='amount']").val();//返利金额
            var payment = datas.find("select[name='payment']").val();
            if(rebateAmount == "" || payment == "0"){
                flag = false;
            }
            var obj = {
                'id':id,
                'applyAmount':rebateAmount,
                'paymentCode':payment
            };
            json.push(obj);
        });
        if(!flag){
            tip("请填写金额或者选择支付方式","error");
            return false;
        }
        if(i == 0){
            tip("请勾选要提交的数据","error");
            return false;
        }
        var costAccountCode = $("#costAccountCode").val();
        var costTypeCode = $("#costTypeCode").val();
        $.ajax({
            url:"ttFridgeRebateController.do?saveTtFridge",
            type:"post",
            data:{
                json:JSON.stringify(json),
                costAccountCode:costAccountCode,
                costTypeCode:costTypeCode
            },async:false,
            success:function(data){
                var d = $.parseJSON(data);
                if(d.success){
                    window.parent.window.$("#ttFridgeMainList").datagrid("reload");
                    window.parent.window.tip("提交成功");
                    close();
                }else{
                    tip(d.msg);
                    flag = false;
                }
            }
        });
        return flag;
	}
//	function aaa1(){
//        var result = validateData();
//        if(!result){
//            return false;
//        }
//	 	safeShowDialog({
//			content : "url:ttFridgeRebateController.do?goTtFridgeSubmitForm",
//			lock : true,
//			title : "申请提交",
//			width : 600,
//			height : 300,
//			left :'50%',
//			cache : false,
//			cancel:true,
//			button:[{name:"提交",callback:function(){
//				iframe = this.iframe.contentWindow;
//				var result = iframe.doSubmit();
//				if(!result){
//				    return false;
//				}
//				var json = [];
//				var i = 0;
//				var flag = true;
//				$(".actTable").find("tbody tr#useApplyTr").each(function(){
//					i++;
//					var datas = $(this);
//					var id = datas.find("input[name='id']").val();
//					var rebateAmount = datas.find("input[name='amount']").val();//返利金额
//					var payment = datas.find("select[name='payment']").val();
//					if(rebateAmount == "" || payment == "0"){
//						flag = false;
//					}
//					var obj = {
//								'id':id,
//								'applyAmount':rebateAmount,
//								'paymentCode':payment
//							  };
//					json.push(obj);
//				});
//				if(!flag){
//				    tip("请填写金额或者选择支付方式","error");
//				    return false;
//				}
//				if(i == 0){
//					tip("请勾选要提交的数据","error");
//					return false;
//				}
//				var name = iframe.$("#name").val();
//				var detail = iframe.$("#detail").val();
//				var costAccountCode = $("#costAccountCode").val();
//				$.ajax({
//					url:"ttFridgeRebateController.do?saveTtFridge",
//					type:"post",
//					data:{
//						json:JSON.stringify(json),
//						name:name,
//						costAccountCode:costAccountCode,
//						detail:detail
//					},async:false,
//					success:function(data){
//						var d = $.parseJSON(data);
//						if(d.success){
//							window.parent.window.$("#ttFridgeMainList").datagrid("reload");
//							window.parent.window.tip("提交成功");
//							close();
//						}else{
//							tip(d.msg);
//							flag = false;
//						}
//					}
//				});
//				return flag;
//			}}]
//		});
//
//	}
	function close(){
		frameElement.api.close();
	}

    //弹出选择费用科目
    function openCostAccountSelect(){
        safeShowDialog({
            content : "url:ttCostAccountController.do?goSelectTtCostAccount&actType=fridge_rebate_type",
            lock : true,
            title : "选择费用科目",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tmCostAccountList').datagrid('getSelected');
                if (rowsData == '' || rowsData == null) {
                    iframe.tip("请选择一条数据");
                    return false;
                } else {
                    $("#costAccountCode" ).val(rowsData.accountCode);
                    $("#costAccountName").val(rowsData.accountName);
                    $("#costTypeCode" ).val(rowsData.costTypeCode);
                    $("#costTypeName").val(rowsData.costTypeName);
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>
