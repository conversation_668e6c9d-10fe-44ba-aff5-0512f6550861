<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.biz.eisp.api.util.Globals" %>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>活动细类</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttCostAccountController.do?saveTtCostAccount" refresh="true">
	<input type="hidden" name="id" value="${vo.id }">
	<input type="hidden" name="enableStatus" value="0">
	<div class="form">
			<label class="Validform_label">活动细类编号: </label>
			<input name="accountCode" id="accountCode" datatype="/^[0-9a-zA-Z\,`~·~\<\>，。？\-：；《》【】‘’!！（）“”—……、\?\|\{\}\[\]\\\.\/\(\)]{1,30}$/"
				   class="inputxt" value="${vo.accountCode}"
				   ajaxUrl="ttCostAccountController.do?validateCode&id=${vo.id}"
			/>
			<span style="color: red;">*</span>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">活动细类名称: </label>
		<input ajaxUrl="ttCostAccountController.do?validTtCostAccount&id=${vo.id}"
			   name="accountName" id="accountName" datatype="/^[0-9a-zA-Z\u4e00-\u9fa5,`~·~\<\>,，。？：；《》【】‘’!！\-（）“”—……、\?\|\{\}\[\]\.\(\)]{1,30}$/" 
			   errormsg="只能填写汉字，数字，大小写英文，标点符号"
			   class="inputxt" value="${vo.accountName}" />
			<span style="color: red;">*</span>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">活动方式: </label>
		<t:dictSelect id="actMode" field="actMode" type="select" defaultVal="${vo.actMode}"
			 typeGroupCode="act_mode" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>


	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">是否需要活动申请: </label>
		<select name="isActApply" id="isActApply" class="input">
			<!-- <option value="">--请选择--</option> -->
			<option value="1" <c:if test="${vo.isActApply== 1}">selected="selected"</c:if>>是</option>
			<option value="0" <c:if test="${vo.isActApply== 0}">selected="selected"</c:if>>否</option>
		</select>
		<span style="color: red;">*</span>
	</div>
	
	<!-- 必填 -->
<%-- 	<div class="form">
		<label class="Validform_label">费用归类: </label>
		<t:dictSelect id="costClassify" field="costClassify" type="select" defaultVal="${vo.costClassify}"
			 typeGroupCode="cost_classify"  hasLabel="true" title="费用归类" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div> --%>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">是否核销: </label>
		<t:dictSelect id="hasAudit" field="hasAudit" type="select" defaultVal="${vo.hasAudit}"
			 typeGroupCode="yesorno"  hasLabel="true" title="是否核销" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>

	<!-- 必填 -->
	<div class="form" id="overAuditForm">
		<label class="Validform_label">超额核销比例(%): </label>
		<input name="overAuditScale" id="overAuditScale" 
		       datatype="/^(([1-9]\d*)|\d)(\.\d{1,2})?$/" errormsg="只能输入大于等于0的数字，包括两位小数" class="inputxt" value="${vo.overAuditScale}" />
		<span style="color: red;">*</span>
	</div>

	<%--<div class="form">
		<label class="Validform_label">超额核销控制: </label>
		<t:dictSelect id="hasOverAudit" field="hasOverAudit" type="radio" defaultVal="${vo.hasOverAudit}"
					  typeGroupCode="hasOverAudit" hasLabel="true" title="超额核销控制" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>--%>

	
	<%--<div class="form" id="auditMonthForm">
		<label class="Validform_label">核销有效期(月): </label>
		<input name="auditValidMonth"  errorMsg="核销有效期(月)不能为空,并且值范围为1-12" id="auditValidMonth" datatype="/^([0-9]|1[0-2]|12)$/" class="inputxt" value="${vo.auditValidMonth}" />
		<span style="color: red;">*</span>
	</div>--%>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">金额是否可以为负: </label>
		<t:dictSelect id="hasNegativeAmount" field="hasNegativeAmount" type="select" defaultVal="${vo.hasNegativeAmount}"
			 typeGroupCode="yesorno" hasLabel="true" title="金额是否可以为负" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	
	<div class="form">
		<label class="Validform_label">是否推送SFA: </label>
		<t:dictSelect id="hasPushSfa" field="hasPushSfa" type="select" defaultVal="${vo.hasPushSfa}"
			 typeGroupCode="yesorno" hasLabel="true" title="是否推送SFA" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	
	<!-- 必填 -->
	<%--<div class="form">
		<label class="Validform_label" id="actTypes">系统界面: </label>
		<t:dictSelect  id="actType" dataType="*" field="actType" type="checkbox" defaultVal="${vo.actType}"
			 typeGroupCode="act_type" hasLabel="true" title="系统界面" lineNum="4">
		</t:dictSelect>
	</div>--%>
	
	<!-- 必填 -->
	<div class="form" id="hasAuditClose">
		<label class="Validform_label">是否允许多次核销: </label>
		<t:dictSelect id="hasMultipleAudit" dataType="*" field="hasMultipleAudit" type="radio" defaultVal="${vo.hasMultipleAudit}"
			 typeGroupCode="yesorno" hasLabel="true" title="是否允许多次核销">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">支付方式: </label>
		<t:dictSelect id="paymentModern" dataType="*" field="paymentModern" type="checkbox" defaultVal="${vo.paymentModern}"
			 typeGroupCode="payment_type"  hasLabel="true" title="支付方式" lineNum="5">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	
	<!-- 必填 -->
	<%--<div class="form" id="readioDeploy">
		<label class="Validform_label">活动发布要求: </label>
		<t:dictSelect id="actDeployRequire" field="actDeployRequire" type="checkbox" defaultVal="${vo.actDeployRequire}"
			 typeGroupCode="act_deploy_require" hasLabel="true" title="活动发布要求" dataType="*" >
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>--%>
	
	<div class="form" id="customerType">
		<label class="Validform_label">是否控制门店金额: </label>
		<t:dictSelect id="hasTerminal" field="hasTerminal" type="radio" defaultVal="${vo.hasTerminal}"
			 typeGroupCode="yesorno" hasLabel="true" title="是否控制门店金额" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>
	<div class="form">
		<label class="Validform_label">产品组:</label>
		<input class="inputxt" name="prodGroupName" id="prodGroupName" readonly="readonly"  value="${vo.prodGroupName}">
		<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="cusPro();"></a>
		<a href="#" class="easyui-linkbutton" plain="true" icon="icon-remove" onClick="cusProClear();"></a>
		<input type="hidden" id="prodGroup" name="prodGroup" value='${vo.prodGroup}' >
		<span style="color: red"></span>
	</div>

	<%--<div class="form" id="customerType">
		<label class="Validform_label">是否控制产品金额: </label>
		<t:dictSelect id="hasProduct" field="hasProduct" type="radio" defaultVal="${vo.hasProduct}"
			 typeGroupCode="yesorno" hasLabel="true" title="是否控制产品金额" dataType="*">
		</t:dictSelect>
		<span style="color: red;">*</span>
	</div>	--%>
	
	<%--<div class="form">
		<label class="Validform_label">活动数据采集要求: </label>
		<t:dictSelect id="actDaRequire" field="actDaRequire" type="checkbox" defaultVal="${vo.actDaRequire}"
			 typeGroupCode="act_da_require" hasLabel="true" title="活动数据采集要求" lineNum="5">
		</t:dictSelect>
	</div>--%>
	
	<div class="form" id="auditCheckClose">
		<label class="Validform_label">核销资料: </label>
		<t:dictSelect id="auditMaterialModern" field="auditMaterialModern" type="checkbox" defaultVal="${vo.auditMaterialModern}"
			 dictTable="tt_audit_material"  dictText="material_name" dictField="material_code" hasLabel="true" title="核销资料" lineNum="4">
		</t:dictSelect>
	</div>
	
	<div class="form">
		<label class="Validform_label">备注: </label>
		<textarea rows="5" cols="20" name="remark" id="remark" style="resize:none;">${vo.remark}</textarea>
	</div>
</t:formvalid>
</body>
</html>
<script type="text/javascript">
    var actList = '${vo.actType}'.split(',');
	$(function(){
        changeCustomerType();
		//控制活动发布要求选择权限 免核销仅仅能选择三种
		$("input[name='actDeployRequire']").click(function() {
			var checkeds=	$("input[name='actDeployRequire']:checked");
			if(checkeds.length>0){
				checkeds.each(function (index, domEle) {
					var val=$(domEle).val();
				    $("[name='actDeployRequire']:checkbox[value='"+val+"']").attr("checked","checked");
				    if(Number(val)==40){
				        $("[name='actDeployRequire']:checkbox[value!='"+val+"']").removeAttr("checked");
				    	$('[name="actDeployRequire"]:not([checked])').attr("disabled","disabled");
				    }
				    else if(Number(val)==30){
				    	 $("[name='actDeployRequire']:checkbox[value='"+10+"']").attr("checked","checked");
				    }
				    else if(Number(val)==20){
				    	 $("[name='actDeployRequire']:checkbox[value='"+10+"']").attr("checked","checked");
				    }else{
				    }
				});
			}else{
				$("input[name='actDeployRequire']").removeAttr("disabled");
			}
			changeCustomerType();
		}); 
		
		if($("#hasAudit").val() == "1" || $("#hasAudit").val() == ""){//是
			$("#overAuditForm input,#auditMonthForm input,#hasAuditClose input,#auditCheckClose input").removeAttr("disabled");
		}else{//否
			if($("#overAuditForm input").val() == ""){
				$("#overAuditForm input").val("0");
			}
			if($("#auditMonthForm input").val() == ""){ 
				$("#auditMonthForm input").val("0");
			}
			
			if(!$('[name="hasMultipleAudit"]:checked').length) {
				$('[name="hasMultipleAudit"][value="0"]').attr("checked","checked");
			}
			$("#overAuditForm input,#auditMonthForm input,#hasAuditClose input,#auditCheckClose input").attr("disabled","disabled");
		}

		$("#hasAudit").change(function(){
			var c = $(this).val();
			if(c == '0'){
				//灰化
				$("#overAuditForm input").val("0");
				$("#auditMonthForm input").val("0");
				$('[name="hasMultipleAudit"][value="1"]').removeAttr("checked","checked");
				$('[name="hasMultipleAudit"][value="0"]').attr("checked","checked");
				$("#auditCheckClose").find("input").each(function(){
					$(this).removeAttr("checked");
				});
				$("#overAuditForm input,#auditMonthForm input,#hasAuditClose input,#auditCheckClose input").attr("disabled","disabled");
			}else{
				//展示
				$("#overAuditForm input").val("");
				$("#auditMonthForm input").val("");
				$('[name="hasMultipleAudit"][value="1"]').removeAttr("checked","checked");
				$('[name="hasMultipleAudit"][value="0"]').removeAttr("checked","checked");
				$("#overAuditForm input,#auditMonthForm input,#hasAuditClose input,#auditCheckClose input").removeAttr("disabled");
                $('input[name="actType"]').each(function () {
                    for(var i =0 ;i<actList.length;i++){
                        if($(this).val()==actList[i]){
                            $(this).attr("checked","checked");
						}
                    }
                    $(this).removeAttr("disabled");
                });

            }
		});
	});
function changeCustomerType(){
	$("#readioDeploy").find("input[value='10']").each(function(){
		$(this).attr("id","raidoCheck");
		$(this).click(function(){
			if($("#raidoCheck")[0].checked == true){//选中
				$("#customerType").show();
			}else{
				$(this).removeAttr("checked");
			}
		});
	});
}

    function cusPro(){
        popClick("prodGroup,prodGroupName", "prodGroup,prodGroupName","ttCostAccountController.do?goSelectTtCostAccountPro&accessEntry="+accessEntry,1000,450);
//        popClickSingle("prodGroup", "prodGroupName","prodGroupName","ttCostAccountController.do?goSelectTtCostAccountPro&accessEntry="+accessEntry,"tmCostAccountProList",null,1000,450);
    }
    function cusProClear(){
        $("#prodGroup").val("");
        $("#prodGroupName").val("");
	}
</script>
