<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report001List" fitColumns="false" title="预估损益报表"
                    pagination="false" autoLoadData="false" actionUrl="report001Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName" hidden="true" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" hidden="true" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="项目" field="itemName" sortable="false" width="400"></t:dgCol>
            <t:dgCol title="审批通过金额" field="approvedAmount" width="150" sortable="false"></t:dgCol>
            <t:dgCol title="已申请金额" field="applyAmount" width="150" sortable="false"></t:dgCol>
            <t:dgCol title="未提交金额" field="pendingApplyAmount" width="150" sortable="false"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report001Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report001Listsearch() {
        var orgCode = $("#report001Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report001Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report001List").datagrid('options').queryParams;
        $("#report001Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report001List").datagrid({url:'report001Controller.do?findReportList'});
    }

</script>
