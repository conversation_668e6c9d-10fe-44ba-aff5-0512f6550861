<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="tdOrgTemplatePartChoose"  fitColumns="false" title="模块列表"
                    actionUrl="tdOrgTemplatePartController.do?findTdConfTemplatePartList" idField="id" fit="true"
                    queryMode="group" pageSize="20">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="状态" field="status" width="50" replace="有效_1,失效_0"></t:dgCol>
            <t:dgCol title="模块名称" field="tplPartName" width="120"></t:dgCol>
            <t:dgCol title="模块标签" field="tplPartTag" width="120"></t:dgCol>
            <t:dgCol title="替代标签" field="replaceTag" width="120"></t:dgCol>
            <t:dgCol title="是否显示" field="isShow" replace="不显示_0,显示_1"  width="70"></t:dgCol>
            <t:dgCol title="显示顺序" field="displaySort" width="70"></t:dgCol>
            <t:dgCol title="所属模板" field="tplType" width="120"></t:dgCol>
            <t:dgCol title="图片元素" field="imgElement" width="120"></t:dgCol>
            <t:dgCol title="是否带图片" field="isPhoto" width="80" replace="是_1,否_0"></t:dgCol>
            <t:dgCol title="备注" field="risk" width="200"></t:dgCol>
            <t:dgCol title="模板ID" hidden="true" field="tplId" width="200"></t:dgCol>
        </t:datagrid>

    </div>
</div>

<script>
//    function delTdtemplatePart() {
//        modifyStatus(0);
//    }
//    function undoTdtemplatePart() {
//        modifyStatus(1);
//    }
//
//
//    function modifyStatus(status) {
//        var rows = $("#tdTemplatePartDatagrid").datagrid('getSelections');
//        if(rows && rows.length > 0){
//            var id = rows[0].id;
//            $.ajax({
//                url: "tdTemplateController.do?saveTdTemplatePart",
//                data: {
//                    status: status,
//                    id: id
//                }, success: function (data) {
//                    data = JSON.parse(data);
//                    if (data.success) {
//                        $("#tdTemplatePartDatagrid").datagrid('reload');
//                    }
//                    tip(data.msg);
//                }
//            })
//        }else{
//            tip("请选择一条记录操作")
//        }
//
//    }
//

</script>

