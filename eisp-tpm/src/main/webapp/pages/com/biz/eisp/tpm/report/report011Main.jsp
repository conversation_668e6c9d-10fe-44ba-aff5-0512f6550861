<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report011List" fitColumns="true" title="实际达成报表"
                    pagination="true" autoLoadData="false" actionUrl="report011Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"   sortable="false"></t:dgCol>
            <t:dgCol title="组织" field="orgName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品大类名称" field="productBigName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品系列名称" field="productSeriesName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品编码" field="productCode"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品" field="productName"   sortable="false" query="true"></t:dgCol>
            <t:dgCol title="本期实际销量" field="actualSales"  sortable="false"></t:dgCol>
            <t:dgCol title="本期实际销售额" field="effectiveSales"  sortable="false"></t:dgCol>
            <t:dgCol title="本期必保任务额" field="taskAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="达成率" field="yieldRateStr"  sortable="false"></t:dgCol>
            <t:dgCol title="较任务差异" field="taskDifference"  sortable="false"></t:dgCol>
            <t:dgCol title="去年同期销售额" field="lastYearSales"  sortable="false"></t:dgCol>
            <t:dgCol title="同比增额" field="incrementAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="同比增率" field="uprateStr"  sortable="false"></t:dgCol>
            <t:dgCol title="上期销售额" field="previousSales"  sortable="false"></t:dgCol>
            <t:dgCol title="环比增额" field="increasesAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="环比增率" field="loopUprateStr"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report011Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report011Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report011Listsearch() {
        var orgCode = $("#report011Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report011Listtb_r").find("input[name='yearMonth']").val();


        var queryParams = $("#report011List").datagrid('options').queryParams;
        $("#report011Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report011List").datagrid({url:'report011Controller.do?findReportList'});
    }

</script>
