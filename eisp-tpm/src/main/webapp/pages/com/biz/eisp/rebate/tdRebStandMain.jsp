<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>


<div id="stpro_list" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="dtRebStandList" checkbox="false" fitColumns="false" title="单位奖励标准"
                    actionUrl="rebateStandController.do?findTdRebStandList" idField="id" fit="true" queryMode="group" pageSize="30">
            <t:dgCol title="id" field="id" hidden="true" queryMode="single" width="80"></t:dgCol>
            <t:dgCol title="返利组" field="rebGpCode" queryMode="single" dictionary="rebate_group" query="true" width="120"></t:dgCol>
            <t:dgCol title="标准类型" field="rebGpStand" dictionary="rebate_st_type" queryMode="single" query="true"  width="60" ></t:dgCol>
            <t:dgCol title="标准值" field="rebGpCt"  queryMode="single"  width="70"></t:dgCol>
            <t:dgCol title="有效期起" field="gpStart"  queryMode="group" width="70" query="true" formatter="yyyy-MM-dd HH:mm:ss"  ></t:dgCol>
            <t:dgCol title="有效期止" field="gpEnd" hidden="false" queryMode="single" query="false" formatter="yyyy-MM-dd HH:mm:ss"   width="130"></t:dgCol>
            <t:dgCol title="开启状态" field="valideStr" hidden="false" replace="未开开启时间_0,有效_1,无效_2" queryMode="single" query="false"  width="130"></t:dgCol>
            
            <t:dgCol title="操作" field="opt" width="120"></t:dgCol>
	        <t:dgFunOpt title="<font style=color:blue>明细</font>"  funname="expdetail(id)"  />
            <t:dgToolBar title="新增" operationCode="add" icon="icon-add" url="rebateStandController.do?goRebStandForm" funname="add" height="450" width="700"></t:dgToolBar>
            <t:dgToolBar title="编辑" operationCode="edit" icon="icon-edit" url="rebateStandController.do?goRebStandForm" funname="update" height="450" width="600"></t:dgToolBar>
            <t:dgToolBar title="停用" operationCode="stop" icon="icon-stop" url="rebateStandController.do?parseTdStRebate" funname="parseStand" height="450" width="600"></t:dgToolBar>
            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-dataOut" url="cgDynamicController.do?goModifyDynamicHead&action=add" funname="add" height="450" width="1200"></t:dgToolBar>

        </t:datagrid>

    </div>
    <div data-options="region:'east',
		title:'明细',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
		style="width: 400px; overflow: hidden;">
		<div class="easyui-panel" style="padding: 0;" fit="true" border="false" id="StproDeatil"></div>
	</div>
</div>

<script type="text/javascript">
$(function() {
	var li_east = 0;
});
//权限列表
function expdetail(id) {
	if(li_east == 0){
	   $('#stpro_list').layout('expand','east'); 
	}
	$('#StproDeatil').panel("refresh", "rebateStandController.do?goStProMain&stId=" +id);
}
function parseStand(title,url, gname){
	gridname = gname;
    var ids = [];
    var rows = $("#" + gname).datagrid('getSelections');
    if (rows.length > 0) {
        getSafeJq().dialog.confirm("你确定停用该返利吗?", function(r) {
            if (r) {
                for ( var i = 0; i < rows.length; i++) {
                	if(rows[i].valideStr=="1")
                    ids.push(rows[i].id);
                }
                $.ajax({
                    url : url,
                    type : 'post',
                    data : {
                        ids : ids.join(',')
                    },
                    cache : false,
                	success : function(data) {
                        var d = $.parseJSON(data);
                        var msg = d.msg;
                        if (d.success) {
                            tip(msg);
                            reloadTable();
                            $("#" + gname).datagrid('unselectAll');
                            ids = '';
                        }else{
            				tip(msg);
            				return;
            			}
                    },
                    error:function(){
                    	  tip("客户端请求错误");
                    	  return false;
                    }
                });
            }
        });
    } else {
        tip("请选择需要删除的数据");
    }
}
</script>
