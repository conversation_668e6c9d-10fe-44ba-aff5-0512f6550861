<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<style>
</style>
<div class="easyui-layout" fit="true">
    <div region="center" style="width:400px;">
        <t:formvalid formid="formobj" layout="div" dialog="true" action="ttHqMaterialController.do?saveMaterial" beforeSubmit="convertDetailToForm" refresh="true">
            <input type="hidden" name="id" id="id" value="${vo.id }">
            <input type="hidden" name="orgId" id="orgId" value="${orgId}">
            <input type="hidden" name="curOrgCode" id="curOrgCode" value="${vo.orgCode}">
            <input type="hidden" name="priceJson" id="priceJson">
            <div class="form">
                <label class="Validform_label">事业部: </label>
                <input type="hidden" name="orgCode" id="orgCode" value="${vo.orgCode}" />
                <input type="text" name="orgName" id="orgName" datatype="*" readonly="readonly" value="${vo.orgName}" />
                <span style="color: red;">*</span>
            </div>

            <div class="form">
                <label class="Validform_label">物料名称: </label>
                <input type="hidden" name="materialCode" id="materialCode" value="${vo.materialCode}"/>
                <input type="hidden" name="materialGroup" id="materialGroup" value="${vo.materialGroup}" />
                <input type="text" name="materialName" id="materialName" datatype="*" onmouseover="this.title=this.value" readonly="readonly" value="${vo.materialName}" />
                <span style="color: red;">*</span>
                <c:if test='${type!="edit"}'>
                    <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="materialSelect();"></a>
                </c:if>
            </div>

            <div class="form">
                <label class="Validform_label">开始申报时间: </label>
                <input name="beginDate" id="beginDate" datatype="*" readonly="readonly" class="Wdate" style="width: 150px;"
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\')}',onpicked:function(){$('.Wdate').blur();}})"
                       value='${vo.beginDate}' />
                <span style="color: red;">*</span>
            </div>
            <div class="form">
                <label class="Validform_label">结束申报时间: </label>
                <input name="endDate" id="endDate" datatype="*" readonly="readonly" class="Wdate" style="width: 150px;"
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
                       value='${vo.endDate}' />
                <span style="color: red;">*</span>
            </div>

            <div class="form">
                <label class="Validform_label">最小包装量: </label>
                <input name="minPack" id="minPack" datatype="*" class="inputxt" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')" value="${vo.minPack}" />
                <span style="color: red;">*</span><span>EA</span>
            </div>
            <div class="form">
                <label class="Validform_label">成本价: </label>
                <input name="costPrice" id="costPrice" datatype="*"  class="inputxt" onkeyup="verifyPrice(this)"  onblur="verifyPriceInput(this)" value="${vo.costPrice}" />
                <span style="color: red;">*</span><span>元</span>
            </div>
            <div class="form">
                <label class="Validform_label">物料单价: </label>
                <input name="price" id="price" datatype="*" class="inputxt" onkeyup="verifyPrice(this)"  onblur="verifyPriceInput(this)" value="${vo.price}" />
                <span style="color: red;">*</span><span>元</span>
            </div>

            <div class="form">
                <label class="Validform_label">客户承担费用: </label>
                <input name="bearCost" id="bearCost" datatype="*" class="inputxt" onblur="countPercent(this);" onkeyup="verifyPrice(this)"  onblur="verifyPriceInput(this)" value="${vo.bearCost}" />
                <span style="color: red;">*</span><span>元</span>
            </div>
            <div class="form">
                <label class="Validform_label">客户承担比例: </label>
                <input name="bearScale" id="bearScale" datatype="*" class="inputxt"  onblur="countPrice(this);" onkeyup="verifyNum(this)"  onblur="verifyInput(this)" value="${vo.bearScale}" />
                <span style="color: red;">*</span><span>%</span>
            </div>
            <div class="form">
                <label class="Validform_label">使用说明: </label>
                <textarea rows="3" cols="20" name="description" id="description" class="inputxt" >${vo.description}</textarea>
                    <%--<input name="description" id="description" class="inputxt" value="${vo.description}" />--%>
            </div>

            <div class="form">
                <label class="Validform_label">可申报组织范围: </label>
                <input name="reportableCodeScope" id="reportableCodeScope" type="hidden"  class="inputxt" value="${vo.reportableCodeScope}" />
                <textarea rows="3" cols="20" name="reportableScope" id="reportableScope" readonly="readonly" class="inputxt">${vo.reportableScope}</textarea>
                <%--<input name="reportableScope" id="reportableScope" readonly="readonly" class="inputxt" value="${vo.reportableScope}" />--%>
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="orgAllowSelect();"></a>
                <a href="#" class="easyui-linkbutton" onClick="cleanInput(this)">清空</a>
            </div>

            <div class="form">
                <label class="Validform_label">排除组织范围: </label>
                <input name="unReportableCodeScope" id="unReportableCodeScope" type="hidden" class="inputxt" value="${vo.unReportableCodeScope}" />
                <textarea rows="3" cols="20" name="unReportableScope" id="unReportableScope" readonly="readonly"  class="inputxt">${vo.unReportableScope}</textarea>
                <%--<input name="unReportableScope" id="unReportableScope" readonly="readonly"  class="inputxt" value="${vo.unReportableScope}" />--%>
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="orgUnAllowSelect();"></a>
                <a href="#" class="easyui-linkbutton" onClick="cleanInput(this)">清空</a>
            </div>

            <div class="form">
                <label class="Validform_label">可申报客户范围: </label>
                <input type="hidden" name="reportableCustomerCode" id="reportableCustomerCode" value="${vo.reportableCustomerCode}">
                <textarea rows="3" cols="20" name="reportableCustomerScope" id="reportableCustomerScope" readonly="readonly"  class="inputxt">${vo.reportableCustomerScope}</textarea>
                <%--<input name="reportableCustomerScope" id="reportableCustomerScope" readonly="readonly"  class="inputxt" value="${vo.reportableCustomerScope}" />--%>
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="customerAllowSelect();"></a>
                <a href="#" class="easyui-linkbutton" onClick="cleanInput(this)">清空</a>
            </div>

            <div class="form">
                <label class="Validform_label">排除客户范围: </label>
                <input type="hidden" name="unReportableCustomerCode" id="unReportableCustomerCode" value="${vo.unReportableCustomerCode}">
                <textarea rows="3" cols="20" name="unReportableCustomerScope" id="unReportableCustomerScope" readonly="readonly"   class="inputxt">${vo.unReportableCustomerScope}</textarea>
                <%--<input name="unReportableCustomerScope" id="unReportableCustomerScope" readonly="readonly"   class="inputxt" value="${vo.unReportableCustomerScope}" />--%>
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="customerUnAllowSelect();"></a>
                <a href="#" class="easyui-linkbutton" onClick="cleanInput(this)">清空</a>
            </div>
        </t:formvalid>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
    $(function(){
        $("input[name='beginDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("input[name='endDate']").attr("class","Wdate").attr("style","height:20px;width:150px;").click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });
    //验证物料月份
    function verifiMonth(){
        var id=$("#id").val();
        var beginDate=$("#beginDate").val();
        var endDate=$("#endDate").val();
        var materialCode=$("#materialCode").val();
        var bearCost=$("#bearCost").val();
        //提交数据
        var flag=true;
        $.ajax({
            async : false,
            cache : false,
            data:{id:id,beginDate:beginDate,endDate:endDate,bearCost:bearCost,materialCode:materialCode},
            type : 'POST',
            url : 'ttHqMaterialController.do?checkData',// 请求的action路径
            success : function(data) {
                var d = $.parseJSON(data);
                if (d.success) {
                }else{
                    tip(d.msg,"error");
                    flag= false;
                }
            }
        });
        return flag;
    }

    //清空
    function cleanInput(obj){
        $(obj).siblings().eq(1).val("");
        $(obj).siblings().eq(2).val("");
    }
    //重置
    function resetCost(){
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            tdArr.eq(2).find("input").eq(0).val("");//费用
            tdArr.eq(3).find("input").eq(0).val("");//比例
        });
        $("#priceJson").val("");
    }


    //提交前把列表信息封装到form
    function convertDetailToForm(){
//        var flag=verifiMonth();
//        if(!flag){
//            return false;
//        }
        var rows=[];
        var tr= $(".actTable tbody tr");
        tr.each(function(trindex,tritem) {//遍历每一行
            var tdArr = $(this).children();
            //console.log(tdArr);
            var customerTypeCode = tdArr.eq(0).html();//类型编码
            var customerTypeName =tdArr.eq(1).html();//类型名称
            var bearCost=tdArr.eq(2).find("input").eq(0).val();//承担费用
            var bearScale = tdArr.eq(3).find("input").eq(0).val();//承担比例
            //组装单据
            var obj = {
                'customerTypeCode': customerTypeCode,
                'customerTypeName': customerTypeName,
                'bearScale': bearScale,
                'bearCost':bearCost
            };
            rows.push(obj);
        });
        var jsonStr=JSON.stringify(rows);
        $("#priceJson").val(jsonStr);
    }


    //计算百分比
    function countPercent(obj){
        verifyPriceInput(obj);
        var bearCost=$(obj).val();
        var price=$("#price").val();
        if(price==null||price==""){
            tip("请输入物料单价");
            $(obj).val("");
        }else if(bearCost!=null&&bearCost!=""){
            var a=parseFloat(bearCost);
            var b=parseFloat(price);
            var value=(a/b*100).toFixed(2);
            $("#bearScale").val(value);
        }
    }
    //计算费用
    function countPrice(obj) {
        verifyInput(obj);
        var bearScale=$(obj).val();
        var price=$("#price").val();
        if(price==null||price==""){
            tip("请输入物料单价","error");
            $(obj).val("");
        }else if(bearScale!=null&&bearScale!=""){
            var a=parseFloat(bearScale);
            var b=parseFloat(price);
            var value=(a*b/100).toFixed(5);
            $("#bearCost").val(value);
        }
    }
    //只允许输入两位小数
    function verifyNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }
    //只允许输入5位小数
    function verifyPrice(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }

    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
        if(obj.value!=""){
            obj.value=(Number(obj.value)).toFixed(2);
        }
    }
    function verifyPriceInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
        if(obj.value!=""){
            obj.value=(Number(obj.value)).toFixed(5);
        }
    }

    //选择物料
    function materialSelect(){
        var curOrgCode=$("#curOrgCode").val();
        safeShowDialog({
            content: "url:tdProductApiController.do?goMaterialMain&curOrgCode="+curOrgCode,
            lock: true,
            title: "选择物料",
            width: 800,
            height: 400,
            cache: false,
            ok: function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null) {
                    tip("请选择一条物料信息");
                    return false;
                } else {
                    $("#materialName").val(selected[0].maktx);
                    $("#materialCode").val(selected[0].matnr);
                    $("#materialGroup").val(selected[0].matkl);
                    /*var price=selected[0].price;
                    if(price!=null&&price!=""){
                        $("#costPrice").val((Number(price)).toFixed(2));
                        $("#price").val((Number(price)).toFixed(2));
                    }else{
                        $("#costPrice").val((Number(10.00)).toFixed(2));
                        $("#price").val((Number(10.00)).toFixed(2));
                    }*/
                    return true;
                }
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
    //当前登录人事业部编码
    var curOrgCode=$("#curOrgCode").val();
    //可申报组织范围选择
    function orgAllowSelect(){
        var orgCode=$("#reportableCodeScope").val();
        var paraArr = {
            textName:'orgCode,orgName',
            inputTextName:'orgCode,orgName',
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型--不传或默认
            callBackFun : allowOrgInfo,
            currentOrgCode:curOrgCode,
            pageData:{
                orgCode : orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    //排除组织范围选择
    function orgUnAllowSelect(){
        var orgCode=$("#unReportableCodeScope").val();
        var paraArr = {
            textName:'orgCode,orgName',
            inputTextName:'orgCode,orgName',
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型--不传或默认
            callBackFun : unAllowOrgInfo,
            currentOrgCode:curOrgCode,
            pageData:{
                orgCode : orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    //可申报组织
    function allowOrgInfo(datas){
        var orgCodeStr = "";
        var orgNameStr="";
        for (var i = 0; i < datas.length; i++) {
            var dataObj = datas[i];
            if(orgCodeStr==""){
                orgCodeStr+=dataObj.orgCode;
                orgNameStr+=dataObj.orgName;
            }else{
                orgCodeStr+=","+dataObj.orgCode;
                orgNameStr+=","+dataObj.orgName;
            }
        }
        $("#reportableCodeScope").val(orgCodeStr);
        $("#reportableScope").val(orgNameStr);
    }

    //排除组织
    function unAllowOrgInfo(datas){
        var orgCodeStr = "";
        var orgNameStr="";
        for (var i = 0; i < datas.length; i++) {
            var dataObj = datas[i];
            if(orgCodeStr==""){
                orgCodeStr+=dataObj.orgCode;
                orgNameStr+=dataObj.orgName;
            }else{
                orgCodeStr+=","+dataObj.orgCode;
                orgNameStr+=","+dataObj.orgName;
            }
        }
        $("#unReportableCodeScope").val(orgCodeStr);
        $("#unReportableScope").val(orgNameStr);
    }
    //获取允许客户
    function allowCustomer(datas){
        var allowCustomerCode="";
        var allowCustomerName="";
        for(var i = 0; i < datas.length; i++){
            var dataObj = datas[i];
            if(allowCustomerCode==""){
                allowCustomerCode+=dataObj.customerCode;
                allowCustomerName+=dataObj.customerName;
            }else{
                allowCustomerCode+=","+dataObj.customerCode;
                allowCustomerName+=","+dataObj.customerName;
            }
        }
        $("#reportableCustomerCode").val(allowCustomerCode);
        $("#reportableCustomerScope").val(allowCustomerName);
    }
    //排除客户
    function unAllowCustomer(datas){
        var unAllowCustomerCode="";
        var unAllowCustomerName="";
        for(var i = 0; i < datas.length; i++){
            var dataObj = datas[i];
            if(unAllowCustomerCode==""){
                unAllowCustomerCode+=dataObj.customerCode;
                unAllowCustomerName+=dataObj.customerName;
            }else{
                unAllowCustomerCode+=","+dataObj.customerCode;
                unAllowCustomerName+=","+dataObj.customerName;
            }
        }
        $("#unReportableCustomerCode").val(unAllowCustomerCode);
        $("#unReportableCustomerScope").val(unAllowCustomerName);
    }
    //排除客户选择
    function customerUnAllowSelect(){
        var customerCodes = $("#unReportableCustomerCode").val();;
        var paraArr = {
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型
            isCouldRemove:false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            callBackFun : unAllowCustomer,
            pageData:{
                customerCodes : customerCodes
            }
        }
        openChooseCustomerSelect(paraArr);
    }
    //允许客户选择
    function customerAllowSelect(){
        var customerCodes = $("#reportableCustomerCode").val();;
        var paraArr = {
            searchType:'1',//查询类型？？
            encapsulationType:'return',//封装类型
            isCouldRemove:false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            callBackFun : allowCustomer,
            pageData:{
                customerCodes : customerCodes
            }
        }
        openChooseCustomerSelect(paraArr);
    }
</script>