<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    iframe {
        height: 480px!important;
    }
    label {
        font-size: 15px;
    }
</style>
<%--<div class="easyui-layout" fit="true">--%>
<%@include file="extkeyindicator/showReferenceData.jsp" %>
<div style="clear:both; width: 1200px;height: 200px;">
    <%--<div region="center" style="padding:1px;">--%>
    <t:datagrid name="tsActApplyVoList"
                fitColumns="false"  pagination="false"
                title="活动详细" actionUrl="ttActOutUploadController.do?findActWorkFlowData&businessId=${businessId}"
                idField="id" fit="true" >
        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="businessId" field="businessId" hidden="true"></t:dgCol>
        <t:dgCol title="审批状态" field="bpmStatus" hidden="false"  dictionary="bpm_status"></t:dgCol>
        <t:dgCol title="门头广告编号" field="dadcodes" hidden="false" ></t:dgCol>
        <t:dgCol title="活动编码" field="actCode" hidden="false" ></t:dgCol>
        <t:dgCol title="活动类型" field="actType" hidden="false" dictionary="act_type" ></t:dgCol>
        <t:dgCol title="广告公司编号" field="advCode" hidden="false" ></t:dgCol>
        <t:dgCol title="广告公司名称" field="advName" hidden="false" ></t:dgCol>
        <%--<t:dgCol title="终端网点名" field="terminalName" hidden="false" ></t:dgCol>--%>
        <t:dgCol title="广告发布地址" field="gpsAddress" width="200" ></t:dgCol>
        <t:dgCol title="广告发布地址2（面积+地址）" field="detailRemark" width="200" ></t:dgCol>
        <t:dgCol title="上刊地址（施工地址）" field="constructionAddress" width="200" ></t:dgCol>
        <t:dgCol title="省" field="province" hidden="false" ></t:dgCol>
        <t:dgCol title="市" field="city" hidden="false" ></t:dgCol>
        <t:dgCol title="区县" field="area" hidden="false" ></t:dgCol>
        <t:dgCol title="终端编码" field="terminalCode" hidden="false" ></t:dgCol>
        <t:dgCol title="终端名称" field="terminalName"  width="200" ></t:dgCol>
        <t:dgCol title="店主姓名" field="linkman" hidden="false" ></t:dgCol>
        <t:dgCol title="店主手机" field="liknmanPhone"  width="200" ></t:dgCol>
        <t:dgCol title="创建人" field="createName" hidden="false"></t:dgCol>
        <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
        <t:dgCol title="更新人" field="updateName" hidden="false"></t:dgCol>
        <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
    </t:datagrid>
    <%--</div>--%>

</div>

<div style="clear:both; width: 1200px;height: 400px;">
    <%--<div region="center" style="padding: 1px;">--%>
    <t:datagrid name="trainPictureList" title="图片视频信息"  actionUrl="tsPictureController.do?findTrainPictureList&businessId=${businessId}"
                idField="id" fit="true"  fitColumns="false"  pagination="false">
        <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="bussinessId" field="bussinessId" hidden="true"></t:dgCol>
        <t:dgCol title="类型" field="imgType" dictionary="sfa_photo_type" width="200"></t:dgCol>
        <t:dgCol title="类型描述" field="imgTypeRemark"  width="200"></t:dgCol>
        <t:dgCol title="位置" field="place"  width="200"></t:dgCol>
        <t:dgCol title="缩略图" field="imgPath"  formatterjs="showInfo"   width="200"></t:dgCol>

    </t:datagrid>
    <%--</div>--%>
</div>

<%@include file="extkeyindicator/advMaterialInfo.jsp" %>

<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>


<div id="outerdiv2" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv2" style="position:absolute;">
        <

        <video src="" id="biVideo" style='width: 120px;height:80px;cursor: pointer;controls:controls;' ></video>
    </div>
</div>

<%--</div>--%>
<script>

    //@Override
    function returnShowReferenceDataHtml(objs) {
        var html = "";
    /*    for(var obj in objs){
            if(html != ''){
                html += "\t|\t"
            }
            html += '<label>' + obj + ':<span style="font-size: 15px;color: red;">' + objs[obj] + '</span></label>';
        }*/
        html = '<label>授信积分:<span style="font-size: 15px;">' + objs['授信积分'] + '</span></label>';
        html += '\t<label>&nbsp&nbsp&nbsp&nbsp(可用积分:<span style="font-size: 15px;color: red;">'+objs['可用积分']+'</span>&nbsp&nbsp&nbsp&nbsp\t|\t&nbsp&nbsp&nbsp&nbspCRMS待审核:'+objs['CRMS待审核']+'&nbsp&nbsp&nbsp&nbsp\t|\t&nbsp&nbsp&nbsp&nbsp本系统待审核:'+objs['本系统待审核']+')</label><br>';
        html += '<label>广告基金账户:'+objs['广告基金账户']+'&nbsp&nbsp&nbsp&nbsp\t|\t&nbsp&nbsp&nbsp&nbsp广告门头未报销:'+objs['广告门头未报销']+'&nbsp&nbsp&nbsp&nbsp\t|\t&nbsp&nbsp&nbsp&nbsp本批次可报销:'+objs['本批次可报销']+'</label><br>';
        html += '<label>本单可报:<span style="font-size: 15px;color: red;">'+objs['本单可报']+'</span>&nbsp&nbsp&nbsp&nbsp(本单提报:'+objs['本单提报']+'&nbsp&nbsp&nbsp&nbsp\t|\t&nbsp&nbsp&nbsp&nbsp报销比例:'+objs['报销比例']+'%)\t<span style="font-size: 15px;color: red;">&nbsp&nbsp&nbsp&nbsp注意:该行数据以审核后为准!</span></label>';
        return html;
    }

    function showInfo(value,row) {
        // if(row.imgType==165){
        //     var url="tsPictureController.do?download&id="+row.id;
        //     return "<a href="+url+">点击下载</a>"
        // }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;

            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }



    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }


    function videoBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        $.dialog({
            title: "视频查看",
            content: "url:tsPictureController.do?showVideo&path="+src,
            lock: true,
            width: "680",
            height: "560",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }


    function showBigPic(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $.dialog({
            title: "图片查看",
            content: "url:tsPictureController.do?showBigPic&path="+src,
            lock: true,
            width: "800",
            height: "450",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function getBusinessKey(){
        return '${businessKey}';
    }

    function getFlagKey(){
        return '${flagKey}';
    }

    function getTaskCode(){
        return '${taskCode}';
    }

    function getSrcType(){
        return '${srcType}';
    }

    function ajaxPost(json,url){
        var json;
        $.ajax({
            url:url,
            data:json,
            dataType:'json',
            async:false,
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
</script>
