<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>稽查结果上传</title>
    <t:base type="jquery,easyui,tools"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" dialog="true" layout="div" action="ttAdvOutDoorReportController.do?saveInspectorResult">
    <input id="taskId" name="taskId" type="hidden" value="" />
    <input id="advid" name="advid" type="hidden" value="${advid}" />
    <div class="form">
        <label class="Validform_label" name="actCode">活动编号: </label>
        <input id="actCode" name="actCode"  readonly="readonly" class="inputxt"  value="${actCode}" />
    </div>

    <div class="form">
        <label class="Validform_label" name="adsCode">门头广告编号: </label>
        <input id="adsCode" name="adsCode"  readonly="readonly" class="inputxt"  value="${adsCode}" />
    </div>
    <div class="form">
        <label class="Validform_label" name="rettype">问题归类: </label>
        <select name="rettype" id="rettype">
            <option value ="没有问题">没有问题</option>
            <option value="虚报面积">虚报面积</option>
            <option value="不可抗力">不可抗力</option>
            <option value="重复报销">重复报销</option>
            <option value ="未达保存时间">未达保存时间</option>
            <option value="其他问题">其他问题</option>
        </select>
    </div>
    <div class="form">
        <td align="right"><label class="Validform_label">稽查结果: </label></td>
        <textarea id="result" rows="4" cols="22" name="result" class="dtci_show_advice" datatype="/^[0-9a-zA-Z_\x21-\x7e\u4e00-\u9fa5\s*]{2,128}$/" errormsg="只能填写长度为2~128，字符类型（英文、数字、中文、标点符号、特殊字符）"></textarea>
    </div>
    <div class="form">
        <label class="Validform_label" name="amtgbk">扣回金额: </label>
        <input id="amtgbk" name="amtgbk"   class="inputxt"  datatype="/^(([0-9]+)|([0-9]+\.[0-9]{1,2}))$/" errormsg="只能填写数字，并且小数点后最多两位" />
    </div>
    <div class="form">
        <label class="Validform_label" name="amtpns">处罚金额: </label>
        <input id="amtpns" name="amtpns"   class="inputxt"  datatype="/^(([0-9]+)|([0-9]+\.[0-9]{1,2}))$/" errormsg="只能填写数字，并且小数点后最多两位"/>
    </div>
</t:formvalid>
</body>