<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report003List" fitColumns="false" title="产品费用台账"
                    pagination="true" autoLoadData="true" actionUrl="report003Controller.do?findReportList" idField="id" fit="true">

            <t:dgCol title="基本信息"  field=""  colspan="31"></t:dgCol>
            <t:dgCol title="申请信息"  field=""  colspan="14"></t:dgCol>
            <t:dgCol title="结案信息" field=""   colspan="7"></t:dgCol>
            <t:dgCol title="上账信息" field=""   colspan="2"></t:dgCol>
            <t:dgCol title="反转信息" field=""   colspan="2"></t:dgCol>
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="费用所属年月" field="yearMonth"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="申请日期" field="applyDate"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="批复日期" field="expenseClassification"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"  sortable="false" ></t:dgCol>
            <t:dgCol title="组织" field="orgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程编码" field="processCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="提交主题" field="submitTheme"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="流程名称" field="processName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动编号" field="actCode"  sortable="false" query="true"></t:dgCol>
            <%--<t:dgCol title="活动名称" field="actName"  sortable="false"></t:dgCol>--%>
            <t:dgCol title="政策编码" field="policyCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="政策名称" field="policyName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="随车政策内容" field="carPolicyContent"  sortable="false"></t:dgCol>
            <t:dgCol title="后返政策内容" field="returnPolicyContent"  sortable="false"></t:dgCol>
            <t:dgCol title="随车预算科目编码" field="carFinancialCode"  sortable="false"></t:dgCol>
            <t:dgCol title="随车预算科目" field="carFinancialName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返预算科目编码" field="returnFinancialCode"  sortable="false"></t:dgCol>
            <t:dgCol title="后返预算科目" field="returnFinancialName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动大类编码" field="returnCostTypeCode"  sortable="false"></t:dgCol>
            <t:dgCol title="后返活动大类名称" field="returnCostTypeName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动细类编码" field="returnCostAccountCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="后返活动细类名称" field="returnCostAccountName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="开始时间" field="beginTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结束时间" field="endTime"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户级别" field="customerlevel"  sortable="false" dictionary="cust_type" ></t:dgCol>
            <t:dgCol title="活动品项编码" field="actConditionCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动品项" field="actConditionName"  sortable="false" query="true"></t:dgCol>
            <!-- 申请信息 -->
            <t:dgCol title="原供价(元)" field="originalPrice"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销量1(EA)" field="targetSales1"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额1(元)" field="targetSalesAmount1"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销量2(EA)" field="targetSales2"  sortable="false"></t:dgCol>
            <t:dgCol title="目标销售额2(元)" field="targetSalesAmount2"  sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品" field="relationProductName"  sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品销量（EA）" field="relationProductQuantity"  sortable="false"></t:dgCol>
            <t:dgCol title="关联考核产品销售额（元）" field="relationProductAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="当期费用(元)" field="periodCharges1"  sortable="false"></t:dgCol>
            <t:dgCol title="当期费率" field="currentRate1Str"  sortable="false"></t:dgCol>
            <t:dgCol title="后返费用(元)" field="afterReturnCharges1"  sortable="false"></t:dgCol>
            <t:dgCol title="后返费率" field="afterReturnRate1Str"  sortable="false"></t:dgCol>
            <t:dgCol title="货补产品" field="supplementProducts1"  sortable="false"></t:dgCol>
            <t:dgCol title="备注" field="remark"  sortable="false"></t:dgCol>
            <!-- 结案信息 -->
            <t:dgCol title="实际达成销量" field="actualSales2"  sortable="false"></t:dgCol>
            <t:dgCol title="实际达成销售额（含税）" field="effectiveSales2"  sortable="false"></t:dgCol>
            <t:dgCol title="达成率" field="yieldRate2Str"  sortable="false"></t:dgCol>
            <t:dgCol title="结案当期费用(元)" field="periodCharges2"  sortable="false"></t:dgCol>
            <t:dgCol title="结案当期费率" field="currentRate2Str"  sortable="false"></t:dgCol>
            <t:dgCol title="结案后返费用(元)" field="afterReturnCharges2"  sortable="false"></t:dgCol>
            <t:dgCol title="结案后返费率" field="afterReturnRate2"  sortable="false"></t:dgCol>
            <!-- 上账信息 -->
            <t:dgCol title="上帐金额" field="yesSzAmount"  sortable="false"></t:dgCol>
            <t:dgCol title="未上账金额" field="noSzAmount"  sortable="false"></t:dgCol>
            <!-- 反转信息 -->
            <t:dgCol title="反转时间" field="reversingTime"  sortable="false"></t:dgCol>
            <t:dgCol title="反转金额" field="amount"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report003Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {

        $("#report003Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
        $("#report003Listtb_r").find("input[name='applyDate']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#report003Listtb_r").find("input[name='expenseClassification']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#report003Listtb_r").find("input[name='beginTime']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
        $("#report003Listtb_r").find("input[name='endTime']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});

//        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
//            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report003Listsearch() {
        var orgCode = $("#report003Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report003Listtb_r").find("input[name='yearMonth']").val();


        var queryParams = $("#report003List").datagrid('options').queryParams;
        $("#report003Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report003List").datagrid({url:'report003Controller.do?findReportList'});
    }

</script>
