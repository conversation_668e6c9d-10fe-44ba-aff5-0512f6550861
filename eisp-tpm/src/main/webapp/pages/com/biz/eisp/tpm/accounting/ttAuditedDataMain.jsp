<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttAuditedDataList" checkbox="false" fitColumns="true"
                    singleSelect="false" actionUrl='ttAccountingController.do?findTtAuditedDataList&type=1&paymentCode=${paymentCode}&accountMoneyType=${accountMoneyType}' idField="id" fit="true" queryMode="group">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="结案申请单号" field="billCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案单名称" field="billName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="结案明细编号" field="auditCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动名称" field="actName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="upupOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织" field="upOrgName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织" field="orgName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="客户编码" field="customerCode" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="客户名称" field="customerName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="产品名称" field="productName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="归属事业部" field="businessUnitName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="活动预算科目" field="financialAccountName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动大类" field="costTypeName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动细类" field="costAccountName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="活动开始时间" field="beginDate" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="结案日期" field="auditedDate" sortable="false" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="支付方式" field="paymentCode" sortable="false" query="false" dictionary="accounting_pay_type" hidden="false"></t:dgCol>
            <c:if test="${empty paymentCode}">
                <t:dgCol title="支付方式" field="paymentCode1"  sortable="false" query="true" dictionary="accounting_pay_type" hidden="true"></t:dgCol>
            </c:if>
            <t:dgCol title="货补产品编码" field="premiumProductCode" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="货补产品" field="premiumProductName" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="结案金额（元）" field="auditAmount" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="已上账金额（元）" field="accountingAmount" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="可上账金额（元）" field="unAccountingAmount" sortable="false" query="false"></t:dgCol>
            <t:dgCol title="上账状态" field="accountingStatus" sortable="false" query="true" dictionary="account_status"></t:dgCol>
            <t:dgCol title="最新上账人" field="accountingName" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="最新上账时间" field="accountDate" sortable="false" hidden="true" query="true" queryMode="group" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="最新上账时间" field="accountingDate" sortable="false" ></t:dgCol>
            <t:dgCol title="预算科目编码" field="financialAccountCode" sortable="false" hidden="true" query="false"></t:dgCol>
            <t:dgCol title="费用科目编码" field="costAccountCode" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="ERP编码" field="erpCode" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="活动方式编码" field="actModeCode" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="SAP成本中心编码" field="sapCostCenter" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="财务科目编码" field="financialCode" sortable="false" hidden="true"></t:dgCol>
            <t:dgCol title="是否保存" field="isSave" sortable="false" hidden="false" query="true" dictionary="is_accounting_save"></t:dgCol>
            <c:if test="${empty accountMoneyType}">
                <t:dgCol title="结案金额" field="accountMoneyType1"  sortable="false" query="true" dictionary="money_type" hidden="true"></t:dgCol>
            </c:if>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //日期格式查询条件 开始日期
        $("input[name='beginDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 结束日期
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        $("input[name='endDate']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 上账时间开始
        $("input[name='accountDate_begin']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
        //日期格式查询条件 上账时间开始
        $("input[name='accountDate_end']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM-dd'}); });
    });
    function checkData(){
        var result = true;
        var rows = $('#ttAuditedDataList').datagrid('getSelections');
        var info = "";
        if(rows.length > 0){
            var paymentCode = "";//支付方式
            var type = "";
            for(var i = 0;i<rows.length;i++){
                var pCode = rows[i].paymentCode;
                var auditAmount = rows[i].auditAmount;

                if(type == "" && auditAmount < 0){
                    type = "2";
                }else if(type == "" && auditAmount >= 0){
                    type = "1";
                }else if(type == "1"  && auditAmount < 0){
                    result = false;
                    info ="结案金额必须全部为正或者全部为负";
                    break;
                }else if(type == "2" && auditAmount >= 0){
                    result = false;
                    info ="结案金额必须全部为正或者全部为负";
                    break;
                }
                if(paymentCode == ""){
                    paymentCode = pCode;
                }else if(paymentCode != pCode && type == "1"){
                    result = false;
                    info ="支付方式不统一";
                    break;
                }
            }
        }
        if(!result){
            tip(info,"error");
        }
        return result;
    }
    //添加全部
    function saveAll(){
        var paymentCode = '${paymentCode}';
        var accountMoneyType = '${accountMoneyType}';
        if(paymentCode == ""){
            paymentCode = $("select[name='paymentCode1']").val();
        }
        if(accountMoneyType == ""){
            accountMoneyType = $("select[name='accountMoneyType1']").val();
        }

        if(accountMoneyType == "" || accountMoneyType == null){
            tip("请选择一种结案金额","error");
            return false;
        }
        if(accountMoneyType == "1" && (paymentCode == "" || paymentCode == null)){
            tip("请选择一种支付方式","error");
            return false;
        }
        return true;
    }
    //获取查询参数
    function getQueryParams(){
        var queryParams = $('#ttAuditedDataList').datagrid('options').queryParams;
        $('#ttAuditedDataList').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var paymentCode = '${paymentCode}';
        var accountMoneyType = '${accountMoneyType}';
        if(paymentCode == ""){
            paymentCode = $("select[name='paymentCode1']").val();
        }else{
            queryParams["paymentCode"] = paymentCode;
        }
        if(accountMoneyType == ""){
            accountMoneyType = $("select[name='accountMoneyType1']").val();
        }else{
            queryParams["accountMoneyType"] = accountMoneyType;
        }
        return queryParams;
    }
</script>