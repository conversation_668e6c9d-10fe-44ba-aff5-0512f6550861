<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
	<title>预算记账</title>
	<t:base type="jquery,easyui,tools,DatePicker,handsontable"></t:base>
	<style>
		#sureFilesDetail{display:table;}
		.sureFilesDetail_a{float:left;padding-right:20px;margin-right:10px;position:relative;}
		.sureFilesDetail_a img{position:absolute;top:7px;right:0;}
		#headDiv > #headParamsDiv > div{
			width:30%;
			float: left;
			padding-bottom: 5px;
		}
		#headDiv > #headParamsDiv > div span{
			width: 30%;
		}
		#headDiv > div{
			float: left;
		}
		#headDiv > #hrDiv{
			height:10px;
		}
		#ttActTempleteListtb_r{
			height: 170px;
		}
	</style>
</head>
<div id="ttBudgetDocument" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<div class="datagrid-wrap panel-body">
			<div id="ttActTempleteListtb_r" class="datagrid-toolbar">

				<div class="datagrid-toolbar-but">
					<c:if test="${optype != 2}">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-save" onclick="saveAllDataInfo()">保存</a>
						</span>
					</c:if>
					<span id="customer_span_temp_info" style="float:right">
					</span>
					<div style="clear:both;float: none;height:0;display: block;"></div>
				</div>
				<div class="datagrid-toolbar-search" id="headDiv" style="height: 80px;">
					<div id="headParamsDiv">
						<input type="text" id="id" class="input" hidden="hidden" style="display: none;" value="${vo.id}" />
						<div>
							<label><span style="padding-left:5px; color: red">*</span>公司代码</label>
							<input type="text" id="bukrs" class="input" value="${vo.bukrs}" maxlength="4" onkeyup="this.value=this.value.replace(/[^\d]/g,'');"/>
						</div>
						<div>
							<label><span style="padding-left:5px; color: red">*</span>凭证类型</label>
							<input type="text" id="blart" class="input" readonly="true" value="${vo.blart}"/>
						</div>
						<div>
							<label><span style="padding-left:5px; color: red">*</span>货&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;币</label>
							<t:dictSelect id="waers" field="waers" typeGroupCode="dict_currency_type" defaultVal="${vo.waers}" ></t:dictSelect>
						</div>

						<div>
							<label><span style="padding-left:5px; color: red">*</span>凭证日期</label>
							<input name="bldat" id="bldatStr" readonly="readonly" class="Wdate" style="width: 150px;" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" value="${vo.bldatStr}" />
						</div>
						<div>
							<label><span style="padding-left:5px; color: red">*</span>过账日期</label>
							<input name="budat" id="budatStr" readonly="readonly" class="Wdate" style="width: 150px;" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'%y-%M-%d'})" value="${vo.budatStr}" />
						</div>

						<div>
							<label><span style="padding-left:5px; color: red">*</span>汇&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;率</label>
							<input type="text" id="kursf" class="input" value="${vo.kursf}" maxlength="9" onkeyup="this.value=this.value.replace(/[^\d]/g,'');" />
						</div>
						<div>
							<label><span style="padding-left:5px; color: red">*</span>抬头文本</label>
							<input type="text" id="bktxt" class="input" value="${vo.bktxt}" maxlength="12" />
						</div>



						<div>
							<label><span style="padding-left:5px; color: red">*</span>制单人&nbsp;&nbsp;&nbsp;</label>
							<input type="text" id="xref1Hd" class="input" value="${vo.xref1Hd}" readonly="true" maxlength="9"  />
						</div>
						<div hidden="true">
							<label><span style="padding-left:5px; color: red">*</span>参考码2&nbsp;&nbsp;</label>
							<input type="text" id="xref2Hd" class="input" value="${vo.xref2Hd}" maxlength="9" />
						</div>
						<div>
							<label><span style="padding-left:5px; color: red">*</span>附件页数</label>
							<input type="text" id="attPages" class="input" value="${vo.attPages}" maxlength="9" onkeyup="this.value=this.value.replace(/[^\d]/g,'');" />
						</div>
					</div>
					<hr >
					<div style="padding-top: 5px; width: 100%;" >
						<label><span style="padding-left:5px; color: red">*</span>入账明细：</label>
						<c:if test="${optype != 2}">
							<a href="#" class="easyui-linkbutton" style="float: right;" plain="true" icon="icon-add" onclick="addOneRow()">添加一行</a>
						</c:if>
					</div>
				</div>
			</div>
			<div class="datagrid-view" style="width: 100% ;height: 310px; overflow: hidden;">
				<div id="example"></div>
			</div>
		</div>
	</div>
</div>
<div id="model" hidden="hidden" >
	<table>
		<td title="id" file="id" type="text" readonly="true" hiddenColumn="true" ></td>


	<%--<td title="平台凭证编号" file="startAddress" type="text" ></td>--%>
		<td title="借/贷" file="shkzg" type="text" width="80" hiddenColumn="true" ></td>
		<td title="借/贷" file="shkzgStr" type="dropdown" width="80" source="dictShkzgsArr" ></td>
		<td title="总账科目" file="hkont" type="text" width="80" ></td>
		<td title="往来单位" file="zwldw" type="text" width="80"  ></td>

		<td title="税码" file="mwskz" type="text" width="80" ></td>
		<td title="金额" file="wrbtr" type="numeric" width="80" formatter="0,0.00" ></td>
		<td title="本位币金额" file="dmbtr" type="numeric" width="80" formatter="0,0.00" ></td>

		<td title="税额" file="taxv" type="numeric" width="80" formatter="0,0.00"  ></td>
		<%--<td title="入账方式" file="vehicle" type="text" ></td>--%>
		<td title="行项目文本" file="sgtxt" width="80" type="text" ></td>
		<td title="反记账" file="xnegp" width="80" type="text" ></td>


		<td title="成本中心" file="kostl" width="80" type="text" readonly="true" ></td>
		<td title="销售部门编码" file="abtei" width="80" type="text" ></td>
		<td title="销售部门名称" file="orgName" width="80" type="text"  ></td>


		<td title="原因代码" file="rstgr" type="dropdown"  source="dictRstgrArr" ></td>
		<td title="贸易伙伴" file="vbund" type="text" ></td>
		<td title="订单" file="aufnr" width="80" type="text" ></td>
		<td title="利润中心" file="prctr" type="text" ></td>
		<td title="工作分解结构元素" file="posid" type="text" ></td>
		<td title="凭证状态" file="docst" type="text" readonly="true"></td>
		<td title="凭证生成时间" file="unixts" type="text" readonly="true"></td>



	<%--	<td title="冲销凭证" ffile="startAddress" type="text" ></td>
		<td title="附件页数" file="startAddress" type="text" ></td>
		<td title="凭证接受状态" file="startAddress" type="text" ></td>
	--%></table>
</div>
<script type="text/javascript"  src="resources/api/mdm/js/tmCreateHandsontable.js"></script>
<script>
	var hot;
//    var handsontable = new initThisHandsontable("model","example");//可行
    var handsontable ;
    var dictShkzgsArr ;
    var dictWaersArr;
    var dictRstgrArr;
    var reg_ymd = /^([0-9]{4})(-)([0-9]{1,2})(-)([0-9]{1,2})$/;//验证yyyy-MM-dd格式
	var otherParamsMap = new HashKey();

    var dictShkzgsMap = new HashKey();
	var dictWaersMap = new HashKey();
	var dictRstgrMap = new HashKey();
    $(document).ready(function(){
        initThisPageParamsBeforeHandson();
        handsontable = new initThisHandsontable("model","example");
    });
    var nullableStr="kursf,attPages,orgName,abtei,zwldw,mwskz,dmbtr,taxv,sgtxt,xnegp,kostl,rstgr,vbund,aufnr,prctr,posid,docst,unixts";
    function initThisPageParamsBeforeHandson(){
        if( getOptype() == '2' ){
            $('#model td').attr("readonly","true");
        }
        initDictShkzgs();
        initDictWaers();
        initDictRstgr();
	}

	function initDictRstgr(){
        dictRstgrArr = [];
        <c:forEach items="${dictRstgr}" var="dictRstgr" >
        dictRstgrArr.push('${dictRstgr.value}');
        dictRstgrMap.set('${dictRstgr.value}','${dictRstgr.key}')
        </c:forEach>
	}

	function initDictShkzgs(){
        dictShkzgsArr = [];
        <c:forEach items="${dictShkzgs}" var="dictShkzg" >
        dictShkzgsArr.push('${dictShkzg.value}');
        dictShkzgsMap.set('${dictShkzg.value}','${dictShkzg.key}')
		</c:forEach>
	}

    /**
	 * 加载货币字典
     */
	function  initDictWaers() {
		dictWaersArr=[];
        <c:forEach items="${dictWaers}" var="dictWaers" >
        dictWaersArr.push('${dictWaers.value}');
        dictWaersMap.set('${dictWaers.value}','${dictWaers.key}')
        </c:forEach>
    }

	function getdictShkzgsArr(){
		return dictShkzgsArr;
	}

	function getdictWaersArr(){
        return dictWaersArr;
	}

    function getdictRstgrArr(){
        return dictRstgrArr;
    }

    function getOptype(){
        return '${optype}';
    }

    //@Override
    function afterInitHandsontable(){
        hot = handsontable.getHandsontableObj();
        loadData();
        initTheOtherParamsMap();
	}

    function loadData(){
        if(getOptype() == '1'||getOptype() == '2'){
            setTimeout(loadDataAll,1000);
        }else{
            initAddOneRow();
        }
    }

    function loadDataAll(){
        var id = $('#id').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(id)){
            return ;
        }
        var data = {
            headId : id
        }
        var url = 'ttBudgetDocumentController.do?findTtBudgetDocumentDetail';
        var d = ajaxPost(data,url);
        if(d.success){
            var objs = d.obj;
            if(checkIsNotUndefinedAndNullAndNullValue(objs)){
                hot.loadData(objs);
            }
        }else{
            tip(d.msg);
        }
    }

    //@Override
    function removeRowCallBack(){
    }

    //@Override钩子型回调函数---有新回调函数请修改
    function afterChangeCallBackFun(changesArr, source){
        if(source == 'edit' || source == 'Autofill.fill' ){
            for(var i = 0 ; i < changesArr.length ; i ++ ){
                var changes = changesArr[i];
                //选择执行函数
                switchFunDoIt(changes);
            }
        }
    }
	function beforeChange(changesArr, source){
	}

    //选择执行函数
    function switchFunDoIt(changes){
        //如果改变前和改变后二者相等则不处理
        if(changes[2] == changes[3]){
            return true;
        }
        var falg = false;
        var fildName = changes[1];
        switch (fildName){
            case 'shkzgStr' : falg = changeToTheShkzgCode(changes,"shkzg"); break;//借贷
            case 'waersStr' : falg = changeToTheWaersCode(changes,"waers"); break;//借贷
           // case 'mwskz' : falg = checkTheMwskz(changes);break;//税率
            case 'wrbtr' : falg = checkTheWrbtr(changes);break;//金额 docDate
            case 'bldatStr' : falg = checkTheDate(changes);break;// 凭证日期 pstngDate
            case 'budatStr' : falg = checkTheDate(changes);break;
            default : break;
        }
        return falg;
    }

    function setCostCenter(rowNum,costCenterCode,targFile){
        hot.setDataAtRowProp(rowNum,targFile,costCenterCode);
	}

    function checkTheDate(changes) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
//        var changeOblValue = changes[2];//暂不用--原数据
        var changeValue = changes[3];//变化数据
        if(!checkTheDateIsNullAndReg(changeValue,reg_ymd)){
            checngeTheClassAndTip(changes,'日期格式不正确（yyyy-mm-dd）');
            return false;
        }
        return true;
    }

    //借贷
    function changeToTheShkzgCode(changes,targFile) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
//        var changeOblValue = changes[2];//原数据
        var changeValue = changes[3];//变化数据
        var targValue = '';

        hot.setDataAtRowProp(rowNum,targFile,targValue);
        if(!checkIsNotUndefinedAndNullAndNullValue(changeValue)){
            return false;
        }
        targValue = dictShkzgsMap.get(changeValue);
        hot.setDataAtRowProp(rowNum,targFile,targValue);
    }



    //货币
    function changeToTheWaersCode(changes,targFile) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
        var changeValue = changes[3];//变化数据
        var targValue = '';

        hot.setDataAtRowProp(rowNum,targFile,targValue);
        if(!checkIsNotUndefinedAndNullAndNullValue(changeValue)){
            return false;
        }
        targValue = dictWaersMap.get(changeValue);
        hot.setDataAtRowProp(rowNum,targFile,targValue);
    }

	//税率
    function checkTheMwskz(changes) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
        var changeOblValue = changes[2];//原数据
        var changeValue = changes[3];//变化数据

        if(!checkIsNotUndefinedAndNullAndNullValue(changeValue)){
            return false;
        }

        if(isNaN(Number(changeValue))){
			return false;
		}

        if((changeValue * 100 ) == changeOblValue){
            return false;
        }

		if(Number(changeValue) > 100){
            //改变样式和提示
            checngeTheClassAndTip(changes,'税率不能大于100');
            return false;
		}
        changeValue = Number(changeValue) / 100;
        hot.setDataAtRowProp(rowNum,fildName,Number(changeValue.toFixed(2)));
        changes[3] = changeValue;
        calDmbtr(changes);
    }

	//金额
    function checkTheWrbtr(changes) {
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
//        var changeOblValue = changes[2];//暂不用--原数据
        var changeValue = changes[3];//变化数据
        if(!checkIsNotUndefinedAndNullAndNullValue(changeValue)){
            return false;
        }
        if(isNaN(Number(changeValue))){
            return false;
        }
        //calDmbtr(changes);
    }

    //汇总到报销金额
    function calDmbtr(changes){
        var rowNum = changes[0];//行号
        var fildName = changes[1];//字段名
        var changeValue = changes[3];//变化数据

        var resulfFile = "dmbtr";
        var result = parseFloat(0);//返回结果

        //读取行指定对象值
        var mwskz = 0;
		if(fildName == 'mwskz'){
            mwskz = changeValue;
		}else{
            mwskz = hot.getDataAtRowProp(rowNum,'mwskz')
		}
        if(!checkIsNotUndefinedAndNullAndNullValue(mwskz)){
            return false;
        }
        if(Number(mwskz) > 100){
            //改变样式和提示
            checngeTheClassAndTip(changes,'税率不能大于100');
            return false;
        }

        //读取行指定对象值
        var wrbtr = hot.getDataAtRowProp(rowNum,'wrbtr');
        if(!checkIsNotUndefinedAndNullAndNullValue(wrbtr)){
            return false;
        }

        result = multiply(mwskz,wrbtr);

        hot.setDataAtRowProp(rowNum,resulfFile,Number(result.toFixed(2)));
    }

    //改变样式和提示
    function checngeTheClassAndTip(changes,message) {
        var rowNum = changes[0];//行号
        var col = getCellColNumByRowNumAndFildName(rowNum,changes[1]);//列号
        var cell = hot.getCell(rowNum,col);
        var cellObj = $(cell);
        cellObj.addClass('htInvalid');
//        var seeRowNum = hot.getRowHeader(rowNum);
//        message = '第' + seeRowNum + '行,' + message;
		if(message != ''){
            tip(message);
		}
    }

    //获取列号
    function getCellColNumByRowNumAndFildName(rowNum,fildName){
        var colNum ;//列号
        //读取meta对象
        var metaCells = hot.getCellMetaAtRow(rowNum);
        for(var i = 0 ; i < metaCells.length ; i ++ ){
            var metaCell = metaCells[i];
            //通过对象关键字匹配
            if( metaCell.prop == fildName ){
                colNum = metaCell.col;
                break;
            }
        }
        return colNum;
    }

    //检查数据是否为空和格式正确
    function checkTheDateIsNullAndReg(dateValue,reg){
        dateValue = dateValue + '';
        if(!checkIsNotUndefinedAndNullAndNullValue(dateValue)){
            return false;
        }
        if(!dateValue.match(reg)){
            return false;
        }
        return true;
    }

    <c:if test="${optype != 3}">
		//保存数据
		function saveAllDataInfo(){
			//检查并封装参数
			var data = {}
			if( !batchCheckTheParamsIsNotNull(data) ){
				return false;
			}

			var id = $('#id').val();
			if(!checkIsNotUndefinedAndNullAndNullValue(id)){
				id = '';
			}

			//检查并得到明细数据
			var info = checkAndGetTheDataDetailData();
			if(!checkIsNotUndefinedAndNullAndNullValue(info)){
				return ;
			}

			data["id"] = id;
			data["info"] = JSON.stringify({dataList:info});
			var url = 'ttBudgetDocumentController.do?saveOrUpdateTtBudgetDocumentDate';
			var d = ajaxPost(data,url);
			if(d.success){
				var obj = d.obj;
				if(checkIsNotUndefinedAndNullAndNullValue(obj)){
					//回写总单id
					$('#id').val(obj);
	//                setTimeout(function(){uploadFile(false);},1000);
					//重新加载明细数据
					loadDataAll();
					W.$('#ttBudgetDocumentList').datagrid('reload');
				}else{
					tip("返回的关键值丢失");
				}
			}
			tip(d.msg);
		}
    </c:if>

    //检查不能为空
    function batchCheckTheParamsIsNotNull(data){
        var keyArr = otherParamsMap.returnKey();
        for ( var i = 0 ; i < keyArr.length ; i ++ ){
            var key = keyArr[i];
            var val = $('#' + key ).val();
            if(checkIsNotUndefinedAndNullAndNullValue(val)){
                data[key] = val;
            }else{
                if(nullableStr.indexOf(key)==-1){
                    tip(otherParamsMap.get(key)+"不能为空");
                    return false;
				}
			}
		}
		return true;
	}

	function initTheOtherParamsMap(){
        var errorMsg = "";
        otherParamsMap.set("bukrs","公司代码" + errorMsg);
        otherParamsMap.set("blart","凭证类型" + errorMsg);
        otherParamsMap.set("waers","货币" + errorMsg);
        otherParamsMap.set("bldatStr","凭证日期" + errorMsg);
        otherParamsMap.set("budatStr","过账日期" + errorMsg);
        otherParamsMap.set("bktxt","抬头文本" + errorMsg);
        otherParamsMap.set("kursf","汇率" + errorMsg);
        otherParamsMap.set("xref1Hd","参考码1" + errorMsg);
        otherParamsMap.set("xref2Hd","参考码2" + errorMsg);
        otherParamsMap.set("attPages","附件页数" + errorMsg);
	}


    //检查并得到明细数据
    function checkAndGetTheDataDetailData(){
        var hot = _privateParams.getHotTemp();
        var globMapTemp = _privateParams.getGlobMap();
        var columnsArrKey = _privateParams.getColumnsArrKey();
        var fileKeyAndTitleMapKey = _privateParams.getFileKeyAndTitleMapKey();

        //读取明细数据--所有
        var detailDataObjArr = hot.getSourceData()
        //读取列file数组集合
        var columnsArr = globMapTemp.get(columnsArrKey);
        //读取file与title对应的集合map
        var fileKeyAndTitleMap = globMapTemp.get(fileKeyAndTitleMapKey);

        //循环遍历
        for(var i = 0 ; i < detailDataObjArr.length ; i ++ ){
            //数据对象
            var detailDataObj = detailDataObjArr[i];
            //遍历file字段
            for(var j in columnsArr){
                var file = columnsArr[j];
                //得到对应的对象file对应的值
                var objValue = detailDataObj[file];
                //检查为空
                if(checkIsNotUndefinedAndNullAndNullValue(objValue) ){
                    detailDataObj.pnum = hot.getRowHeader(i);
                }else{
                    if(nullableStr.indexOf(file)==-1){
                        var seeRowNum = hot.getRowHeader(i);
                        var message = '第' + seeRowNum + '行,' + fileKeyAndTitleMap.get(file) + "不能为空!";
                        tip(message);
                        return '';
					}
                }
            }
        }
        return detailDataObjArr;
    }

    function afterOnCellMouseDownCallBackFun(data){
        var rowNum = data[1].row;
        var col = data[1].col;
        //console.log(handsontable);
        var trList = $("#model td")
		if(getOptype()!='2'){
            var tdNum = getCenterCostNum(trList);
            var financialNum = getFinancialNum(trList);

            //判断科目是否是8开头如果是 才给予成本中心弹框
            var hkont = hot.getDataAtRowProp(rowNum,'hkont');


            var flag = false;
            if(hkont==''||hkont==null||hkont==undefined){
                flag = false;
            }else{
                if(hkont.indexOf('8')==0){
                    flag=true;
				}
			}



            if(col==tdNum&&flag){//如果点击的是成本中心列  弹出框
                var targetUrl = "ttBudgetDocumentController.do?goTtCostCenterMain";
                createwindowExt('选择成本中心', targetUrl, '450', '350', {
                    ok: function () {
                        iframe = this.iframe.contentWindow;
                        var seletctTarget = iframe.$('#ttCostCenterList').datagrid('getSelections');
                        if (seletctTarget == null || seletctTarget == "") {
                            //tipChooseData();
                            return false;
                        }
                        var costCenterCode = seletctTarget[0].costCenterCode;
                        var orgCode = seletctTarget[0].orgCode;
                        var orgName = seletctTarget[0].orgName;
                        setCostCenter(rowNum, costCenterCode, 'kostl');
                        setCostCenter(rowNum, orgCode,'abtei');
                        setCostCenter(rowNum, orgName,'orgName');
                        return;
                    }
                });
            }

            if(col==financialNum){//如果点击的是总账科目列  弹出框
                var targetUrl = "ttFinancialAccountController.do?goFinancialAccountMainSelect";
                createwindowExt('选择总账科目', targetUrl, '1000', '450', {
                    ok: function () {
                        iframe = this.iframe.contentWindow;
                        var seletctTarget = iframe.$('#findFinancialSelectList').datagrid('getSelections');
                        if (seletctTarget == null || seletctTarget == "") {
                            //tipChooseData();
                            return false;
                        }
                        var accountCode = seletctTarget[0].accountCode;
                        setCostCenter(rowNum, accountCode, 'hkont');
                        return;
                    }
                });
            }
		}


    }
    function getCenterCostNum(trList){//
        for(var i=0;i<trList.length;i++){
			if($("#model td").eq(i).attr('file')=='kostl'){
			    return i;
			}
        }

	}

    function getFinancialNum(trList){
        for(var i=0;i<trList.length;i++){
            if($("#model td").eq(i).attr('file')=='hkont'){
                return i;
            }
        }
    }

</script>