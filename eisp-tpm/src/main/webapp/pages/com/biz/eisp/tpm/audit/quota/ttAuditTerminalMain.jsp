<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<style>
    .actTable tr td:nth-child(1){display:none;}
    .g-img-popup{
        width:300px;
        height:450px;
        padding: 10px;
        border-radius: 5px;
        background-color: #ccc;
        position: fixed;
        top:10px;
        display: none;
        transition: all 1s;
    }
    ul#ulTemp{
        padding: 20px;
        width:420px;
    }
    ul#ulTemp li{
        width: 100px;
        height: 100px;
        float: left;
        margin: 1px;
    }
</style>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="tmCostAccountMain" class="easyui-layout" fit="true">
    <div region="center" style="width:500px;">
        <input name="auditId" id="auditId" value="${auditId}" type="hidden">
            <t:datagrid name="ttAuditQuotaMainList" fit="true" fitColumns="false" singleSelect="false"
                        title="门店"
                        queryMode="group"
                        actionUrl="ttAuditTerminalController.do?findAuditTerminalList&auditId=${auditId}"
                        idField="id" checkbox="true"
                        autoLoadData="true" onLoadSuccess = "setAllApplyAmountAndQuantity">

                <t:dgCol field="id" title="主键" hidden="true"></t:dgCol>

                <%--<t:dgCol field="bpmStatus" title="审批状态" hidden="true"></t:dgCol>--%>

                <t:dgCol field="terminalName" title="门店名称" query="true" width="120" frozenColumn = "true"></t:dgCol>
                <t:dgCol field="terminalCode" title="门店编码" width="120" query="true"></t:dgCol>

                <t:dgCol field="includeImg" title="是否包含图片"  dictionary = "yesorno" query="true" width="120"></t:dgCol>
                <t:dgCol field="planQuantity" title="门店申请数量" width="120"></t:dgCol>
                <t:dgCol field="totalAmount" title="活动总金额" width="120"></t:dgCol>
                <t:dgCol field="companyAmount" title="门店申请金额" width="120"></t:dgCol>
                <t:dgCol field="applyAuditQuantity" title="门店申请结案数量" editor="{type:'numberbox',options:{precision:2,min:0}}" width="150"></t:dgCol>
                <t:dgCol field="applyAuditAmount" title="门店申请结案金额" editor="{type:'numberbox',options:{precision:2}}" width="150"></t:dgCol>
                <t:dgCol field="auditAmount" title="门店审核结案金额" width="150"></t:dgCol>
                <t:dgCol field="auditQuantity" title="门店审核结案数量" width="150"></t:dgCol>

                <c:if test="${ft == '1'}">
                    <t:dgCol field="displayTypeName" title="陈列类型" width="120"></t:dgCol>
                    <t:dgCol field="standard" title="标准" width="120"></t:dgCol>
                </c:if>

                <t:dgCol field="actPlanAmount" title="申请备注" width="200"></t:dgCol>
                <t:dgCol field="auditRemark"   title="结案备注" width="200" editor="{type:'text'}"></t:dgCol>

                <t:dgToolBar title="添加" icon="icon-log"  url=""  onclick="addData()" width="1200"></t:dgToolBar>
                <t:dgToolBar title="移除" icon="icon-ok" onclick="deleteData()"></t:dgToolBar>
                <t:dgToolBar title="保存" icon="icon-ok" onclick="saveData()"></t:dgToolBar>
            </t:datagrid>
        </div>
    <div data-options="region:'east',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
         style="width: 500px; overflow: hidden;">
        <div class="easyui-panel" style="padding: 1px;" fit="true" border="false" id="showPictrue"></div>
    </div>

</div>
<script type="text/javascript">

    var li_east = 0;
    var editIndex = undefined;
    $(function(){

        $("#tmCostAccountMain").layout("expand","east");
        li_east = 1;
        $("#showPictrue").panel("refresh","ttAuditQuotaPictureController.do?goTerminalPhotos&auditId=${auditId}&ft=${ft}");

        //绑定当行点击事件
        $('#ttAuditQuotaMainList').datagrid({
            onClickRow: function(index,row){
                //if(row.bpmStatus == 1 || row.bpmStatus == 4 ||row.bpmStatus == 5 ){
                    editRow(index,row);
               // }
            },onEndEdit:function(){
                    alert(1);
            }
        });
        $(function () {
            var str="";
            str += '<label title="申请结案数量" >数量:</label><input id="applayAmount" onkeyup="lostFocusEvent(this)" />';
            str += '<label title="申请结案金额" >金额:</label><input id="applayMoney" onkeyup="lostFocusEvent(this)" />';
            str +='<label title="门店审核结案金额汇总" >门店审核结案金额汇总:</label> <span style="color: red" id="allApplyAmount">0</span><label title="门店审核结案数量汇总">门店审核结案数量汇总:</label> <span style="color: red" id="allApplyQuantity">0</span>';

            $("#ttAuditQuotaMainList_toolbar_div").parent().append(str);
            $("#ttAuditQuotaMainList_toolbar_div").remove();
        })
    });
    //失去焦点事件
    function lostFocusEvent(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
        changeValue();
    }
    //批量修改
    function changeValue(){
        var rows = $("#ttAuditQuotaMainList").datagrid("getSelections");
        if(rows.length > 0){
            var applayAmount = $("#applayAmount").val();
            var applayMoney = $("#applayMoney").val();
            for(var i =0;i<rows.length;i++){
                var index=$("#ttAuditQuotaMainList").datagrid("getRowIndex",rows[i]);
                editRow(index,rows[i]);
                if(applayMoney != null && applayMoney != ""){
                    var ed = $("#ttAuditQuotaMainList").datagrid('getEditor', {
                        index : index,
                        field : 'applyAuditAmount'
                    });//获取编辑行
                    rows[i].applyAuditAmount = applayMoney;
                    $(ed.target).numberbox('setValue', applayMoney);
                }
                if(applayAmount != null && applayAmount != ""){
                    var ed = $("#ttAuditQuotaMainList").datagrid('getEditor', {
                        index : index,
                        field : 'applyAuditQuantity'
                    });//获取编辑行
                    rows[i].applyAuditQuantity = applayAmount;
                    $(ed.target).numberbox('setValue', applayAmount);
                }
                endEditing();
            }
        }
    }
    function endEditing(){
        if (editIndex == undefined){return true}
        if ($('#ttAuditQuotaMainList').datagrid('validateRow', editIndex)){
            $('#ttAuditQuotaMainList').datagrid('endEdit', editIndex);
            editIndex = undefined;
            return true;
        } else {
            return false;
        }
    }

    //保存
    function saveData(){
        var rows=$("#ttAuditQuotaMainList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttAuditQuotaMainList").datagrid("getRowIndex",row);
            $("#ttAuditQuotaMainList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttAuditQuotaMainList").datagrid("getChanges","updated");
        for(var i = 0;i<updated.length;i++){
            var cc = $("#ttAuditQuotaMainList").datagrid("getRowIndex",updated[i].id);
            updated[i].index = cc;
        }
        $.ajax({
            url : "ttAuditTerminalController.do?saveAuditTerminal",
            type : 'post',
            data : {terminalJson : JSON.stringify(updated),auditId:'${auditId}'},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    tip(data.msg,"info");
                    $("#ttAuditQuotaMainList").datagrid("reload");
                }else {
                    tip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttAuditQuotaMainList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
       // setAllApplyAmountAndQuantity();
    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttAuditQuotaMainList").datagrid('getColumnFields',true).concat($("#ttAuditQuotaMainList").datagrid('getColumnFields'));
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaMainList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
            var planAmount = row.planAmount;
            if ((fields[i] == "applyAuditAmount"||fields[i] == "applyAuditQuantity"||fields[i] == "auditRemark")){
                //暂时没有确认那些不启动
            }
        }
        $("#ttAuditQuotaMainList").datagrid('beginEdit',index);
        var editors=$("#ttAuditQuotaMainList").datagrid('getEditors',index);
        for(var i=0; i<fields.length; i++){
            var col = $("#ttAuditQuotaMainList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }
    //删除门店
    function deleteData(){
        var rows = $('#ttAuditQuotaMainList').datagrid("getSelections");
        if(rows == null || rows == ""){tip("请至少选择一条数据","error");return false;}
        var id = [];
        for(var i = 0;i<rows.length;i++){
            id.push(rows[i].id);
        }
        getSafeJq().dialog.confirm("你确定删除该数据吗?",
            function(r) {
                if (r) {
                    var ids = id.join(",");
                    var url = "ttAuditTerminalController.do?deleteAuditTerminalList&auditId=${auditId}&ids="+ids;
                    $.ajax({url:url,type:"post",success:function(data){
                        var d = $.parseJSON(data);
                        tip(d.msg,"error");
                        $('#ttAuditQuotaMainList').datagrid("reload");
                    }});
                }
            });
        //setAllApplyAmountAndQuantity();
    }
    //弹出框,添加门店
    function addData(){
        var url = "ttAuditTerminalController.do?goTerminalAddMain&id=${auditId}";
        $.dialog({
            title: "选择列表",
            content: "url:" + url,
            lock: true,
            width: "950",
            height: "500",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var rows = iframe.$("#ttAuditQuotaMainList").datagrid("getSelections");
                if(rows == null || rows.length == 0){
                    iframe.tip("请选择数据","error");
                    return false;
                }
                var id = [];
                for(var i = 0;i<rows.length;i++){
                    id.push(rows[i].id);
                }
                var auditId=$("#auditId").val();
                var url = "ttAuditTerminalController.do?saveActTerminal";
                var result=false;
                 $.ajax({url:url,type:"post",data:{auditId:auditId,ids:id.join(",")},async:false,success:function(data){
                             var d = $.parseJSON(data);
                     result=d.success;
                             if(d.success){
                                 $("#ttAuditQuotaMainList").datagrid("reload");
                             }else{
                                 tip(d.msg);
                             }
                         }
                   });
                 return result;
            },
            cancelVal: '关闭',
            cancel: true
        });
        //setAllApplyAmountAndQuantity();
    }
    function submitForm(iframe){
        //核销子单id
        var auditId = $("#auditId").val();
        //需要保存的json
        var terminalJson = [];
        var trs = $("#contents").find("tr");
        var flag = true;
        $(trs).each(function(index,con){
            //tt_act_terminal的id
            var relationId = $(this).find("input[name='relationId']").val();
            //申请金额
            var applyAuditAmount = $(this).find("input[name='applyAuditAmount']").val();
            //备注
            var auditRemark = $(this).find("input[name='auditRemark']").val();
            //数量
            var planQuantity = $(this).find("td[name='planQuantity']").text();

            var obj = {};
            obj.auditId = auditId;
            obj.relationId = relationId;
            obj.applyAuditAmount = applyAuditAmount;
            obj.auditRemark = auditRemark;
            obj.planQuantity = planQuantity;
            terminalJson.push(obj);
        });
        var url = "ttAuditTerminalController.do?saveAuditTerminal";
        $.ajax({url:url,data:{terminalJson:JSON.stringify(terminalJson)},type:"post",async:false,success:function(data){
            var d = $.parseJSON(data);
            iframe.tip(d.msg,"error");
            if(!d.success){
                flag = false;
            }
        }});
        return flag;
    }

    function verifyInputQuantity(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.-]/g,""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\.-/g,"") //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
    }
    function setAllApplyAmountAndQuantity() {
        var rows = $("#ttAuditQuotaMainList").datagrid("getRows");
        var allApplyAmount = 0;
        var allApplyQuantity = 0;
        for ( var i = 0; i < rows.length; i++) {
            allApplyAmount =allApplyAmount+parseFloat(rows[i].applyAuditAmount);
            allApplyQuantity=allApplyQuantity+parseFloat(rows[i].applyAuditQuantity);
        }
        $("#allApplyAmount").html(allApplyAmount);
        $("#allApplyQuantity").html(allApplyQuantity);
    }
</script>