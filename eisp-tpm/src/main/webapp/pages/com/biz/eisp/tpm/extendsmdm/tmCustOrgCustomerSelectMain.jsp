<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_orgList" class="easyui-layout" fit="true">
<div region="center" style="padding: 1px;">
		<t:datagrid name="custOrgList" title="客户组织管理" actionUrl="tmTerminalExtendMdmController.do?findTmCustOrgCustomerSelectList"
			idField="id" fit="true" fitColumns="false" pagination="false" queryMode="group">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="客户组织编码" field="custOrgCode" query="true"></t:dgCol>
			<t:dgCol title="客户组织名称" field="customerOrgName" query="true"></t:dgCol>
			<t:dgCol title="客户组织类型" field="custOrgType" dictionary="custorg_type"></t:dgCol>
		</t:datagrid>
	</div>

</div>

<script type="text/javascript">
	//保存成功后，触发搜索操作
	function postSubmit(obj) {
		$("#custOrgListtb_r").find("input[name='custOrgCode']").val(
				obj.attributes.custOrgCode);
		$("#custOrgListtb_r").find("input[name='text']").val(
				obj.attributes.customerOrgName);
		custOrgListsearch();
	}
	$(function() {
		var li_east = 0;
	});
	function updateBusiness(title, url, id, width, height) {
		var rowData = $('#' + id).datagrid('getSelected');
		if (rowData) {
			url += '&id=' + rowData.id;
		}
		popClick(obj, name, url, width, height);

	}
	/**
	 * 点击查看关联客户 、关联终端
	 */
	function detail2(title, url, id, width, height) {
		var rowsData = $('#custOrgList').treegrid('getSelections');
		var rowData = $('#' + id).datagrid('getSelected');
		if (!rowsData || rowsData.length == 0) {
			tip('请选择客户组织');
			return;
		}
		if (rowsData.length > 1) {
			tip('请选择一条记录再编辑');
			return;
		}
		if (rowData) {
			url += '&id=' + rowData.id;
		}
		openwindow(title, url, id, 700, 400);
	}
	/**
	 *点击查看业务归属
	 */
	function detail3(title, url, id, width, height) {
		var rowsData = $('#custOrgList').treegrid('getSelections');
		var rowData = $('#' + id).datagrid('getSelected');
		if (!rowsData || rowsData.length == 0) {
			tip('请选择客户组织');
			return;
		}
		if (rowsData.length > 1) {
			tip('请选择一条记录再编辑');
			return;
		}
		if (rowData) {
			url += '&custOrgId=' + rowData.id;
		}
		openwindow(title, url, id, 900, 450);
	}

	//删除客户组织
	function deleteALLSelect3(title, url) {
		var rowsData = $('#custOrgList').treegrid('getSelections');
		var rowData = $('#custOrgList').datagrid('getSelected');
		if (!rowsData || rowsData.length == 0) {
			tip('请选择客户组织');
			return;
		}
		if (rowsData.length > 1) {
			tip('请选择一条记录再编辑');
			return;
		}
		if (rowData.code == '001') {
			tip("根节点不能删除");
			return;
		}
		var ids = [];
		var rows = $('#custOrgList').datagrid('getSelections');
		if (rows.length > 0) {
			$.dialog.confirm('是否删除选中数据？', function(r) {
				if (r) {
					for (var i = 0; i < rows.length; i++) {
						ids.push(rows[i].id);
					}
					$.ajax({
						url : url,
						type : 'post',
						data : {
							ids : ids.join(',')
						},
						cache : false,
						success : function(data) {
							var d = $.parseJSON(data);
							var msg = d.msg;
							if (d.success) {
								tip(msg);
								for (var i = 0; i < rows.length; i++) {
									$("#custOrgList").treegrid('remove',
											rows[i].id);
								}
								ids = '';

							} else {
								tip(msg);
								return;
							}
						}
					});
				}
			});
		} else {
			tip("请选择要删除的数据！");
		}
	}
</script>

