<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="border-left:0px">
        <t:datagrid name="ttBkwAuditList" fit="true" fitColumns="true" singleSelect="true"
                    title="宝库旺费用结案子单"
                    queryMode = "group"
                    actionUrl="ttBkwAuditController.do?findTtBkwAuditList&phoneSend=1&billMainId=${billMainId}"
                    idField="id"
                    autoLoadData="true">

            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="结案主单主键" field="businessKey" hidden="true"></t:dgCol>
            <t:dgCol title="审批状态"  field="bpmStatus" dictionary="bpm_status"></t:dgCol>
            <t:dgCol field="billCode" title="结案申请单号"></t:dgCol>
            <t:dgCol title="子单编码"  field="auditCode" query="true"></t:dgCol>
            <t:dgCol title="流程类型"  field="costTypeCode" dictionary="bpm_status"></t:dgCol>
            <t:dgCol title="活动编号"  field="actCode" query="true"></t:dgCol>

            <t:dgCol title="活动名称"  field="actName" query="true"></t:dgCol>
            <t:dgCol title="客户名称"  field="customerName" query="true"></t:dgCol>
            <t:dgCol title="产品名称"  field="productName" query="true"></t:dgCol>
            <t:dgCol title="活动细类"  field="costAccountName" query="true"></t:dgCol>


            <t:dgCol title="活动开始时间" field="beginDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="活动结束时间" field="endDate" formatter="yyyy-MM-dd"></t:dgCol>

            <t:dgCol title="活动申请金额(元)"  field="planAmount"></t:dgCol>
            <t:dgCol title="超额核销比"  field="overAuditScale" ></t:dgCol>
            <%--<t:dgCol title="活动已结案金额(元)"  field="actAuditedAmount"></t:dgCol>--%>

            <t:dgCol title="本次结案金额(元)(含税)"  field="applyAmount"></t:dgCol>

       <%--     <t:dgCol title="实际结案金额"  field="auditAmount" editor="{type:'numberbox',options:{precision:2,min:0}}"></t:dgCol>--%>
            <t:dgCol title="实际结案金额"  field="auditAmount"></t:dgCol>

            <t:dgCol title="支付方式"  field="paymentName"></t:dgCol>
            <t:dgCol title="结案状态"  field="auditStatus"  dictionary="audit_bkw_status"></t:dgCol>
            <t:dgToolBar title="移除" icon="icon-remove" url="" funname="deleteALLAuditSelect"></t:dgToolBar>

            <%--<t:dgToolBar title="保存" icon="icon-save" onclick="saveBkwAuditForm()" ></t:dgToolBar>--%>
        </t:datagrid>
    </div>

</div>
<script type="text/javascript">
    $(function () {
        //绑定当行点击事件
        $('#ttBkwAuditList').datagrid({
            onClickRow: function(index,row){
                    editRow(index,row);
            }
        });
    })
    //附件上传
    function fileUpload() {
        var auditQuotaList = $("#ttBkwAuditList").datagrid("getSelections");
        if(auditQuotaList == null || auditQuotaList == ""){tip("请至少选择一条数据","error");return false;}
        if(auditQuotaList.lenght > 1){tip("请选择一条数据","error");return false;}
        var bpmStatus = auditQuotaList[0].bpmStatus;
        var id = auditQuotaList[0].id;
        var url = "ttBkwAuditAttachemntController.do?goAuditFileUploadForm&phoneSend=1&id="+id+"&bpmStatus="+bpmStatus;
        openwindow("附件上传", url,"ttBkwAuditList", 600, 400);
    }

    //保存行编辑数据
    function saveBkwAuditForm(){

        var rows=$("#ttBkwAuditList").datagrid("getRows");
        $.each(rows,function (index,row){
            var rowIndex=$("#ttBkwAuditList").datagrid("getRowIndex",row);
            $("#ttBkwAuditList").datagrid('endEdit', rowIndex);
        });
        var updated=$("#ttBkwAuditList").datagrid("getChanges","updated");
        $.ajax({
            url : "ttBkwAuditController.do?saveWorkFlowBkwAuditByRows&phoneSend=1",
            type : 'post',
            data : {saveJsonData : JSON.stringify(updated)},
            dataType:"json",
            success : function(data) {
                if (data.success) {
                    newTip(data.msg,"info");
                    $("#ttBkwAuditList").datagrid("reload");
                }else {
                    newTip(data.msg,"error");
                    $.each(updated,function (index,row){
                        var rowIndex=$("#ttBkwAuditList").datagrid("getRowIndex",row);
                        editRow(rowIndex,row);
                    });
                }
            }
        });
    }
    //开启行编辑
    function editRow(index,row) {
        var fields = $("#ttBkwAuditList").datagrid('getColumnFields',true).concat($("#ttBkwAuditList").datagrid('getColumnFields'));
        for(var i=0; i<fields.length; i++){
            var col = $("#ttBkwAuditList").datagrid('getColumnOption', fields[i]);
            col.editor1 = col.editor;
        }
        $("#ttBkwAuditList").datagrid('beginEdit', index);

       // var editors=$("#ttBkwAuditList").datagrid('getEditors',index);
        for(var i=0; i<fields.length; i++){
            var col = $("#ttBkwAuditList").datagrid('getColumnOption', fields[i]);
            col.editor = col.editor1;
        }
    }
    //删除
    function deleteALLAuditSelect() {
        var ids = [];
        var rows = $("#ttBkwAuditList").datagrid('getSelections');
        if (rows.length > 0) {
            $.messager.confirm("确定","你确定移除该结案明细数据吗？", function(r) {
                if (r) {
                    for ( var i = 0; i < rows.length; i++) {
                        ids.push(rows[i].id);
                    }
                    $.ajax({
                        async : false,
                        url : "ttBkwAuditMainController.do?deleteTtBkwAuditMain",
                        type : 'post',
                        data : {
                            ids : ids.join(',')
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                loadTotal();
                                $("#ttBkwAuditList").datagrid('reload');
                                $("#ttBkwAuditList").datagrid('unselectAll');
                                ids = '';
                                newTip(msg,'info');
                            }else{
                                newTip(msg,'error');
                                return;
                            }
                        }
                    });
                }
            });
        } else {
            newTip("请选择需要删除的数据","error");
        }
    }
</script>