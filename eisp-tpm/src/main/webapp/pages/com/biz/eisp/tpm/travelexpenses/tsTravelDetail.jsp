<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="top_panel">
    <div style="float: left; width: 593px;height: 260px;">
        <t:datagrid name="trainActChecks" title="列车活动检查"
          checkbox="false"  fit="true" actionUrl="tsTravelRecordController.do?findTrainActChecks&userName=${tsTravelApplyVo.userName}&startDate=${tsTravelApplyVo.startDate}&endDate=${tsTravelApplyVo.endDate}" idField="id">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="车次号"  field="trainNumber"></t:dgCol>
            <t:dgCol title="车底号"  field="trainBtNumber"></t:dgCol>
            <t:dgCol title="定位位置"  field="gpsAddress"></t:dgCol>
            <t:dgCol title="创建时间"  field="createDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="审批人"  field="auditFullName"></t:dgCol>
            <t:dgCol title="审批状态"  field="bpmStatus" dictionary="bpm_status"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<div>
    <div style="float: left;width:590px;height: 260px;">
        <t:datagrid name="actChecks" pagination="true" queryMode="group" checkbox="false" title="户外门头检查信息" singleSelect="true" fit="true"
                    actionUrl="tsTravelRecordController.do?findActChecks&userName=${tsTravelApplyVo.userName}&startDate=${tsTravelApplyVo.startDate}&endDate=${tsTravelApplyVo.endDate}" idField="id">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="活动编码" field="actCode"></t:dgCol>
            <t:dgCol title="终端名称" field="terminalName"  ></t:dgCol>
            <t:dgCol title="定位地址" field="gpsAddress"></t:dgCol>
            <%--<t:dgCol title="活动细类" field="costTypeName"></t:dgCol>--%>
            <t:dgCol title="创建时间" field="createDate"  formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="审批状态"  field="bpmStatus" dictionary="bpm_status"></t:dgCol>
            <t:dgCol title="审批人" field="auditFullName"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<div>
    <div style="float: left; width: 1183px;height: 200px;">
        <t:datagrid name="workAttendances"  queryMode="group"  pagination="true" checkbox="false" title="出行考勤" fit="true"
                    actionUrl="tsTravelRecordController.do?findWorkAttendances&userName=${tsTravelApplyVo.userName}&startDate=${tsTravelApplyVo.startDate}&endDate=${tsTravelApplyVo.endDate}" idField="id">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="人员名称" field="fullName"></t:dgCol>
            <t:dgCol title="考勤日期" field="waDate"></t:dgCol>
            <t:dgCol title="考勤年月" field="yearMonth" ></t:dgCol>
            <t:dgCol title="上班状态" field="onAttendanceStatus"  dictionary="onAttendanceStatus"></t:dgCol>
            <t:dgCol title="上班考勤地址" field="onAttendanceAddress"></t:dgCol>
            <t:dgCol title="下班状态" field="offAttendanceStatus"  dictionary="offAttendanceStatus"></t:dgCol>
            <t:dgCol title="上班考勤地址" field="offAttendanceAddress"></t:dgCol>
            <t:dgCol title="出勤时间" field="attendanceTime"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<div id="buttom_pannel" style="float: left; width: 593px;height: 260px;">
    <t:datagrid name="tsVisitDetails"  queryMode="group"  pagination="true" checkbox="false" title="出行拜访" fit="true"
                actionUrl="tsTravelRecordController.do?findTsVisitDetails&userName=${tsTravelApplyVo.userName}&startDate=${tsTravelApplyVo.startDate}&endDate=${tsTravelApplyVo.endDate}" idField="id">
        <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="终端名称" field="customerName"></t:dgCol>
        <t:dgCol title="客户类型" field="customerType"  dictionary="dict_customer_type"></t:dgCol>
        <t:dgCol title="拜访时间" field="visitDate"   formatter="yyyy-MM-dd"></t:dgCol>
        <t:dgCol title="拜访状态" field="visitStatus"  dictionary="visit_status"></t:dgCol>
        <t:dgCol title="创建时间" field="createDate"  formatter="yyyy-MM-dd"></t:dgCol>
        <t:dgCol title="拜访地址" field="realaddress"></t:dgCol>
       </t:datagrid>
</div>
<div>
    <div style="float:left; width: 590px;height: 260px;">
        <t:datagrid name="tsFoemanSituation"  queryMode="group"  pagination="true" checkbox="false" title="敌情异动" fit="true" singleSelect="true"
                    actionUrl="tsTravelRecordController.do?findFoemanSituationList&userName=${tsTravelApplyVo.userName}&startDate=${tsTravelApplyVo.startDate}&endDate=${tsTravelApplyVo.endDate}" idField="id">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="提交人" field="fullName"></t:dgCol>
            <t:dgCol title="接收人" field="receiveFullName"></t:dgCol>
            <t:dgCol title="提交时间" field="createDate" formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="描述" field="describe"></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">

</script>



