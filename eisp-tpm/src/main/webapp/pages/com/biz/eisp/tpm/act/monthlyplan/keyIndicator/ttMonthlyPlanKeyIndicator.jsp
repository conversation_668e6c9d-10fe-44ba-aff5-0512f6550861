<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttMonthlyPlanWorkFlowList" checkbox="false" fitColumns="true" title=""
                    actionUrl="ttMonthlyPlanWorkFlowController.do?findTtMonthlyPlanWorkFlowList&flagKey=${flagKey}"
                    idField="id" fit="true" queryMode="group" pagination="false">
            <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
            <t:dgCol title="事业部必保收入总额（元）" field="protectAmount" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="事业部计划销售总额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="收入计划差异额（元）" field="differenceAmount" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="事业部同比（元）" field="synchronismAmount" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="事业部环比（元）" field="sequential" sortable="false" width="100" ></t:dgCol>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
      /*  //日期格式查询条件 开始日期
        $("input[name='yearMonth']").attr("class","Wdate").click(function(){ WdatePicker({dateFmt:'yyyy-MM'}); });*/
    });
</script>

