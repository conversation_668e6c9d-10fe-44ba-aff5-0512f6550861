<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="top_panel" fit="true" style="height: 90px;">
    <span style="font-size: 16px;color: mediumvioletred;font-family: 微软雅黑;font-weight: bold">
          &nbsp;1、遇到红脸(<img src='resources/img/unUploaded.gif' title='有照片未上传，快到手机APP离线队列看看'/>)不要慌，先"查看照片小视频"(按钮)，都上传了就不用管，确实没上传就找到上传照片的手机，打开APP的离线队列，会自动上传。
    <br />
        &nbsp;2、施工后拍照，必须到现场！照片和小视频必须拍清门头广告编号，请竖屏拍摄。离门头近一些拍，远了看不清楚门头广告编号。
    <br />
        &nbsp;3、门头撤回后，可以重新提交设计稿，无须重新选址，除非该门头选址的照片、小视频有误。
        <br />
        &nbsp;4、剩余制作天数：门头申请从”选址申请提交成功 到 提交完施工后照片“，有效期为2个月，逾期将被系统自动作废，请您及时制作并跟踪到位！
    </span>

</div>
<div id="buttom_pannel" style="clear:both; width: 100%;height: 615px;">
        <t:datagrid name="tsActApplyVoList"
                    fitColumns="true" checkbox="true"
                    title="" actionUrl="ttActOutUploadController.do?findAdvertisementMain"
                    idField="id" fit="true" queryMode="group" singleSelect="false" pageSize="20">
            <t:dgCol title="主键" field="id" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="任务ID" field="wfid" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="流程ID" field="wfProcId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="businessId" field="businessId" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatus" hidden="true" query="true" dictionary="bpm_status" sortable="false" ></t:dgCol>
            <t:dgCol title="删除" field="delMark" hidden="true" query="true" replace="否_0,是_1" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="已作废" field="delMarkStr" hidden="false" query="false"  sortable="false" align="center"></t:dgCol>
            <t:dgCol title="剩余制作天数" field="ldays" hidden="false" query="false"  sortable="false" align="center"></t:dgCol>
            <t:dgCol title="申请创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="调过尺寸?" field="isModify" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="驳回过?" field="reject" hidden="false" query="false" sortable="false" align="center"></t:dgCol>


            <t:dgCol title="当前审批人" field="activityName" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="照片" field="isUpldMark" hidden="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="当前审批人" field="activityNameAux" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="审批状态" field="bpmStatusStr" hidden="false" sortable="false" ></t:dgCol>

            <t:dgCol title="活动单号" field="actCode" hidden="false" query="true" sortable="false" align="center"></t:dgCol>

            <t:dgCol title="门头广告编号" field="dadcodes"  hidden="false" query="false" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="重复制作" field="isRepeat" replace="是_1,-_0" hidden="false" query="true" sortable="false" align="center"></t:dgCol>
            <t:dgCol title="活动类型" field="actType" hidden="true" dictionary="act_type" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告类型" field="advType" hidden="false" dictionary="act_type" query="false" sortable="false" align="center"></t:dgCol>


            <t:dgCol title="图片上传状态" field="isUploaded" hidden="true" sortable="false" ></t:dgCol>
            <t:dgCol title="申请创建人" field="createName" hidden="false" query="true"  ></t:dgCol>
            <t:dgCol title="终端编号" field="terminalCode" hidden="true" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="1-店铺名称" field="terminalName" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="2-店铺老板" field="linkman" hidden="false"  sortable="false" ></t:dgCol>
            <t:dgCol title="3-店铺老板手机" field="linkmanPhone" hidden="false" query="true" sortable="false" ></t:dgCol>
            <t:dgCol title="4-老板手机(招牌上)" field="rLinkPhone" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址1（选址）如非现场，则参考广告发布地址2" field="gpsAddress" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告发布地址2（选址）" field="alternatives" hidden="false"  sortable="false" ></t:dgCol>

            <%--t:dgCol title="省" field="province" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="市" field="city" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="区县" field="area" hidden="false" query="false" sortable="false" ></t:dgCol--%>


            <%--<t:dgCol title="更新人" field="updateName" hidden="false" sortable="false" ></t:dgCol>
            <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司账号" field="mobilephone" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司手机" field="advCode" hidden="false" query="false" sortable="false" ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" hidden="false" query="false" sortable="false" ></t:dgCol>--%>
            <%-- <t:dgCol title="当前任务处理人" field="activityUser" hidden="true" sortable="false" ></t:dgCol>


             <t:dgToolBar title="上传设计稿" icon="icon-upload" onclick="uploadLayout()"></t:dgToolBar>
 --%>
            <t:dgToolBar title="查看材料尺寸" icon="icon-log" onclick="findDetailLayout()"></t:dgToolBar>
            <t:dgToolBar title="查看驳回原因" icon="icon-log" onclick="findRejectReason()"></t:dgToolBar>
            <t:dgToolBar title="恢复（误删除找回）" icon="icon-log" onclick="changeDelMark(0)" ></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频" icon="icon-log" url="tsPictureController.do?findPictureListByHW" funname="queryPic"></t:dgToolBar>
            <t:dgToolBar title="施工后照片自助处理" icon="icon-bcbgrid" url="tsPictureController.do?findPictureListByHW3" funname="querySGPic"></t:dgToolBar>
            <%--<t:dgToolBar title="添加" icon="icon-add" url=""  funname="add"></t:dgToolBar>--%>
            <%--<t:dgToolBar title="编辑" icon="icon-edit" url=""  funname="update"></t:dgToolBar>--%>
            <%--<t:dgToolBar title="删除" icon="icon-remove" onclick=""></t:dgToolBar>--%>

            <t:dgToolBar  icon="icon-bcbundo1"  onclick="reject()" title="撤回门头 — 重新上传施工后效果"></t:dgToolBar>
            <t:dgToolBar title="导出" icon="icon-dataOut" operationCode="dataOut" url="ttActOutUploadController.do?exportExcelReport"  funname="excelExport"></t:dgToolBar>

            <t:dgToolBar  icon="icon-bcbundo"  onclick="rejectToApply(0)" title="撤回门头 — 重新提交设计稿"></t:dgToolBar>
            <t:dgToolBar  icon="icon-bcbdel"  onclick="rejectToApply(1)" title="门头作废"></t:dgToolBar>
            <t:dgToolBar title="查看施工后照片(被驳回后的删除记录)" icon="icon-log" url="tsPictureController.do?findPictureListByHWHis" funname="queryPic"></t:dgToolBar>
            <t:dgToolBar title="查看照片小视频(新)" icon="icon-bcbgrid" url="tsPictureController.do?goPicList" funname="goPostPicture"></t:dgToolBar>
        </t:datagrid>
</div>
<script>

    function goPostPicture(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择编辑项目');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        update(title, url, id, 1250, 700);
    }

    $(function () {
        $("a").each(function () {
            if($(this).hasClass("easyui-linkbutton l-btn l-btn-plain")){
                $(this).removeClass();
                $(this).addClass("easyui-linkbutton l-btn");
            }
        });
    });

    //驳回到待上传施工图
    function reject() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("抱歉：每次只能处理一条");
            return ;
        }
        var current = row[0].activityName;
        var auditor = row[0].bpmStatusStr;
        if(auditor == '审批通过'){
            tip("抱歉：该流程已结束，不能进行撤回操作！");
            return ;
        }
        if(current == null || (current != '7-市场部后审' && current != '市场部（施工审核）')){
            tip("抱歉：该流程还未上传过施工后照片，不能进行撤回操作！");
            return ;
        }
        var taskId = row[0].wfid;//任务id
        var comment = '广告公司自助撤回';//审批意见
        var processInstanceId = row[0].wfProcId;

        var returnThisNode = $("input:checkbox:checked").val();
        var node = 'task1514890882411';

        var formData = {
            taskId : taskId,
            comment : comment,
            processInstanceId : processInstanceId,
            taskDefKey : node,
            returnThisNode:"N"
        };
        $.dialog.confirm('即将撤回流程，您要重新【上传施工后照片】吗？',function(r) {
            if (r) {
                $.ajax({
                    type : "post",
                    data : formData,
                    url : "ttActApplyWFCallBackController.do?saveReject",
                    error : function() {
                    },
                    success : function(data) {
                        var d = $.parseJSON(data);
                        if (d.success) {
                            var msg = d.msg;
                            top.tip(msg);
                            $("#tsActApplyVoList").datagrid("reload");
                        }
                    }
                });
            }
        });
    }

    //流程追回
    function rejectToApply(status) {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("抱歉：每次只能处理一条");
            return ;
        }
        var auditor = row[0].bpmStatusStr;
        if(auditor == '审批通过'){
            tip("抱歉：该流程已结束，不能进行撤回操作！");
            return ;
        }
        var current = row[0].activityName;
        if(current == null){//20190326  增加待上传设计稿状态的作废
            //tip("抱歉：该流程尚未提交设计稿，进行撤回操作毫无意义！");
            $.dialog.confirm('系统：您确定要删除吗?', function(r) {
                var ids = []
                ids.push(row[0].id);
                var thisData = {
                    ids: ids.join(','),
                    status: 1
                }
                var url = "ttActOutUploadController.do?changeDelMark";
                var d = ajaxPost(thisData, url);
                if (d.success) {
                    $("#tsActApplyVoList").datagrid("reload");
                }
                tip(d.msg);
            });
           //return;
        } else {

        var taskId = row[0].wfid;//任务id
        var comment = '广告公司自助撤回';//审批意见
        var processInstanceId = row[0].wfProcId;
        var id = row[0].id;
        var formData = {
            taskId : taskId,
            comment : comment,
            processInstanceId : processInstanceId
        };
        $.dialog.confirm('撤回流程 意味着重新审批，您确定要撤回流程？',function(r) {
            if (r) {
                $.ajax({
                    type : "post",
                    data : formData,
                    url : "ttActApplyWFCallBackController.do?saveRejectToApplyAndDel&id="+id+"&status="+status,
                    error : function() {
                    },
                    success : function(data) {
                        var d = $.parseJSON(data);
                        if (d.success) {
                            var msg = d.msg;
                            top.tip(msg);
                            $("#tsActApplyVoList").datagrid("reload");
                        }
                    }
                });
            }
        });
        }
    }

    function queryPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }

        update(title, url, id, 930, 500);
    }

    function querySGPic(title, url, id, width, height) {
        var rowsData = $('#' + id).datagrid('getSelections');
        if (!rowsData || rowsData.length == 0) {
            tip('请选择一行');
            return;
        }
        if (rowsData.length > 1) {
            tip('请选择一条记录再修改');
            return;
        }
        var current = rowsData[0].activityName;
        var auditor = rowsData[0].bpmStatusStr;
        if(auditor == '审批通过'){
            tip("抱歉：该流程已结束，不允许修改照片内容！");
            return ;
        }
//        if(current == null || (current != '7-市场部后审' && current != '市场部（施工审核）')){
//            tip("抱歉：该流程还未上传过施工后照片，不能进行施工后照片操作！");
//            return ;
//        }
        update(title+" —— 请谨慎操作，删除后的照片无法恢复！！！", url, id, 930, 500);
    }

    function changeDelMark(status){

        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var ids = []
        for(var i = 0;i<row.length;i++){
            ids.push(row[i].id);
            if(row[i].delMark == '0'){
                tip("该流程未打删除标记，没必要做恢复操作！");
                return;
            }
        }

        var thisData = {
            ids : ids.join(','),
            status : status
        }

        var url = "ttActOutUploadController.do?changeDelMark";

        var d = ajaxPost(thisData,url);

        if(d.success){
            $("#tsActApplyVoList").datagrid("reload");
        }
        tip(d.msg);
    }

    function uploadLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var bpmStatus=row[0].bpmStatus;
        if(!(bpmStatus=='10'||bpmStatus=='4'||bpmStatus=='5'||bpmStatus=='6'||bpmStatus=='12')){
            tip("该状态不能上传设计稿!");
            return ;
        }
        var actType=row[0].actType;
        if(actType==undefined||actType==""||actType==null){
            tip("流程状态异常,不能操作!");
            return ;
        }
        gridname="tsActApplyVoList";
        var url = "ttActOutUploadController.do?goActOutUploadForm&id="+row[0].id;
        if(actType=="2"){
            url = "ttActOutUploadController.do?goActOutDoorUploadForm&id="+row[0].id;
        }
        $.dialog({
            title: "设计稿上传",
            content: "url:"+url,
            lock: true,
            width: "1000",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var confirmBtn = $('#btn_sub', iframe.document);
                if(actType=="2"){
                    $.dialog.confirm('公司规定：超20平方/门头将按照20平方核销，是否已知悉？',function(r) {
                        if (r) {
                            confirmBtn.click();
                        }
                    });
                }else{
                    confirmBtn.click();
                }

                return false;
            },
            cancelVal: '关闭',
            cancel: function () {
                $("#tsActApplyVoList").datagrid("reload");
            }
        });
    }
    function findDetailLayout() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goActDetailMain&id="+row[0].id;
        $.dialog({
            title: "门头材料尺寸信息",
            content: "url:"+url,
            lock: true,
            width: "1200",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    function findRejectReason() {
        var row = $("#tsActApplyVoList").datagrid('getSelections');
        if (row.length==null||row.length<1){
            tip("请选择一条记录进行操作");
            return ;
        }
        if (row.length>1){
            tip("只能对单行进行操作");
            return ;
        }
        var url = "ttActOutUploadController.do?goRejectReason&id="+row[0].id+"&actCode="+row[0].actCode;
        $.dialog({
            title: "驳回原因(历史记录)",
            content: "url:"+url,
            lock: true,
            width: "700",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            cancelVal: '关闭',
        });
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }
</script>
