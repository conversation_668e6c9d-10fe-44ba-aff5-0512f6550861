<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_ttBudgetPeriodList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttBudgetPeriodList" title="年度预算管理"  actionUrl="ttBudgetPeriodController.do?findTtBudgetPeriodList" 
	  		 onLoadSuccess="loadTotal" idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group" singleSelect="false">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
  		    <t:dgCol title="费用预算编号" align="center" field="budgetPeriodCode" query="true"></t:dgCol>
  		    <t:dgCol title="年份" query="true" align="center"   field="year"></t:dgCol>
  		    <t:dgCol title="月份" query="true" align="center" field="month"></t:dgCol>
	  		<%--<t:dgCol title="年月" field="yearMonth" hidden="true" query="true"></t:dgCol>--%>
  		    
  		    <%--<t:dgCol title="上上级组织" align="center" field="grandfatherOrgName"></t:dgCol>
  		    <t:dgCol title="上级组织" align="center" field="parentOrgName"></t:dgCol>--%>
			<t:dgCol title="组织编码" align="center" field="orgCode" query="true"></t:dgCol>
  		    <t:dgCol title="组织名称" align="center" field="orgName" ></t:dgCol>
			<t:dgCol title="组织" field="orgNames"  query="true" hidden="true"></t:dgCol>

			<t:dgCol title="预算科目编码" align="center" field="accountCode" query="false"></t:dgCol>
  		    <t:dgCol title="预算科目" align="center" field="accountName" query="true"></t:dgCol>
  		    <t:dgCol title="费用金额(元)" align="center" field="amount" formatterjs="numExtend"></t:dgCol>
  		    
  		    <%--<t:dgCol title="费率%" align="center" field="rate"></t:dgCol>--%>
  		    
	  		<t:dgCol title="创建人" field="createName" query="true"></t:dgCol>
	  	 	<t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		<t:dgCol title="最近更新人" field="updateName"></t:dgCol>
	  		<t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
	  		
	  		  		    
			<t:dgToolBar operationCode="add" title="录入费用预算" icon="icon-add" url="ttBudgetPeriodController.do?goTtBudgetPeriodForm" funname="add"></t:dgToolBar>
			<t:dgToolBar operationCode="edit" title="编辑期初金额" icon="icon-edit" url="" funname="updatePeriodAmount"></t:dgToolBar>
			<t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="ttBudgetPeriodController.do?delTtBudgetPeriod" funname="deleteALLSelect"></t:dgToolBar>
			<t:dgToolBar operationCode="input" title="导入" icon="icon-dataIn" onclick="importDataByXml({impName:'ttBudgetPeriod', gridName:'ttBudgetPeriodList'})"></t:dgToolBar>
			<t:dgToolBar operationCode="out" title="导出" icon="icon-dataOut" url="ttBudgetPeriodController.do?exportXls" funname="excelExport"></t:dgToolBar>
			<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detailLog" width="1200" height="500"></t:dgToolBar>
			<t:dgToolBar operationCode="inputOver"  title="导入覆盖" icon="icon-dataIn" onclick="importDataByXml({impName:'ttBudgetPeriodCover', gridName:'ttBudgetPeriodList'})"></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
//初始化
$(function(){
	$("#ttBudgetPeriodListtb_r").find("input[name='year']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy'});});
	$("#ttBudgetPeriodListtb_r").find("input[name='month']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'MM'});});
	$("input[name='orgId']").combotree({
		url:'tmOrgController.do?getParentOrg',
		width:200
	});

    $("#ttBudgetPeriodList_toolbar_div").parent().append("&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
    $("#ttBudgetPeriodList_toolbar_div").remove();

    $("input[name='orgNames']").attr("readonly",true).attr("id","orgNames").attr("style","width:180px").click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='orgCodes' id='orgCodes'/>");

});

function openOrgSelect(){
    var orgCode = $('#orgCode').val();
    orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
    var currentOrgCode='${currentOrgCode}';
    var paraArr = {
        textName: 'orgCode,orgName',
        inputTextName: 'orgCodes,orgNames',
        searchType: '1',//查询类型？？
        currentOrgCode:currentOrgCode,
        encapsulationType: 'input',//封装类型--不传或默认
        isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
        pageData: {
            orgCode: orgCode
        }
    }
    openChooseOrgSelect(paraArr);
}

function loadTotal(){
    var queryParams = $('#ttBudgetPeriodList').datagrid('options').queryParams;
    $('#ttBudgetPeriodListtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
    var url="ttBudgetPeriodController.do?getTtBudgetPeriodByTotal";
    $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
        var d = $.parseJSON(data);
        if(d.success){
            $("#totalAmount").html("费用总金额："+accounting.formatMoney(d.obj,""));
        }
    }
    });
}

	//编辑期初金额
	function updatePeriodAmount(title, url, id, width, height){
		gridname = id;
		var seletctTarget =  $("#ttBudgetPeriodList").datagrid("getSelections");
		var title = "";
		if(seletctTarget==null || seletctTarget==""){
			tip("必须选择一条数据");
			return;
		}
		var flag = true;
		//校验是否可以编辑
		var url = "ttBudgetPeriodController.do?validateBudgetPeriodAmountIsEdit&id="+seletctTarget[0].id;
		$.ajax({
			url:url,type:"post",async:false,success:function(data){
				var d = $.parseJSON(data);
				if(d.success == false){
					flag = false;
					tip(d.msg);
				}
			}
		});
		if(flag){
			var goUrl = "ttBudgetPeriodController.do?goEditTtBudgetPeriodForm&id="+seletctTarget[0].id;
			add('编辑期初金额',goUrl,gridname,350,300);
		}
	}
	
	//删除
	function deletePeriod(gridname){
		var seletctTarget =  $("#ttBudgetPeriodList").datagrid("getSelections");
		var title = "";
		if(seletctTarget==null || seletctTarget==""){
			tip("必须选择一条数据");
			return;
		}
		var flag = true;
		//校验期初预算是否可以删除
/*		var url = "ttBudgetPeriodController.do?validateBudgetPeriodAmountIsDetele&id="+seletctTarget[0].id;
		$.ajax({
			url:url,type:"post",async:false,success:function(data){
				var d = $.parseJSON(data);
				if(d.success == false){
					flag = false;
					tip(d.msg);
				}
			}
		});*/
		if(flag){
			$.messager.confirm('操作提示','确定删除?',function(r){ 
			    if (r){
			    	$.ajax({
			        	type : "POST",
			        	url : "ttBudgetPeriodController.do?delTtBudgetPeriod&ids="+seletctTarget[0].id,
			        	dataType : "json",
			        	async:false,
			        	cache : false,
			        	success : function(data) {
			        		tip(data.msg);
			        		$("#ttBudgetPeriodList").datagrid("reload");
			        	}
				   });
			    }
			});
		}
	}
</script>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
