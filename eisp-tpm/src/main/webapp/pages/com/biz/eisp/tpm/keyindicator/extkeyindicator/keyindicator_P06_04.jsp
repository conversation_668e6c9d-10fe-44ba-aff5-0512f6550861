<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    iframe {
        height: 480px!important;
    }
    .l-btn-left{
        font-weight: bold;
        color:darkmagenta;
    }
    .datagrid-editable-input{
        border-color: red !important;
    }
</style>

<div style="clear:both; width: 1280px;height: 160px;">
    <t:datagrid name="tsActApplyVoListSub" fitColumns="false"  pagination="false" title=""
                actionUrl="ttActOutUploadController.do?findActWorkFlowData&businessId=${businessId}" idField="id" fit="true" >
        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="businessId" field="businessId" hidden="true"></t:dgCol>
        <t:dgCol title="活动单号" field="actCode" hidden="false" align="center"></t:dgCol>
        <t:dgCol title="活动类型" field="actType" hidden="false" dictionary="act_type" align="center"></t:dgCol>
        <t:dgCol title="制作类型" field="adType" hidden="false" dictionary="ad_type" align="center"></t:dgCol>
        <t:dgCol title="门头位置" field="mtadtype" hidden="false" align="center"></t:dgCol>
        <t:dgCol title="门头广告编号" field="adCode" hidden="false" align="center"></t:dgCol>
        <%--t:dgCol title="门头类别" field="adType" hidden="false" align="center" editor="{type:'selectbox',options:{'01':'新增','02':'替换'}}"></t:dgCol--%>
        <t:dgCol title="门头类别" field="mttype" hidden="false" align="center"></t:dgCol>

        <t:dgCol title="经销商" field="createName" hidden="false" width="135" align="center"></t:dgCol>
        <t:dgCol title="制作前选址地址" field="gpsAddress" width="215" ></t:dgCol>
        <t:dgCol title="选址备注" field="detailRemark" width="187" ></t:dgCol>
        <t:dgCol title="上刊地址（施工地址）" field="constructionAddress" width="210" ></t:dgCol>
        <t:dgCol title="广告公司手机" field="mobilephone" hidden="false" ></t:dgCol>
        <t:dgCol title="广告公司名称" field="advName" hidden="false" ></t:dgCol>
        <t:dgToolBar title="查看照片小视频" icon="icon-search" url="tsPictureController.do?findPictureListByHWV2" funname="queryPic" ></t:dgToolBar>

        <t:dgToolBar title="广告全景地图" icon="icon-search" url="ttActApplyExcuteWorkFlowController.do?goShowTheAllMap" funname="showTheAllMap" ></t:dgToolBar>
        <t:dgToolBar title="查看照片小视频(新)" icon="icon-bcbgrid" url="tsPictureController.do?goPicList" funname="goPostPicture"></t:dgToolBar>

        <t:dgToolBar title="设置《门头类型》为 — 新增门头" icon="icon-bcbgrid" url="tsPictureController.do?setAdType" funname="setAdType1"></t:dgToolBar>
        <t:dgToolBar title="设置《门头类型》为 — 替换门头" icon="icon-bcbgrid" url="tsPictureController.do?setAdType" funname="setAdType2"></t:dgToolBar>
        <t:dgToolBar title="设置《门头位置》为 — 正门头" icon="icon-bcbgrid" url="tsPictureController.do?setMtAdType" funname="setMtAdType1"></t:dgToolBar>
        <t:dgToolBar title="设置《门头位置》为 — 侧门头" icon="icon-bcbgrid" url="tsPictureController.do?setMtAdType" funname="setMtAdType2"></t:dgToolBar>

        <t:dgToolBar title="查看审批意见" icon="icon-bcbgrid" url="ttActOutUploadController.do?goAuditpage" funname="goAuditpage"></t:dgToolBar>

    </t:datagrid>

</div>
<%@include file="showReferenceData.jsp" %>
<div style="clear:both; width: 1280px;height: 170px;">

    <t:datagrid name="advList"  title="" actionUrl="ttActOutUploadController.do?findAdvInfo&actCode=${actCode}"
                idField="id" fit="true"  fitColumns="false"  pagination="false" onLoadSuccess="editRow" >
        <t:dgCol title="id" field="id" hidden="true" ></t:dgCol>
        <t:dgCol title="门头广告编号" field="adCode"  width="120" align="center"></t:dgCol>

        <t:dgCol title="长(m)" field="mlength"  width="50" align="center"></t:dgCol>
        <t:dgCol title="高(m)" field="mwidth"  width="50" align="center"></t:dgCol>
        <t:dgCol title="数量" field="nums"  width="50" align="center"></t:dgCol>
        <t:dgCol title="面积(㎡)" field="mspace"   width="55"  align="center"></t:dgCol>
        <t:dgCol title="单价(元/㎡)" field="money" formatterjs="setMoney"  width="68" align="center"></t:dgCol>
        <t:dgCol title="总金额" field="totalAmount" formatterjs="setMoney"  width="75" align="center"></t:dgCol>
        <t:dgCol title="折扣率(%)" field="discount" formatterjs="setMoney"
                 editor="{type:'numberbox',options:{min:0,precision:2,max:100}}" width="80" align="center"></t:dgCol>


        <t:dgCol title="实报金额" field="realAmount"  editor="{type:'numberbox',options:{min:0,precision:2}}" width="75" align="center" funname="changeValue"></t:dgCol>
        <t:dgCol title="打折备注" field="remark" formatterjs="setMoney" editor="text" width="150" ></t:dgCol>
        <t:dgCol title="材料" field="materialName"  width="110" align="center"></t:dgCol>
        <t:dgCol title="网点编号" field="terminalCode"  width="73" align="center" ></t:dgCol>
        <t:dgCol title="网点名称" field="terminalName"  width="150" align="center" ></t:dgCol>


        <t:dgToolBar title="保存-报销折扣 —— —— —— —— —— —— —— —— ——  —— —— —— —— —— —— —— 别忘保存！！！" icon="icon-save" onclick="saveRealAmount()"></t:dgToolBar>
    </t:datagrid>

</div>

<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>


<div id="outerdiv2" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv2" style="position:absolute;">
        <

        <video src="" id="biVideo" style='width: 120px;height:80px;cursor: pointer;controls:controls;' ></video>
    </div>
</div>
<%--<jsp:include page="keyindicator_P06_04.jsp"></jsp:include>--%>
<%@include file="showSuspiciousOpinions.jsp" %>

<%--</div>--%>

<%@include file="keyindicator_P06_init_js.jsp" %>
<script>
//    function resizeWord(value,row,index){
//        return '<div style=\"width=250px;word-break:break-all; word-wrap:break-word;white-space:pre-wrap;\">'+value+'</div>';
//    }
function goPostPicture(title, url, id, width, height) {
    $('#picISReaded', window.parent.document).attr("value",1);
    var rowsData = $('#' + id).datagrid('getRows');
    url += '&id=' + rowsData[0].id;
    $.dialog({
        title: "照片小视频",
        content: "url:" + url,
        lock: true,
        width: "1250",
        height: "700",
        zIndex: 100000,
        parent: windowapi,
        cancelVal: '关闭',
        cancel: true
    });
}

function goAuditpage(title, url, id, width, height) {

    var processInstanceId =  window.parent.document.getElementById("processInstanceId").value;
    var rowsData = $('#' + id).datagrid('getRows');
    url += '&id=' + processInstanceId;
    $.dialog({
        title: "查看审批意见",
        content: "url:" + url,
        lock: true,
        width: "1250",
        height: "450",
        zIndex: 100000,
        parent: windowapi,
        cancelVal: '关闭',
        cancel: true
    });
}


//开始
function setAdType1(title, url, id, width, height){

    var rowsData = $('#' + id).datagrid('getSelections');
    if (!rowsData || rowsData.length == 0) {
        tip('请选择一个门头');
        return;
    }
    if (rowsData.length > 1) {
        tip('每次只能处理一个门头');
        return;
    }
    var thisData = {
        id : rowsData[0].adCode,
        type : "新增门头"
    }
    var d = ajaxPost(thisData, url);
    if(d.success){
        $("#" + id).datagrid('reload');
        flg = true;
    }
    newTip(d.msg);
    return flg;
}

//开始
function setMtAdType1(title, url, id, width, height){

    var rowsData = $('#' + id).datagrid('getSelections');
    if (!rowsData || rowsData.length == 0) {
        tip('请选择一个门头');
        return;
    }
    if (rowsData.length > 1) {
        tip('每次只能处理一个门头');
        return;
    }
    var thisData = {
        id : rowsData[0].adCode,
        type : "正门头"
    }
    var d = ajaxPost(thisData, url);
    if(d.success){
        $("#" + id).datagrid('reload');
        flg = true;
    }
    newTip(d.msg);
    return flg;
}

//开始
function setMtAdType2(title, url, id, width, height){
    var rowsData = $('#' + id).datagrid('getSelections');
    if (!rowsData || rowsData.length == 0) {
        tip('请选择一个门头');
        return;
    }
    if (rowsData.length > 1) {
        tip('每次只能处理一个门头');
        return;
    }

    var thisData = {
        id : rowsData[0].adCode,
        type : "侧门头"
    }
    var d = ajaxPost(thisData, url);
    if(d.success){
        $("#" + id).datagrid('reload');
        flg = true;
    }
    newTip(d.msg);
    return flg;
}

//开始
function setAdType2(title, url, id, width, height){
    var rowsData = $('#' + id).datagrid('getSelections');
    if (!rowsData || rowsData.length == 0) {
        tip('请选择一个门头');
        return;
    }
    if (rowsData.length > 1) {
        tip('每次只能处理一个门头');
        return;
    }

    var thisData = {
        id : rowsData[0].adCode,
        type : "替换门头"
    }
    var d = ajaxPost(thisData, url);
    if(d.success){
        $("#" + id).datagrid('reload');
        flg = true;
    }
    newTip(d.msg);
    return flg;
}

function goPostPicture_BK(title, url, id, width, height) {
    var rowsData = $('#' + id).datagrid('getSelections');
    if (!rowsData || rowsData.length == 0) {
        tip('请选择编辑项目');
        return;
    }
    if (rowsData.length > 1) {
        tip('请选择一条记录再修改');
        return;
    }
    update(title, url, id, 1250, 700);
}

$(function () {
    $('#picISReaded', window.parent.document).attr("value",0);
    $('#haveSaved', window.parent.document).attr("value",0);
});
function queryPic(title, url, id, width, height) {
    $('#picISReaded', window.parent.document).attr("value",1);
    var rowsData = $('#' + id).datagrid('getRows');
    url += '&id=' + rowsData[0].id;
    $.dialog({
        title: "照片小视频",
        content: "url:" + url,
        lock: true,
        width: "900",
        height: "500",
        zIndex: 100000,
        parent: windowapi,
        cancelVal: '关闭',
        cancel: true
    });
}

    function showInfo(value,row) {
        // if(row.imgType==165){
        //     var url="tsPictureController.do?download&id="+row.id;
        //     return "<a href="+url+">点击下载</a>"
        // }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;

            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }

    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            newTip("图片不存在!");
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }

    function showTheAllMap(title, url, gname, callback){
        var thisData = {
            flagKey : getFlagKey(),
            businessKey : getBusinessKey()
        }

        var url = url + changeDataToUrlData(thisData) ;

        $.dialog({
            title: "全景地图",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    //重写--Override
    function getTitle(){
        return '是否疑似造假';
    }

    function setMoney(value) {
        if(value==''||value==null){
            return 0.0;
        }else{
            return value;
        }

    }

    function editRow(rowData) {
//        strWritTotalAmountSpanShow("***");
        var rows = rowData.rows;
        if(!(rows != null && rows.length > 0)){
            return ;
        }
        var gridObj = $("#advList");
        $.each(rows,function (index,row){
            gridObj.datagrid('endEdit', index);
            gridObj.datagrid('beginEdit', index);
            //总金额
            var totalAmount = row.totalAmount;
            //折扣率
            var discountEd = getEditorObj(gridObj,index,"discount");
            var realAmountEd = getEditorObj(gridObj,index,"realAmount");
            //标记不可操作
            changeEditorIsDoNotEdit(realAmountEd);

            //计算实际金额
            discountEd.target.bind('blur', function () {
                var discount = returnNotNullNumber(getEditorValue(gridObj,index,"discount"));
                //计算实际金额 = 总金额 * 折扣
                var total = (parseFloat(totalAmount) * parseFloat(discount) / 100 ).toFixed(2);
                $(realAmountEd.target).numberbox('setValue',Number(total));
            });
        });
    }

    //保存计算后金额
    function saveRealAmount() {

        var obj = $('#advList');
        var rows = obj.datagrid('getRows');
        var list = [];
        var falgTemp = true;
        for (var i = 0; i < rows.length; i++) {
            var index = obj.datagrid('getRowIndex', rows[i]);
            // 获取输入框编辑器的值---
            var discount = getEditorValue(obj,index,'discount');
            var realAmount = getEditorValue(obj,index,'realAmount');
            var remark = getEditorValue(obj,index,'remark');
            if (discount != '' || realAmount != '') {
                var pnum = Number(index) + 1;
                var numStr = "第" + pnum + "行，";
                if (!checkIsOk(discount,numStr + "折扣率",'')
                    || !checkIsOk(realAmount,numStr + "实际金额",'')) {
                    falgTemp = false;
                }
                if(!checkRemarkIsOk(discount,remark,numStr)){
                    falgTemp = false;
                }
                if (falgTemp) {
                    var objTemp = {};
                    objTemp.pnum = pnum;
                    objTemp.id =  rows[i].id;
                    objTemp.discount = discount;
                    objTemp.realAmount = realAmount;
                    objTemp.remark = remark;
                    list.push(objTemp);
                }
            }
            if (!falgTemp) {
                return ;
            }
        }
        startSaveRealAmount(list,obj);
    }

    function checkRemarkIsOk(discount,remark,numStr){
        if($.trim(discount ).length != 0){
            var disNum = Number(discount);
            if(disNum<100&&$.trim(remark ).length <= 0){
                newTip(numStr + "折扣小于100%,备注不能为空");
                return false;
            }
        }
        return true;
    }

    //开始保存
    function startSaveRealAmount(list,obj){
        var thisdata = {
            info : JSON.stringify({dataList:list})
        }
        var url = "ttActOutUploadController.do?saveRealAmount";
        var d = ajaxPost(thisdata,url);
        if(d.success) {//20200615 成功以后更新页面数据，同时打上可用保存标记
            getShowReferenceData();
            $('#haveSaved', window.parent.document).attr("value",1);
        }
        newTip(d.msg);
    }

    function setMoney(value) {
        if(value==''||value==null){
            return 0.0;
        }else{
            return value;
        }
    }

    //=====================当前使用===============================

    //处理两个小数点
    function changeNum(num){
        var amountTemp = 0;
        var array = num.split(".");
        if (array.length > 1) {
            if (array[1].length == 1) {
                amountTemp = parseFloat(num).toFixed(1)
            }else if(array[1].length > 1){
                amountTemp = parseFloat(num).toFixed(2)
            }
        }else{
            amountTemp = parseFloat(num);
        }
        return amountTemp;
    }

    function returnNotNullNumber(value){
        return value == '' ? 0 : value;
    }

    function changeEditorIsDoNotEdit(ed){
        ed.target.attr('readonly', 'readonly').attr('disabled',true).attr('border','0').css("border-style","none");
    }

    //检查是否合格--数字类型--空不管
    function checkIsOk(objValue,message,reg){
        if($.trim(objValue ).length != 0){
            if (typeof(reg) != 'undefined' && reg != '' && !reg.test(objValue)) {
                newTip(message + "格式不正确");
                return false;
            }
        }else{
            newTip(message + "不能为空");
            return false;
        }
        return true;
    }

    // 获取输入框编辑器对象
    function getEditorObj(gridObj,index,clomnName){
        // 获取输入框编辑器对象
        return gridObj.datagrid('getEditor', {index:index,field:clomnName});
    }
    //返回数据
    function returnEditorValueByEditorObj(ed){
        if(checkIsNotUndefinedAndNull(ed)){
            return $(ed.target).val();
        }else{
            return '';
        }
    }
    //获取编辑器的值
    function getEditorValue(gridObj,index,clomnName){
        // 获取输入框编辑器的值
        var ed = getEditorObj(gridObj,index,clomnName);
        return returnEditorValueByEditorObj(ed);
    }

</script>
