<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttBudgetAdm" title="管理版费用预算"  actionUrl="ttBudgetAdmController.do?findBudgetAdmList"
	  		 onLoadSuccess="loadTotal" idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		 
				<t:dgCol field="id" title="id" hidden="true" ></t:dgCol>
				<t:dgCol field="budgetCode" title="费用预算编号" query="true" ></t:dgCol>
				<t:dgCol field="yearMonth" title="年月" hidden="true"></t:dgCol>
				<t:dgCol field="year" title="年份" query="true"></t:dgCol>
				<t:dgCol field="month" title="月份" query="true"></t:dgCol>

				<t:dgCol field="orgName" title="销售部" query="true"></t:dgCol>

				<t:dgCol field="productName" title="产品层级名称" query="true" ></t:dgCol>
				<t:dgCol field="accountName" title="预算科目" query="true"  ></t:dgCol>
				<t:dgCol field="amount" title="费用金额" formatterjs="numExtend" ></t:dgCol>
				<t:dgCol field="createName" title="创建人"  ></t:dgCol>
				<t:dgCol field="createDate" title="创建时间" formatter="yyyy-MM-dd hh:mm:ss"  ></t:dgCol>
				<t:dgCol field="updateName" title="最近更新人"  ></t:dgCol>
				<t:dgCol field="updateDate" title="最近更新时间"  formatter="yyyy-MM-dd hh:mm:ss" ></t:dgCol>
				
	  		<t:dgToolBar operationCode="add" title="录入费用预算" icon="icon-add" url="ttBudgetAdmController.do?goTtBudgetAdmForm" funname="add"></t:dgToolBar>
	  		<t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttBudgetAdmController.do?goTtBudgetAdmForm" funname="update"></t:dgToolBar>
	  		<t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="" funname="deleteData()"></t:dgToolBar>
			<t:dgToolBar operationCode="input"  title="导入" icon="icon-dataIn" onclick="importDataByXml({impName:'ttBudgetAdmInput', gridName:'ttIncomeTargetList'})"></t:dgToolBar>
			<t:dgToolBar operationCode="out"  title="导出" icon="icon-dataOut" url="ttBudgetAdmController.do?exportXls" funname="excelExport"></t:dgToolBar>
			<t:dgToolBar operationCode="log"   title="日志" icon="icon-log" url="ttBudgetAdmController.do?goTtBudgetAdmLogMain" funname="detailLog" width="1200" height="500"></t:dgToolBar>
		</t:datagrid>
	</div>
</div>
<script type="text/javascript">
    $(function() {
        $("#ttBudgetAdmtb_r").find("input[name='year']").addClass("Wdate").css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
            WdatePicker({dateFmt: 'yyyy'});
        });
        $("#ttBudgetAdmtb_r").find("input[name='month']").addClass("Wdate").css({
            'height': '20px',
            'width': '100px'
        }).click(function () {
            WdatePicker({dateFmt: 'MM'});
        });

        $("#ttBudgetAdm_toolbar_div").parent().append("&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <font id='totalAmount' color=red></font>");
        $("#ttBudgetAdm_toolbar_div").remove();
    });

    function loadTotal(){
        var queryParams = $('#ttBudgetAdm').datagrid('options').queryParams;
        $('#ttBudgetAdmtb').find('*').each(function(){queryParams[$(this).attr('name')]=$(this).val();});
        var url="ttBudgetAdmController.do?getTtBudgetAdmByTotal";
        $.ajax({url:url,type:"post",data:queryParams,async:false,success:function(data){
            var d = $.parseJSON(data);
            if(d.success){
                $("#totalAmount").html("费用总金额："+accounting.formatMoney(d.obj,""));
            }
        }
        });
    }

//删除
function deleteData(){
	/*选择数据中的一行*/
	var seletctTarget =  $("#ttBudgetAdm").datagrid("getSelections");
	//判断当前选择的这一行不为空并且不为空串
	if(seletctTarget==null || seletctTarget==""){
		//上面条件成立弹出这句话
		tip("必须选择一条数据");
		//执行到这里终止程序的运行
		return;
	}
	//执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
		$.messager.confirm('操作提示','确定删除?',function(r){ 
			//如果r等于true就执行下面的操作
		    if (r){
		    	//发送ajax请求
		    	$.ajax({
		    		//method为post请求
		        	type : "POST",
		        	//请求的链接地址+上这一行的数组取第0个索引就是第一个id
		        	url : "ttBudgetAdmController.do?deleteBudgetAdm&id="+seletctTarget[0].id,
		        	//是否是同步还是异步
		        	async:true,
		        	//提示消息
		        	success : function(data) {
		        		var d = $.parseJSON(data);
		        		tip(d.msg);
		        		//从新加载页面
		        		$("#ttBudgetAdm").datagrid("reload");
		        	}
			   });
		    }
		});
}
</script>
