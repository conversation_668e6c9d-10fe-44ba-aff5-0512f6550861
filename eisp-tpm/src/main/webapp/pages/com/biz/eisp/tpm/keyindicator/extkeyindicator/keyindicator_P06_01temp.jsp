<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
    iframe {
        height: 480px!important;
    }
</style>
<%--<div class="easyui-layout" fit="true">--%>
<div style="clear:both; width: 1200px;height: 200px;">
    <%--<div region="center" style="padding:1px;">--%>
    <div id = "showReferenceData" style="margin-bottom: 5px; margin-left: 5px;">
    </div>
    <div>

    </div>
    <t:datagrid name="tsActApplyVoList" fitColumns="false"  pagination="false" title="活动详细"
                actionUrl="ttActOutUploadController.do?findActWorkFlowData&businessId=${businessId}" idField="id" fit="true" >
        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="businessId" field="businessId" hidden="true"></t:dgCol>
        <t:dgCol title="审批状态" field="bpmStatus" hidden="false"  dictionary="bpm_status"></t:dgCol>
        <t:dgCol title="门头广告编号" field="adCode" hidden="false" ></t:dgCol>
        <t:dgCol title="活动编码" field="actCode" hidden="false" ></t:dgCol>
        <t:dgCol title="活动类型" field="actType" hidden="false" dictionary="act_type" ></t:dgCol>
        <t:dgCol title="广告公司编号" field="advCode" hidden="false" ></t:dgCol>
        <t:dgCol title="广告公司名称" field="advName" hidden="false" ></t:dgCol>
        <%--<t:dgCol title="终端网点名" field="terminalName" hidden="false" ></t:dgCol>--%>
        <t:dgCol title="广告发布地址" field="gpsAddress" width="200" ></t:dgCol>
        <t:dgCol title="广告发布地址2（面积+地址）" field="detailRemark" width="200" ></t:dgCol>
        <t:dgCol title="上刊地址（施工地址）" field="constructionAddress" width="200" ></t:dgCol>
        <t:dgCol title="省" field="province" hidden="false" ></t:dgCol>
        <t:dgCol title="市" field="city" hidden="false" ></t:dgCol>
        <t:dgCol title="区县" field="area" hidden="false" ></t:dgCol>
        <t:dgCol title="终端编码" field="terminalCode" hidden="false" ></t:dgCol>
        <t:dgCol title="终端名称" field="terminalName"  width="200" ></t:dgCol>
        <t:dgCol title="创建人" field="createName" hidden="false"></t:dgCol>
        <t:dgCol title="创建时间" field="createDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
        <t:dgCol title="更新人" field="updateName" hidden="false"></t:dgCol>
        <t:dgCol title="更新时间" field="updateDate" hidden="false" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
        <t:dgToolBar title="检查范围终端" icon="icon-search" url="ttActApplyExcuteWorkFlowController.do?goSearchActTerminal" funname="showTheWithinScopeTerminal" ></t:dgToolBar>
        <t:dgToolBar title="全景地图" icon="icon-search" url="ttActApplyExcuteWorkFlowController.do?goShowTheAllMap" funname="showTheAllMap" ></t:dgToolBar>
    </t:datagrid>
    <%--</div>--%>

</div>

<div style="clear:both; width: 1200px;height: 800px;">
    <%--<div region="center" style="padding: 1px;">--%>
    <t:datagrid name="trainPictureList" title="图片视频信息"  actionUrl="tsPictureController.do?findTrainPictureList&businessId=${businessId}"
                idField="id" fit="true"  fitColumns="false"  pagination="false">
        <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="bussinessId" field="bussinessId" hidden="true"></t:dgCol>
        <t:dgCol title="类型" field="imgType" dictionary="sfa_photo_type" width="200"></t:dgCol>
        <t:dgCol title="类型描述" field="imgTypeRemark"  width="200"></t:dgCol>
        <t:dgCol title="位置" field="place"  width="200"></t:dgCol>
        <t:dgCol title="缩略图" field="imgPath"  formatterjs="showInfo"   width="200"></t:dgCol>
    </t:datagrid>
    <%--</div>--%>
</div>

<div style="clear:both; width: 1200px;height: 200px;margin-bottom:1200px">
    <%--<div region="center" style="padding: 1px;">--%>
    <t:datagrid name="advList" title="广告材料信息"  actionUrl="ttActOutUploadController.do?findAdvInfo&actCode=${actCode}"
                idField="id" fit="true"  fitColumns="false"  pagination="false">
        <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="门头广告编号" field="adCode"  width="200"></t:dgCol>
        <t:dgCol title="网点编号" field="terminalCode"  width="200"></t:dgCol>
        <t:dgCol title="材料" field="materialName"  width="200"></t:dgCol>
        <t:dgCol title="宽" field="mwidth"  width="200"></t:dgCol>
        <t:dgCol title="长" field="mlength"  width="200"></t:dgCol>
        <t:dgCol title="面积" field="mspace"   width="200"  ></t:dgCol>
        <t:dgCol title="价格" field="money" formatterjs="setMoney"  width="200"  ></t:dgCol>

    </t:datagrid>
    <%--</div>--%>
</div>

<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>


<div id="outerdiv2" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv2" style="position:absolute;">
        <

        <video src="" id="biVideo" style='width: 120px;height:80px;cursor: pointer;controls:controls;' ></video>
    </div>
</div>

<div style="clear:both; width: 1200px;height: 800px;">
    <%--<div region="center" style="padding: 1px;">--%>
    <t:datagrid name="suspiciousOpinionsList" title="可疑意见"  actionUrl="ttActApplyExcuteWorkFlowController.do?findTheSuspiciousOpinionsList&flagKey=${flagKey}"
                idField="id" fit="true"  fitColumns="false"  pagination="false">
        <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="节点" field="nodeCode" hidden="true"></t:dgCol>
        <t:dgCol title="是否可疑" field="isSuspicious" dictionary="dict_is_suspicious" width="80"></t:dgCol>
        <t:dgCol title="意见" field="opinion"  width="200"></t:dgCol>
        <t:dgToolBar title="填写意见" icon="icon-edit" url="ttActApplyExcuteWorkFlowController.do?saveOrUpdateTheSuspOpin" funname="editTheSuspOpin" ></t:dgToolBar>
    </t:datagrid>
    <%--</div>--%>
</div>

<%--</div>--%>
<script>

    $(function () {
        getShowReferenceData();
    });

    function getShowReferenceData(){
        var thisData = {
            businessKey : getBusinessKey(),
            flaKey : getFlagKey()
        }
        var url = "ttActApplyExcuteWorkFlowController.do?getShowReferenceData";
        var d = ajaxPost(thisData,url);

        if(d.success){
            var html = returnShowReferenceDataHtml(d.obj);
            $('#showReferenceData').html(html)
        }
    }

    function returnShowReferenceDataHtml(objs) {
        var html = "";
        for(var obj in objs){
            if(html != ''){
                html += "\t|\t"
            }
            html += '<label>' + obj + ':<span style="font-size: 15px;color: red;">' + objs[obj] + '</span></label>';
        }
        return html;
    }

    function showInfo(value,row) {
        // if(row.imgType==165){
        //     var url="tsPictureController.do?download&id="+row.id;
        //     return "<a href="+url+">点击下载</a>"
        // }
        if(value.indexOf('mp4')>0){
            return showvideo(value);
        }else if(value.indexOf('jpg')>0||value.indexOf('png')>0){
            return picture(value);
        }else {
            // value='/image/'+value;
            var url="tsPictureController.do?download&id="+row.id;

            // url=encodeURI(url);
            return "<a href="+url+">点击下载</a>"
        }
    }

    function picture(value) {
        value='/image/'+value;
        var img = value;
        if(value != ""){
            var str = "<img class='imgW' style='width: 120px;height:80px;cursor: pointer;' src="+value + "  onclick='showBigPic(this)'>";
            return str;
        }
    }

    function showvideo(value) {
        if(value != ""){
            value='/image/'+value;
            var str = "<video  style='width: 120px;height:80px;cursor: pointer;controls:controls;' src="+value+ "  onclick='videoBig(this)'+/>";
            //   onmouseover
            return str;
        }
    }

    function picBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $("#bigimg").attr("src", src);//设置#bigimg元素的src属性
        /*获取当前点击图片的真实大小，并显示弹出层及大图*/
        $("<img/>").attr("src", src).load(function(){
            var windowW = $(window).width();//获取当前窗口宽度
            var windowH = $(window).height();//获取当前窗口高度
            var realWidth = this.width;//获取图片真实宽度
            var realHeight = this.height;//获取图片真实高度
            var imgWidth, imgHeight;
            $("#bigimg").css("width",600);//以最终的宽度对图片缩放
            $("#bigimg").css("height",500);//以最终的宽度对图片缩放

            var w = (windowW-600)/2;//计算图片与窗口左边距
            var h = (windowH-500)/2;//计算图片与窗口上边距
            $("#innerdiv").css({"top":h, "left":w});//设置#innerdiv的top和left属性
            $("#outerdiv").fadeIn("fast");//淡入显示#outerdiv及.pimg
        });

        $("#outerdiv").click(function(){//再次点击淡出消失弹出层
            $(this).fadeOut("fast");
        });
    }


    function videoBig(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("视频不存在!")
            return false;
        }
        $.dialog({
            title: "视频查看",
            content: "url:tsPictureController.do?showVideo&path="+src,
            lock: true,
            width: "680",
            height: "560",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }


    function showBigPic(obj){
        var src = obj.src;//获取当前点击的pimg元素中的src属性
        if(src==''||src==null){
            tip("图片不存在!")
            return false;
        }
        $.dialog({
            title: "图片查看",
            content: "url:tsPictureController.do?showBigPic&path="+src,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            cancelVal: '关闭',
            cancel: true
        });
    }

    //展示500米范围内的终端
    function showTheWithinScopeTerminal(title, url, gname, callback){

        var thisData = {
            flagKey : getFlagKey(),
            businessKey : getBusinessKey()
        }

        var url = url + changeDataToUrlData(thisData) ;

        $.dialog({
            title: "范围终端活动",
            content: "url:" + url,
            lock: true,
            width: "800",
            height: "500",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    function showTheAllMap(title, url, gname, callback){
        var thisData = {
            flagKey : getFlagKey(),
            businessKey : getBusinessKey()
        }

        var url = url + changeDataToUrlData(thisData) ;

        $.dialog({
            title: "全景地图",
            content: "url:" + url,
            lock: true,
            width: "1200",
            height: "600",
            zIndex: 100000,
            parent: windowapi,
            cancelVal: '关闭',
            cancel: true
        });
    }

    //检查预算积分
    function checkTheTrialIntegral(){
        var flag = false;
        var trialIntegral = $('#trialIntegral').val();
        if(!checkIsNotUndefinedAndNullAndNullValue(trialIntegral)){
            tip("请填写测算积分");
            return ;
        }

        var thisData = {
            flagKey : getFlagKey(),
            businessKey : getBusinessKey(),
            trialIntegral : trialIntegral
        }

        var url = "ttActApplyExcuteWorkFlowController.do?checkTheTrialIntegral";
        var d = ajaxPost(thisData,url);
        var class1;
        var oldClass1;
        var title;
        var spanHtml = '';
        if(d.success){
            if(d.obj.success){
                flag = true;

            }else{
                flag = false;
                spanHtml = d.obj.msg;
                confirm(d.obj.msg);
            }
        }else{
            flag = false;
            tip(d.msg);
        }
        if(flag){
            title = "检测通过";
            class1 = "Validform_checktip Validform_right";
            oldClass1 = "Validform_checktip Validform_wrong";
        }else{
            title = "检测不通过";
            oldClass1 = "Validform_checktip Validform_right";
            class1 = "Validform_checktip Validform_wrong";
        }

        changeTheTipStyle(oldClass1,class1,title,spanHtml);
    }

    //添加样式
    function changeTheTipStyle(olcClass1,class1,title,spanHtml) {
        var showObj = $('#showStyle');
        showObj.removeClass(olcClass1).addClass(class1);
        showObj.attr("title",title);
        showObj.html(spanHtml);
    }

    //填写意见
    function editTheSuspOpin(title, url, gname, callback){
        //获取数据
        var rowData = getTheRowData(gname,getTaskCode());

        var html = returnShowHtml();

        $.dialog({
            title: "编写意见",
            content: html,
            lock: true,
            width: "380",
            height: "200",
            zIndex: 100000,
            parent: windowapi,
            // parent: windowapi.parent.content,
            init : function(){
                var parentTemp = parent.parent.parent;
                parentTemp.$('#tempIsSuspicious').val(rowData.isSuspicious);
                parentTemp.$('#tempMsg').val(rowData.opinion);
            },
            ok : function() {
                var parentTemp = parent.parent.parent;
                var isSuspicious = parentTemp.$('#tempIsSuspicious').val();
                if(!checkIsNotUndefinedAndNullAndNullValue(isSuspicious)){
                    alert('是否可疑不能为空');
                    return false;
                }
                var opinion = parentTemp.$('#tempMsg').val();
                if(!checkIsNotUndefinedAndNullAndNullValue(opinion)){
                    alert('意见不能为空');
                    return false;
                }
                var tempData = {
                    opinion : opinion,
                    isSuspicious : isSuspicious
                }
                //开始保存
                return starEditTheSuspOpin(gname,rowData,url,tempData);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    function returnShowHtml() {
        var html = "";
        html += '<div class="form"><label class="Validform_label" name="tempIsSuspicious"><span style="color: red;">*</span>是否可疑：</label> ';
        html += structureRadio();
        html += ' </div>';
        html += '<div class="form" style="margin-top: 10px;"><label class="Validform_label" style="position: relative;left: 0;top: 0;font-size: 15px; float: left; "><span style="color: red;">*</span>意见：</label><textarea id="tempMsg" rows="10" cols="40"></textarea><div>';
        return html;
    }
    function structureRadio(){
        var selectHtml = '<select id="tempIsSuspicious">';
        selectHtml += '<option value="">--请选择--</option>';
        <c:forEach items="${dictIsSuspiciousList}" var="dictIsSuspicious" >
        selectHtml += '<option value="${dictIsSuspicious.dictCode}">${dictIsSuspicious.dictValue}</option>';
        </c:forEach>
        selectHtml += '</select>';
        return selectHtml;
    }

    //开始
    function starEditTheSuspOpin(gname,rowData,url,tempData){
        var flg = false;
        var thisData = {
            id : rowData.id,
            flagKey : getFlagKey(),
            nodeCode : getTaskCode(),
            opinion : tempData.opinion,
            isSuspicious : tempData.isSuspicious
        }
        var d = ajaxPost(thisData, url);
        if(d.success){
            $("#" + gname).datagrid('reload');
            flg = true;
        }
        newTip(d.msg);
        return flg;
    }

    //获取一条数据
    function getTheRowData(gname,targ){
        var rowData = {}
        var rows = $("#" + gname).datagrid('getRows');
        if(checkIsNotUndefinedAndNullAndNullValue(rows) && rows.length > 0){
            for(var i = 0 ; i < rows.length ; i ++ ){
                var row = rows[i];
                if(targ == row.nodeCode){
                    rowData = row;
                    break;
                }
            }
        }
        return rowData;
    }


    /*===================共用=====================*/
    /**
     * 通用ajax post 方法
     * @param json 传入参数json
     * @param url  调用url
     */
    function ajaxPost(json,url){
        var json;
        $.ajax({
            url:url,
            data:json,
            dataType:'json',
            async:false,
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });
        return json;
    }

    //检查是否为未定义或为空不为undefined
    function checkIsNotUndefinedAndNull(value){
        return (typeof(value) != 'undefined' && $.trim(value).length > 0)
    }
    //检查是否为未定义或为空不为undefined和不为空值（'null'）
    function checkIsNotUndefinedAndNullAndNullValue(value){
        return (checkIsNotUndefinedAndNull(value) && value != 'null');
    }

    //将obj转换为urlData
    function changeDataToUrlData(data){
        var urlData = '';
        if(typeof(data) != 'undefined' && data != '' && data != null){
            for (var name in data){
                urlData += '&' + name + '=' + data[name];
            }
        }
        return urlData;
    }

    function setMoney(value) {
        if(value==''||value==null){
            return 0.0;
        }else{
            return value;
        }

    }

</script>
