<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style type="text/css">
    .datagrid-toolbar-search form div label {
        width: 100px;
    }

</style>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="ttDirectIncomeList" title="直营收入拆分"
                    actionUrl="ttDirectIncomeController.do?findTtDirectIncomeList"
                    idField="id" fit="true" fitColumns="false" pagination="true" queryMode="group" >
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>

            <t:dgCol title="原组织编码" field="orgCode" query="true" width="120"></t:dgCol>
            <t:dgCol title="原组织" field="orgName"   width="120"></t:dgCol>
            <t:dgCol title="原组织" field="orgNames"  query="true" hidden="true"></t:dgCol>
            <t:dgCol title="原组织" field="org_id" hidden="true"   width="150"></t:dgCol>
            <t:dgCol title="收入并入组织编码" field="intoOrgCode" query="true" width="120"></t:dgCol>
            <t:dgCol title="收入并入组织" field="intoOrgName"  width="120"></t:dgCol>
            <t:dgCol title="收入并入组织" field="intoOrgNames"  query="true" hidden="true"></t:dgCol>
            <t:dgCol title="收入并入组织" field="intoOrg_id" hidden="true"  width="150"></t:dgCol>
            <t:dgCol title="活动时间" field="activityDate" query="true" hidden="true" queryMode="group"
                     formatter="yyyy-MM-dd"></t:dgCol>
            <t:dgCol title="开始时间" field="beginDate" width="120" query="false"></t:dgCol>
            <t:dgCol title="结束时间" field="endDate" width="120" query="false"></t:dgCol>
            <t:dgCol title="创建人" field="createName" query="true" width="150"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss" queryMode="group"
                     width="130"></t:dgCol>
            <t:dgCol title="备注" field="remark" width="120"></t:dgCol>


            <t:dgToolBar operationCode="add" title="新增" icon="icon-add" url="ttDirectIncomeController.do?goTtDirectIncomeForm&optype=0" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttDirectIncomeController.do?goTtDirectIncomeForm&optype=1" funname="update"></t:dgToolBar>
            <t:dgToolBar operationCode="del" title="删除"  icon="icon-remove" url=""  funname="deleteTtDirectIncome"></t:dgToolBar>
            <t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttDirectIncomeController.do?goTtDirectIncomeLogMain" funname="detail" width="1200"></t:dgToolBar>
            <t:dgToolBar operationCode="out" title="导出" icon="icon-dataOut" url="ttDirectIncomeController.do?exportXls" funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
    <input type="text">
</div>
<script type="text/javascript">

    $(document).ready(function () {
        //日期格式查询条件 开始日期起始
        $("input[name='beginDate']")
            .attr("class", "Wdate")
            .attr("style", "height:20px;width:90px;")
            .click(function () {
                WdatePicker({dateFmt: 'yyyy-MM-dd'});
            });

        //日期格式查询条件 开始日期截止
        $("input[name='endDate']")
            .attr("class", "Wdate")
            .attr("style", "height:20px;width:90px;")
            .click(function () {
                WdatePicker({dateFmt: 'yyyy-MM-dd'});
            });
        $("input[name='orgNames']").attr("readonly",true).attr("id","orgNames").attr("style","width:180px").click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='orgCodes' id='orgCodes'/>");
        $("input[name='intoOrgNames']").attr("readonly",true).attr("id","intoOrgNames").attr("style","width:180px").click(function(){openIntoOrgSelect();}).parent().append("<input type='hidden' name='intoOrgCodes' id='intoOrgCodes'/>");

    });

    function ownAdd(title, url, grid, width, height) {
        createwindowExt(title, url, 550, 450, {
            ok: function () {
                iframe = this.iframe.contentWindow;
                //传递参数
                iframe.saveData();
                return false;
            },
            cancelVal: '关闭',
            cancel: function () {
                return true;
            }
        });
    }

    //删除
    function deleteTtDirectIncome() {
        var ttDirectIncomeTarget = $("#ttDirectIncomeList").datagrid("getSelected");
        if (ttDirectIncomeTarget == null || ttDirectIncomeTarget == "") {
            tip("请选择一条需要删除的数据");
            return false;
        }
        getSafeJq().dialog.confirm("你确定要删除该数据吗?", function (r) {
            if (r) {
                $.ajax({
                    url: "ttDirectIncomeController.do?deleteTtDirectIncome",
                    type: 'post',
                    data: {
                        id: ttDirectIncomeTarget.id
                    },
                    cache: false,
                    success: function (data) {
                        var d = $.parseJSON(data);
                        var msg = d.msg;
                        if (d.success) {
                            tip(msg);
                            reloadTable();
                        } else {
                            tip(msg);
                            return false;
                        }
                    },
                    error: function () {
                        tip("客户端请求错误");
                        return false;
                    }
                });
            }
        });
    }
    function openOrgSelect(){
        var orgCode ='${orgCode}';
        var currentOrgCode='01';
        orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
        var paraArr = {
            textName: 'orgCode,orgName',
            inputTextName: 'orgCodes,orgNames',
            searchType: '1',//查询类型？？
            encapsulationType: 'input',//封装类型--不传或默认
            isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            currentOrgCode:currentOrgCode,
            pageData: {
                orgCode: orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }

    function openIntoOrgSelect(){
        var orgCode ='${orgCode}';
        var currentOrgCode='01';
        orgCode = (typeof(orgCode) == "undefined") ? '' : orgCode;
        var paraArr = {
            textName: 'orgCode,orgName',
            inputTextName: 'intoOrgCodes,intoOrgNames',
            searchType: '1',//查询类型？？
            encapsulationType: 'input',//封装类型--不传或默认
            isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
            currentOrgCode:currentOrgCode,
            pageData: {
                orgCode: orgCode
            }
        }
        openChooseOrgSelect(paraArr);
    }
</script>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>