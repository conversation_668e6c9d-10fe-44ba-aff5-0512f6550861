<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<style>
    .actTable tr td:nth-child(1){display:none;}
    .g-img-popup{
        width:300px;
        height:450px;
        padding: 10px;
        border-radius: 5px;
        background-color: #ccc;
        position: fixed;
        top:10px;
        display: none;
        transition: all 1s;
    }
    ul#ulTemp{
        padding: 20px;
        width:420px;
    }
    ul#ulTemp li{
        width: 100px;
        height: 100px;
        float: left;
        margin: 1px;
    }
</style>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="tmCostAccountMain" class="easyui-layout" fit="true">
    <div region="center" style="width:500px;:500px;border:0px;">
        <t:datagrid name="ttQuotaTerminalSelectMainList" fitColumns="false" title="活动门店申请" queryMode = "group" idField="id" pagination="true"
                    autoLoadData="true" actionUrl="ttAuditQuotaController.do?findttQuotaTerminalSelectMainList&auditId=${auditId}" singleSelect="false">
            <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
            <%--<t:dgCol title="审批状态"  field="bpmStatus" dictionary="bpm_status"  width="100"></t:dgCol>--%>
            <t:dgCol title="核销子单ID" field="auditId" hidden ="true"></t:dgCol>
            <t:dgCol title="门店名称" field="terminalName"  width="150"></t:dgCol>
          <%--  <t:dgCol title="活动总金额(元)" field="actPlanAmount" width="100"></t:dgCol>--%>
            <t:dgCol title="活动金额" field="amount" width="100"></t:dgCol>
            <t:dgCol field="planQuantity" title="门店申请数量" width="120"></t:dgCol>
            <t:dgCol field="companyAmount" title="门店申请金额" width="120"></t:dgCol>
            <t:dgCol field="applyAuditQuantity" title="门店申请结案数量"  width="150"></t:dgCol>
            <t:dgCol field="applyAuditAmount" title="门店申请结案金额"  width="150"></t:dgCol>
            <t:dgCol field="auditAmount" title="门店审核结案金额" width="150"></t:dgCol>
            <t:dgCol field="auditQuantity" title="门店审核结案数量" width="150"></t:dgCol>

            <c:if test="${ft == '1'}">
                <t:dgCol title="陈列类型" field="displayTypeName" width="100"></t:dgCol>
                <t:dgCol title="标准" field="standard" width="100"></t:dgCol>
            </c:if>

            <t:dgCol title="申请备注" field="applyRemark" width="150"></t:dgCol>
            <t:dgCol title="结案备注" field="auditRemark" width="150"></t:dgCol>
        </t:datagrid>
    </div>

    <div data-options="region:'east',
		collapsed:true,
		split:true,
		border:false,
		onExpand : function(){
			li_east = 1;
		},
		onCollapse : function() {
		    li_east = 0;
		}"
         style="width: 600px; overflow: hidden;">
        <div class="easyui-panel" style="padding: 1px;" fit="true" border="false" id="showPictrue"></div>
    </div>
</div>
<script type="text/javascript">
    var li_east = 0;
    $(function(){
        $("#tmCostAccountMain").layout("expand","east");
        li_east = 1;
        $("#showPictrue").panel("refresh","ttAuditQuotaPictureController.do?goTerminalPhotos&auditId=${auditId}&ft=${ft}");
    });
</script>