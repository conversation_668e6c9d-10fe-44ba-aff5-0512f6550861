<%@ page language="java" import="java.util.*"
	contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>费用池手动上账</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
	
</head>
<body style="overflow-y: hidden" scroll="no">
		<t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
			 action="rebatePoolController.do?saveTdRebatePoolForm" >
			<input id="id" name="id" type="hidden" value="${rebPoolVo.id }">
				<div class="form">
            	<label class="Validform_label"><span style="color:red;">*</span>客户编号:</label>
            	<input name="sapSn" id="sapSn" class="inputxt" style="width: 150px;" readonly="readonly" value="${rebPoolVo.dealerId}">
                <a iconcls="icon-search" class="easyui-linkbutton l-btn" plain="true" href="javascript:;"
					   onclick="serachCustomer()"></a>        
        	</div>
        	<div class="form">
            	<label class="Validform_label">客户名称:</label>
            	<input id="sapName" name="sapName" class="inputxt" style="width: 150px;" readonly="readonly" value="${rebPoolVo.dealerName}">
        	</div>

			<div class="form">
				<label class="Validform_label"><span style="color:red;">*</span>经销商编码:</label>
				<input id="dealerId" name="dealerId" class="inputxt" style="width: 150px;" readonly="readonly" datatype="*" errormsg="找不到经销商编码,请联系管理员" value="${rebPoolVo.dealerName}">
			</div>

			<div class="form">
				<label class="Validform_label">经销商名称:</label>
				<input id="dealerName" name="dealerName" class="inputxt" style="width: 150px;" readonly="readonly" value="${rebPoolVo.dealerName}">
			</div>
			<div class="form">
            	<label class="Validform_label">费用类型:</label>
            	<select name="costType"  id="costType">
                	<option value="3" selected="selected">手工入账</option>
            	</select>
        	</div>

			<div class="form">
				<label class="Validform_label"><span style="color: red">*</span>上账金额: </label>
				<input name="moneyOrNum" class="inputxt" style="width: 150px;" value="${rebPoolVo.moneyOrNum}" datatype="/^[+-]?\d*\.?\d{1,2}$/" errormsg="上账金额必须为整数或最多保留两位的浮点数">
			</div>
			<div class="form">
            	<label class="Validform_label">支付方式:</label>
            	<select name="payType" style="width: 150px;" id="payType">
                	<option value="1" <c:if test="${rebPoolVo.payType == 1}">selected="selected"</c:if> >货补</option>
                	<option value="2" <c:if test="${rebPoolVo.payType == 2}">selected="selected"</c:if> >折扣</option>
            	</select>
        	</div>
			<div class="form">
				<label class="Validform_label">备注: </label>
				<textarea style="width:200px;height:150px;" name="remark">${rebPoolVo.remark}</textarea>
			</div>
			
		</t:formvalid>

</body>

<script>
//添加经销商
function serachCustomer(obj) {
	var name = "dealerName";
	var name2 = "dealerId";
	
	$.dialog.setting.zIndex = 2000;

	safeShowDialog({
		content : "url:tdContractController.do?goCustomerforContractForm",
		lock : true,
		title : "选择客户",
		width : 900,
		height : 500,
		cache : false,
		ok : function() {
			iframe = this.iframe.contentWindow;
			var selected = iframe.getSelectRows();
			if (selected == '' || selected == null || selected.length != 1) {
				iframe.tip("请选择一条数据");
				return false;
			} else {
				var sapSn = "";
				var sapName = "";
				$.each(selected, function(i, n) {
					if (i == 0) {
                        sapName = n['customerName'];
                        sapSn = n['erpCode'];
					}
				});
				$('#sapSn').val(sapSn);
				$('#sapName').val(sapName);
                findDealerIdAndNameByErpCode(sapSn);//设置经销商编码和名称
				return true;
			}

		},
		cancelVal : '关闭',
		cancel : true
	/* 为true等价于function(){} */
	});
}

/**
 * 获取客户上级经销商编码和名称
 * 如果客户是分销商，返回上级经销商编码和名称
 * 如果客户本身是经销商，返回自己的编码和名称
 */
function findDealerIdAndNameByErpCode(erpCode) {


    $.ajax({
        url: "rebatePoolController.do?findDealerIdAndNameByErpCode",
        data: {
         erpCode:erpCode
        },
        type: "post",
        success: function (e) {
            var back = JSON.parse(e);
            if (!back.success) {
                alert(back.msg);
                return false;
            }
            var mapData=back.attributes;
            console.log("mapData:"+JSON.stringify(mapData));
            $('#dealerId').val(mapData.dealerErpCode);
            $('#dealerName').val(mapData.dealerName);
        }
    });



}






</script>
</html>