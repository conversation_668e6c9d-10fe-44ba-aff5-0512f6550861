<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report012List" fitColumns="false" title="业绩追踪报表"
                    pagination="false" autoLoadData="false" actionUrl="report012Controller.do?findReportList" idField="id" fit="true">
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" hidden="true" sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上上级组织" field="superGrandOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上上级组织" field="grandOrgName" sortable="false" width="400"></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName" width="150" sortable="false"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="人员" field="personnel" width="150" sortable="false"></t:dgCol>
            <t:dgCol title="产品层级编码" field="productHierarchyCode" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="产品层级名称" field="productHierarchyName" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="产品" field="productName" hidden="true" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="产品编码" field="productCode" hidden="true" width="150" query="true" sortable="false"></t:dgCol>
            <t:dgCol title="实际销售额" field="effectiveSales" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="必保任务额" field="taskAmount" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="达成率" field="yieldRate" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="同比增量" field="increment" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="同比增率" field="uprate" width="150"  sortable="false"></t:dgCol>
            <t:dgCol title="环比增率" field="loopUprate" width="150"  sortable="false"></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report012Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report012Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'120px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:180px")
            .click(function(){openOrg();}).parent().append("<input type='hidden' name='orgCode' id='orgCode'/>");
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    function report012Listsearch() {
        var orgCode = $("#report012Listtb_r").find("input[name='orgCode']").val();
        var yearMonth = $("#report012Listtb_r").find("input[name='yearMonth']").val();

        if(orgCode == null || orgCode == "") {
            tip("请选择查询组织");
            return;
        }

        if(yearMonth == null || yearMonth == "") {
            tip("请选择查询年月");
            return;
        }

        var queryParams = $("#report012List").datagrid('options').queryParams;
        $("#report012Listtb_r").find('*').each(
            function() {
                queryParams[$(this).attr('name')]=$(this).val();
            }
        );
        $("#report012List").datagrid({url:'report012Controller.do?findReportList'});
    }

</script>
