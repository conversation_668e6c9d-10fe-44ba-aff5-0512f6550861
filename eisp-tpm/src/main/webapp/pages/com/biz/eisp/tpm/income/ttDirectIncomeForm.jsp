<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>直营收入拆分</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
    <link rel="stylesheet" href="resources/Validform/css/divfrom.css" type="text/css"/>
    <link rel="stylesheet" href="resources/Validform/css/style.css" type="text/css"/>
    <link rel="stylesheet" href="resources/Validform/css/tablefrom.css" type="text/css"/>
    <script type="text/javascript" src="resources/Validform/js/Validform_v5.3.1_min_zh-cn.js"></script>
    <script type="text/javascript" src="resources/Validform/js/Validform_Datatype_zh-cn.js"></script>
    <script type="text/javascript" src="resources/Validform/js/datatype_zh-cn.js"></script>
</head>
<body style="overflow-y: hidden" scroll="no">
<div id="content">
    <div id="wrapper">
        <div id="steps">
            <t:formvalid formid="formobj"   layout="div" dialog="true" action="ttDirectIncomeController.do?saveTtDirectIncome" refresh="true" tiptype="3"  beforeSubmit="validate">
            <%--<form  id="formobj" name="formobj" action="ttDirectIncomeController.do?saveTtDirectIncome" method="post">--%>

                <input type="hidden" name="id" id="id" value="${vo.id }">
                <input type="hidden" name="enableStatus" value="0">
                <input type="hidden" name="delStatus" value="0">

                <div class="form">
                    <label class="Validform_label">活动开始时间: </label>
                    <input name="beginDate" id="beginDate" datatype="*" class="Wdate" style="width: 150px;"
                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\')}',onpicked:function(){$('.Wdate').blur();}})"
                           readonly="readonly"  value="${vo.beginDate}" />
                    <span style="color: red;">*</span>
                </div>

                <div class="form">
                    <label class="Validform_label">活动结束时间: </label>
                    <input name="endDate" id="endDate" datatype="*" class="Wdate" style="width: 150px;"
                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
                           readonly="readonly"  value="${vo.endDate}" />
                    <span style="color: red;">*</span>
                </div>

                <div class="form">
                    <label class="Validform_label">原组织:</label>
                    <input name="orgName" id="orgName" class="inputxt"  datatype="*"  readonly="readonly"  readonly="readonly" value="${vo.orgName}" />
                    <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openOrgSelect();"></a>
                    <input type="hidden" id="orgCode"  name="orgCode" value='${vo.orgCode}' >
                    <input type="hidden" id="orgLevel"  name="orgLevel" value='${vo.orgLevel}' >
                </div>

                <div class="form">
                    <label class="Validform_label">收入并入组织:</label>
                    <input name="intoOrgName" id="intoOrgName" class="inputxt" datatype="*"  readonly="readonly"  readonly="readonly" value="${vo.intoOrgName}" />
                    <a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="openIntoOrgSelect();"></a>
                    <input type="hidden"  id="intoOrgCode"  name="intoOrgCode" value='${vo.intoOrgCode}' >
                    <input type="hidden" id="intoOrgLevel"  name="intoOrgLevel" value='${vo.intoOrgLevel}' >
                </div>

                <div class="form">
                    <label class="Validform_label">备注: </label>
                    <textarea class="inputxt" rows="4" cols="22" name="remark" id="remark" >${vo.remark }</textarea>
                </div>
            <%--</form>--%>
            </t:formvalid>
        </div>
    </div>
</div>

</body>
</html>
<script type="text/javascript">




    $(document).ready(function() {
        $("#orgCode").val('${vo.orgCode}');
        $("#intoOrgCode").val('${vo.intoOrgCode}');
    });

    //弹出选择组织
    function openOrgSelect() {
        safeShowDialog({
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            title : "选择企业组织",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function()
            {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#orgList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#orgCode").val("");
                    $("#orgName").val("");
                    return true;
                }
                $("#orgCode").val(rowsData.orgCode);
                $("#orgName").val(rowsData.text);
                $("#orgLevel").val(rowsData.orgType);
                lostfocus("orgName");
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }


    //弹出选择并入组织
    function openIntoOrgSelect() {
        safeShowDialog({
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            title : "选择并入企业组织",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function()
            {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#orgList').datagrid('getSelected');
                if ((rowsData == '' || rowsData == null)) {
                    $("#intoOrgCode").val("");
                    $("#intoOrgName").val("");
                    return true;
                }
                $("#intoOrgCode").val(rowsData.orgCode);
                $("#intoOrgName").val(rowsData.text);
                $("#intoOrgLevel").val(rowsData.orgType);
                lostfocus("intoOrgName");
                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    function  lostfocus(id){
        $('#' + id).blur();
    }
    //验证组织和并入组织
    function validate() {
        //组织编码
        var orgCode =  $("#orgCode").val();
        //并入组织编码
        var intoOrgCode =  $("#intoOrgCode").val();
        if(orgCode == intoOrgCode){
            newTip("组织和并入组织不能相同！");
            return false;
        }

    }
</script>