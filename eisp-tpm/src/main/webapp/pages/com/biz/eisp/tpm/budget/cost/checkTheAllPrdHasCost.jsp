<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <%--无成本的可够清单产品(产成品)--%>
        <t:datagrid queryMode="group"  name="hasNoCostTheProduct" fitColumns="false" title=""
                    actionUrl="${datagridUrl}" idField="id" fit="true">
            <t:dgCol title="主键" hidden="true" field="id" width="90" ></t:dgCol>
            <t:dgCol title="产品编码" field="productCode" width="100" ></t:dgCol>
            <t:dgCol title="产品名称" field="productName" width="300" ></t:dgCol>
        </t:datagrid>
    </div>
</div>

