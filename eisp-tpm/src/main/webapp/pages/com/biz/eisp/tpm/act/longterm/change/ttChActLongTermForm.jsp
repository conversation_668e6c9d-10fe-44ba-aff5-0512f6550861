<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="west" style="width:380px;">
        <t:formvalid formid="formobj" layout="div" dialog="true" action="ttActLongtermChangeController.do?saveTtChActLongTerm" refresh="true"
                     beforeSubmit="handlerSubmit">
            <div>
                <!-- id -->
                <input name="id" type="hidden" value="${vo.id}">
                <!-- 头表code -->
                <input name="billCode" id="billCode" type="hidden" value="${vo.billCode}">
                <!-- 流程状态 -->
                <input name="bpmStatus" type="hidden" value="1">
                <!-- 拆分明细 -->
                <input name="splitCostJson" id="splitCostJson" type="hidden" value='${vo.splitCostJson}' />
                <!-- 拆分到产品明细 -->
                <input name="splitCostToTermJson" id="splitCostToTermJson" type="hidden" value='${vo.splitCostToTermJson}' />

                <!-- 是否改变了值 -->
                <input id="isViewCostTermForJson" type="hidden" value='true'/>

                <!--  金额口否为负数-->
                <input id="isNagative" type="hidden" value='${isNagative}'/>

                <!-- 是否到产品 -->
                <input id="booleanDepoly" type="hidden" value="${booleanDepoly}">

                <input id="productCode" type="hidden" value="${vo.productCode}">
                <input id="productName" type="hidden" value="${vo.productName}">
                <div class="form">
                    <label class="Validform_label">活动名称: </label>
                    <input	name="billName" id="billName"
                              datatype="/^[0-9a-zA-Z\u4e00-\u9fa5,`~·~\<\>,，。？：；《》【】‘’!！\-（）“”—……、\?\|\{\}\[\]\.\(\)]{1,30}$/"
                              class="inputxt" value="${vo.billName}" errormsg="只能填写汉字，数字，大小写英文，标点符号"/>
                    <span style="color: red;">*</span>
                </div>


                <div class="form">
                    <label class="Validform_label">活动开始时间: </label>
                    <input name="beginDate" id="beginDate" datatype="*" class="Wdate" style="width: 150px;" onchange="initDate()"
                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'${oriBeginDate}',maxDate:'#F{$dp.$D(\'endDate\')}',onpicked:function(){$('.Wdate').blur();}})"
                           readonly="readonly"  value="${vo.beginDate}" />
                    <span style="color: red;">*</span>
                </div>
                <div class="form">
                    <label class="Validform_label">活动结束时间: </label>
                    <input name="endDate" id="endDate" datatype="*" class="Wdate" style="width: 150px;" onchange="initDate()"
                           onclick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'beginDate\')||\'%y-%M-%d\'}',onpicked:function(){$('.Wdate').blur();}})"
                           readonly="readonly"  value="${vo.endDate}" />
                    <span style="color: red;">*</span>
                </div>


                <div class="form">
                    <label class="Validform_label">流程类型: </label>
                    <t:dictSelect id="actTypeCode" field="actTypeCode" type="select" defaultVal="${vo.actTypeCode}"
                                  typeGroupCode="longterm_act_type" dataType="*">
                    </t:dictSelect>
                    <span style="color: red;">*</span>
                </div>

                <div class="form">
                    <label class="Validform_label">活动细类: </label>
                    <input name="costAccountName" id="costAccountName" class="inputxt"  datatype="*" readonly="readonly"  readonly="readonly" value="${vo.costAccountName}" />
                    <input type="hidden" id="costAccountCode" datatype="*" name="costAccountCode" value='${vo.costAccountCode}' >
                    <input type="hidden" id="costTypeCode" name="costTypeCode" value='${vo.costTypeCode}' >
                    <input type="hidden" id="costTypeName" name="costTypeName" value='${vo.costTypeName}' >
                    <span style="color: red">*</span>
                    <span class="Validform_checktip" id="costError"></span>
                </div>

                <div class="form">
                    <label class="Validform_label">费用归属事业部: </label>
                        <%-- 				<t:comboBox name="businessUnitCode" url="tmCommonMdmController.do?findOrgCombox"
                                             defaultVal="${vo.businessUnitCode}"  width="150"></t:comboBox> --%>
                    <t:comboBox name="businessUnitCode" url="tmCommonMdmController.do?findOrgCombox"
                                defaultVal="${vo.businessUnitCode }" width="150" ></t:comboBox>
                        <%--<input id="businessUnitName" class="easyui-combobox" name="businessUnitName" value="${vo.businessUnitName}"/>
                        <input id="businessUnitCode" name="businessUnitCode" value="${vo.businessUnitCode}" type="hidden"/>--%>
                </div>


                <div class="form">
                    <label class="Validform_label">活动总金额: </label>
                    <input name="amount" id="amount" datatype="*" class="inputxt" value="${vo.amount}" onchange="writeAmount()"/>
                    <span style="color: red;">*</span>
                </div>

                <div class="form">
                    <label class="Validform_label">客户名称: </label>
                    <input type="hidden" id="customerCode" name="customerCode" value="${vo.customerCode }">
                    <input readonly="readonly" class="inputxt" id="customerName" name="customerName" value="${vo.customerName }">
                    <%--<a href="#" class="easyui-linkbutton" plain="true" icon="icon-search" onClick="popCustomer();"></a>--%>
                    <span class="Validform_checktip" id="customerError"></span>
                </div>
                <div class="form">
                    <label class="Validform_label">所属组织: </label>
                    <input type="hidden" id="orgCode" name="orgCode" value="${vo.orgCode}">
                    <input type="text" readonly="readonly" datatype="*" class="inputxt" id="orgName" name="orgName" value="${vo.orgName}">
                </div>
                <div class="form">
                    <label class="Validform_label">支付方式: </label>
                    <t:comboBox name="paymentCode" id="paymentCode" url="ttCostAccountController.do?findPaymentComboxByAccountCode&costAccountCode=${vo.costAccountCode}"
                                defaultVal="${vo.paymentCode}" required="true" width="150" onChangeFun="clearPro"></t:comboBox>
                    <span style="color: red;">*</span>
                </div>

                <div class="form">
                    <label class="Validform_label">货补产品:</label>
                    <input type="hidden" id="premiumProductCode"  name="premiumProductCode" value="${vo.premiumProductCode}">
                    <input type="text" id="premiumProductName"  name="premiumProductName" class="inputxt" readonly="readonly" value="${vo.premiumProductName}">
                    <a href="#" class="easyui-linkbutton" plain="true" icon="icon-edit" onClick="openSelectProduct();" ></a>
                </div>

                <div class="form">
                    <label class="Validform_label">文本描述: </label>
                    <textarea id="remark" name="remark" style="resize:none;width:150px;height:100px;">${vo.remark }</textarea>
                </div>
            </div>
        </t:formvalid>
    </div>
    <div region="center">
        <div class="panel datagrid">
            <div class="datagrid-wrap panel-body">
                <div class="datagrid-toolbar">
                    <div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost" onclick="costShareToProduct();">添加产品</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-allot_cost" onclick="costShare();">费用分摊</a>
							<a href="#" class="easyui-linkbutton" plain="true" icon="icon-reset" onclick="resetCost();">重置</a>
						</span>
                        <span style="float:right;margin-right: 30px;">
							待分摊金额:<span id="waiteAmount"></span>
						</span>
                        <div style="clear:both;float: none;height:0;display: block;"></div>
                    </div>
                    <div class="datagrid-toolbar-search">
                    </div>
                </div>
                <div class="datagrid-view">
                    <table class="actTable" id="avgTable">
                        <thead>
                        <tr>
                            <td>序号</td>
                            <td>年月</td>
                            <td>费用金额(元)</td>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
    $(function(){
        //月份费用精确到2位
        $(".actTable").find("input[type='text']").each(function(){
            $(this).numberbox({
                min:0,
                precision:2
            });
        });
        var isNagative = $("#isNagative").val();
        if(isNagative != ''){
            if(isNagative == 0){
                $('#amount').numberbox({
                    min:0,
                    precision:2
                });
            }else{
                $('#amount').numberbox({
                    precision:2
                });
            }
        }
        //编辑时 展示月份分配金额
        if($("#id").val() != ""){splitCostView();}
    });
    //写金额
    function writeAmount(){
        var amount = $("#amount").val();
        $("#waiteAmount").text("");
        $("#waiteAmount").text(amount);
    }
    //清空货补产品
    function clearPro(){
        $("#premiumProductCode").val("");
        $("#premiumProductName").val("");

    }
</script>
<script type="text/javascript">
    //根据所选科目查询支付方式
    function initPaymentComboBox(accountCode){
        $("#paymentCode").val("");
        $("#cbpaymentCode").combobox('clear');
        $("#cbpaymentCode").combobox("reload","ttCostAccountController.do?findPaymentComboxByAccountCode&costAccountCode="+accountCode)
    }
    //弹出选择活动细类
    function openCostAccountSelect(){
        safeShowDialog({
            content : "url:ttCostAccountController.do?goSelectTtCostAccount&actType=longterm_act_type",
            lock : true,
            title : "选择活动细类",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#tmCostAccountList').datagrid('getSelected');
                if (rowsData == '' || rowsData == null) {
                    iframe.tip("请选择一条数据");
                    return false;
                } else {
                    $("#costAccountCode" ).val(rowsData.accountCode);
                    $("#costAccountName").val(rowsData.accountName);
                    $("#costTypeCode" ).val(rowsData.costTypeCode);
                    $("#costTypeName").val(rowsData.costTypeName);
                    initPaymentComboBox(rowsData.accountCode);//加载支付方式
                    //根据选择的活动细类控制金额是否可以为负数
                    validateAmountIsNagative(rowsData.accountCode);
                    //活动是否到产品
                    validateIsDepoly(rowsData.accountCode);
                    $("#costError").removeClass("Validform_wrong").removeAttr("title").addClass("Validform_right");
                    $("#costAccountName").removeClass("Validform_error");
                    changeViewCostTermForJson();
                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }

    //根据活动细类加载金额是否到产品
    function validateIsDepoly(costAccountCode){
        var url = "ttBudgetApiController.do?validateCostAccountIsProduct&costAccountCode="+costAccountCode;
        $.ajax({
            url:url,
            type:"post",
            success:function(data){
                var d = $.parseJSON(data);
                if(d.success){
                    $("#booleanDepoly").val(d.obj);
                }
            }
        });
    }

    //根据活动细类加载金额可否为负
    function validateAmountIsNagative(costAccountCode){
        var url = "ttBudgetApiController.do?validateAmountIsNagative&costAccountCode="+costAccountCode;
        $.ajax({
            url:url,
            type:"post",
            success:function(data){
                var d = $.parseJSON(data);//1是,0否
                if(d.success){
                    var nagative = d.obj;
                    if(nagative == 0){
                        $('#amount').numberbox({min:0,precision:2});
                    }else{
                        $('#amount').numberbox({precision:2});
                    }
                }else{
                    tip(d.msg,"error")
                }
            }
        });
    }
    //展示月份金额
    function splitCostView(){
        var splitCostJson = '${vo.splitCostJson}';
        if(splitCostJson == ""){return false;}
        var splitCostObj = JSON.parse(splitCostJson);
        if(splitCostObj != null){
            $.each(splitCostObj,function(k,v){
                var i=Number(k)+1;
                var str='<tr name="contents">'
                    +'<td>'+ i +'</td>'
                    +'<td name="yearMonth">'+v.yearMonth+'</td>'
                    +'<td><input type="text" name="avgAmount" id="'+v.yearMonth+'"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="'+v.amount+'"  onblur="deductionAmount();" placeholder="请填写金额"/></td>'
                    +'</tr>';
                $(".actTable tbody").append(str);
            });
        }
    }

    //费用分摊到产品--跳转到产品选择
    function costShareToProduct(){
        var account = $("#costAccountCode").val();
        if(account == '' || account == null){
            tip("请选择活动细类",'warning');
            return false;
        }
        var dep = $("#booleanDepoly").val();
        if(dep == 'false'){
            tip("该活动细类不支持到产品","warning");
            return false;
        }
        var amount = $("#amount").val();
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        if(amount == null || amount == '' ||beginDate == null || beginDate == '' || endDate == null || endDate == ''){
            tip("金额，开始日期,结束日期不能为空");
            return false;
        }
        var splitCostToTermJson = [];
        $("#avgTable").find("input[type='text']").each(function(){
            var con = {};
            con.yearMonth = $(this).attr("id");
            con.amount = $(this).val();
            splitCostToTermJson.push(con);
        });
        splitCostToTermJson = JSON.stringify(splitCostToTermJson);
        var url ="ttActLongTermController.do?goTtSplitCostToProductMain";
        url += "&isViewCostTermForJson=" + $("#isViewCostTermForJson").val();
        var xx = $("#splitCostToTermJson").val();
        if(xx != "" && xx != '[]'){
            splitCostToTermJson = xx;
        }
        url += "&splitCostToTermJson="+splitCostToTermJson;
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : "分摊费用到产品",
            width : 850,
            height : 500,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var splitCostToTermJson =[];
                var tr= iframe.$(".actTable tbody tr");
                var rows=[];
                tr.each(function(trindex,tritem){//遍历每一行
                    var tdArr = $(this).children();
                    var productCode=tdArr.eq(3).find("input").eq(0).val();//产品编码
                    var productName=tdArr.eq(3).find("span").eq(0).html();//产品名称
                    var yearMonth = tdArr.eq(2).html();//年月
                    var amount =tdArr.eq(4).find("input").eq(0).val();//金额
                    var row={"amount":amount,"yearMonth":yearMonth,"productName":productName,"productCode":productCode};
                    rows.push(row);
                });

                $("#splitCostToTermJson").val(JSON.stringify(rows));
                if(rows.length == 0){
                    $("#isViewCostTermForJson").val("false");
                }else{
                    $("#isViewCostTermForJson").val("true");
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    //当选择日期的时候，加载日期
    function initDate(){
        var beginDate = $("#beginDate").val();
        var endDate = $("#endDate").val();
        if(beginDate == null || beginDate == '' || endDate == null || endDate == '' ){
            return;
        }
        $(".actTable tbody").html("");
        var yearMonths=getMonthBetween(beginDate,endDate);
        if(yearMonths!=null&&yearMonths.length>0){
            $.each(yearMonths,function(k,v){
                var i=Number(k)+1;
                var str='<tr name="contents">'
                    +'<td>'+ i +'</td>'
                    +'<td name="yearMonth">'+v+'</td>'
                    +'<td><input type="text" name="avgAmount" id="'+v+'"  onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="0"  onblur="deductionAmount(this);" placeholder="请填写金额"/></td>'
                    +'</tr>';
                $(".actTable tbody").append(str);
            });
        }
        changeViewCostTermForJson();
    }
    //获取两月份之间的所有年月
    function getMonthBetween(start,end){
        var result = [];
        var url = "ttActLongTermController.do?getBetweenYearMonth&minDate="+start+"&maxDate="+end;
        $.ajax({url:url,type:"post",async:false,success:function(data){
            var d = $.parseJSON(data);
            result = d;
        }});
        return result;
    }
    function deductionAmount(obj){
        var sumAmount = $("#amount").val();//填写总金额
        var writeAmount = $(obj).val();//每行填写的金额
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            $(obj).val("0");
            return;
        }
        if(Number(writeAmount) > Number(sumAmount)){
            tip("输入的金额不能大于总分配金额","error");
            $(obj).val("0");
            return;
        }
        var rows = $("#avgTable tbody tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {return;}
        var waitingAssgin = sumAmount;
        var totalAmount=0;
        $.each(rows, function(i, row) {
            var avgAmount = $(row).find("input[name='avgAmount']").val();
            totalAmount=addNum(totalAmount,Number(avgAmount));
        });
        if(Number(totalAmount) > Number(sumAmount)){tip("所有列的总金额不能大于待分配金额","error");$(obj).val("0");return;}
        $("#waiteAmount").text("0");
        $("#waiteAmount").text(subtract(sumAmount,totalAmount));
        changeViewCostTermForJson();
    }
    //费用分摊
    function costShare(){
        var sumAmount = $("#amount").val();
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            return;
        }
        var rows = $("#avgTable tbody tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {
            return;
        }
        var waitingAssgin = sumAmount;
        var avgAmount = (waitingAssgin / amountSize).toFixed(2);
        var totalAmount=0;
        $.each(rows, function(i, row) {
            totalAmount=addNum(totalAmount,avgAmount);
            $(row).find("input[name='avgAmount']").val(avgAmount);
        });

        if(Number(totalAmount)!=Number(sumAmount)){
            var num=subtract(sumAmount,totalAmount);
            $(rows[rows.length -1]).find("input[name='avgAmount']").val(addNum(avgAmount,num));
            totalAmount = addNum(totalAmount,num);
        }
        $("#waiteAmount").text();
        $("#waiteAmount").text(subtract(sumAmount,totalAmount));
        changeViewCostTermForJson();
    }
    //重置
    function resetCost(){
        var rows = $("#avgTable tbody tr");
        $.each(rows,function(i,row){
            $(this).find("input[name='avgAmount']").val("");
        });
    }
    //是否显示产品分摊信息
    function changeViewCostTermForJson(){
        $("#isViewCostTermForJson").val("false");
        $("#splitCostToTermJson").val("");
    }
    //统一选择支付方式为货补时 弹出选择货补产品
    function openSelectProduct(){
        var payment=$("#paymentCode").val();
        if(20!=Number(payment)){
            tip("请选择支付方式为货补");
            return;
        }
        safeShowDialog({
            content : "url:tdProductApiController.do?goTmPremiumProductMain",
            lock : true,
            title : "选择货补产品",
            width : 500,
            height : 450,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var rowsData = iframe.$('#ttProductList').datagrid('getSelected');
                if (!rowsData || rowsData.length == 0) {
                    newTip('请选择货补产品!');
                    return false;
                }
                if (rowsData.length > 1) {
                    newTip('只能选择一条数据');
                    return false;
                }
                $("#premiumProductName").val(rowsData.productName);
                $("#premiumProductCode").val(rowsData.maproductCodetnr);

                return true;
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
    /**********************************************************************************************************/
    //提交
    function handlerSubmit(){
        //验证
        var bd = $("#beginDate").val();
        var ed = $("#endDate").val();
        if(bd > ed){
            tip("活动开始时间大于活动结束时间");
            return false;
        }
        //组装月份分摊json
        var splitJson = [];
        $(".actTable").find("input[type='text']").each(function(){
            var splitCostValue = $(this).val();
            var splitData = {};
            if(splitCostValue != ""){
                splitData.amount = splitCostValue;
                splitData.yearMonth = $(this).attr("id");
                splitJson.push(splitData);
            }
        });
        if(splitJson.length == 0){
            tip("未分摊金额到月份");
            return false;
        }
        $("#splitCostJson").val(JSON.stringify(splitJson));

        //当保存了产品之后,改变了主页面的内容,需要重新填写
        var dep = $("#booleanDepoly").val();
        if(dep == 'true'){
            var val = $("#splitCostToTermJson").val();
            if(val == "" || val == '[]'){
                tip("活动细类到产品,但是没有选择产品,请检查");
                return false;
            }
        }
        /*var isChange = $("#isViewCostTermForJson").val();
         if(isChange == "false"){
         tip("主页面数据已经改变,请重新选择产品");
         return false;
         }*/
        //检查分摊金额是否等于总金额
        return isEqualSplitCostAmount();
    }

    //检查总金额是否等于分摊金额总和
    function isEqualSplitCostAmount(){
        var amount = $("#amount").val();
        var splitCostAmount = 0;
        //计算分摊金额总和
        $(".actTable").find("input[type='text']").each(function(){
            var splitCostValue = $(this).val();
            if(splitCostValue != ""){
                splitCostAmount = addNum(splitCostAmount,splitCostValue);
            }
        });
        if(amount != splitCostAmount){
            tip("活动总金额与分摊总金额不相等,请检查");
            return false;
        }
        return true;
    }
    /************************************************************************************************************/

    //获取客户
    function popCustomer(){
        popMyClick("customerName,customerCode","customerName,customerCode","tmCommonMdmController.do?goCustomerSearch&singleSelect=true" ,400,400);
    }
    /************************************************************************************************************/
    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }
    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }
    /*
     * 高精减法函数
     */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精度加法函数
     */
    function addNum(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();
        try {
            m += s1.split(".")[1].length;
        } catch (e) { }
        try {
            m += s2.split(".")[1].length;
        } catch (e) {}
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch(e) {
            r2 = 0;
        }
        return Math.pow(10, Math.max(r1, r2));
    }

    function popMyClick(obj, name, url, width, height) {
        var names = name.split(",");
        var objs = obj.split(",");
        safeShowDialog({
            content : "url:" + url,
            lock : true,
            title : "选择",
            width : width==null?700:width,
            height : height==null?400:height,
            left :'85%',
            cache : false,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var selected = iframe.getSelectRows();
                if (selected == '' || selected == null || selected.length != 1 ) {
                    iframe.$.messager.alert('错误',"选择一条数据","error");
                    return false;
                } else {
                    for ( var i1 = 0; i1 < names.length; i1++) {
                        var str = "";
                        $.each(selected, function(i, n) {
                            if (i == 0){
                                str += n[names[i1]];
                            }else {
                                str += ",";
                                str += n[names[i1]];
                            }
                        });
                        if ($("#" + objs[i1]).length >= 1) {
                            $("#" + objs[i1]).val("");
                            $("#" + objs[i1]).val(str);
                        } else {
                            $("input[name='" + objs[i1] + "']").val("");
                            $("input[name='" + objs[i1] + "']").val(str);
                        }
                    }
                    $("#orgCode").val("");
                    $("#orgName").val("");
                    $("#orgCode").val(selected[0].orgCode);
                    $("#orgName").val(selected[0].orgName);

                    $("#customerError").removeClass("Validform_wrong").removeAttr("title").addClass("Validform_right");
                    $("#customerName").removeClass("Validform_error");


                    return true;
                }
            },
            cancelVal : '关闭',
            cancel : true
        });
    }
</script>