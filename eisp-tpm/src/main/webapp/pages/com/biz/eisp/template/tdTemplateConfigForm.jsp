<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true"
                 action="tdTemplateController.do?saveTdTemplateConfig">
        <input type="hidden" name="id" value="${templateConfig.id}">
        <div>

            <div class="form">
                <label class="Validform_label">所属模块:</label>
                <input class="inputxt" name="tplPartName" id="tplPartName" value="${templateConfig.tplPartName}">
                <input type="hidden" name="tplPartId" id="tplPartId" value="${templateConfig.tplPartId}">
                <t:choose hiddenName="tplPartId" hiddenid="id"
                          url="tdTemplateController.do?goTdTemplatePartMain&type=choose" name="tdTemplatePartDataGrid"
                          icon="icon-search"
                          title="模块列表" textname="tplPartName" inputTextname="tplPartName" isclear="true"
                          width="400" height="400"></t:choose>
            </div>

            <div class="form">
                <label class="Validform_label">优先级:</label>
                <input class="inputxt" name="displaySort" value="${templateConfig.displaySort}">
            </div>
            <div class="form">
                <label class="Validform_label">备注:</label>
                <textarea name="risk" id="risk" rows="5" cols="40">${templateConfig.risk}</textarea>
            </div>
            <div class="form">
                <label class="Validform_label">结构代码（tplElement）:</label>
                <textarea name="structureXml" id="structureXml" rows="5" cols="40">${templateConfig.structureXml}</textarea>
                <input type="hidden" id="hiddenHtmlEditorGetId" value="structureXml">
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-log"
                   onclick="openHtmlEditor()" title="打开新窗口编辑代码">Html</a>
            </div>

            <div class="form">
                <label class="Validform_label">json数据（jsonContent）:</label>
                <textarea name="jsonContent" id="jsonContent" rows="5" cols="40">${templateConfig.jsonContent}</textarea>
                <input type="hidden" id="hiddenJsonEditorGetId" value="jsonContent">
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-log"
                   onclick="openJsonEditor()" title="打开新窗口编辑代码">Json</a>
            </div>
            <div class="form">
                <label class="Validform_label">逻辑代码:</label>
                <textarea name="controlCode" id="controlCode" rows="5" cols="40">${templateConfig.controlCode}</textarea>
                <input type="hidden" id="hiddenJavascriptEditorGetId" value="controlCode">
                <a href="#" class="easyui-linkbutton" plain="true" icon="icon-log"
                   onclick="openJavascriptEditor()" title="打开新窗口编辑代码">Javascript</a>
            </div>


        </div>
    </t:formvalid>
</div>


<script>
    function openJsonEditor() {
        $.dialog({
            title: "json编辑窗口",
            content: "url:tdTemplateController.do?goEditorForm&type=json",
            lock: true,
            width: "500",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var jsonContent = iframe.$("#jsonContent").val();
                $("#jsonContent").val(jsonContent);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    function openHtmlEditor() {
        $.dialog({
            title: "html编辑窗口",
            content: "url:tdTemplateController.do?goEditorForm&type=html",
            lock: true,
            width: "500",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var jsonContent = iframe.$("#htmlContent").val();
                $("#structureXml").val(jsonContent);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }

    function openJavascriptEditor() {
        $.dialog({
            title: "javascript编辑窗口",
            content: "url:tdTemplateController.do?goEditorForm&type=javascript",
            lock: true,
            width: "500",
            height: "400",
            zIndex: 10000,
            parent: windowapi,
            ok: function () {
                iframe = this.iframe.contentWindow;
                var jsonContent = iframe.$("#javaScriptContent").val();
                $("#controlCode").val(jsonContent);
            },
            cancelVal: '关闭',
            cancel: true
        });
    }
</script>