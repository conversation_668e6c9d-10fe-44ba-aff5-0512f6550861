<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>期初费用预算</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttBudgetCustomerController.do?saveBudgetCustomer" refresh="true" beforeSubmit="validateOnly()">
	<input type="hidden" name="id" value="${vo.id }">
	
	<c:if test="${not empty vo.id }">
		<div class="form">
			<label class="Validform_label">预算编码: </label>
			<input type="text" readonly="readonly" value="${vo.budgetPeriodCode}" class="inputxt">
		    <span style="color: red">*</span>
		</div>
	</c:if>
	
	<div class="form">
		<label class="Validform_label">年: </label>
		<input type="text" id="year" name="year" value="${vo.yearMonth}" class="Wdate"
				   onclick="WdatePicker({dateFmt:'yyyy'})" readonly="readonly" datatype="*" 
				   errormsg="年月不能为空" style="width:150px;" onblur="validateOnly()">
				   <span style="color: red">*</span>
	</div>
	
	<div class="form">
		<label class="Validform_label">季度: </label>
		<select name="quarter" id="quarter" datatype="*" onblur="validateOnly()">
			<option value="">--请选择--</option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
		</select>
		<span style="color: red">*</span>
	</div>
	
	<div class="form">
		<label class="Validform_label">部门: </label>
		<input id="orgName" name="orgName" class="easyui-combotree" value="${targetVo.orgName }"/>
		<input type="hidden" name="orgId" id="orgId" dataType="*"/>
		<span style="color: red;">*</span>
		<span class="Validform_checktip" ></span>
	</div>
	
	<div class="form">
		<label class="Validform_label">费用类型: </label>
		<input type="hidden" id="costTypeName" name="costTypeName" value="${vo.costTypeName}">
		<select dataType="*" name="costTypeCode" id="costTypeCode" onblur="validateOnly()">
			<option value="">--请选择--</option>
			<c:forEach items="${costType}" var="cost">
				<option value="${cost.costTypeCode}" <c:if test="${cost.costTypeCode == vo.costTypeCode}">selected='selected'</c:if>  >${cost.costTypeName}</option>		
			</c:forEach>
		</select>
		<span style="color: red;">*</span>
	</div>
	
	<div class="form">
			<label class="Validform_label" name="custName">客户: </label> 
			<input id="customerName" name="customerName" value="${vo.customerName}" class="inputxt" readonly="readonly"/>
			<input id="customerCode" name="customerCode" value="${vo.customerCode}" type="hidden" datatype="*"/> 
			<span style="color: red;">*</span>
			<a href="#" class="easyui-linkbutton" plain="true" name="choose" icon="icon-search" onClick="openCustomerSelect()"></a> 
	</div> 
	
	<div class="form">
		<label class="Validform_label">期初金额: </label>
		<input name="amount" id="amount" datatype="/^(([1-9]\d*)|\d)(\.\d{1,2})?$/" errormsg="只能输入数字，带两位小数" 
		       class="inputxt" value="${vo.amount}" onblur="validateAmount()"/>
		<span style="color: red;">*</span>
		<span class="Validform_checktip" id="useBalance"></span>
		<input type="hidden" id="balanceAmount">
	</div>
</t:formvalid>
</body>
</html>
<script type="text/javascript">
//校验填写的金额是否大于可用余额
function validateAmount(){
	var useable = $("#balanceAmount").val();
	var amount = $("#amount").val();
	if(parseFloat(amount) > parseFloat(useable)){
		tip("填写的期初金额大于可用余额");
		$("#amount").val("");
		$("#useBalance").addClass("Validform_wrong");
	}else{
		$("#useBalance").removeClass("Validform_wrong");
	}
}

//根据选择的年+季度+组织+费用类型,实时的计算可新增的余额
function loadAvailableBalanceAmount(){
	var year = $("#year").val();
	var quarter = $("#quarter").val();
	var orgId = $("#orgId").val();
	var costTypeCode = $("#costTypeCode").val();
	
	var flag = true;
	
	if(year != "" && quarter != "" && orgId != "" && costTypeCode != ""){
		//加载余额
		var url2 = "ttBudgetCustomerController.do?getBudgetCustomerMaxAmount&year="+ year+ "&quarter="+ quarter+ "&orgId="+ orgId + "&costTypeCode=" + costTypeCode;
		$.ajax({
			url : url2,
			type : "post",
			async:false,
			success : function(data) {
				var d = $.parseJSON(data);
				if (d.success == false) {
					flag = false;
					tip(d.msg);
				} else {
					$("#useBalance").text("");
					$("#useBalance").text("可填最大期初金额"+d.obj);
					$("#balanceAmount").val(d.obj);
				}
			}
		});
	}
	return flag;
}
//获取客户
function openCustomerSelect(){
	var targetUrl = "ttIncomeTargetController.do?goCustomerSelectMain";
	createwindowExt('选择客户',targetUrl,'450','350',{
		ok:function(){
			iframe = this.iframe.contentWindow;
			var seletctTarget = iframe.$('#customerSelect').datagrid('getSelections');
				if(seletctTarget==null || seletctTarget==""){
					tipChooseData();
				return false;
			}
			var customerName = seletctTarget[0].customerName;
			$("#customerName").val(customerName);
			$("#customerCode").val(seletctTarget[0].customerCode);
		}
	});
}

//校验年+季度+组织+费用类型唯一,要在期初中存在
function validateOnly(){
	var year = $("#year").val();
	var quarter = $("#quarter").val();
	var orgId = $("#orgId").val();
	var cost = $("#costTypeCode").val();
	
	var flag = true;
	
	if(year != "" && quarter != "" && orgId != "" && cost != ""){
		//调用一次加载期初最大金额
		flag = loadAvailableBalanceAmount();
		if(flag == false){
			return false;
		}
		var url = "ttBudgetCustomerController.do?validateYearQuartOrgCostOnly&year="+year+"&quarter="+quarter+"&orgId="+orgId+"&costTypeCode="+cost;
		$.ajax({
			url:url,
			type:"post"
			,async:false,
			success:function(data){
			var d = $.parseJSON(data);
			if(d.success == false){
				flag = false;
				tip(d.msg);
			}
		}});
	}
	if(flag == false){
		return false;
	}
}

//初始值
$(document).ready(function(){
	$("#orgName").combotree('reload','tmOrgController.do?getParentOrg&selfId='+'');
	$("#orgName").combotree({
		onShowPanel:function (){
		var text=$("#orgName").combotree('getText');
			$("#orgName").combotree('setValue',text);
			$("#orgName").combotree('reload','tmOrgController.do?getParentOrg');
		},onChange:function(){
			validateOnly();
		}
	});
	$('#orgName').combotree({
		width: 150,
		onSelect : function(node) {
			$("#orgId").val(node.id);
		}
	});
});

</script>