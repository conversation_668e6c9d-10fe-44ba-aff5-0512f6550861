<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>产品政策计算</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body>
<div region="center" fit="true">
    <t:formvalid formid="formobj" layout="div" dialog="true" refresh="true" action="ttProductCalculateController.do?saveManualCalculate">
    <div class="form">
        <label class="Validform_label">活动编码:</label>
        <input class="inputxt" name="actCode" datatype="*"><span style="color: red">*</span>
    </div>
    <div class="form">
        <label class="Validform_label">核算结案金额:</label>
        <input class="inputxt" name="auditAmount" datatype="fixed2"><span style="color: red">*</span>
    </div>
</div>
</t:formvalid>
</div>

</body>
</html>

<script type="text/javascript">
    $(function () {
        $.Datatype.fixed2 = function(gets, obj, curform, regxp) {
            reg2 = /^(\-\d|\d)+(?:\.\d{1,2})?$/;
            var value = curform.find("input[name='" + obj.attr("name") + "']").val().trim();
            if(value.length == 0) {
                return "请填写数据";
            }
            return value.length!=0 && (reg2.test(value)) ? true : "最多两位的小数的正浮点数";
        };
    })
</script>
