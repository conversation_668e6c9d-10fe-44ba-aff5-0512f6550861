<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_ttcostaccountlist" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="ttCostAccountList" title="活动细类管理"  actionUrl="ttCostAccountController.do?findTtCostAccountList" 
	  		 idField="id" fit="true"  fitColumns="true" pagination="true" queryMode="group">
	  		<t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
  		    <t:dgCol title="活动细类编号" field="accountCode" query="true" width="150"></t:dgCol>
  		    <t:dgCol title="活动细类名称" field="accountName" query="true" width="150"></t:dgCol>
  		    <t:dgCol title="活动大类名称" field="costTypeName" query="true" width="150"></t:dgCol>
  		    <t:dgCol title="活动大类编码" field="costTypeCode" query="true" width="150"></t:dgCol>
			<t:dgCol title="产品组" field="prodGroupName" query="true" width="200"></t:dgCol>
  		    <t:dgCol title="预算科目名称" field="financialAccountName" query="true" width="150"></t:dgCol>
  		    <t:dgCol title="预算科目编码" field="financialAccountCode" query="true" width="150"></t:dgCol>
  		    <t:dgCol title="备注" field="remark" width="200"></t:dgCol>
  		    <%-- <t:dgCol title="费用归类" field="costClassify" query="true" width="150" dictionary="cost_classify"></t:dgCol> --%>
  		    <t:dgCol title="生效状态" field="enableStatus" query="true" dictionary="enable_status"></t:dgCol>
  		    <t:dgCol title="创建人" field="createName" width="150" query="true"></t:dgCol>
  		    <t:dgCol title="创建时间" field="createDate" width="150" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>
  		    <t:dgCol title="最近更新人" field="updateName" width="150" query="true"></t:dgCol>
  		    <t:dgCol title="最近更新时间" field="updateDate" width="150" formatter="yyyy-MM-dd hh:mm:ss"></t:dgCol>

			<t:dgToolBar operationCode="add" title="录入活动细类" icon="icon-add" url="ttCostAccountController.do?goTtCostAccountForm" funname="add" width="750" height="750"></t:dgToolBar>
			<t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttCostAccountController.do?goTtCostAccountForm" funname="update" width="650" height="720" ></t:dgToolBar>

			<t:dgToolBar operationCode="del" title="删除" icon="icon-remove" url="" funname="del"></t:dgToolBar>

			<t:dgToolBar operationCode="start" title="启用"  icon="icon-start" url="" funname="startOrStop(0)"></t:dgToolBar>
	        <t:dgToolBar operationCode="stop" title="停用"  icon="icon-stop" url="" funname="startOrStop(1)"></t:dgToolBar>
	        <t:dgToolBar operationCode="view" title="查看"  icon="icon-look" url="" funname="viewAccount()"></t:dgToolBar>
			<t:dgToolBar operationCode="out" title="导出" icon="icon-dataOut" url="ttCostAccountController.do?exportXls"  funname="excelExport"></t:dgToolBar>
			<t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="tmLogController.do?goTmLogDetailMain" funname="detailLog" width="1200" height="500"></t:dgToolBar>
		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

    function del(){
        var seletctTarget =  $("#ttCostAccountList").datagrid("getSelections");
        var title = "";
        if(seletctTarget==null || seletctTarget==""){
            tip("必须选择一条数据");
            return;
        }
        $.messager.confirm('操作提示','确定删除?',function(r){
            if (r){
                $.ajax({
                    type : "POST",
                    url : "ttCostAccountController.do?delTtCostAccountById&id="+seletctTarget[0].id,
                    async:false,
                    success : function(data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        $("#ttCostAccountList").datagrid("reload");
                    }
                });
            }
        });
    }


	//查看
	function viewAccount(gridname){
		var select = $("#ttCostAccountList").datagrid('getSelections');
		if(select==null || select==""){
			tip("请至少选择一条数据");
			return false;
		}
		var url = "ttCostAccountController.do?goTtCostAccountView&id="+select[0].id;
		openwindow('查看',url,gridname,650,800);
	}
	//启用停用
	function startOrStop(flag){
		var costAccountTarget = $("#ttCostAccountList").datagrid('getSelected');
		if(costAccountTarget == null){
			tip("请选择一条要操作的数据");
			return;
		}
		var tipmMsg = "";
		if(flag == 0){
			tipmMsg = "确定要启用该数据吗?"
			if(costAccountTarget.enableStatus == flag){
				tip("已经处于启用状态,无需再次启用");
				return false;
			}
		}else{
			if(costAccountTarget.enableStatus == flag){
				tip("已经处于停用状态,无需再次停用");
				return false;
			}
			tipmMsg = "确定要停用该数据吗?"
		}
		$.messager.confirm('操作提示',tipmMsg,function(r){ 
		    if (r){
		    	$.ajax({
		        	type : "POST",
		        	url : "ttCostAccountController.do?startOrStop",
		        	data : {
		            	"id" : costAccountTarget.id,
		            	"enableStatus": flag
		        	},
		        	dataType : "json",
		        	success : function(data) {
		        		tip(data.msg);
		        		$("#ttCostAccountList").datagrid('reload');
		        	},
		        	error:function(){
		        		tip("客户端请求异常,请稍后再试");
		        	}
			   });
		    }
		});
	}
</script>
