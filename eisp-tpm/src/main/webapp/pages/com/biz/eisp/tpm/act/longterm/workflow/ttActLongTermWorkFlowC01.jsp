<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
	<div region="center" style="padding:1px;">
		<t:datagrid name="ttProductActWorkFlowList" checkbox="false" fitColumns="true"  title="" pagination="false"
					actionUrl="ttActLongTermWorkFlowController.do?findTtActLongTermWorkFlowC01&flagKey=${flagKey}" idField="id" fit="true" queryMode="group">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol field="orgName" title="组织" ></t:dgCol>
			<t:dgCol field="yearMonth" title="年月"  ></t:dgCol>
			<t:dgCol field="productName" title="品类" hidden="true" ></t:dgCol>
			<t:dgCol field="budgetIncome" title="必保收入预算"  ></t:dgCol>
			<t:dgCol field="monthlyplan" title="月度销售计划" ></t:dgCol>
			<t:dgCol field="finacialName" title="预算科目"></t:dgCol>
			<t:dgCol field="admBudgetAmount" title="管理版预算金额"  sortable="false" ></t:dgCol>
			<t:dgCol field="amount" title="费用金额"></t:dgCol>
			<t:dgCol  field="accumulativeAmount"  title="累计费用" sortable="false" ></t:dgCol>
			<t:dgCol field="balanceAmount" title="结余费用"   ></t:dgCol>
			<t:dgCol field="rateStr" title="费率"  ></t:dgCol>
			<t:dgCol field="accumulativeRateStr" title="累计费率"></t:dgCol>
		</t:datagrid>
	</div>
</div>

