<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
<title>活动细类</title>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="" refresh="true">
	<input type="hidden" name="id" value="${vo.id }">
	<input type="hidden" name="enableStatus" value="0">
	
	<div class="form">
		<label class="Validform_label">活动细类编号: </label>
		<input disabled="disabled" class="inputxt" value="${vo.accountCode}" />
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">活动细类名称: </label>
		<input class="inputxt" value="${vo.accountName}" disabled="disabled"/>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">活动方式: </label>
		<t:dictSelect  id="actMode" isView="true" field="actMode" type="select" defaultVal="${vo.actMode}" typeGroupCode="act_mode"></t:dictSelect>
	</div>

	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">是否核销: </label>
		<t:dictSelect id="hasAudit" field="hasAudit" type="select" isView="true" defaultVal="${vo.hasAudit}" typeGroupCode="yesorno"  hasLabel="true" title="是否核销">
		</t:dictSelect>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">超额核销比例(%): </label>
		<input class="inputxt" value="${vo.overAuditScale}" disabled="disabled"/>
	</div>
	<div class="form">
		<label class="Validform_label">超额核销控制: </label>
		<t:dictSelect id="hasOverAudit" field="hasOverAudit" type="radio" defaultVal="${vo.hasOverAudit}"
					  typeGroupCode="hasOverAudit" hasLabel="true" title="超额核销控制" dataType="*" isView="true">
		</t:dictSelect>
	</div>


	<div class="form">
		<label class="Validform_label">核销有效期(月): </label>
		<input class="inputxt" value="${vo.auditValidMonth}" disabled="disabled"/>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">金额是否可以为负: </label>
		<t:dictSelect id="hasNegativeAmount" field="hasNegativeAmount" type="select" defaultVal="${vo.hasNegativeAmount}"
			 typeGroupCode="yesorno" hasLabel="true" title="金额是否可以为负" isView="true">
		</t:dictSelect>
	</div>
	
	<div class="form">
		<label class="Validform_label">是否推送SCI: </label>
		<t:dictSelect id="hasPushSfa" field="hasPushSfa" type="select" defaultVal="${vo.hasPushSfa}"
			 typeGroupCode="yesorno" hasLabel="true" title="是否推送SFA" isView="true">
		</t:dictSelect>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">系统界面: </label>
		<t:dictSelect id="actType" field="actType" type="checkbox" defaultVal="${vo.actType}"
			 typeGroupCode="act_type" hasLabel="true" title="系统界面" isView="true" lineNum="2">
		</t:dictSelect>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">是否允许多次核销: </label>
		<t:dictSelect id="hasMultipleAudit" dataType="*" field="hasMultipleAudit" type="radio" defaultVal="${vo.hasMultipleAudit}"
			 typeGroupCode="yesorno" hasLabel="true" title="是否允许多次核销" isView="true">
		</t:dictSelect>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">支付方式: </label>
		<t:dictSelect id="paymentModern" field="paymentModern" type="checkbox" defaultVal="${vo.paymentModern}"
			 typeGroupCode="payment_type"  hasLabel="true" title="支付方式" isView="true">
		</t:dictSelect>
	</div>
	
	<!-- 必填 -->
	<div class="form">
		<label class="Validform_label">活动发布要求: </label>
		<t:dictSelect id="actDeployRequire" field="actDeployRequire" type="checkbox" defaultVal="${vo.actDeployRequire}"
			 typeGroupCode="act_deploy_require" hasLabel="true" title="活动发布要求" isView="true">
		</t:dictSelect>
	</div>

	<div class="form" id="customerType">
		<label class="Validform_label">是否控制门店金额: </label>
		<t:dictSelect id="hasTerminal" field="hasTerminal" type="radio" defaultVal="${vo.hasTerminal}"
					  typeGroupCode="yesorno" hasLabel="true" title="是否控制门店金额" dataType="*" isView="true">
		</t:dictSelect>
	</div>
	<div class="form" id="customerType">
		<label class="Validform_label">是否控制产品金额: </label>
		<t:dictSelect id="hasProduct" field="hasProduct" type="radio" defaultVal="${vo.hasProduct}"
					  typeGroupCode="yesorno" hasLabel="true" title="是否控制产品金额" dataType="*" isView="true">
		</t:dictSelect>
	</div>

	<div class="form">
		<label class="Validform_label">活动数据采集要求: </label>
		<t:dictSelect id="actDaRequire" field="actDaRequire" type="checkbox" defaultVal="${vo.actDaRequire}"
			 typeGroupCode="act_da_require" hasLabel="true" title="活动数据采集要求" isView="true">
		</t:dictSelect>
	</div>
	
	<div class="form">
		<label class="Validform_label">核销资料: </label>
		<t:dictSelect id="auditMaterialModern" field="auditMaterialModern" type="checkbox" defaultVal="${vo.auditMaterialModern}" lineNum="5"
			 dictTable="tt_audit_material"  dictText="material_name" dictField="material_code" hasLabel="true" title="核销资料" isView="true">
		</t:dictSelect>
	</div>
	<div class="form">
		<label class="Validform_label">备注: </label>
		<textarea rows="5" cols="20" disabled="disabled" style="resize:none;">${vo.remark}</textarea>
	</div>
</t:formvalid>
</body>
</html>
