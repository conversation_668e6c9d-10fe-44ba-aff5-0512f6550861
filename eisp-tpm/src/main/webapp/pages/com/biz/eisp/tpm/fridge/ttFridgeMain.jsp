<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript"  src="resources/api/mdm/js/tmSelectMain.js"></script>
<div id="system_ttFridgeMainList" class="easyui-layout" fit="true">
 <div region="center" style="padding: 1px;">
   <t:datagrid name="ttFridgeMainList" title="冰柜台账管理"  actionUrl="ttFridgeMainController.do?findFridgeList"
               idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="true">
   <t:dgCol title="id"  field="id"  hidden="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="冰柜编号"  field="fridgeCode"  hidden="false" query="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="冰柜类型"  field="fridgeType"  hidden="false"  dictionary="fridge_type" query="true" ></t:dgCol>
   <t:dgCol title="冰柜型号"  field="fridgeModel"  hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="冰柜规格"  field="fridgeStandard"  hidden="false" query="true" queryMode="single"  ></t:dgCol>
   <t:dgCol title="冰柜品牌"  field="fridgeSupplier" dictionary="fridge_brand"  query="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="业务归属"  field="type" query="true" dictionary="fridge_ascription" hidden="false"  queryMode="single"  ></t:dgCol>

   <%--<t:dgCol title="客户所属组织编码" field="customerOrgCode" hidden="true"></t:dgCol>--%>
   <t:dgCol title="销售部" field="saleDept" query="true"></t:dgCol>
   <t:dgCol title="组织" field="customerOrgName" query="true"></t:dgCol>

   <t:dgCol title="所属经销商编码"  field="customerCode"  hidden="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="所属客户"  field="customerName" query="true"   hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="客户合作性质"  field="nature"   dictionary="cooperative_type" query="true" hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="所属门店编码"  field="terminalCode"  hidden="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="所属门店"  field="terminalName" query="true"  hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="投放时间"  field="deliveryDate" formatter="yyyy-MM-dd"  hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="购买类型"  field="purchaseType" dictionary="purchase_type" query="true"   hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="责任人"  field="userName"  hidden="false"  queryMode="single"  ></t:dgCol>

   <t:dgCol title="购买时间"  field="purchaseDate" formatter="yyyy-MM-dd" query="true" hidden="false"  queryMode="group"  ></t:dgCol>
   <t:dgCol title="冰柜总金额"  field="fridgeWorth"  hidden="false" query="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="返款总额"  field="totalAmount"  hidden="false"   queryMode="single"  ></t:dgCol>
   <t:dgCol title="返款/折旧年限"  field="rebateYear"  hidden="false" queryMode="single"  ></t:dgCol>
   <t:dgCol title="开始返款/折旧时间"  field="beginRebateDate" formatter="yyyy-MM-dd" hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="结束返款/折旧时间"  field="endRebateDate" formatter="yyyy-MM-dd" hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="累计已返金额"  field="totalRebateAmount" hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="未返金额"  field="isNotRebateAmount" hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="启用状态"  field="enableStatus" dictionary="fridge_status"  hidden="false"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="备注" field="remark"></t:dgCol>


   <t:dgCol title="创建人"  field="createName" query="true"></t:dgCol>
   <t:dgCol title="创建时间"  field="createDate" queryMode="group" query="true" formatter="yyyy-MM-dd hh:mm:ss" ></t:dgCol>
   <t:dgCol title="最近更新人"  field="updateName"  query="true"></t:dgCol>
   <t:dgCol title="最近更新时间"  field="updateDate"  queryMode="group" query="true" formatter="yyyy-MM-dd hh:mm:ss" ></t:dgCol>

   <t:dgCol title="创建职位编码"  field="createPost"  hidden="true"  queryMode="single"  ></t:dgCol>
   <t:dgCol title="修改职位编码"  field="updatePost"  hidden="true"  queryMode="single"></t:dgCol>

   <t:dgToolBar operationCode="add" title="录入" icon="icon-add" url="ttFridgeMainController.do?goTtFridgeForm" funname="add" width="800" height="550"></t:dgToolBar>
   <t:dgToolBar operationCode="edit" title="编辑" icon="icon-edit" url="ttFridgeMainController.do?goTtFridgeForm" funname="doUpdate" width="850" height="550"></t:dgToolBar>
   <t:dgToolBar operationCode="view" title="查看" icon="icon-look" url="ttFridgeMainController.do?goTtFridgeForm" funname="detail" width="800" height="550"></t:dgToolBar>
   <t:dgToolBar operationCode="detail" title="返还明细" icon="icon-look" url="ttFridgeRebateController.do?goFridgeRebateDetail" funname="detail" width="800" height="550"></t:dgToolBar>
   <t:dgToolBar operationCode="start" title="启用"  icon="icon-start" url="ttFridgeMainController.do?doBatchDel" onclick="changeStatus(0)"></t:dgToolBar>
   <t:dgToolBar operationCode="stop" title="停用"  icon="icon-stop" url="ttFridgeMainController.do?doBatchDel" onclick="changeStatus(1)"></t:dgToolBar>
   <t:dgToolBar operationCode="scrap" title="报废"  icon="icon-stop" url="ttFridgeMainController.do?doBatchDel" onclick="changeStatus(2)"></t:dgToolBar>
   <t:dgToolBar operationCode="lose" title="丢失"  icon="icon-remove" url="ttFridgeMainController.do?doBatchDel" onclick="changeStatus(3)"></t:dgToolBar>
   <t:dgToolBar operationCode="dataIn" title="导入" icon="icon-dataIn" onclick="importDataByXml({impName:'ttFridgeMain', gridName:'ttFridgeMainList'})"></t:dgToolBar>
   <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttFridgeMainController.do?exportXls" funname="excelExport"></t:dgToolBar>
   <t:dgToolBar operationCode="dataDetailOut" title="导出明细" icon="icon-dataOut" url="ttFridgeMainController.do?exportDetailXls" funname="excelExport"></t:dgToolBar>
   <t:dgToolBar operationCode="upload" title="附件"  icon="icon-upload" url="tbAttachmentController.do?goTbAttachmentMain&extendService=ttFridgeExtendsService&attachmentType=50" funname="detail"></t:dgToolBar>
   <t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttFridgeMainController.do?goFridgeLogMain" funname="detail" width="1200"></t:dgToolBar>

   <t:dgToolBar operationCode="change" title="批量调整客户" icon="icon-edit" url="" funname="changeFun" width="1200"></t:dgToolBar>


   </t:datagrid>
  </div>
 </div>
 <script type="text/javascript">
     //批量调整客户
    function changeFun(){
        safeShowDialog({
            content : "url:ttFridgeBatchController.do?goUpdateBatchFridgeMain",
            lock : true,
            title : "批量调整冰柜客户",
            width : 1200,
            height : 800,
            left :'50%',
            cache : true,
            cancel:true,
            button:[{name:"确定",callback:function(){
                iframe = this.iframe.contentWindow;

                //加载待选列表
                var exclusiveCodes = "";
                var checkedTarget = iframe.$("#ttActLongtermSelectedList").datagrid("getRows");
                if(checkedTarget != null && checkedTarget != ""){
                    for(var i = 0;i<checkedTarget.length;i++){
                        if(exclusiveCodes != ""){
                            exclusiveCodes += ",";
                        }
                        exclusiveCodes += checkedTarget[i].fridgeCode;
                    }
                }
                var customerCode = iframe.$("#customerCode").val();
                if(customerCode == "" || customerCode == null || customerCode == 'undefine'){
                    iframe.tip("客户必选择","error");
                    return false;
                }
                var flag = true;
                var url = "ttFridgeBatchController.do?updateBatchFridge";
                $.ajax({
                    url:url,
                    type:"post",
                    data:{
                        exclusiveCodes:exclusiveCodes,
                        customerCode:customerCode
                    },success:function(data){
                        var data = $.parseJSON(data);
                        if(data.success == false){
                            flag = false;
                            iframe.tip(data.msg,"error");
                        }else{
                            tip(data.msg);
                            $("#ttFridgeMainList").datagrid('reload');
                        }
                    }
                });
                return flag;
            }}],cancelVal:'关闭'
        });
    }

 $(document).ready(function(){
//     $("input[name='customerOrgId']").combotree({
//         url:'tmOrgController.do?getParentOrg',
//         width:"200"
//     });
     $("input[name='customerOrgName']").attr("readonly",true).attr("id","customerOrgName").attr("style","width:180px")
         .click(function(){openOrgSelect();}).parent().append("<input type='hidden' name='customerOrgCode' id='customerOrgCode'/>");
 });

 //移除焦点
 $("input[name='createDate_begin']").removeAttr("onfocus");
 $("input[name='createDate_end']").removeAttr("onfocus");
 $("input[name='updateDate_begin']").removeAttr("onfocus");
 $("input[name='updateDate_end']").removeAttr("onfocus");
 //给时间控件加上样式
 $("input[name='createDate_begin']").addClass("Wdate").css({
     'height': '20px',
     'width': '150px'
 }).click(function () {
     WdatePicker({dateFmt: 'yyyy-MM-dd'});
 });
 $("input[name='createDate_end']").addClass("Wdate").css({
     'height': '20px',
     'width': '150px'
 }).click(function () {
     WdatePicker({dateFmt: 'yyyy-MM-dd'});
 });
 $("input[name='updateDate_begin']").addClass("Wdate").css({
     'height': '20px',
     'width': '150px'
 }).click(function () {
     WdatePicker({dateFmt: 'yyyy-MM-dd'});
 });
 $("input[name='updateDate_end']").addClass("Wdate").css({
     'height': '20px',
     'width': '150px'
 }).click(function () {
     WdatePicker({dateFmt: 'yyyy-MM-dd'});
 });
 $("input[name='purchaseDate_begin']").addClass("Wdate").css({
     'height': '20px',
     'width': '150px'
 }).click(function () {
     WdatePicker({dateFmt: 'yyyy-MM-dd'});
 });
 $("input[name='purchaseDate_end']").addClass("Wdate").css({
     'height': '20px',
     'width': '150px'
 }).click(function () {
     WdatePicker({dateFmt: 'yyyy-MM-dd'});
 });
 function openOrgSelect(){
     var customerOrgCode = $('#customerOrgCode').val();
     customerOrgCode = (typeof(customerOrgCode) == "undefined") ? '' : customerOrgCode;
     var currentOrgCode='${currentOrgCode}';
     var paraArr = {
         textName: 'orgCode,orgName',
         inputTextName: 'customerOrgCode,customerOrgName',
         searchType: '1',//查询类型？？
         encapsulationType: 'input',//封装类型--不传或默认
         isCouldRemove: false,//false：不可移除已选组织，true：可移除已选组织，默认为true；
         currentOrgCode:currentOrgCode,
         pageData: {
             customerOrgCode: customerOrgCode
         }
     }
     openChooseOrgSelect(paraArr);
 }

function doUpdate(title, url, id, width, height){
    var rowsData = $('#ttFridgeMainList').datagrid('getSelections');
    if (!rowsData || rowsData.length == 0) {
        tip('请选择编辑项目');
        return;
    }
    if (rowsData.length > 1) {
        tip('请选择一条记录再编辑');
        return;
    }
   var enableStatus=rowsData[0].enableStatus;
    if(enableStatus!=0){
        tip('非启用状态不能编辑');
        return;
    }
    var orgCode = "${orgCode}";
    if(orgCode!=null&&orgCode!=""){
        var type = rowsData[0].type;
        if(Number(type) == 1){
            tip('你不能编辑业务归属为资产管理的冰柜！');
            return;
        }
    }else {
        var type = rowsData[0].type;
        if(Number(type) == 0){
            tip('你不能编辑业务归属为费用管理的冰柜！');
            return;
        }
    }
    update(title, url, id, width, height);
}


 function startStatus(){
	 var tmUseTarget = $("#ttFridgeMainList").datagrid('getSelected');
     if(tmUseTarget == null){
         tip("请选择一条要操作的数据");
         return;
     }
     if(tmUseTarget.enableStatus != '1'){
    	 tip("必须处于停用状态的数据才可以启用")
    	 return ;
     }
  	safeShowDialog({
		content : "url:ttFridgeMainController.do?goTtFridgeStartMain&id="+tmUseTarget.id,
		lock : true,
		title : "启用",
		width : 1200,
		height : 800,
		left :'50%',
		cache : true,
		cancel:true,
		button:[{name:"确定",callback:function(){
			var flag = false;
			iframe = this.iframe.contentWindow;
			$('#btn_sub', iframe.document).click();
			$("ttFridgeMainList").datagrid("reload");
			return flag;
		}}],cancelVal:'关闭'
	});
 }

 //启用停用
 function changeStatus(flag){
     var tmUseTarget = $("#ttFridgeMainList").datagrid('getSelected');
     if(tmUseTarget == null){
         tip("请选择一条要操作的数据");
         return;
     }

     var tipmMsg = "确定进行当前操作吗";

     $.messager.confirm('操作提示',tipmMsg,function(r){
         if (r){
             $.ajax({
                 type : "POST",
                 url : "ttFridgeMainController.do?updateFridge",
                 data : {
                     "id" : tmUseTarget.id,
                     "enableStatus": flag
                 },
                 dataType : "json",
                 success : function(data) {
                     tip(data.msg);
                     $("#ttFridgeMainList").datagrid('reload');
                 },
                 error:function(){
                     tip("服务器异常，请稍后再试");
                 }
             });
         }
     });
 }

 </script>
