<%@ page language="java" import="java.util.*" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>结案主单</title>
    <t:base type="jquery,easyui,tools,DatePicker"></t:base>
</head>
<body style="overflow-y: hidden" scroll="no">
<t:formvalid formid="formobj" layout="div" dialog="true" action="ttAuditQuotaMainController.do?saveTtAuditQuotaMain" refresh="true">
    <input type="hidden" id="id" name="id" value="${vo.id }">
    <input type="hidden" id="createDate" name="createDate" value="${vo.createDate }">
    <input type="hidden" id="createName" name="createName" value="${vo.createName }">
    <input type="hidden" id="positionCode" name="positionCode" value="${vo.positionCode }">
    <input type="hidden" id="positionName" name="positionName" value="${vo.positionName }">

    <input type="hidden" id="bpmStatus" name="bpmStatus" value="1">
    <div class="form">
        <label class="Validform_label">结案类型: </label>
        <%--<c:choose>
            <c:when test="${vo.billCode== null}">
                <select id="actTypeCode" name="actTypeCode" datatype="*">
                    <option value=''>--请选择--</option>
                    <c:forEach items="${actTypeList}" var="pay">
                        <option value='${pay.value }' <c:if test="${pay.value eq vo.actTypeCode}">selected</c:if> > ${pay.text}</option>
                    </c:forEach>
                </select>
            </c:when>
            <c:otherwise>
                <t:dictSelect field="actTypeCode"  extendJson="{style:'width:150px'}"
                              divClass="STYLE_LEG" type="select" typeGroupCode="process_audit_quota_type"
                              defaultVal="${vo.actTypeCode}" dataType="*"
                              hasLabel="true" title="结案类型" isView = "true"></t:dictSelect>
            </c:otherwise>
        </c:choose>--%>
        <select id="actTypeCode" name="actTypeCode" datatype="*">
            <option value=''>--请选择--</option>
            <c:forEach items="${actTypeList}" var="pay">
                <option value='${pay.value }' <c:if test="${pay.value eq vo.actTypeCode}">selected</c:if> > ${pay.text}</option>
            </c:forEach>
        </select>
        <span style="color: red">*</span>
    </div>

    <c:if test="${vo.billCode!= null}">
        <div class="form">
            <label class="Validform_label">结案申请编码: </label>
            <input name="billCode"  class="inputxt" value="${vo.billCode}" readonly = "readonly"/>
        </div>
    </c:if>

    <div class="form">
        <label class="Validform_label">结案申请名称: </label>
        <input ajaxUrl="ttAuditQuotaMainController.do?validateTtAuditQuotaMain&id=${vo.id }"
               name="billName" id="billName" datatype="*" class="inputxt" value="${vo.billName}" />
        <span style="color: red;">*</span>
    </div>

    <div class="form">
        <label class="Validform_label">结案申请备注: </label>
        <textarea rows="10" cols="30" id="remark" name="remark">${vo.remark }</textarea>
    </div>

</t:formvalid>
<script type="text/javascript">

</script>
</body>
</html>