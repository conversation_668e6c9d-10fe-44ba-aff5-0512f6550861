<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/jlb_util/processTheme.js"></script>
<div id="ttProductActSubmitSelectMain" class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid name="ttMonthlyPlanGatherList" fitColumns="false" title="" queryMode = "group" idField="id" pagination="true"
                    autoLoadData="true" actionUrl="ttMonthlyPlanWorkFlowController.do?findTtMonthlyPlanSelectList&sourceType=2&year=${ttMonthlyPlanVo.year}&month=${ttMonthlyPlanVo.month}" >
            <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
            <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="100" ></t:dgCol>
            <t:dgCol title="销售部名称" field="salesDepartName" sortable="false" width="100" ></t:dgCol>
            <%--<t:dgCol title="同期（元）" field="synchronismAmount" sortable="false" width="100" ></t:dgCol>--%>
            <t:dgCol title="必保任务额（元）" field="protectAmount" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="计划销售额（元）" field="totalPlanSales" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="差异额（元）" field="differenceAmount" sortable="false" width="100" ></t:dgCol>
            <t:dgCol title="状态" field="bpmStatus" dictionary="monthlyPlan_Bpm_status" sortable="false" width="100" ></t:dgCol>
        </t:datagrid>
    </div>
    <div id="btn_sub" onclick="submitForm()"></div>
</div>
</div>
<script type="text/javascript">
    $(document).ready(function(){

    });
    function getYear(){
        return '${ttMonthlyPlanVo.year}';
    }
    function getMonth(){
        return '${ttMonthlyPlanVo.month}';
    }
    //提交流程
    function submitForm() {
        var rows = $("#ttMonthlyPlanGatherList").datagrid("getRows");
        if(rows == null || rows == ""){
            tip("请至少选择一条数据");
            return false;
        }
        var year = getYear();
        if (year == '') {
            tip("年份不能为空");
            return ;
        }
        var month = getMonth();
        if (month == '') {
            tip("月份不能为空");
            return ;
        }
        var codes = [];
        var processKeyType='direct_monthly_plan_type';
        var actType=$("select[name='actTypeCode']").val();
        if(actType!=""&&actType!=''&&actType==1){
            processKeyType='materiel_act_type';
        }
        var params = {
            processKeyType:processKeyType,
            year : year,
            month : month
        };
        jblSubmitDialog(codes.join(","),"","","com.biz.eisp.tpm.act.monthlyplan.controller.TtMonthlyPlanWorkFlowController",JSON.stringify(params))
    }
</script>

