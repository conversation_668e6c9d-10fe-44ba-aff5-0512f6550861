<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div class="easyui-layout" fit="true">
    <div region="center" style="padding:1px;">
        <t:datagrid queryMode="group" name="report023List" fitColumns="true" title="预提汇总报表"
                    pagination="true" autoLoadData="true" actionUrl="report023Controller.do?findReportList" idField="id" fit="true">
            <!-- 基本信息 -->
            <t:dgCol title="id" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="预提年月" field="yearMonth"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="上级组织编码" field="parentOrgCode"  sortable="false" ></t:dgCol>
            <t:dgCol title="上级组织" field="parentOrgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织编码" field="orgCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="组织名称" field="orgName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="成本中心编码" field="costCenterCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="成本中心名称" field="costCenterName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="CRM预算科目编码" field="financialCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="CRM预算科目" field="financialName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="SAP财务科目编码" field="erpCode"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="SAP财务科目" field="erpName"  sortable="false" query="true"></t:dgCol>
            <t:dgCol title="预提金额(元)" field="withholdingAmount"  sortable="false" ></t:dgCol>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="report023Controller.do?exportXls"  funname="excelExport"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script>
    $(function () {
        $("#report023Listtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'70px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

//        $("input[name='orgName']").attr("readonly",true).attr("style","height:20px;width:200px")
//            .click(function(){openOrg();});
    });

    //选择组织
    function openOrg() {
        $.dialog({
            title : "组织列表",
            content : "url:tmCommonMdmController.do?goOrgSearch&flag=true",
            lock : true,
            width : "500",
            height : "400",
            zIndex : 10000,
            parent : windowapi,
            ok : function() {
                iframe = this.iframe.contentWindow;
                var row = iframe.$("#orgList").datagrid('getSelected');
                $("input[name='orgCode']").val(row.orgCode);
                $("input[name='orgName']").val(row.text);
            },
            cancelVal : '关闭',
            cancel : true
        });
    }


</script>
