<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>

<link href="resources/jsoneditor/jsoneditor.min.css" rel="stylesheet" type="text/css">
<script src="resources/jsoneditor/jsoneditor.min.js"></script>


<div fit="true">

    <div region="center">
        <div id="jsoneditor" style="width: 400px; height: 300px;"></div>
        <div>
            <button id="toDown" style="padding:10px" title="转字符串">
                ↓
            </button>
            <button id="toUp" style="padding:10px" title="转json视图">
                ↑
            </button>
        </div>

        <textarea id="jsonContent" name="jsonContent" rows="10" cols="60"></textarea>
    </div>
</div>


<script>

    //获取父窗口
    var json_$ = getSafeJq();
    //获取取值控件的id
    var hiddenJsonEditorGetId = json_$("#hiddenJsonEditorGetId").val();

    var parentJsonContent = json_$("#"+hiddenJsonEditorGetId).val();

    var baseJson = {
        "containerElement": "绑定的元素class或id",
        "url": "action.do?method",
        "options": {
            "page": 1,
            "rows": 10,
            "args1": "123123"
        },
        "isCache":false,
        "isShowLoading":false
    };
    try {
        parentJsonContent = JSON.parse(parentJsonContent) || baseJson;
    } catch (e) {
        parentJsonContent = baseJson;
        console.log(e);
    }


    // create the editor
    var container = document.getElementById("jsoneditor");
    var options = {};
    var editor = new JSONEditor(container, options);

    // set json
    editor.set(parentJsonContent);

    // get json
    var json = editor.get();

    document.getElementById("jsonContent").value = JSON.stringify(json);


    $("#toDown").click(function () {
        // get json
        var json = editor.get();

        document.getElementById("jsonContent").value = JSON.stringify(json);
    });


    $("#toUp").click(function () {
        var textJson = document.getElementById("jsonContent").value
        editor.set(JSON.parse(textJson));
    });

</script>

