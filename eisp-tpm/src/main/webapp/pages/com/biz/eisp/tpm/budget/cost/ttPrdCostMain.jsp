<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/context/mytags.jsp" %>
<t:base type="jquery,easyui,tools"></t:base>
<div id="tmCostAccountMain" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
            <t:datagrid queryMode="group"  name="tmCostAccountList" fitColumns="false" title="产品成本管理"
                        actionUrl="${datagridUrl}" idField="id" fit="true">
            <t:dgCol title="主键" hidden="true" field="id"></t:dgCol>
            <t:dgCol title="产品实际成本编号" field="costCode"></t:dgCol>
            <t:dgCol title="年月" field="yearMonth"  query="true"></t:dgCol>
            <t:dgCol title="产品编码" field="productCode" query="true"></t:dgCol>
            <t:dgCol title="产品名称" field="productName" query="true"></t:dgCol>
            <t:dgCol title="单位" field="unit" dictionary="cost_unit" query="true"></t:dgCol>
            <t:dgCol title="产品成本（元）" field="costAmount"></t:dgCol>
            <t:dgCol title="成本类型" field="costTypeName" dictionary="cost_type"  query="true"></t:dgCol>
            <t:dgCol title="成本类型编码" field="costTypeCode" dictionary="cost_type" hidden="true"></t:dgCol>
            <t:dgCol title="创建人" field="createName"></t:dgCol>
            <t:dgCol title="创建时间" field="createDate" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>
            <t:dgCol title="最近更新人" field="updateName"></t:dgCol>
            <t:dgCol title="最近更新时间" field="updateDate" formatter="yyyy-MM-dd HH:mm:ss"></t:dgCol>

            <t:dgToolBar operationCode="add"  title="录入产品成本" height="500" width="800" icon="icon-add" url="ttPrdCostController.do?goTtPrdCostForm" funname="add"></t:dgToolBar>
            <t:dgToolBar operationCode="edit" title="编辑"  height="500" width="800" icon="icon-edit" url="ttPrdCostController.do?goTtPrdCostForm"  funname="update"></t:dgToolBar>
            <t:dgToolBar operationCode="look" title="查看"  height="500" width="800" icon="icon-look" url="ttPrdCostController.do?goTtPrdCostForm" funname="detail" ></t:dgToolBar>
            <t:dgToolBar operationCode="remove" title="删除" icon="icon-remove" onclick="deleteData()"></t:dgToolBar>
            <t:dgToolBar operationCode="dataIn" title="导入" icon="icon-dataIn" url="" onclick="importDataByXml({impName:'ttPrdCostInput', gridName:'tmCostAccountList'})"></t:dgToolBar>
            <t:dgToolBar operationCode="dataOut" title="导出" icon="icon-dataOut" url="ttPrdCostController.do?exportRebatePoolXls"  funname="excelExport"></t:dgToolBar>
            <t:dgToolBar operationCode="log" title="日志" icon="icon-log" url="ttPrdCostController.do?goTtPrdCostLogMain" funname="detail" width="1200"></t:dgToolBar>
            <t:dgToolBar operationCode="check" title="产品成本检查" width="600" icon="icon-search" url="ttPrdCostController.do?goCheckTheAllPrdHasNotCost" funname="ownDetail" ></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    //年月格式化
    $("#tmCostAccountListtb_r").find("input[name='yearMonth']").addClass("Wdate").css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM'});});

    //删除
    function deleteData(){
        /*选择数据中的一行*/
        var seletctTarget =  $("#tmCostAccountList").datagrid("getSelections");
        //判断当前选择的这一行不为空并且不为空串
        if(seletctTarget==null || seletctTarget==""){
            //上面条件成立弹出这句话
            tip("必须选择一条数据");
            //执行到这里终止程序的运行
            return;
        }
        //执行到这里提示是否确定删除，function（这里确定就是true 取消就是false）
        $.messager.confirm('操作提示','确定删除?',function(r){
            //如果r等于true就执行下面的操作
            if (r){
                //发送ajax请求
                $.ajax({
                    //method为post请求
                    type : "POST",
                    //请求的链接地址+上这一行的数组取第0个索引就是第一个id
                    url : "ttPrdCostController.do?deleteTtPrdCost&id="+seletctTarget[0].id,
                    //是否是同步还是异步
                    async:true,
                    //提示消息
                    success : function(data) {
                        var d = $.parseJSON(data);
                        tip(d.msg);
                        //从新加载页面
                        $("#tmCostAccountList").datagrid("reload");
                    }
                });
            }
        });
    }

    function ownDetail(title,url, id,width,height){
        createdetailwindow(title,url,width,height);
    }

</script>

