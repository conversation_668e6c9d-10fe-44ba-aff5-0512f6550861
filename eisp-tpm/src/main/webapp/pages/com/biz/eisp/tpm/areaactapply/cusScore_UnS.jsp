<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
    <div region="center" style="padding: 1px;">
        <t:datagrid name="dealerScoreList" title="经销商积分详情列表"  actionUrl="ttAreaActApplyController.do?findCusScoreList_UnS"
                    checkbox="false"  fit="true" idField="id"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
            <t:dgCol title="经销商名称" field="dealerName" query="true"  width="200"></t:dgCol>
            <t:dgCol title="经销商编码" field="dealerCode" query="true"   width="80" ></t:dgCol>
            <t:dgCol title="可用积分" field="available"   width="90" ></t:dgCol>
            <t:dgCol title="(1) 授信积分" field="applyAmount"   width="85" ></t:dgCol>
            <t:dgCol title="(1) 更新日期" field="crDate"   width="85" ></t:dgCol>
            <t:dgCol title="(1) 截止日期" field="enDate"   width="85" ></t:dgCol>
            <t:dgCol title="(2) SAP基金余额" field="fundBalance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(3) SAP未报销" field="otherBalance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(4) CRMS待报销" field="crmsBlance" width="115" sortable="false" ></t:dgCol>
            <t:dgCol title="(5) 本系统未报销(含经销商未审批)" field="eblance" width="115" sortable="false" ></t:dgCol>

            <t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
        </t:datagrid>
    </div>
</div>
<script type="text/javascript">
    //导出
    function toExcel(){
        excelExport("ttAreaActApplyController.do?exportXlsQ","dealerScoreList");
    }

</script>
