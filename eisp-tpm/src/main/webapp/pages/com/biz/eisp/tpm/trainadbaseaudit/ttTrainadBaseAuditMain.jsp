<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="system_org_tbaList" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
		<t:datagrid name="trainADBaseList" title="高铁普列广告核销列表"  actionUrl="ttTrainAdBaseAuditController.do?findrainAdBaseAuditList"
	  		 checkbox="true" idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group" singleSelect="false">
			<t:dgCol title="id" field="id" hidden="true"></t:dgCol>
			<t:dgCol title="状态" field="bpmStatus" dictionary="bpm_status" query="true" width="80" ></t:dgCol>
			<t:dgCol title="标题" field="title" query="true"  width="200"></t:dgCol>
			<t:dgCol title="核销单号" field="auditCode"   width="200"></t:dgCol>
			<t:dgCol title="经办人" field="operator" hidden="true"  width="200" ></t:dgCol>
			<t:dgCol title="申请人" field="createName" query="true"  ></t:dgCol>
			<t:dgCol title="申请部门"  field="orgName" width="200" ></t:dgCol>
			<t:dgCol title="所属单位" field="unitName" query="true" width="200"></t:dgCol>
			<t:dgCol title="核报日期" field="createDate" formatter="yyyy-MM-dd"  width="200"></t:dgCol>
			<t:dgCol title="费用开始日期" field="costStartDate"  formatter="yyyy-MM-dd"  width="200"></t:dgCol>
			<t:dgCol title="费用截止日期" field="costEndDate"  formatter="yyyy-MM-dd"  width="200"></t:dgCol>
			<t:dgCol title="核销申请金额" field="auditAmount"  width="200"></t:dgCol>
			<t:dgCol title="瑕疵率" field="flawChance"  width="200"></t:dgCol>
			<t:dgCol title="实际核销金额" field="factAmount" hidden="true"  width="200"></t:dgCol>
			<t:dgCol title="备注" field="remark"  width="200" hidden="true"></t:dgCol>
			<t:dgToolBar title="创建" icon="icon-add" operationCode="add" url="" funname="createAudit"></t:dgToolBar>
			<t:dgToolBar title="发起核销" icon="icon-edit"  operationCode="update" url=""  funname="submit_act"></t:dgToolBar>
			<t:dgToolBar title="导出" operationCode="dataOut" icon="icon-putout" url="" funname="toExcel"></t:dgToolBar>
			<t:dgToolBar title="编辑" icon="icon-edit"  operationCode="update" url=""  funname="updateInfo"></t:dgToolBar>
			<t:dgToolBar title="查看" icon="icon-edit"  operationCode="update" url=""  funname="showInfo"></t:dgToolBar>
			<t:dgToolBar operationCode="delete" title="删除"  icon="icon-remove" url=""  funname="deleteAudit"></t:dgToolBar>

		</t:datagrid>
	</div>
	<input type="text">
</div>
<script type="text/javascript">

    function deleteAudit() {
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<1){
            tip("请至少选择一条数据")
		}else {
            for ( var i = 0; i < rows.length; i++) {
                if(rows[i].bpmStatus!=1){
                    if(rows[i].bpmStatus!=4){
                        tip("只能选择待提交或者驳回的数据");
                        return false;
					}
                }
            }
            getSafeJq().dialog.confirm("你确定删除所选数据吗?", function(r) {
                if (r) {
                    var ids='';
                    for ( var i = 0; i < rows.length; i++) {
                        var subid="'"+rows[i].id+"'";
                        ids+=subid+",";
                    }
                    var url="ttTrainAdBaseAuditController.do?deleteTrainAudit";
                    $.ajax({
                        url : url,
                        type : 'post',
                        data : {
                            id : ids
                        },
                        cache : false,
                        success : function(data) {
                            var d = $.parseJSON(data);
                            var msg = d.msg;
                            if (d.success) {
                                tip(msg,'info');
                                ids = '';
                            }else{
                                tip(msg,'error');
                                return;
                            }
                            window.location.reload();
                        },
                        error:function(){
                            tip("客户端请求错误",'error');
                            return false;
                        }
                    });
                }
            });
		}

    }

    function showInfo() {
        gridname="trainADBaseList";
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<1) {
            tip("请至少选择一条数据")
            return false;
        }
        if(rows.length>1) {
            tip("只能选择一条数据进行操作")
            return false;
        }

        var url = "ttTrainAdBaseAuditController.do?showAudit&id="+rows[0].id;



        createwindowExt("查看",url,1000,500, {
            button:[{
                name:'关闭',
                cancel : function() {
                    return true;
                }

			}]

            // cancelVal : '关闭',
            // cancel : function() {
            //     return true;
            // }
        });

    }

    function updateInfo() {
        gridname="trainADBaseList";
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length<1) {
            tip("请至少选择一条数据")
            return false;
        }
        if(rows.length>1) {
            tip("只能选择一条数据进行操作")
            return false;
        }



        if(rows[0].bpmStatus!=1){
            if(rows[0].bpmStatus!=4){
                tip("只能选择待提交或者驳回的数据");
                return false;
            }

        }

        var url = "ttTrainAdBaseAuditController.do?updateTrainAudit&id="+rows[0].id;



        createwindowExt("编辑",url,1000,500, {
            button:[
                {
                    name : "保存",
                    callback : function() {
                        // debugger;
                        iframe = this.iframe.contentWindow;
                        var submitForm=iframe.$("#submitForm");
                        // var unitName=submitForm[0].children.unitName.value;
                        var title=submitForm[0].children.title.value;
                        var flawChance=submitForm[0].children.flawChance.value;
                        var auditAmount=submitForm[0].children.auditAmount.value;
                        var ids="";
                        var rows = iframe.$("#tainAdCheckList").datagrid('getRows');
                        var partnerCompany= iframe.$("input[name='partnerCompany']").val();
                        var createDate_begin= iframe.$("input[name='createDate_begin']").val();
                        var createDate_end= iframe.$("input[name='createDate_end']").val();
                        // alert(partnerCompany+"--------"+createDate_begin+"-----"+createDate_end);
                        submitForm[0].children.costStartDate.value=createDate_begin;
                        submitForm[0].children.costEndDate.value=createDate_end;
                        submitForm[0].children.unitName.value=partnerCompany;

                        if(rows!=null&&rows.length!=0){
                            for(var i=0;i<rows.length;i++){
                                ids+=rows[i].id+',';
                            }
                            submitForm[0].children.checkIds.value=ids;
                        }

                        // if(isNull(ids)){
                        //     tip("没有可核销的项目");
                        //     return false;
                        // }


                        if(isNull(title)){
                            tip("请输入标题");
                            return false;
                        }



                        if(isNull(auditAmount)){
                            tip("请输入核销金额");
                            return false;
                        }else {
                            var reg=/^\d+(\.\d+)?$/;
                            if(reg.test(auditAmount)){
                                if(parseFloat(auditAmount)<0){
                                    tip("核销金额应为大于0的正数");
                                    return false;
                                }
                            }else{
                                tip("核销金额应为大于0的正数");
                                return false;
                            }
                        }



                        //submitForm.submit();
                        $('#submitForm', iframe.document).form('submit', {
                            onSubmit : function() {
                            },
                            success : function(r) {
                                var data =$.parseJSON(r);
                                tip(data.msg);
                                reloadTable();

                            }
                        });
                        return true;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });

    }

    //导出
    function toExcel(){
        excelExport("ttTrainAdBaseAuditController.do?exportXls","trainADBaseList");
    }


    function createAudit(){
        gridname="trainADBaseList";
        var url = "ttTrainAdBaseAuditController.do?goTrainActCheck";

        createwindowExt("创建",url,1000,500, {
            button:[
                {
                    name : "保存",
                    callback : function() {
                        debugger;
                        iframe = this.iframe.contentWindow;
                        var submitForm=iframe.$("#submitForm");
                       // var unitName=submitForm[0].children.unitName.value;
                        var title=submitForm[0].children.title.value;
                        var flawChance=submitForm[0].children.flawChance.value;
                        var auditAmount=submitForm[0].children.auditAmount.value;
                        var ids="";
                        var rows = iframe.$("#tainAdCheckList").datagrid('getRows');
                        var partnerCompany= iframe.$("input[name='partnerCompany']").val();
                        var createDate_begin= iframe.$("input[name='createDate_begin']").val();
                        var createDate_end= iframe.$("input[name='createDate_end']").val();
                        if(isNull(partnerCompany)){
                            tip("请输入所属公司");
                            return false;
						}

                        if(isNull(createDate_begin)&&isNull(createDate_end)){
                            tip("请输检查日期");
                            return false;
                        }
                        // alert(partnerCompany+"--------"+createDate_begin+"-----"+createDate_end);N
                        submitForm[0].children.costStartDate.value=createDate_begin;
                        submitForm[0].children.costEndDate.value=createDate_end;
                        submitForm[0].children.unitName.value=partnerCompany;

                        if(rows!=null&&rows.length!=0){
                            for(var i=0;i<rows.length;i++){
                                ids+=rows[i].id+',';
                            }
                            submitForm[0].children.checkIds.value=ids;
                        }

                        // if(isNull(ids)){
                        //     tip("没有可核销的项目");
                        //     return false;
                        // }


                        if(isNull(title)){
                            tip("请输入标题");
                            return false;
                        }



                            if(isNull(auditAmount)){
                                tip("请输入核销金额");
                                return false;
                            }else {
                                var reg=/^\d+(\.\d+)?$/;
                                if(reg.test(auditAmount)){
                                    if(parseFloat(auditAmount)<0){
                                        tip("核销金额应为大于0的正数");
                                        return false;
                                    }
                                }else{
                                    tip("核销金额应为大于0的正数");
                                    return false;
                                }
						}



                        //submitForm.submit();
                        $('#submitForm', iframe.document).form('submit', {
                            onSubmit : function() {
                            },
                            success : function(r) {
                                var data =$.parseJSON(r);
                                tip(data.msg);
                                reloadTable();

                            }
                        });
                        return true;
                    }
                }],
            cancelVal : '关闭',
            cancel : function() {
                return true;
            }
        });
    }


    //提交工作流
    function submit_act(){
        var rows = $("#trainADBaseList").datagrid('getSelections');
        if(rows.length>1){
            tip("只能选择一条数据!");
            return false;
        }else if(rows==null||rows.length==0){
            tip("必须选择一条数据!");
            return false;
        }else{
            var auditCode=rows[0].auditCode;
            var bpmStatus=rows[0].bpmStatus;
            //alert(bpmStatus);
            if(bpmStatus!=1){
                tip("请选择待提交的数据");
                return false;
			}
            if(auditCode!=null&&auditCode!=""){
                var params = {processKeyType:'act_bpm_type'};
                customSubmitDialog(auditCode,"","","com.biz.eisp.tpm.trainadbaseaudit.controller.TtTrainAuditWorkFlowController",JSON.stringify(params));
			}else{
                tip("核销单号不能为空!");
			}

		}

    }

    function isNull(obj) {
        if(obj == ""|| obj == null||obj == undefined){
            return true;
        }
    }
</script>
