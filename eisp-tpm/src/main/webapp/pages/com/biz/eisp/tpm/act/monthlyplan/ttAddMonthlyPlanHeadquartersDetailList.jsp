<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<style>
	.top_tip{}
	.top_tip label{margin-left:30px;color:red;}
	.top_tip span{min-width:80px;}
</style>
<div id="ttReportMonthlyPlanMain" class="easyui-layout" fit="true">
	<div data-options="region:'center'" style="padding:1px;">
		<t:datagrid name="ttMonthlyPlanProductList" fitColumns="false" title="" 
			queryMode = "group" idField="id" pagination="true" autoLoadData="true" 
			actionUrl="ttMonthlyPlanHeadquartersConfirmController.do?findttAddMonthlyPlanDetailList&salesDepartCode=${monthlyPlanVo.salesDepartCode }&productCode=${monthlyPlanVo.productCode}&yearMonth=${monthlyPlanVo.yearMonth}" >
	        <t:dgCol title="主键" hidden="true" field="id" width="100" ></t:dgCol>
	        <t:dgCol title="客户编码" field="customerCode" sortable="false" width="70" ></t:dgCol>
	        <t:dgCol title="客户名称" field="customerName" sortable="false" width="90" ></t:dgCol>
	        <t:dgCol title="组织编号" field="orgCode" sortable="false" width="90" ></t:dgCol>
	        <t:dgCol title="组织名称" field="orgName" sortable="false" width="70" ></t:dgCol>
	        <t:dgCol title="年月" field="yearMonth" sortable="false" formatter="yyyy年MM月" width="90" ></t:dgCol>
	        <t:dgCol title="产品编号" field="productCode" sortable="false" width="90" ></t:dgCol>
	  		<t:dgCol title="产品名称" field="productName" sortable="false" width="90" ></t:dgCol>
	  		<t:dgCol title="单价（元）" field="price" sortable="false" width="80" ></t:dgCol>
	  		<t:dgCol title="计划销量（EA）" field="planSales" sortable="false" width="90" ></t:dgCol>
	  		<t:dgCol title="赠品量（EA）" field="premiumQuantity" sortable="false" width="80" ></t:dgCol>
	  		<t:dgCol title="计划销额（元）" field="totalPlanSales" sortable="false" width="90" ></t:dgCol>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
$(document).ready(function(){
	//给时间控件加上样式
	$("#ttMonthlyPlanProductListForm").find("input[name='yearMonth']").attr("readonly",true).attr("class","Wdate").attr("style","height:20px;width:90px;").click(function(){WdatePicker({dateFmt:'yyyy-MM'});});
});
	/* //查询
	function ttMonthlyPlanGatherListSearchFunction(){
		var year = $('#year').val();
		var month = $('#month').val();
		var customerCode = $('#customerCode').val();
		var customerName = $('#customerName').val();
		
		var queryParams = $('#ttMonthlyPlanGatherList').datagrid('options').queryParams;
        queryParams.customerCode = customerCode;
        queryParams.customerName = customerName;
        queryParams.year = year;
        queryParams.month = month;
		$("#ttMonthlyPlanGatherList").datagrid({url:"ttMonthlyPlanController.do?findTtMonthlyPlanGatherList"});
	}
	
	//重置
	function searchReset(){
		$("#year").val('');
		$("#month").val('');
		$("#customerCode").val('');
		$("#customerName").val('');
		ttMonthlyPlanGatherListSearchFunction();
	} */
</script>