<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<div id="system_org_tbaList" class="easyui-layout" fit="true">

    <div region="center" style="padding: 1px;">

        <t:datagrid name="ttCostCenterList" title="成本中心"  actionUrl="ttBudgetDocumentController.do?findTtCostCenterList"
                    idField="id" fit="true"  fitColumns="false" pagination="true" queryMode="group">

            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <t:dgCol title="成本中心编码" field="costCenterCode" query="true" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="成本中心名称" field="costCenterName" query="true" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="销售部门编码" field="orgCode" query="true" sortable="false"  hidden="false"  ></t:dgCol>
            <t:dgCol title="销售部门名称" field="orgName" query="true" sortable="false"  hidden="false"  ></t:dgCol>
        </t:datagrid>
    </div>
    <input type="text">
</div>
<script type="text/javascript">

    $(function(){
       creatediv();
        $("#ttBudgetDocumentListtb_r").find("input[name='budat']").addClass("Wdate")
            .css({'height':'20px','width':'100px'}).click(function(){WdatePicker({dateFmt:'yyyy-MM-dd'});});
    });

    //政策申请
    function addBudgetDocument(title, url, grid, width, height){
        gridname = grid;
        openWindOwn(title, url,width, height);
    }

    //修改
    function updateBudgetDocument(title, url, grid, width, height){
        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var rowData = rowDatas[0];
        /*if (rowData.bpmStatus == 2){
         tip("处理中的数据不可编辑");
         return ;
         }else if (rowData.bpmStatus == 3){
         tip("审批通过的数据不可编辑");
         return ;
         }*/
        var id = rowData.id;
//        if (checkThisDataCouldToUpdate(id)){
        url += "&id=" + id;
        openWindOwn(title, url,width, height);
//        }
    }

    //过账
    function sendToSap(title, url, grid, width, height){
        gridname = grid;
        var rowDatas = $('#' + grid).datagrid('getSelections');
        if (rowDatas.length != 1) {
            tip("请选择一条项目");
            return ;
        }
        var thisData = {
            id : rowDatas[0].id
        }
        var d = ajaxPost(thisData,url);
        if(d.success){
            $('#' + grid).datagrid("reload");
        }
        tip(d.msg);
    }


    //-------------------------共用----------------------
    function openWindOwn(title, url,width, height){
        width = width == '' ? '1000': width;
        height = height == '' ? '700': height;
        createwindowExt(title,url,width, height,{
            button : [
                {name : "关闭",
                    callback : function() {
                        return true;
                    }}
            ]
        });
    }

    //ajax请求
    function ajaxPost(json,url){
        $.ajax({
            async : false,
            cache : false,
            url:url,
            data:json,
            dataType:'json',
            success:function(data){
                json = data;
            },
            error:function(error){
                json = {"sucess":false,"msg":"服务器错误!"};
            }
        });

        return json;
    }

    var creatediv= function(){
        $(".datagrid-toolbar-but").prepend("<div style='color: red'>若未找到成本中心或成本中心对应销售部门有误，请先到SAP核实成本中心信息，然后在本系统记账页面点击同步成本中心按钮，同步成功后，再进行记账操作</div>");
    }
</script>
