<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools,DatePicker"></t:base>
<script type="text/javascript" src="<%=basePath %>resources/custom_util/processTheme.js"></script>
<div id="top_panel">
    <div style="float: left; width: 40%;height: 260px;">
        <t:datagrid name="userList" queryMode="group" onClick="showTask" title="用户信息" singleSelect="true"
          checkbox="true"           fit="true" actionUrl="tmUserController.do?findTmUserList" idField="id">
            <t:dgCol title="主键" hidden="true" field="id"></t:dgCol>
            <t:dgCol title="登录帐号"  query="true"  width="50"  align="center" field="userName"></t:dgCol>
            <t:dgCol title="人员名称"  query="true"  width="50"  align="center" field="fullName"></t:dgCol>
            <t:dgCol title="职位" width="100" query="true" field="positionName"></t:dgCol>
        </t:datagrid>
    </div>
</div>

<div style="float: left;width: 20%;height: 260px;">
    <div id="time_panel" class="easyui-panel" title="选择时间"  style="height:260px;text-align: center;">
        开始时间: <input type="text" id="startDate" class="Wdate" style="width: 120px" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"><br>
        结束时间: <input type="text" id="endDate" class="Wdate" style="width: 120px" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
        <div id="visit_time" style="text-align: center;"></div>
        <input id="visit_time_input" type="hidden">
    </div>
</div>
<div>
    <div style="float: left;width:40%;height: 260px;">
        <t:datagrid name="actApplyList" pagination="false" queryMode="group" checkbox="true" title="检查信息" singleSelect="true" fit="true" actionUrl="ttActOutUploadController.do?findActApplyData&bpmStatus=3" idField="id">
            <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
            <%--<t:dgCol title="审批状态" field="bpmStatus"  query="true" dictionary="bpm_status"></t:dgCol>--%>
           <%-- <select id="costTypeName" name="de" style="width:100px;">
                <c:forEach items="${list}">
                    <option value=""></option>
                </c:forEach>
            </select>--%>
            <t:dgCol title="活动单号" field="actCode"  ></t:dgCol>
            <t:dgCol title="活动类型" field="accountName"  query="true"></t:dgCol>
            <%--<t:dgCol title="活动细类" field="costTypeName"></t:dgCol>--%>
            <t:dgCol title="广告公司编号" field="advCode"  ></t:dgCol>
            <t:dgCol title="广告公司名称" field="advName" query="true"  ></t:dgCol>
            <t:dgCol title="终端网点名" field="terminalName" query="true" ></t:dgCol>
            <t:dgCol title="当前定位地址" field="gpsAddress"  ></t:dgCol>
            <t:dgCol title="省" field="province"  ></t:dgCol>
            <t:dgCol title="市" field="city"  ></t:dgCol>
            <t:dgCol title="区县" field="area"  ></t:dgCol>

            <t:dgToolBar title="添加任务指派"  url="" icon="icon-add" funname="addAssgin();"></t:dgToolBar>
        </t:datagrid>
    </div>
<div id="submitDiv" hidden="hidden">
    <form  id="submitForm" name="submitForm" layout="" dialog=""  method="post"  action="ttOpenActAssignController.do?saveOpenActAssign"  >
        <input type="hidden" name="actCode" id="actCode" value=""   />
        <input type="hidden" name="actType" id="actType" value=""   />
        <input type="hidden" name="advCode" id="advCode" value=""   />
        <input type="hidden" name="advName" id="advName" value=""   />
        <input type="hidden" name="terminalCode" id="terminalCode" value=""   />
        <input type="hidden" name="terminalName" id="terminalName" value=""   />
        <input type="hidden" name="gpsAddress" id="gpsAddress" value=""   />
        <input type="hidden" name="assignUser" id="assignUser" value=""   />
        <input type="hidden" name="assignUserName" id="assignUserName" value=""   />
        <input type="hidden" name="starttime" id="starttime" value=""   />
        <input type="hidden" name="endtime" id="endtime" value=""   />
        <input type="hidden" name="accountCode" id="accountCode" value=""   />

    </form>>
</div>
</div>

<div id="buttom_pannel" style="clear:both; width: 100%;height: 600px;">
    <t:datagrid name="taskAssignList"  queryMode="group"  pagination="true" checkbox="true" title="任务指派列表" fit="true" actionUrl="ttOpenActAssignController.do?findOpenActAssign" idField="id">
        <t:dgCol title="主键" field="id" hidden="true"></t:dgCol>
        <t:dgCol title="状态" field="receiveStatus"  query="true" dictionary="Task_status"></t:dgCol>
        <t:dgCol title="任务编码" field="taskCode"  ></t:dgCol>
        <t:dgCol title="活动单号" field="actCode"  ></t:dgCol>
        <t:dgCol title="活动类型" field="accountName"  query="true"></t:dgCol>
        <t:dgCol title="广告公司编号" field="advCode"  ></t:dgCol>
        <t:dgCol title="广告公司名称" field="advName" query="true" ></t:dgCol>
        <t:dgCol title="指派人" field="assignUser"  ></t:dgCol>
        <t:dgCol title="终端网点名" field="terminalName" query="true" ></t:dgCol>
        <t:dgCol title="当前定位地址" field="gpsAddress"  ></t:dgCol>
        <t:dgCol title="开始日期" field="starttime" formatter="yyyy-MM-dd"  ></t:dgCol>
        <t:dgCol title="结束日期" field="endtime" formatter="yyyy-MM-dd"  ></t:dgCol>
        <t:dgCol title="拒绝理由" field="reason"></t:dgCol>
          <%--<t:dgToolBar  title="删除"  icon="icon-remove" url="visitNoteController.do?doBatchDel" funname="deleteALLSelect"></t:dgToolBar>--%>
       </t:datagrid>
</div>
<script type="text/javascript">
    var accountCode;
    window.onload=function () {
        $("input[name='accountName']").combobox({
            url : 'ttActOutUploadController.do?findAccountDetail',
            valueField: 'accountName',
            textField: 'accountName',
            width:'100',
            panelHeight : '150',
            onSelect: function (data) {
                accountCode = data.accountCode;
            }
        });
    }

    function addAssgin() {
        var startDate=$("#startDate").val();
        if(startDate==null||startDate==''){
            tip("请选择开始时间！");
            return false;
        }
        $("#starttime").val(startDate);
        var endDate=$("#endDate").val();
        if(endDate==null||endDate==''){
            tip("请选择结束时间！");
            return false;
        }
        $("#endtime").val(endDate);
        var userRows = $("#userList").datagrid('getSelections');
        if(userRows.length==0){
            tip("请选择用户");
            return false;
        }
        var applyRows = $("#actApplyList").datagrid('getSelections');
        if(applyRows.length==0){
            tip("请选择检查信息");
            return false;
        }

        var userRow=userRows[0];
        $("#assignUser").val(userRow.fullName);
        $("#assignUserName").val(userRow.userName);
        var applyRow=applyRows[0];
        $("#actCode").val(applyRow.actCode);
        $("#actType").val(applyRow.actType);
        $("#advCode").val(applyRow.advCode);
        $("#advName").val(applyRow.advName);
        $("#terminalName").val(applyRow.terminalName);
        //$("#terminalCode").val(startDate);
        $("#gpsAddress").val(applyRow.gpsAddress);
        //$("#submitForm").submit();

        $('#submitForm').form('submit', {
            onSubmit : function() {
            },
            success : function(r) {
                var data =$.parseJSON(r);
               // alert(data);
                tip(data.msg);
                reloadTable();
                showTask();
            }
        });

         //$('#taskAssignList').datagrid('load',{});



        // window.location.reload();
         //$("#taskAssignList").datagrid('reload');
        // $("#ttAccruedFormulaList").datagrid('reload');

    }

    function showTask() {
        var userRows = $("#userList").datagrid('getSelections');
        var name=userRows[0].fullName;
        $('#taskAssignList').datagrid('load',{assignUser:name});
    }
</script>



