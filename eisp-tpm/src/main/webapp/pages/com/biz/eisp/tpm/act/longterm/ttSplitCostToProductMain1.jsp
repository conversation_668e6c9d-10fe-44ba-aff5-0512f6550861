<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<t:base type="jquery,easyui,tools"></t:base>
<style>
	.actTable tr td:nth-child(1){display:none;}
</style>
<div class="easyui-layout" fit="true">
	<div region="center">
		<div class="panel datagrid">
			<div class="datagrid-wrap panel-body">
				<div class="datagrid-toolbar">
					<div class="datagrid-toolbar-but">
						<span style="float:left;">
							<a href="#" class="easyui-linkbutton l-btn" plain="true" icon="icon-add" onclick="actTableAdd()">添加产品</a>
							<a href="#" class="easyui-linkbutton l-btn" plain="true" icon="icon-remove" onclick="deleteall()">移除产品</a>
							<a href="#" class="easyui-linkbutton l-btn" plain="true" icon="icon-allot_cost" onclick="splitCost()">费用分摊</a>
						</span>
						<span style="float:right">
							<%-- <span style="color:red;line-height:30px;margin-right:50px;" >可分配费用金额合计：<span id="totalAmount">${vo.amount}</span></span> --%>
							<input type="hidden" id="totalAmount" value="${vo.amount}">
							<input type="hidden" id="amount" value="${vo.amount}">
						</span>
						<div style="clear:both;float: none;height:0;display: block;"></div>
					</div>
					<div class="datagrid-toolbar-search">
					</div>
				</div>
				<div class="datagrid-view">
				<t:formvalid formid="formobj" layout="div" dialog="true" action="ttActLongTermController.do?saveSplitCostToTerm" refresh="false"
					beforeSubmit="handlerSubmit" callback="splitCostToTerm">
					
					<input type="hidden" id="splitCostToTermJson" name="splitCostToTermJson" value="${vo.splitCostToTermJson}">
					<input type="hidden" id="isViewCostTermForJson" value="${vo.isViewCostTermForJson}">
					<input type="hidden" id="trigger">
					<input type="hidden" id="months" value="${vo.months }">
					<input type="hidden" id="amounts" value="${vo.amounts }">
					
					<input type="hidden" id="useAmount">
					
					
					<table class="actTable" >
						<thead>
							<tr>
								<td><input type="checkbox" onclick="this.checked?selectall('actTableId',true):selectall('actTableId',false)" /></td>
								<td>序号</td>
								<td>年月</td>
								<td>产品层级</td>
								<td>费用金额(元)</td>
							</tr>
						</thead>
						<tbody id="productCostDetail">
							
						</tbody>
					</table>
				</t:formvalid>
				</div>
			</div>
		</div>
	</div>
</div>
<link rel="stylesheet" type="text/css" href="resources/simpletable/css/style.css">
<script type="text/javascript" src="resources/simpletable/js/simpletable.js"></script>
<script type="text/javascript">
	$(function(){
		//父窗口分摊json
		var spiltCostJson = '${vo.splitCostToTermJson}';
		
		//是否根据json展示
		var isViewCostTermForJson = $("#isViewCostTermForJson").val();
		
		//父窗口费用类型 费用科目 时间 都没有变化的清空下根据json显示分摊信息
		if(isViewCostTermForJson == "true" && spiltCostJson != "" && spiltCostJson.indexOf("{") != -1){
			//展示门店分摊信息
			viewsSplitCostToTerm(spiltCostJson);
		}
	});
	
	//添加门店
	function actTableAdd(){
		var tr = $("#productCostDetail").find("tr");
		var termIds = "";
		if(tr.length > 0){
			$(tr).each(function(){
				if(termIds != ""){
					termIds += ",";
				}
				termIds += $(this).find("#termId").val();
			});
		}	
		popOptClickNow("trigger", "id", "tmCommonMdmController.do?goSelectProductTreeMain&flag=true",addContent ,800, 500,"选择产品");
	}

	//添加选择的产品
	function addContent(rowData){
		var content = '';
		if(rowData != null){
			var len = $("#productCostDetail").find("tr").length;
			var months = $("#months").val().split(",");
            var index = 0;
            var proArray = new Array();

            $.each(months,function(k,v){
                $.each(rowData,function(i,row){
                    var yearPro = v+row.productCode;
                    proArray[index] = yearPro;
                    index++;
                });
            });

            var tr= $("#productCostDetail tr");
            tr.each(function(tri,trc){
                var yearMonth = $(this).find("input[name='yearMonth']").val();
                var productCode = $(this).find("input[name='proCode']").val();
                var yearPro = yearMonth+productCode;
                var index = $.inArray(yearPro,proArray);
                if(index >= 0){
                    $(this).remove();
                }
            });
			for(var index = 0;index<months.length;index++){
				$.each(rowData,function(i,v){
					content += '<tr>'
						+'<td><input type="checkbox" name="actTableId" /></td>'
						+'<td>'+(len+1)+'</td>'
						+'<td><input name="yearMonth" type="text" value="'+months[index]+'" readonly="readonly"/></td>'
						+'<td><span name="productCode">'+v.productName+'</span><input name="proCode" type="hidden" value="'+v.productCode+'" /></td>'
						+'<td><input name="amount" type="text" onkeydown="keyCode(event)" onkeyup="verifyInput(this)"/></td>'
						+'</tr>';
					len += 1;
				});
			}
			$("#productCostDetail").append(content);
            refreshAmountDisplay();
		}
		return true;
	}
	//封装json信息
	function splitCostToTermJson(){
		var splitCostToTerm = [];
		var tr = $("#productCostDetail").find("tr");
		if(tr.length > 0){
			$(tr).each(function(){
				var yearMonth = $(this).find("input[name='yearMonth']").val();
				var productCode = $(this).find("input[name='proCode']").val();
				var amount = $(this).find('input[name="amount"]').val();
				var productName = $(this).find("span[name='productCode']").text();
				var obj = {};
				obj.yearMonth = yearMonth;
				obj.productCode = productCode;
				obj.amount = amount;
				obj.productName = productName;
				splitCostToTerm.push(obj);
			});
			$("#splitCostToTermJson").val(JSON.stringify(splitCostToTerm));
			closeWindowThis();
			return JSON.stringify(splitCostToTerm);
		}
	}
	function validateAmount(){
		var sumAmount = 0;	
		var tr = $("#productCostDetail").find("tr");
		if(tr.length > 0){
			$(tr).each(function(){
				var amount = $(this).find('input[name="amount"]').val();
				var num = Number(amount);
				sumAmount = addNum(sumAmount,num);
			});
			var amount = Number($("#amount").val());
			if(amount != sumAmount){
				tip("填写的金额汇总与总金额不相等,请检查");
				return false;
			}
		}
		return true;
	}
	//关闭当前页面
	function closeWindowThis(){
		frameElement.api.close();
	}
	
	//选中
	function selectall(name,checked){
		var obj = $("[name='"+name+"']");
		var cnt=obj.length;
		for(i=0;i<cnt;i++)
		{
			if(obj[i].type=="checkbox"){
				obj[i].checked=typeof(checked)=="undefined"?true:checked;
			}
		}
	}
	//删除选中
	function deleteall(){
		var trTarget = $("#productCostDetail tr");
		if(trTarget.length == 0){
			tip("请至少选中一条数据");
			return false;
		}
		var productCode = [];
		$(trTarget).each(function(){
			if ($(this).find("[name='actTableId']")[0].checked){
                var proCode = $(this).find("input[name='proCode']").val();
				if($.inArray(proCode,productCode) == -1){
                    productCode.push(proCode);
                }
            }
		});
        $(trTarget).each(function(){
			var proCode = $(this).find("input[name='proCode']").val();
			if($.inArray(proCode,productCode) != -1){
				$(this).remove();
			}
        });
		//合计金额
		splitCostTotal();	
		refreshAmountDisplay();
	}
	/**
	 * 刷新
	 */
	function refreshAmountDisplay() {
		var sumAmountAll = 0;
		$("#productCostDetail input[name='amount']").each(function(i, o) {
			var num = Number($(o).val());
			sumAmountAll = addNum(sumAmountAll,  num);
		});
		var allAmount = Number($("#amount").val());
		
		if (sumAmountAll > allAmount) {
			$("#productCostDetail input[name='amount']").each(function(i, o) {
				$(o).val("");
			});
			refreshAmountDisplay();
			return;
		}
		$("#totalAmount").html(subtract(allAmount, sumAmountAll));
		
		// 排序
		$("#productCostDetail tr").each(function(i, o) {
//			if (i == 0) {
//				return true;
//			}
			$(o).find("td").eq(1).html(i+1);
		});
	}

	//分摊费用
	function splitCost(){
        var sumAmount = $("#amount").val();
        if (sumAmount == '' || sumAmount == undefined) {
            tip("请输入待分配金额");
            return;
        }
        var rows = $("#productCostDetail tr");
        var amountSize = Number(rows.size());
        if (amountSize == 0) {
            return;
        }
        var waitingAssgin = sumAmount;
        var avgAmount = (waitingAssgin / amountSize).toFixed(2);
        var totalAmount=0;
        $.each(rows, function(i, row) {
            totalAmount=addNum(totalAmount,avgAmount);
            $(row).find("input[name='amount']").val(avgAmount);
        });
        if(Number(totalAmount)!=Number(sumAmount)){
            var num=subtract(sumAmount,totalAmount);
            $(rows[rows.length -1]).find("input[name='amount']").val(addNum(avgAmount,num));
        }
/*        var sum = 0;
        $.each(rows, function(i, row) {
       		var arg2 = $(row).find("input[name='amount']").val();
            sum = addNum(sum, arg2);
        });
        var residueAmount = subtract(sumAmount, sum);
        var addToElement = $("#productCostDetail tr:eq(0) input[name='amount']");
        var num = addToElement.val();
        addToElement.val(addNum(num, residueAmount));*/
        splitCostTotal();
	}
	//提交前验证
	function handlerSubmit(){
		splitCostToTermJson();
		return true;
	}
	//封装值
	function pakageValue(monthFalg,termId,data){
		var cont = "";
		if(data.length > 0){
			for (var i = 0; i < data.length; i++) {
				if(monthFalg == data[i].month && termId == data[i].terminalId){
					cont = 'dataId="'+data[i].id+'" value="'+data[i].amount+'"';
				}
			}			
		}
		return cont;
	}
	
	//展示费用到页面
	function viewsSplitCostToTerm(jsonData){
		if(typeof(jsonData) == "string"){
			jsonData = JSON.parse(jsonData);
		}
		var content = "";
		if(jsonData.length > 0){
			for (var i = 0; i < jsonData.length; i++) {
				var v = jsonData[i];
				content += '<tr>'
					+'<td><input type="checkbox" name="actTableId" /></td>'
					+'<td>'+(i+1)+'</td>'
					+'<td><input name="yearMonth" type="text" value="'+v.yearMonth+'" readonly="readonly"/></td>'
					+'<td><span name="productCode">'+v.productName+'</span><input name="proCode" type="hidden" value="'+v.productCode+'" /></td>'
					+'<td><input name="amount" type="text" onkeydown="keyCode(event)" onkeyup="verifyInput(this)" value="'+v.amount+'"/></td>'
				+'</tr>';
			}
			$("#productCostDetail").html(content);        				
		}
		//合计金额
		splitCostTotal();		
	}
	
	//计算分摊金额合计
	function splitCostTotal(obj){
		if(obj != undefined){
			//验证门店分配的值
			var reg = /^[0-9]+(\.[0-9]{1,2})?$/;
			if(!reg.test(obj.value)){
				tip("只能输入正数,并且最多为2位小数点");
				obj.value = "";
			}
		}
		//获取可编辑的月份 计算已经输入的总金额
		var all = $("#amount").val();
		var totalAmount = 0;
		var inputTarget = $("#productCostDetail tr");
		if(inputTarget.length > 0){
			$(inputTarget).each(function(){
				var amount = $(this).find("input[name='amount']").val();
				if(amount != "" && amount != null){
					totalAmount += amount * 1;
				}
			});
		}
		$("#totalAmount").html( (all - totalAmount.toFixed(2)).toFixed(2) );
	}
</script>














<script type="text/javascript">
	/*********************************************************************************/
    /**
     * 监听Tab按键
     */
    function keyCode(event) {
        var x = event.keyCode;
        if (x == 9) {
            verifyInput(event.target);
        }
    }
    /**
     * 页面输入验证
     */
    function verifyInput(obj) {
        obj.value = obj.value.replace(/[^\d.]/g,""); // 清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g,""); // 验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g,"."); // 只保留第一个, 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); // 只能输入两个小数
    }
	/*
	 * 高精减法函数
	 */
    function subtract(minuend, subtrahend) {
        var power = getMaxPowerForTen(minuend, subtrahend);
        return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
    }
    /**
     * 高精度加法函数
     */
    function addNum(summand1, summand2){
        var power = getMaxPowerForTen(summand1, summand2);
        return (multiply(summand1, power) + multiply(summand2, power)) / power;
    }
    /**
     * 高精乘法函数
     */
    function multiply(multiplier, multiplicand) {
        var m=0;
        var s1 = multiplier.toString();
        var s2 = multiplicand.toString();
        try {
            m += s1.split(".")[1].length;
        }catch (e) {
        }
        try {
            m += s2.split(".")[1].length;
        }catch (e) {
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
    }
    /**
     * 获取最大次幂
     */
    function getMaxPowerForTen(arg1, arg2) {
        var r1 = 0;
        var r2 = 0;
        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch(e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch(e) {
            r2 = 0;
        }
        // 动态求出哪一个位数最多，再得出10的n次幂
        return Math.pow(10, Math.max(r1, r2));
    }
	function popOptClickNow(obj, name, url,optFunction ,width, height,title) {
	    var names = name.split(",");
	    var objs = obj.split(",");
	    safeShowDialog({
	            content : "url:" + url,
	            lock : true,
	            title : (title==null || title=="" || title==undefined)?"选择":title,
	            width : width==null?700:width,
	            height : height==null?400:height,
	            left :'85%',
	            cache : false,
	            ok : function() {
	                iframe = this.iframe.contentWindow;
	                var selected = iframe.$("#ttProductSeletedList").datagrid("getRows");
	                if (selected == '' || selected == null) {
	                	iframe.$.messager.alert('错误','请至少选择一条数据','error');
	                    return false;
	                } else {
	                    for ( var i1 = 0; i1 < names.length; i1++) {
	                        var str = "";
	                        $.each(selected, function(i, n) {
	                            if (i == 0){
	                                str += n[names[i1]];
	                            }else {
	                                str += ",";
	                                str += n[names[i1]];
	                            }
	                        });
	                        if ($("#" + objs[i1]).length >= 1) {
	                            $("#" + objs[i1]).val("");
	                            $("#" + objs[i1]).val(str);
	                        } else {
	                            $("input[name='" + objs[i1] + "']").val("");
	                            $("input[name='" + objs[i1] + "']").val(str);
	                        }
	                    }
	                    
	                    //是否回调方法
	                    if(optFunction != null && optFunction != undefined){
	                    	return optFunction(selected);
	                    }
	                    return true;
	                }
	            },
	            cancelVal : '关闭',
	            cancel : true
	        });
	}
</script>

