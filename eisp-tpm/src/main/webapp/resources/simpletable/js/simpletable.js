
$(function(){
	
	setTimeout('actTableStyle()',3000);
	$(window).resize(function() {
		//actTableStyle();
	})
	$(".actTable").on("click","tbody tr",function(){
		var oldValue = $(this).find("[type='checkbox']")[0].checked;
		$(this).find("[type='checkbox']")[0].checked = !oldValue;
		checkedValid();//验证所有勾选项
	})
	
	$(".actTable").on("click","tbody input,tbody select",function(e){
		var oldValue = $(this).closest("tr").find("[type='checkbox']")[0].checked;
		if(this.type != "checkbox" && oldValue == false){
			$(this).closest("tr").find("[type='checkbox']")[0].checked = true;
			checkedValid();//验证所有勾选项
		}
		e.stopPropagation();
	})
	
	$(".actTable").on("change","tbody [type='checkbox']",function(e){
		checkedValid();//验证所有勾选项
	})
	
})

function actTableStyle(){
	var actTableBodyObj = $(".actTable").closest(".panel-body");
	actTableBodyObj.css({border:0,height:"100%",width:"100%"});
	actTableBodyObj.find(".datagrid-view").height( actTableBodyObj.height() - actTableBodyObj.find(".datagrid-toolbar").height() - 3 );
	actTableBodyObj.find(".datagrid-view").css({overflow:"auto"});
}

function selectall(name,checked){
	$("[name='"+name+"']").each(function(){
		if(this.type=="checkbox"){
			this.checked=typeof(checked)=="undefined"?true:checked;
			//$(this).trigger('change');
		}
	})
	checkedValid();//验证所有勾选项
}

function deleteall(name){
	$("[name='"+name+"']").each(function(){
		if (this.checked) $(this).closest("tr").remove();
	})
	checkedValid();//验证所有勾选项
}

function checkedValid(){
	var checkedNum = 0;
	$(".actTable tbody [type='checkbox']").each(function(k,v){
		if (this.checked){
			$(this).closest("tr").addClass("active");
			checkedNum++;
		}else{
			$(this).closest("tr").removeClass("active");
		}
	})
	var newValue = (checkedNum==$(".actTable tbody [type='checkbox']").length && checkedNum>0)?true:false;
	$(".actTable thead tr:first [type='checkbox']:first")[0].checked = newValue;
}
