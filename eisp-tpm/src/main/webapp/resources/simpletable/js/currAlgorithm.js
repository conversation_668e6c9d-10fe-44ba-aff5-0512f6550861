/*
 * 高精减法函数
 */
function subtract(minuend, subtrahend) {
    var power = getMaxPowerForTen(minuend, subtrahend);
    return (multiply(minuend, power) - multiply(subtrahend, power)) / power;
}
/**
 * 高精度加法函数
 */
function addNum(summand1, summand2){
    var power = getMaxPowerForTen(summand1, summand2);
    return (multiply(summand1, power) + multiply(summand2, power)) / power;
}
/**
 * 高精乘法函数
 */
function multiply(multiplier, multiplicand) {
    var m=0;
    var s1 = multiplier.toString();
    var s2 = multiplicand.toString();
    try {
        m += s1.split(".")[1].length;
    } catch (e) { }
    try {
        m += s2.split(".")[1].length;
    } catch (e) {}
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10,m);
}
/**
 * 获取最大次幂
 */
function getMaxPowerForTen(arg1, arg2) {
    var r1 = 0;
    var r2 = 0;
    try {
        r1 = arg1.toString().split(".")[1].length;
    } catch(e) {
        r1 = 0;
    }
    try {
        r2 = arg2.toString().split(".")[1].length;
    } catch(e) {
        r2 = 0;
    }
    return Math.pow(10, Math.max(r1, r2));
}