CREATE TABLE tt_product_act (
id VARCHAR2(32) NOT NULL,
year_month VARCHAR2(7) NULL,
product_policy_code VARCHAR2(50) NULL,
product_policy_name VARCHAR2(200) NULL,
product_code VARCHAR2(50) NULL,
product_name VARCHAR2(200) NULL,
customer_code VARCHAR2(50) NULL,
customer_name VARCHAR2(200) NULL,
erp_code VARCHAR2(50) NULL,
begin_date VARCHAR2(10) NULL,
end_date VARCHAR2(10) NULL,
ori_price NUMBER(10,2) NULL,
pre_month_quantity NUMBER(10,0) NULL,
pre_month_amount NUMBER(10,2) NULL,
month_plan_quantity NUMBER(10,0) NULL,
month_plan_amount NUMBER(10,2) NULL,
target_quantity NUMBER(10,0) NULL,
target_amount NUMBER(10,2) NULL,
target_quantity2 NUMBER(10,0) NULL,
target_amount2 NUMBER(10,2) NULL,
target_quantity3 NUMBER(10,0) NULL,
target_amount3 NUMBER(10,2) NULL,
current_period_cost NUMBER NULL,
current_period_cost_rate NUMBER(10,4) NULL,
later_period_cost NUMBER NULL,
later_period_cost_rate NUMBER(10,4) NULL,
execution_mode VARCHAR2(400) NULL,
remark VARCHAR2(400) NULL,
create_date TIMESTAMP(6) NULL,
create_name VARCHAR2(100) NULL,
update_date TIMESTAMP(6) NULL,
update_name VARCHAR2(100) NULL,
PRIMARY KEY (id)
)
NOCOMPRESS
NOPARALLEL ;

COMMENT ON TABLE tt_product_act IS '产品活动申请表';
COMMENT ON COLUMN tt_product_act.id IS '逻辑主键';
COMMENT ON COLUMN tt_product_act.year_month IS '结案年月（yyyy-MM）';
COMMENT ON COLUMN tt_product_act.product_policy_code IS '产品政策编码';
COMMENT ON COLUMN tt_product_act.product_policy_name IS '产品政策名称';
COMMENT ON COLUMN tt_product_act.product_code IS '产品编码';
COMMENT ON COLUMN tt_product_act.product_name IS '产品名称';
COMMENT ON COLUMN tt_product_act.customer_code IS '客户编码';
COMMENT ON COLUMN tt_product_act.customer_name IS '客户名称';
COMMENT ON COLUMN tt_product_act.erp_code IS '客户erp编码';
COMMENT ON COLUMN tt_product_act.begin_date IS '活动开始时间（yyyy-MM-dd）';
COMMENT ON COLUMN tt_product_act.end_date IS '活动结束时间（yyyy-MM-dd）';
COMMENT ON COLUMN tt_product_act.ori_price IS '原供价';
COMMENT ON COLUMN tt_product_act.pre_month_quantity IS '上月销售数量';
COMMENT ON COLUMN tt_product_act.pre_month_amount IS '上月销售金额';
COMMENT ON COLUMN tt_product_act.month_plan_quantity IS '月度销售计划量';
COMMENT ON COLUMN tt_product_act.month_plan_amount IS '月度销售计划金额';
COMMENT ON COLUMN tt_product_act.target_quantity IS '目标数量';
COMMENT ON COLUMN tt_product_act.target_amount IS '目标金额';
COMMENT ON COLUMN tt_product_act.target_quantity2 IS '目标数量2';
COMMENT ON COLUMN tt_product_act.target_amount2 IS '目标金额2';
COMMENT ON COLUMN tt_product_act.target_quantity3 IS '目标数量3';
COMMENT ON COLUMN tt_product_act.target_amount3 IS '目标金额3';
COMMENT ON COLUMN tt_product_act.current_period_cost IS '当期费用';
COMMENT ON COLUMN tt_product_act.current_period_cost_rate IS '当期费率';
COMMENT ON COLUMN tt_product_act.later_period_cost IS '后返费用';
COMMENT ON COLUMN tt_product_act.later_period_cost_rate IS '后返费率';
COMMENT ON COLUMN tt_product_act.execution_mode IS '终端执行方式';
COMMENT ON COLUMN tt_product_act.remark IS '备注';
COMMENT ON COLUMN tt_product_act.create_date IS '创建时间';
COMMENT ON COLUMN tt_product_act.create_name IS '创建人';
COMMENT ON COLUMN tt_product_act.update_date IS '修改时间';
COMMENT ON COLUMN tt_product_act.update_name IS '修改人';


CREATE TABLE tt_product_policy (
  id VARCHAR2(32) NOT NULL,
  product_policy_code VARCHAR2(50) NULL,
  product_policy_name VARCHAR2(200) NULL,
  begin_date VARCHAR2(10) NULL,
  end_date VARCHAR2(10) NULL,
  policy_type VARCHAR2(50) NULL,
  policy_cycle VARCHAR2(50) NULL,
  policy_desc VARCHAR2(500) NULL,
  product_code VARCHAR2(50) NULL,
  product_name VARCHAR2(200) NULL,
  enable_dms_cost VARCHAR2(1) NULL,
  dms_cost_account_code VARCHAR2(50) NULL,
  dms_cost_account_name VARCHAR2(200) NULL,
  dms_cost_payment_code VARCHAR2(50) NULL,
  dms_cost_payment_name VARCHAR2(100) NULL,
  dms_cost_give_flag VARCHAR2(1) NULL,
  dms_cost_discount_flag VARCHAR2(1) NULL,
  dms_cost_buy_quantity NUMBER(10,0) NULL,
  dms_cost_give_quantity NUMBER(10,0) NULL,
  dms_cost_discount_price NUMBER(10,2) NULL,
  dms_cost_ori_price NUMBER(10,2) NULL,
  dms_cost_rate NUMBER(10,4) NULL,
  enable_tpm_cost VARCHAR2(1) NULL,
  tpm_cost_account_code VARCHAR2(50) NULL,
  tpm_cost_account_name VARCHAR2(200) NULL,
  tpm_cost_payment_code VARCHAR2(50) NULL,
  tpm_cost_payment_name VARCHAR2(100) NULL,
  tpm_cost_rate NUMBER(10,4) NULL,
  PRIMARY KEY (id)
)
NOCOMPRESS
NOPARALLEL ;

COMMENT ON TABLE tt_product_policy IS '产品政策配置表';
COMMENT ON COLUMN tt_product_policy.id IS '逻辑主键';
COMMENT ON COLUMN tt_product_policy.product_policy_code IS '产品政策编码';
COMMENT ON COLUMN tt_product_policy.product_policy_name IS '产品政策名称';
COMMENT ON COLUMN tt_product_policy.begin_date IS '活动开始时间（yyyy-MM-dd）';
COMMENT ON COLUMN tt_product_policy.end_date IS '活动结束时间（yyyy-MM-dd）';
COMMENT ON COLUMN tt_product_policy.policy_type IS '政策类型';
COMMENT ON COLUMN tt_product_policy.policy_cycle IS '政策周期';
COMMENT ON COLUMN tt_product_policy.policy_desc IS '政策描述';
COMMENT ON COLUMN tt_product_policy.product_code IS '目标产品编码';
COMMENT ON COLUMN tt_product_policy.product_name IS '目标产品名称';
COMMENT ON COLUMN tt_product_policy.enable_dms_cost IS '启用dms费用，即随车，特价费用。启用：Y，不启用：N';
COMMENT ON COLUMN tt_product_policy.dms_cost_account_code IS 'DMS费用科目编码，即活动细类编码';
COMMENT ON COLUMN tt_product_policy.dms_cost_account_name IS 'DMS费用科目名称，即活动细类名称';
COMMENT ON COLUMN tt_product_policy.dms_cost_payment_code IS 'DMS费用支付方式编码';
COMMENT ON COLUMN tt_product_policy.dms_cost_payment_name IS 'DMS费用支付方式名称';
COMMENT ON COLUMN tt_product_policy.dms_cost_give_flag IS 'DMS随车搭赠标记，Y：是，N：否';
COMMENT ON COLUMN tt_product_policy.dms_cost_discount_flag IS 'DMS折扣标记，即特价标记。Y：是，N：否';
COMMENT ON COLUMN tt_product_policy.dms_cost_buy_quantity IS 'DMS费用购买数量';
COMMENT ON COLUMN tt_product_policy.dms_cost_give_quantity IS 'DMS费用赠送数量';
COMMENT ON COLUMN tt_product_policy.dms_cost_discount_price IS 'DMS费用特价，即打折后的价格';
COMMENT ON COLUMN tt_product_policy.dms_cost_ori_price IS 'DMS费用原价';
COMMENT ON COLUMN tt_product_policy.dms_cost_rate IS 'DMS费率';
COMMENT ON COLUMN tt_product_policy.enable_tpm_cost IS '启用tpm费用，即随车，特价费用。启用：Y，不启用：N';
COMMENT ON COLUMN tt_product_policy.tpm_cost_account_code IS 'TPM费用科目编码，即活动细类编码';
COMMENT ON COLUMN tt_product_policy.tpm_cost_account_name IS 'TPM费用科目名称，即活动细类名称';
COMMENT ON COLUMN tt_product_policy.tpm_cost_payment_code IS 'TPM费用支付方式编码';
COMMENT ON COLUMN tt_product_policy.tpm_cost_payment_name IS 'TPM费用支付方式名称';
COMMENT ON COLUMN tt_product_policy.tpm_cost_rate IS 'TPM费率';


CREATE TABLE tt_r_prod_policy_prod (
id VARCHAR2(32) NOT NULL,
product_policy_code VARCHAR2(50) NULL,
product_policy_name VARCHAR2(200) NULL,
product_code VARCHAR2(50) NULL,
product_name VARCHAR2(200) NULL,
create_date TIMESTAMP(6) NULL,
create_name VARCHAR2(100) NULL,
PRIMARY KEY (id)
)
NOCOMPRESS
NOPARALLEL ;

COMMENT ON TABLE tt_r_prod_policy_prod IS '产品政策关联考核产品关系表';
COMMENT ON COLUMN tt_r_prod_policy_prod.id IS '逻辑主键';
COMMENT ON COLUMN tt_r_prod_policy_prod.product_policy_code IS '产品政策编码';
COMMENT ON COLUMN tt_r_prod_policy_prod.product_policy_name IS '产品政策名称';
COMMENT ON COLUMN tt_r_prod_policy_prod.product_code IS '产品编码';
COMMENT ON COLUMN tt_r_prod_policy_prod.product_name IS '产品名称';
COMMENT ON COLUMN tt_r_prod_policy_prod.create_date IS '创建时间';
COMMENT ON COLUMN tt_r_prod_policy_prod.create_name IS '创建人';


CREATE TABLE tt_product_policy_area (
id VARCHAR2(32) NOT NULL,
product_policy_code VARCHAR2(50) NULL,
product_policy_name VARCHAR2(200) NULL,
org_code VARCHAR2(50) NULL,
org_name VARCHAR2(200) NULL,
customer_code VARCHAR2(50) NULL,
customer_name VARCHAR2(200) NULL,
protype NUMBER(10,0) NULL,
create_date TIMESTAMP(6) NULL,
create_name VARCHAR2(100) NULL,
PRIMARY KEY (id)
)
NOCOMPRESS
NOPARALLEL ;

COMMENT ON TABLE tt_product_policy_area IS '返利区域表';
COMMENT ON COLUMN tt_product_policy_area.id IS '逻辑主键id';
COMMENT ON COLUMN tt_product_policy_area.product_policy_code IS '产品政策编码';
COMMENT ON COLUMN tt_product_policy_area.product_policy_name IS '产品政策名称';
COMMENT ON COLUMN tt_product_policy_area.org_code IS '组织编码';
COMMENT ON COLUMN tt_product_policy_area.org_name IS '组织名称';
COMMENT ON COLUMN tt_product_policy_area.customer_code IS '经销商编码';
COMMENT ON COLUMN tt_product_policy_area.customer_name IS '经销商名称';
COMMENT ON COLUMN tt_product_policy_area.protype IS '0非包含，1包含';
COMMENT ON COLUMN tt_product_policy_area.create_date IS '创建时间';
COMMENT ON COLUMN tt_product_policy_area.create_name IS '创建人';


CREATE TABLE tt_product_policy_formula (
id VARCHAR2(32) NOT NULL,
product_policy_code VARCHAR2(50) NULL,
product_policy_name VARCHAR2(200) NULL,
formula_con VARCHAR2(200) NULL,
formula_val VARCHAR2(200) NULL,
create_date TIMESTAMP(6) NULL,
create_name VARCHAR2(100) NULL,
PRIMARY KEY (id)
)
NOCOMPRESS
NOPARALLEL ;

COMMENT ON TABLE tt_product_policy_formula IS '产品政策公式信息表';
COMMENT ON COLUMN tt_product_policy_formula.id IS '逻辑主键';
COMMENT ON COLUMN tt_product_policy_formula.product_policy_code IS '产品政策编码';
COMMENT ON COLUMN tt_product_policy_formula.product_policy_name IS '产品政策名称';
COMMENT ON COLUMN tt_product_policy_formula.formula_con IS '公式条件';
COMMENT ON COLUMN tt_product_policy_formula.formula_val IS '公式值';
COMMENT ON COLUMN tt_product_policy_formula.create_date IS '创建时间';
COMMENT ON COLUMN tt_product_policy_formula.create_name IS '创建人';

-- Add/modify columns
alter table TT_PRODUCT_POLICY add ENABLE_STATUS number;
alter table TT_PRODUCT_POLICY add SUPERIMPOSITION_FLAG varchar2(1);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.ENABLE_STATUS
  is '启用状态：0启用，1停用';
comment on column TT_PRODUCT_POLICY.SUPERIMPOSITION_FLAG
  is '是否叠加 Y是，N否';



DELETE FROM tm_dict_type WHERE dict_type_code = 'product_policy_cycle';
INSERT INTO tm_dict_type(ID, dict_type_code, dict_type_name, dict_desc,create_date, create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), 'product_policy_cycle', 'TPM产品政策周期','TPM产品政策周期',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'product_policy_cycle';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '10', '年度', '年度',NULL,'product_policy_cycle',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '20', '季度', '季度',NULL,'product_policy_cycle',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '30', '月度', '月度',NULL,'product_policy_cycle',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '40', '单次', '单次',NULL,'product_policy_cycle',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);


-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add create_date timestamp(6);
alter table TT_PRODUCT_POLICY add create_name varchar2(100);
alter table TT_PRODUCT_POLICY add update_date timestamp(6);
alter table TT_PRODUCT_POLICY add update_name varchar2(100);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.create_date
  is '创建时间';
comment on column TT_PRODUCT_POLICY.create_name
  is '创建人';
comment on column TT_PRODUCT_POLICY.update_date
  is '修改时间';
comment on column TT_PRODUCT_POLICY.update_name
  is '修改人';

DELETE FROM tm_dict_type WHERE dict_type_code = 'product_policy_type';
INSERT INTO tm_dict_type(ID, dict_type_code, dict_type_name, dict_desc,create_date, create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), 'product_policy_type', 'TPM产品政策类型','TPM产品政策类型',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'product_policy_type';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '10', '产品政策月度申请', '产品政策月度申请',NULL,'product_policy_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '20', '产品政策变更，临时申请', '产品政策变更，临时申请',NULL,'product_policy_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '30', '产品政策特批申请', '产品政策特批申请',NULL,'product_policy_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '40', 'KA产品政策申请', 'KA产品政策申请',NULL,'product_policy_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add product_level_code VARCHAR2(50);
alter table TT_PRODUCT_POLICY add product_level_name VARCHAR2(100);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.product_level_code
  is '产品层级编码';
comment on column TT_PRODUCT_POLICY.product_level_name
  is '产品层级名称';

DELETE FROM tm_dict_type WHERE dict_type_code = 'tpl_type';
INSERT INTO tm_dict_type(ID, dict_type_code, dict_type_name, dict_desc,create_date, create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), 'tpl_type', '模板类型','模板类型',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

DELETE FROM tm_dict_data WHERE dict_type_code = 'tpl_type';
INSERT INTO tm_dict_data(ID, dict_code,dict_value,dict_desc,parent_id,dict_type_code,create_date,create_name,update_date,update_name,is_system)
VALUES(lower(sys_guid()), '10', '返利模式', '返利模式',NULL,'tpl_type',SYSDATE,'系统初始化',SYSDATE,'系统初始化',0);

-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Drop columns
alter table TT_PRODUCT_POLICY drop column DMS_COST_DISCOUNT_FLAG;
alter table TT_PRODUCT_POLICY rename column DMS_COST_GIVE_FLAG to DMS_COST_TYPE;
alter table TT_PRODUCT_POLICY modify DMS_COST_TYPE VARCHAR2(10);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.DMS_COST_TYPE
  is 'DMS随车搭赠类型：GIVE:随车搭赠，DISCOUNT:特价';

-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Drop columns
alter table TT_PRODUCT_POLICY drop column POLICY_TYPE;
alter table TT_PRODUCT_POLICY add apply_begin_date varchar2(10);
alter table TT_PRODUCT_POLICY add apply_end_date varchar2(10);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.apply_begin_date
  is '可申请开始时间';
comment on column TT_PRODUCT_POLICY.apply_end_date
  is '可申请结束时间';

-- Add/modify columns
alter table TT_PRODUCT_ACT add ACT_CODE VARCHAR2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.ACT_CODE
  is '活动编码';



-- Add/modify columns
alter table TT_PRODUCT_ACT add dms_Policy_Flag VARCHAR2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.dms_Policy_Flag
  is '是否推送dms政策 Y是N否';


-- Add/modify columns
alter table TT_PRODUCT_ACT add DMS_REBATE_FLAG VARCHAR2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.DMS_REBATE_FLAG
  is '是否计算返利 Y是N否';

-- Add/modify columns
alter table TT_PRODUCT_ACT add state NUMBER ;
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.state
  is '1开启0关闭';
-- Add/modify columns
alter table TT_PRODUCT_ACT add refer_Act_Code VARCHAR2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.refer_Act_Code
  is '关联原始活动号';
-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add customer_type varchar2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.customer_type
  is '客户级别';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add SUPPLY_PRODUCT_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add SUPPLY_PRODUCT_NAME VARCHAR2(200);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.SUPPLY_PRODUCT_CODE
  is '货补产品编码';
comment on column TT_PRODUCT_ACT.SUPPLY_PRODUCT_NAME
  is '货补产品名称';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add POLICY_CYCLE varchar2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.POLICY_CYCLE
  is '政策周期';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add BPM_STATUS number;
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.BPM_STATUS
  is '工作流状态';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add policy_desc varchar2(400);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.policy_desc
  is '政策描述';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add position_code varchar2(50);
alter table TT_PRODUCT_ACT add position_name varchar2(100);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.position_code
  is '创建职位编码';
comment on column TT_PRODUCT_ACT.position_name
  is '创建职位名称';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Drop columns
alter table TT_PRODUCT_ACT drop column PRE_MONTH_QUANTITY;
alter table TT_PRODUCT_ACT drop column PRE_MONTH_AMOUNT;
alter table TT_PRODUCT_ACT drop column MONTH_PLAN_QUANTITY;
alter table TT_PRODUCT_ACT drop column MONTH_PLAN_AMOUNT;

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add PRODUCT_LEVEl_FLAG varchar2(1);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.PRODUCT_LEVEl_FLAG
  is '产品层级标记:Y：是，N：否';


-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add org_code varchar2(50);
alter table TT_PRODUCT_ACT add org_name varchar2(100);
alter table TT_PRODUCT_ACT add dms_cost_type_code varchar2(50);
alter table TT_PRODUCT_ACT add dms_cost_type_name varchar2(200);
alter table TT_PRODUCT_ACT add dms_financial_account_code varchar2(50);
alter table TT_PRODUCT_ACT add dms_financial_account_name varchar2(200);
alter table TT_PRODUCT_ACT add dms_cost_account_code varchar2(50);
alter table TT_PRODUCT_ACT add dms_cost_account_name varchar2(200);
alter table TT_PRODUCT_ACT add tpm_cost_type_code varchar2(50);
alter table TT_PRODUCT_ACT add tpm_cost_type_name varchar2(200);
alter table TT_PRODUCT_ACT add tpm_financial_account_code varchar2(50);
alter table TT_PRODUCT_ACT add tpm_financial_account_name varchar2(200);
alter table TT_PRODUCT_ACT add tpm_cost_account_code varchar2(50);
alter table TT_PRODUCT_ACT add tpm_cost_account_name varchar2(200);

-- Add comments to the columns
comment on column TT_PRODUCT_ACT.org_code
  is '组织编码';
comment on column TT_PRODUCT_ACT.org_name
  is '组织名称';
comment on column TT_PRODUCT_ACT.dms_cost_type_code
  is 'DMS费用类型编码';
comment on column TT_PRODUCT_ACT.dms_cost_type_name
  is 'DMS费用类型名称';
comment on column TT_PRODUCT_ACT.dms_financial_account_code
  is 'DMS财务科目编码';
comment on column TT_PRODUCT_ACT.dms_financial_account_name
  is 'DMS财务科目名称';
comment on column TT_PRODUCT_ACT.dms_cost_account_code
  is 'DMS费用科目编码';
comment on column TT_PRODUCT_ACT.dms_cost_account_name
  is 'DMS费用科目名称';
  comment on column TT_PRODUCT_ACT.tpm_cost_type_code
  is 'TPM费用类型编码';
comment on column TT_PRODUCT_ACT.tpm_cost_type_name
  is 'TPM费用类型名称';
comment on column TT_PRODUCT_ACT.tpm_financial_account_code
  is 'TPM财务科目编码';
comment on column TT_PRODUCT_ACT.tpm_financial_account_name
  is 'TPM财务科目名称';
comment on column TT_PRODUCT_ACT.tpm_cost_account_code
  is 'TPM费用科目编码';
comment on column TT_PRODUCT_ACT.tpm_cost_account_name
  is 'TPM费用科目名称';

-- Alter table
alter table TT_HI_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_HI_PRODUCT_ACT add org_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add org_name varchar2(100);
alter table TT_HI_PRODUCT_ACT add dms_cost_type_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add dms_cost_type_name varchar2(200);
alter table TT_HI_PRODUCT_ACT add dms_financial_account_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add dms_financial_account_name varchar2(200);
alter table TT_HI_PRODUCT_ACT add dms_cost_account_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add dms_cost_account_name varchar2(200);
alter table TT_HI_PRODUCT_ACT add tpm_cost_type_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add tpm_cost_type_name varchar2(200);
alter table TT_HI_PRODUCT_ACT add tpm_financial_account_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add tpm_financial_account_name varchar2(200);
alter table TT_HI_PRODUCT_ACT add tpm_cost_account_code varchar2(50);
alter table TT_HI_PRODUCT_ACT add tpm_cost_account_name varchar2(200);

-- Add comments to the columns
comment on column TT_HI_PRODUCT_ACT.org_code
  is '组织编码';
comment on column TT_HI_PRODUCT_ACT.org_name
  is '组织名称';
comment on column TT_HI_PRODUCT_ACT.dms_cost_type_code
  is 'DMS费用类型编码';
comment on column TT_HI_PRODUCT_ACT.dms_cost_type_name
  is 'DMS费用类型名称';
comment on column TT_HI_PRODUCT_ACT.dms_financial_account_code
  is 'DMS财务科目编码';
comment on column TT_HI_PRODUCT_ACT.dms_financial_account_name
  is 'DMS财务科目名称';
comment on column TT_HI_PRODUCT_ACT.dms_cost_account_code
  is 'DMS费用科目编码';
comment on column TT_HI_PRODUCT_ACT.dms_cost_account_name
  is 'DMS费用科目名称';
  comment on column TT_HI_PRODUCT_ACT.tpm_cost_type_code
  is 'TPM费用类型编码';
comment on column TT_HI_PRODUCT_ACT.tpm_cost_type_name
  is 'TPM费用类型名称';
comment on column TT_HI_PRODUCT_ACT.tpm_financial_account_code
  is 'TPM财务科目编码';
comment on column TT_HI_PRODUCT_ACT.tpm_financial_account_name
  is 'TPM财务科目名称';
comment on column TT_HI_PRODUCT_ACT.tpm_cost_account_code
  is 'TPM费用科目编码';
comment on column TT_HI_PRODUCT_ACT.tpm_cost_account_name
  is 'TPM费用科目名称';


-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add dms_cost_type_code varchar2(50);
alter table TT_PRODUCT_POLICY add dms_cost_type_name varchar2(200);
alter table TT_PRODUCT_POLICY add dms_financial_account_code varchar2(50);
alter table TT_PRODUCT_POLICY add dms_financial_account_name varchar2(200);

comment on column TT_PRODUCT_POLICY.dms_cost_type_code
  is '费用类型编码';
comment on column TT_PRODUCT_POLICY.dms_cost_type_name
  is '费用类型名称';
comment on column TT_PRODUCT_POLICY.dms_financial_account_code
  is '财务科目编码';
comment on column TT_PRODUCT_POLICY.dms_financial_account_name
  is '财务科目名称';



-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add tpm_cost_type_code varchar2(50);
alter table TT_PRODUCT_POLICY add tpm_cost_type_name varchar2(200);
alter table TT_PRODUCT_POLICY add tpm_financial_account_code varchar2(50);
alter table TT_PRODUCT_POLICY add tpm_financial_account_name varchar2(200);

comment on column TT_PRODUCT_POLICY.tpm_cost_type_code
  is '费用类型编码';
comment on column TT_PRODUCT_POLICY.tpm_cost_type_name
  is '费用类型名称';
comment on column TT_PRODUCT_POLICY.tpm_financial_account_code
  is '财务科目编码';
comment on column TT_PRODUCT_POLICY.tpm_financial_account_name
  is '财务科目名称';

-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add TPM_FINANCIAL_CODE VARCHAR2(50);
alter table TT_PRODUCT_POLICY add DMS_FINANCIAL_CODE VARCHAR2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.TPM_FINANCIAL_CODE
  is '财务科目，对应SAP';
comment on column TT_PRODUCT_POLICY.DMS_FINANCIAL_CODE
  is '财务科目，对应SAP';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add DMS_FINANCIAL_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add TPM_FINANCIAL_CODE VARCHAR2(50);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.DMS_FINANCIAL_CODE
  is 'DMS财务科目编码,对应SAP';
comment on column TT_PRODUCT_ACT.TPM_FINANCIAL_CODE
  is 'TPM财务科目编码,对应SAP';


-- Alter table
alter table TT_HI_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_HI_PRODUCT_ACT add DMS_FINANCIAL_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add TPM_FINANCIAL_CODE VARCHAR2(50);
-- Add comments to the columns
comment on column TT_HI_PRODUCT_ACT.DMS_FINANCIAL_CODE
  is 'DMS财务科目编码，对应SAP';
comment on column TT_HI_PRODUCT_ACT.TPM_FINANCIAL_CODE
  is 'TPM财务科目编码，对应SAP';


-- Alter table
alter table TT_PRODUCT_POLICY_FORMULA
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY_FORMULA modify FORMULA_CON VARCHAR2(500);
alter table TT_PRODUCT_POLICY_FORMULA modify FORMULA_VAL VARCHAR2(500);
alter table TT_PRODUCT_POLICY_FORMULA add FORMULA_CON_DESC VARCHAR2(500);
alter table TT_PRODUCT_POLICY_FORMULA add FORMULA_VAL_DESC VARCHAR2(500);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY_FORMULA.FORMULA_CON_DESC
  is '公式条件描述';
comment on column TT_PRODUCT_POLICY_FORMULA.FORMULA_VAL_DESC
  is '公式值描述';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add ORG_SAP_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add ORG_SAP_COST_CENTER VARCHAR2(50);
alter table TT_PRODUCT_ACT add ORG_TYPE_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add DMS_COST_CLASSIFY_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add DMS_COST_CLASSIFY_NAME VARCHAR2(100);
alter table TT_PRODUCT_ACT add DMS_BUSINESS_COST_TYPE_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add DMS_BUSINESS_COST_TYPE_NAME VARCHAR2(100);
alter table TT_PRODUCT_ACT add TPM_COST_CLASSIFY_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add TPM_COST_CLASSIFY_NAME VARCHAR2(100);
alter table TT_PRODUCT_ACT add TPM_BUSINESS_COST_TYPE_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add TPM_BUSINESS_COST_TYPE_NAME VARCHAR2(100);
alter table TT_PRODUCT_ACT add USER_NAME VARCHAR2(50);
alter table TT_PRODUCT_ACT add GROUP_NO VARCHAR2(100);
alter table TT_PRODUCT_ACT add USE_ORG_CODE VARCHAR2(50);
alter table TT_PRODUCT_ACT add USE_ORG_NAME VARCHAR2(200);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.ORG_SAP_CODE
  is 'SAP的部门编号';
comment on column TT_PRODUCT_ACT.ORG_SAP_COST_CENTER
  is 'SAP成本中心编码';
comment on column TT_PRODUCT_ACT.ORG_TYPE_CODE
  is '部门类型';
comment on column TT_PRODUCT_ACT.DMS_COST_CLASSIFY_CODE
  is 'DMS财务费用分类编码';
comment on column TT_PRODUCT_ACT.DMS_COST_CLASSIFY_NAME
  is 'DMS财务费用分类名称';
comment on column TT_PRODUCT_ACT.DMS_BUSINESS_COST_TYPE_CODE
  is 'DMS业务费用分类编码';
comment on column TT_PRODUCT_ACT.DMS_BUSINESS_COST_TYPE_NAME
  is 'DMS业务费用分类名称';
comment on column TT_PRODUCT_ACT.TPM_COST_CLASSIFY_CODE
  is 'TPM财务费用分类编码';
comment on column TT_PRODUCT_ACT.TPM_COST_CLASSIFY_NAME
  is 'TPM财务费用分类名称';
comment on column TT_PRODUCT_ACT.TPM_BUSINESS_COST_TYPE_CODE
  is 'TPM业务费用分类编码';
comment on column TT_PRODUCT_ACT.TPM_BUSINESS_COST_TYPE_NAME
  is 'TPM业务费用分类名称';
comment on column TT_PRODUCT_ACT.USER_NAME
  is '账号';
comment on column TT_PRODUCT_ACT.GROUP_NO
  is '集团号';
comment on column TT_PRODUCT_ACT.USE_ORG_CODE
  is '费用归属事业部编码';
comment on column TT_PRODUCT_ACT.USE_ORG_NAME
  is '费用归属事业部名称';

-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT rename column USE_ORG_CODE to SYB_ORG_CODE;
alter table TT_PRODUCT_ACT rename column USE_ORG_NAME to SYB_ORG_NAME;


-- Alter table
alter table TT_HI_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_HI_PRODUCT_ACT add ORG_SAP_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add ORG_SAP_COST_CENTER VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add ORG_TYPE_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add DMS_COST_CLASSIFY_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add DMS_COST_CLASSIFY_NAME VARCHAR2(100);
alter table TT_HI_PRODUCT_ACT add DMS_BUSINESS_COST_TYPE_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add DMS_BUSINESS_COST_TYPE_NAME VARCHAR2(100);
alter table TT_HI_PRODUCT_ACT add TPM_COST_CLASSIFY_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add TPM_COST_CLASSIFY_NAME VARCHAR2(100);
alter table TT_HI_PRODUCT_ACT add TPM_BUSINESS_COST_TYPE_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add TPM_BUSINESS_COST_TYPE_NAME VARCHAR2(100);
alter table TT_HI_PRODUCT_ACT add USER_NAME VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add GROUP_NO VARCHAR2(100);
alter table TT_HI_PRODUCT_ACT add USE_ORG_CODE VARCHAR2(50);
alter table TT_HI_PRODUCT_ACT add USE_ORG_NAME VARCHAR2(200);
-- Add comments to the columns
comment on column TT_HI_PRODUCT_ACT.ORG_SAP_CODE
  is 'SAP的部门编号';
comment on column TT_HI_PRODUCT_ACT.ORG_SAP_COST_CENTER
  is 'SAP成本中心编码';
comment on column TT_HI_PRODUCT_ACT.ORG_TYPE_CODE
  is '部门类型';
comment on column TT_HI_PRODUCT_ACT.DMS_COST_CLASSIFY_CODE
  is 'DMS财务费用分类编码';
comment on column TT_HI_PRODUCT_ACT.DMS_COST_CLASSIFY_NAME
  is 'DMS财务费用分类名称';
comment on column TT_HI_PRODUCT_ACT.DMS_BUSINESS_COST_TYPE_CODE
  is 'DMS业务费用分类编码';
comment on column TT_HI_PRODUCT_ACT.DMS_BUSINESS_COST_TYPE_NAME
  is 'DMS业务费用分类名称';
comment on column TT_HI_PRODUCT_ACT.TPM_COST_CLASSIFY_CODE
  is 'TPM财务费用分类编码';
comment on column TT_HI_PRODUCT_ACT.TPM_COST_CLASSIFY_NAME
  is 'TPM财务费用分类名称';
comment on column TT_HI_PRODUCT_ACT.TPM_BUSINESS_COST_TYPE_CODE
  is 'TPM业务费用分类编码';
comment on column TT_HI_PRODUCT_ACT.TPM_BUSINESS_COST_TYPE_NAME
  is 'TPM业务费用分类名称';
comment on column TT_HI_PRODUCT_ACT.USER_NAME
  is '账号';
comment on column TT_HI_PRODUCT_ACT.GROUP_NO
  is '集团号';
comment on column TT_HI_PRODUCT_ACT.USE_ORG_CODE
  is '费用归属事业部编码';
comment on column TT_HI_PRODUCT_ACT.USE_ORG_NAME
  is '费用归属事业部名称';

-- Alter table
alter table TT_HI_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_HI_PRODUCT_ACT rename column USE_ORG_CODE to SYB_ORG_CODE;
alter table TT_HI_PRODUCT_ACT rename column USE_ORG_NAME to SYB_ORG_NAME;



--活动变更表
drop table tt_product_act_change;
CREATE TABLE TT_PRODUCT_ACT_CHANGE
   (	ID VARCHAR2(32 BYTE),
	ORG_CODE VARCHAR2(32 BYTE),
	ORG_NAME VARCHAR2(32 BYTE),
	YEAR_MONTH VARCHAR2(32 BYTE),
	CG_TYPE NUMBER,
	CUSTOMER_CODE VARCHAR2(32 BYTE),
	ERP_CODE VARCHAR2(32 BYTE),
	CUSTOMER_NAME VARCHAR2(150 BYTE),
	ACT_CODE VARCHAR2(32 BYTE),
	OLD_PRODUCT_POLICY_CODE VARCHAR2(32 BYTE),
	OLD_PRODUCT_POLICY_NAME VARCHAR2(150 BYTE),
	OLD_BEGIN_DATE VARCHAR2(32 BYTE),
	OLD_END_DATE VARCHAR2(32 BYTE),
	OLD_TARGET_AMOUNT NUMBER(10,2),
	OLD_TARGET_QUANTITY NUMBER,
	OLD_TARGET_AMOUNT2 NUMBER(10,2),
	OLD_TARGET_QUANTITY2 NUMBER,
	OLD_TARGET_AMOUNT3 NUMBER(10,2),
	OLD_TARGET_QUANTITY3 NUMBER,
	NEW_PRODUCT_POLICY_NAME VARCHAR2(150 BYTE),
	NEW_PRODUCT_POLICY_CODE VARCHAR2(32 BYTE),
	NEW_BEGIN_DATE VARCHAR2(32 BYTE),
	NEW_END_DATE VARCHAR2(32 BYTE),
	NEW_TARGET_AMOUNT NUMBER,
	NEW_TARGET_QUANTITY NUMBER,
	NEW_TARGET_AMOUNT2 NUMBER,
	NEW_TARGET_QUANTITY2 NUMBER,
	NEW_TARGET_AMOUNT3 NUMBER,
	NEW_TARGET_QUANTITY3 NUMBER,
	SUPPLY_PRODUCT_CODE VARCHAR2(32 BYTE),
	SUPPLY_PRODUCT_NAME VARCHAR2(100 BYTE),
	PRODUCT_CODE VARCHAR2(32 BYTE),
	PRODUCT_NAME VARCHAR2(100 BYTE),
	BPM_STATUS NUMBER,
	CURRENT_PERIOD_COST NUMBER,
	CURRENT_PERIOD_COST_RATE NUMBER(10,4),
	LATER_PERIOD_COST NUMBER,
	LATER_PERIOD_COST_RATE NUMBER(10,4),
	DMS_FINANCIAL_ACCOUNT_CODE VARCHAR2(50 BYTE),
	DMS_FINANCIAL_ACCOUNT_NAME VARCHAR2(200 BYTE),
	TPM_FINANCIAL_ACCOUNT_CODE VARCHAR2(50 BYTE),
	TPM_FINANCIAL_ACCOUNT_NAME VARCHAR2(200 BYTE),
	POSITION_CODE VARCHAR2(32 BYTE),
	EXECUTION_MODE VARCHAR2(400 BYTE),
	REMARK VARCHAR2(400 BYTE),
	CREATE_NAME VARCHAR2(100 BYTE),
	CREATE_DATE DATE,
	UPDATE_NAME VARCHAR2(100 BYTE),
	UPDATE_DATE DATE,
	REBATE_PRICE NUMBER,
	TERMINAL_PRICE NUMBER,
	DISTRIBUTOR_GROSS_PROFIT_RATE NUMBER
   );

   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.CUSTOMER_CODE IS '客户编码';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.ERP_CODE IS 'erp编码';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.CUSTOMER_NAME IS '经销商名';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.ACT_CODE IS '原活动编码';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_PRODUCT_POLICY_CODE IS '原政策编码';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_PRODUCT_POLICY_NAME IS '原政策名';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_BEGIN_DATE IS '原活动开始时间';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_END_DATE IS '原活动结束时间';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_TARGET_AMOUNT IS '原目标金额1';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_TARGET_QUANTITY IS '原目标数量1';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_TARGET_AMOUNT2 IS '原目标金额2';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_TARGET_QUANTITY2 IS '原目标数量2';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_TARGET_AMOUNT3 IS '原目标金额3';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.OLD_TARGET_QUANTITY3 IS '原目标数量3';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_PRODUCT_POLICY_NAME IS '新政策名';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_PRODUCT_POLICY_CODE IS '新政策编码';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_BEGIN_DATE IS '新活动开始时间';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_END_DATE IS '新活动结束时间';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_TARGET_AMOUNT IS '新目标金额1';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_TARGET_QUANTITY IS '新目标数量1';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_TARGET_AMOUNT2 IS '新目标金额2';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_TARGET_QUANTITY2 IS '新目标数量2';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_TARGET_AMOUNT3 IS '新目标金额3';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.NEW_TARGET_QUANTITY3 IS '新目标数量3';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.SUPPLY_PRODUCT_CODE IS '货补产品编码';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.SUPPLY_PRODUCT_NAME IS '货补产品名';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.BPM_STATUS IS '工作流审核状态';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.EXECUTION_MODE IS '执行广本';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.REMARK IS '备注';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.REBATE_PRICE IS '折合供价';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.TERMINAL_PRICE IS '终端供价';
   COMMENT ON COLUMN TT_PRODUCT_ACT_CHANGE.DISTRIBUTOR_GROSS_PROFIT_RATE IS '经销商毛利率';


-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add position_code VARCHAR2(50);
alter table TT_PRODUCT_POLICY add position_name VARCHAR2(100);
alter table TT_PRODUCT_POLICY add org_code VARCHAR2(50);
alter table TT_PRODUCT_POLICY add org_name VARCHAR2(100);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.position_code
  is '创建职位编码';
comment on column TT_PRODUCT_POLICY.position_name
  is '创建职位名称';
comment on column TT_PRODUCT_POLICY.org_code
  is '创建组织编码';
comment on column TT_PRODUCT_POLICY.org_name
  is '创建组织名称';

-- Alter table
alter table TT_HI_PRODUCT_ACT
  storage
  (
    next 8
  )
;
-- Add/modify columns
alter table TT_HI_PRODUCT_ACT add PRODUCT_LEVEl_FLAG varchar2(1);
-- Add comments to the columns
comment on column TT_HI_PRODUCT_ACT.PRODUCT_LEVEl_FLAG
  is '产品层级标记:Y：是，N：否';


-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add rebate_price number(10,2);
alter table TT_PRODUCT_ACT add terminal_price number(10,2);
alter table TT_PRODUCT_ACT add distributor_gross_profit_rate number(10,4);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.rebate_price
  is '折合供价';
comment on column TT_PRODUCT_ACT.terminal_price
  is '终端供价';
comment on column TT_PRODUCT_ACT.distributor_gross_profit_rate
  is '经销商毛利率';


-- Alter table
alter table TT_HI_PRODUCT_ACT
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_HI_PRODUCT_ACT add rebate_price number(10,2);
alter table TT_HI_PRODUCT_ACT add terminal_price number(10,2);
alter table TT_HI_PRODUCT_ACT add distributor_gross_profit_rate number(10,4);
-- Add comments to the columns
comment on column TT_HI_PRODUCT_ACT.rebate_price
  is '折合供价';
comment on column TT_HI_PRODUCT_ACT.terminal_price
  is '终端供价';
comment on column TT_HI_PRODUCT_ACT.distributor_gross_profit_rate
  is '经销商毛利率';

-- Alter table
alter table TT_PRODUCT_POLICY
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_PRODUCT_POLICY add dms_cost_price number(10,2);
alter table TT_PRODUCT_POLICY add dms_standard_price number(10,2);
alter table TT_PRODUCT_POLICY add dms_actual_cost_rate number(10,4);
-- Add comments to the columns
comment on column TT_PRODUCT_POLICY.dms_cost_price
  is '随车活动成本价';
comment on column TT_PRODUCT_POLICY.dms_standard_price
  is '随车活动标准价';
comment on column TT_PRODUCT_POLICY.dms_actual_cost_rate
  is '随车活动实际费率';


alter table tt_product_act_change add (rebate_Price number);
COMMENT on COLUMN tt_product_act_change.rebate_Price is '折合供价';

alter table tt_product_act_change add (terminal_Price number);
COMMENT on COLUMN tt_product_act_change.terminal_Price is '终端供价';

alter table tt_product_act_change add (distributor_gross_profit_rate number);
COMMENT on COLUMN tt_product_act_change.distributor_gross_profit_rate is '经销商毛利率';


-- Alter table
alter table TT_PRODUCT_ACT
  storage
  (
    next 1
  )
;
-- Add/modify columns
alter table TT_PRODUCT_ACT add create_org_code VARCHAR2(50);
alter table TT_PRODUCT_ACT add create_org_name VARCHAR2(100);
-- Add comments to the columns
comment on column TT_PRODUCT_ACT.create_org_code
  is '创建人组织编码';
comment on column TT_PRODUCT_ACT.create_org_name
  is '创建人组织名称';



