-- Create table
create table TD_HI_PROMOTION_FEE_NO
(
  id                 VARCHAR2(32) not null,
  create_date        DATE,
  create_name        VA<PERSON><PERSON>R2(50),
  update_date        DATE,
  update_name        VA<PERSON>HAR2(50),
  pm_id              VARCHAR2(32),
  fee_no             VARCHAR2(32),
  status             NUMBER,
  data_status        NUMBER,
  refer_business_key VARCHAR2(64),
  submit_date        TIMESTAMP(6),
  flag_key           VARCHAR2(64),
  submit_name        VARCHAR2(128)
)
tablespace USERS
  pctfree 10
  initrans 1
  maxtrans 255;
  

  
CREATE OR REPLACE
procedure proc_hi_promotion_fee_no(
ids in varchar2,
flagId in varchar2,
submitName in varchar2
) as
begin
  	insert into td_hi_promotion_fee_no(
  		id,
  		create_date,
  		create_name,
  		update_date,
  		update_name,
  		pm_id,
  		fee_no,
  		status,
  		data_status,
  		refer_business_key,
  		submit_date,
  		flag_key,
  		submit_name
  	)
  	select
  		lower(sys_guid()) AS ID,
  		create_date,
  		create_name,
  		update_date,
  		update_name,
  		(select id from td_hi_promotion where flag_key = flagId ) as pm_id,
  		fee_no,
  		status,
  		'1' as data_status,
  		id as refer_business_key,
  		sysdate as submit_date,
  		flagId as flag_key,
  		submitName as submit_name
  	from td_promotion_fee_no 
  	where instr(ids, pm_id) > 0;
commit;
end;