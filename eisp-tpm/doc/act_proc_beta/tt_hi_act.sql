CREATE OR REPLACE
procedure proc_hi_act(
ids in varchar2,
flagId in varchar2,
submitName in varchar2
) as
begin
INSERT INTO tt_hi_act(
          ID,
          act_code,
          act_name,
          position_code,
          position_name,
          begin_date,
          end_date,
          YEAR,
          MONTH,
          year_month,
          org_type_code,
          org_type_name,
          org_code,
          org_name,
          customer_type_code,
          customer_type_name,
          channel_code,
          channel_name,
          customer_code,
          customer_name,
          erp_code,
          act_type_code,
          act_type_name,
          act_templete_code,
          act_templete_name,
          cost_type_code,
          cost_type_name,
          financial_account_code,
          financial_account_name,
          cost_account_code,
          cost_account_name,
          act_mode_code,
          act_mode_name,
          payment_code,
          payment_name,
          cost_classify_code,
          cost_classify_name,
          has_audit,
          over_audit_scale,
          audit_valid_month,
          has_negative_amount,
          has_multiple_audit,
          has_push_sfa,
          act_da_require_code,
          act_da_require_name,
          audit_material_code,
          audit_material_name,
          amount,
          final_amount,
          audit_amount,
          bank_num,
          premium_product_code,
          premium_product_name,
          business_key,
          audit_status,
          final_report_status,
          bpm_status,
          create_date,
          create_name,
          data_status,
          refer_business_key,
          submit_date,
          submit_name,
          has_upload_photo,
          remark,
          flag_key,
          org_sap_code,
          receive_bank_name,
          receive_bank_country,
          receive_bank_code,
          receive_bank_account_owner,
          receive_bank_account,
          use_org_code,
          use_org_name,
          reimbursement_person_code,
          reimbursement_person_name,
          reimbursement_position_code,
          reimbursement_position_name,
          position_level_code,
          position_level_name,
          bvtyp,
      tax,
      payment_condition
       )
       SELECT
          lower(sys_guid()) AS ID,
          act_code,
          act_name,
          position_code,
          position_name,
          begin_date,
          end_date,
          YEAR,
          MONTH,
          year_month,
          org_type_code,
          org_type_name,
          org_code,
          org_name,
          customer_type_code,
          customer_type_name,
          channel_code,
          channel_name,
          customer_code,
          customer_name,
          erp_code,
          act_type_code,
          act_type_name,
          act_templete_code,
          act_templete_name,
          cost_type_code,
          cost_type_name,
          financial_account_code,
          financial_account_name,
          cost_account_code,
          cost_account_name,
          act_mode_code,
          act_mode_name,
          payment_code,
          payment_name,
          cost_classify_code,
          cost_classify_name,
          has_audit,
          over_audit_scale,
          audit_valid_month,
          has_negative_amount,
          has_multiple_audit,
          has_push_sfa,
          act_da_require_code,
          act_da_require_name,
          audit_material_code,
          audit_material_name,
          amount,
          final_amount,
          audit_amount,
          bank_num,
          premium_product_code,
          premium_product_name,
          (SELECT id FROM tt_hi_act_bill_main WHERE flag_key=flagId) AS business_key,
          audit_status,
          final_report_status,
          bpm_status,
          create_date,
          create_name,
          1 AS data_status,
          ID AS refer_business_key,
          sysdate AS submit_date,
          submitName AS submit_name,
          has_upload_photo,
          remark,
          flagId AS flag_key,
          org_sap_code,
          receive_bank_name,
          receive_bank_country,
          receive_bank_code,
          receive_bank_account_owner,
          receive_bank_account,
          use_org_code,
          use_org_name,
          reimbursement_person_code,
          reimbursement_person_name,
          reimbursement_position_code,
          reimbursement_position_name,
          position_level_code,
          position_level_name,
          bvtyp,
      tax,
      payment_condition
        FROM tt_act
        WHERE instr(ids, business_key) > 0;
 commit;
 end;

