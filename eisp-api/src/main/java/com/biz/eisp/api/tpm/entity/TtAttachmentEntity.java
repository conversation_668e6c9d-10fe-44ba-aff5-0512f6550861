package com.biz.eisp.api.tpm.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.biz.eisp.base.core.entity.TbAttachmentEntity;

/**
 * 核销附件.
 * <AUTHOR>
 * @version v1.0
 */
@Entity
@Table(name = "tt_attachment", schema = "")
@SuppressWarnings("serial")
public class TtAttachmentEntity extends TbAttachmentEntity{
	/**业务id*/
	private String businessId;
	/**业务code */
	private String otherCode;
	/**描述*/
	private String remark;
	/**扩展字段1*/
	private String extChar1;
	/**扩展字段2*/
	private String extChar2;
	
	/**
	 * getter businessId
	 * @return businessId
	 */
	@Column(name = "BUSINESS_ID", length = 32)
	public String getBusinessId() {
		return businessId;
	}
	/**
	 * setter businessId   
	 */
	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}
	/**
	 * getter remark
	 * @return remark
	 */
	@Column(name = "REMARK", length = 200)
	public String getRemark() {
		return remark;
	}
	/**
	 * setter remark   
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}
	/**
	 * getter extChar1
	 * @return extChar1
	 */
	@Column(name = "EXTCHAR1", length = 200)
	public String getExtChar1() {
		return extChar1;
	}
	/**
	 * setter extChar1   
	 */
	public void setExtChar1(String extChar1) {
		this.extChar1 = extChar1;
	}
	/**
	 * getter extChar2
	 * @return extChar2
	 */
	@Column(name = "EXTCHAR2", length = 100)
	public String getExtChar2() {
		return extChar2;
	}
	/**
	 * setter extChar2   
	 */
	public void setExtChar2(String extChar2) {
		this.extChar2 = extChar2;
	}
	@Column(name = "OTHER_CODE", length = 32)
	public String getOtherCode() {
		return otherCode;
	}
	public void setOtherCode(String otherCode) {
		this.otherCode = otherCode;
	}
}
