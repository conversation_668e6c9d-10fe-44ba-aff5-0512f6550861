package com.biz.eisp.api.synccrms.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * 同步日志
 * <AUTHOR>
 * @create 2018-01-22 上午8:50
 */
@Entity
@Table(name="sync_log")
public class SyncLogEntity {
    /**
     * 主键
     */
    private String id;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 同步类型
     */
    private String syncType;
    /**
     * 日志内容
     */
    private String content;

    /**
     * 是否有效--目前是为了解决同步组织时造成的时间差
     */
    private String status;

    @Id
    @GeneratedValue(generator = "paymentableGenerator")
    @GenericGenerator(name = "paymentableGenerator", strategy = "uuid")
    @Column(name = "ID", nullable = false, length = 32)
    public String getId() {
        return this.id;
    }


    public void setId(String id) {
        this.id = id;
    }


    @Column(name = "CREATE_NAME")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "sync_type")
    public String getSyncType() {
        return syncType;
    }
    @Column(name = "content")
    public String getContent() {
        return content;
    }

    public void setSyncType(String syncType) {
        this.syncType = syncType;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Column(name = "status")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
