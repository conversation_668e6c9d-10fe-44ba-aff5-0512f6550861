package com.biz.eisp.api.synccrms.service;

import com.biz.eisp.api.synccrms.vo.CrmsAdsComrateVo;
import com.biz.eisp.api.synccrms.vo.UserInfoDto;
import com.biz.eisp.base.core.service.BaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @create 2018-02-07 上午8:39
 */
public interface CrmsAdsComrateService extends BaseService {
    /**
     * 查询达成率对象
     *
     * @param vo
     * @return
     */
    public CrmsAdsComrateVo getCrmsAdsComrateVo(CrmsAdsComrateVo vo);

    //管式任务
    CrmsAdsComrateVo getCrmsCsComrateVo(CrmsAdsComrateVo vo);

    //查询吨信息
    String queryNtgew(List<String> names,String type);

    List<UserInfoDto> queryUserAreaInfo(List<String> dealers);

}
