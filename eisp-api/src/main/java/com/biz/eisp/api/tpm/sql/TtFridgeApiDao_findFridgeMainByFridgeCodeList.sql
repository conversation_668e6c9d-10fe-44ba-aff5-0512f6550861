SELECT
  t.id AS id,
  t.fridge_Code AS fridgeCode,
  t.fridge_Supplier AS fridgeSupplier,
  t.fridge_Type AS fridgeType,
  to_char(t.delivery_Date, 'yyyy-MM-dd') AS deliveryDate
FROM tt_fridge_main t
    LEFT JOIN (
SELECT
  distinct
  t1.id AS id,
  t1.customer_Code AS customerCode,
  t1.enable_Status AS enableStatus
FROM tm_customer t1
  LEFT JOIN TM_R_TERM_CUST_POS_BG t2 ON t1.id = t2.customer_id
  LEFT JOIN tm_terminal t3 ON t2.terminal_id = t3.id
WHERE 1=1
	AND t3.terminal_Code = '${vo.terminalCode}') c ON t.customer_Code = c.customerCode
	AND c.enableStatus = 0
WHERE 1=1

  and t.customer_code in c.customercode
  AND (t.enable_Status = 0 OR t.enable_Status = 3)
  <#if vo.fridgeCode ?exists && vo.fridgeCode ?length gt 0>
	  AND t.fridge_Code LIKE '%${vo.fridgeCode}%'
  </#if>
	  AND (t.terminal_Code != '${vo.terminalCode}' or t.terminal_code is null)

