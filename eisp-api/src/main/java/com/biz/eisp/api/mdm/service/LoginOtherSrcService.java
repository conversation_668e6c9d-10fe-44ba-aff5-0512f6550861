package com.biz.eisp.api.mdm.service;

import com.biz.eisp.api.mdm.vo.TmLoginTempVo;
import com.biz.eisp.base.core.service.BaseService;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by clare on 2018/2/6.
 */
public interface LoginOtherSrcService extends BaseService{

    /**
     * 检查唯一码
     * @param tempVo
     * @param request
     * @return
     */
    public void checkUnDyIdentif(TmLoginTempVo tempVo, HttpServletRequest request);

    /**
     * 封装请求参数用户信息
     * @param tempVo
     * @param request
     */
    public void otherSrcInTo(TmLoginTempVo tempVo, HttpServletRequest request);

    public void loginAndWriteSession(TmLoginTempVo tempVo, HttpServletRequest request);
}
