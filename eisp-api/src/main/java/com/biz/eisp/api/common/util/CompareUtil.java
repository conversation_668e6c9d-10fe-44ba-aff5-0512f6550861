package com.biz.eisp.api.common.util;

import com.biz.eisp.base.common.util.StringUtil;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * @Title: 值比较相等
 * @Description: 描述
 * @date 2016/12/2
 */
public class CompareUtil {

    /**
     * 比较值相等
     * BigDecimal,String,Integer
     * @param o1
     * @param o2
     * @return
     */
    public static boolean eq(Object o1, Object o2) {
        boolean flag = false;
        if (StringUtil.isNotEmpty(o1) && StringUtil.isNotEmpty(o2)) {
            if (o1 instanceof Integer && o2 instanceof Integer) {
                int o1int = ((Integer) o1).intValue();
                int o2int = ((Integer) o2).intValue();
                if (o1int == o2int && o1.equals(o2)) {
                    flag = true;
                }
            } else if (o1 instanceof String && o2 instanceof String) {
                if (o1.equals(o2)) {
                    flag = true;
                }
            } else if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
                if (((BigDecimal) o1).compareTo((BigDecimal) o2) == 0) {
                    flag = true;
                }
            }
        }
        return flag;
    }

    /**
     * 比较值相等
     * 两个值均非null
     * BigDecimal,String,Integer
     * @param o1
     * @param o2
     * @return
     */
    public static boolean eqNotNull(Object o1, Object o2){
        boolean flag = false;
        if (StringUtil.isNotEmpty(o1) && StringUtil.isNotEmpty(o2)) {
            if (o1 instanceof Integer && o2 instanceof Integer) {
                int o1int = ((Integer) o1).intValue();
                int o2int = ((Integer) o2).intValue();
                if (o1int == o2int && o1.equals(o2)) {
                    flag = true;
                }
            } else if (o1 instanceof String && o2 instanceof String) {
                if (o1.equals(o2)) {
                    flag = true;
                }
            } else if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
                if (((BigDecimal) o1).compareTo((BigDecimal) o2) == 0) {
                    flag = true;
                }
            } else {
                throw new RuntimeException("类型不匹配，不能比较[o1为" + o1.getClass() + "类型，o2为" + o2.getClass() + "]");
            }
        } else {
            throw new RuntimeException("比较值不能为空,o1值为[" + o1 + "]，o2值为[" + o2 + "]");
        }
        return flag;
    }

    /**
     * 不相等
     * @param o1
     * @param o2
     * @return
     */
    public static boolean neq(Object o1, Object o2) {
        return !eq(o1, o2);
    }

    /**
     * 判断o1大于o2
     * @param o1
     * @param o2
     * @return
     */
    public static boolean gt(Object o1, Object o2) {
        boolean flag = false;
        if (StringUtil.isNotEmpty(o1) && StringUtil.isNotEmpty(o2)) {
            if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
                if (((BigDecimal) o1).compareTo((BigDecimal) o2) > 0) {
                    flag = true;
                }
            } else if (o1 instanceof Integer && o2 instanceof Integer) {
                int o1int = ((Integer) o1).intValue();
                int o2int = ((Integer) o2).intValue();
                if (o1int > o2int) {
                    flag = true;
                }
            } else {
                throw new RuntimeException("类型不匹配，不能比较[o1为" + o1.getClass() + "类型，o2为" + o2.getClass() + "]");
            }
        } else {
            throw new RuntimeException("比较值不能为空,o1值为[" + o1 + "]，o2值为[" + o2 + "]");
        }
        return flag;
    }


    /**
     * 判断o1大于等于o2
     * @param o1
     * @param o2
     * @return
     */
    public static boolean ge(Object o1, Object o2) {
        boolean flag = false;
        if (StringUtil.isNotEmpty(o1) && StringUtil.isNotEmpty(o2)) {
            if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
                if (((BigDecimal) o1).compareTo((BigDecimal) o2) >= 0) {
                    flag = true;
                }
            } else {
                throw new RuntimeException("类型不匹配，不能比较[o1为" + o1.getClass() + "类型，o2为" + o2.getClass() + "]");
            }
        } else {
            throw new RuntimeException("比较值不能为空,o1值为[" + o1 + "]，o2值为[" + o2 + "]");
        }
        return flag;
    }


    /**
     * 判断对象是否小于0
     * @param o
     * @return
     */
    public static boolean lt0(BigDecimal o) {
        return lt(o, BigDecimal.ZERO);
    }

    /**
     * 判断对象是否小于等于0
     * @param o
     * @return
     */
    public static boolean le0(BigDecimal o){
        return le(o,BigDecimal.ZERO);
    }

    /**
     * 判断对象是否大于0
     * @param o
     * @return
     */
    public static boolean gt0(BigDecimal o) {
        return o != null ? gt(o, BigDecimal.ZERO) : false;
    }

    /**
     * 判断对象是否大于等于0
     * @param o
     * @return
     */
    public static boolean ge0(BigDecimal o){
        return ge(o,BigDecimal.ZERO);
    }


    /**
     * 判断对象是否等于0
     * @param o
     * @return
     */
    public static boolean eq0(BigDecimal o) {
        return o != null ? eq(o, BigDecimal.ZERO) : false;
    }

    public static boolean eq0(Integer o){
        return o != null ? eq(o, 0) : false;
    }
    /**
     * 判断o1小于o2
     * @param o1
     * @param o2
     * @return
     */
    public static boolean lt(Object o1, Object o2) {
        boolean flag = false;
        if (StringUtil.isNotEmpty(o1) && StringUtil.isNotEmpty(o2)) {
            if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
                if (((BigDecimal) o1).compareTo((BigDecimal) o2) < 0) {
                    flag = true;
                }
            } else {
                throw new RuntimeException("类型不匹配，不能比较[o1为" + o1.getClass() + "类型，o2为" + o2.getClass() + "]");
            }
        } else {
            throw new RuntimeException("比较值不能为空,o1值为[" + o1 + "]，o2值为[" + o2 + "]");
        }
        return flag;
    }

    /**
     * 判断o1大于等于o2
     * @param o1
     * @param o2
     * @return
     */
    public static boolean le(Object o1, Object o2) {
        boolean flag = false;
        if (StringUtil.isNotEmpty(o1) && StringUtil.isNotEmpty(o2)) {
            if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
                if (((BigDecimal) o1).compareTo((BigDecimal) o2) <= 0) {
                    flag = true;
                }
            } else {
                throw new RuntimeException("类型不匹配，不能比较[o1为" + o1.getClass() + "类型，o2为" + o2.getClass() + "]");
            }
        } else {
            throw new RuntimeException("比较值不能为空,o1值为[" + o1 + "]，o2值为[" + o2 + "]");
        }
        return flag;
    }
}
