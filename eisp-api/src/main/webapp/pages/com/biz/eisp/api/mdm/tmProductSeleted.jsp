<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@include file="/context/mytags.jsp"%>
<div id="tmCostAccountSelectedMain" class="easyui-layout" fit="true">
	<div region="center" style="padding: 1px;">
	    <t:datagrid name="ttProductSeletedList" pagination="false" fitColumns="true" title="已选产品" queryMode = "group" singleSelect="false"
	    onLoadSuccess="loadElecteGrid"  idField="id" actionUrl="tmCommonMdmController.do?findSelectedProductList&id=${id }">
	        <t:dgCol title="主键" hidden="true" field="id" ></t:dgCol>
	        <t:dgCol title="产品编码" hidden="true" field="productCode" ></t:dgCol>
	    	<t:dgCol title="产品层级" field="productLevel"  width="120" dictionary="pro_type"></t:dgCol>
	        <t:dgCol title="产品层级名称" field="productName"  width="120"></t:dgCol>
	    	<t:dgToolBar title="移除" icon="icon-remove"  onclick="removeAccount()"></t:dgToolBar>
	    	<t:dgToolBar title="全部移除" icon="icon-remove"  onclick="removeAllAccount()"></t:dgToolBar>
	    </t:datagrid>
	</div>
</div>

<script type="text/javascript">
	//移除
	function removeAccount(){
		var checkListTarget =  $("#ttProductSeletedList").datagrid("getSelections");
		if(checkListTarget==null || checkListTarget==""){
			tip("请至少选择一条数据");
			return false;
		}
		var selectRows = [];
		//选中数据加入数组
		for (var i = 0; i < checkListTarget.length; i++) {
			selectRows.push(checkListTarget[i]);
		}
		for (var i = 0; i < selectRows.length; i++) {
			var checkRowIndex = $("#ttProductSeletedList").datagrid("getRowIndex",selectRows[i]);
			//移除该数据
			$("#ttProductSeletedList").datagrid("deleteRow",checkRowIndex);
		}
		loadElecteGrid();
	}
	//加载待选角色
	function loadElecteGrid(){
		//加载待选列表
		var excludeId = "'-1'";//默认一个值
		var checkedTarget = $("#ttProductSeletedList").datagrid("getRows");
		if(checkedTarget != null && checkedTarget != ""){
			excludeId = "";
			for(var i = 0;i<checkedTarget.length;i++){
				if(excludeId != ""){
					excludeId+=",";
				}
				excludeId += "'"+checkedTarget[i].id+"'";
			}
		}
		//拼装查询条件 然后重新加载
		var queryParams = $("#ttProductList").datagrid('options').queryParams;
		$("#ttProductListListtb_r").find('*').each(
		function() {
			queryParams[$(this).attr('name')]=$(this).val();
		});
		queryParams["excludeId"]=excludeId;
		
		$('#ttProductList').datagrid({
			queryParams: queryParams
		});
	}
	
	function removeAllAccount() {
		var rows = $("#ttProductSeletedList").datagrid("getRows");
		$.each(rows, function(i, obj) {
			$("#ttProductSeletedList").datagrid("selectRow", i);
		});
		removeAccount();
	}
</script> 